package com.yelink.dfs.service.impl.device;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.asyncexcel.core.exporter.DataExportParam;
import com.asyncexcel.core.model.ExcelTask;
import com.asyncexcel.springboot.ExcelService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yelink.dfs.common.UserAuthenService;
import com.yelink.dfs.constant.AppendixTypeEnum;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.CycleEnum;
import com.yelink.dfs.constant.ImportTypeEnum;
import com.yelink.dfs.constant.RedisKeyPrefix;
import com.yelink.dfs.constant.common.ModelUploadFileEnum;
import com.yelink.dfs.constant.device.DeviceAddTypeEnum;
import com.yelink.dfs.constant.device.DeviceTypeEnum;
import com.yelink.dfs.constant.device.DevicesStateEnum;
import com.yelink.dfs.constant.device.DevicesUseStateEnum;
import com.yelink.dfs.constant.model.FacilitiesCountTypeEnum;
import com.yelink.dfs.constant.model.FacilitiesTypeEnum;
import com.yelink.dfs.constant.model.ModelIdEnum;
import com.yelink.dfs.constant.screen.ScreenConstant;
import com.yelink.dfs.constant.sensor.SensorParamEnum;
import com.yelink.dfs.constant.sensor.SensorTypeCodeEnum;
import com.yelink.dfs.constant.target.TargetConst;
import com.yelink.dfs.constant.target.TargetTypeEnum;
import com.yelink.dfs.entity.alarm.AlarmEntity;
import com.yelink.dfs.entity.common.CommonState;
import com.yelink.dfs.entity.device.DeviceEntity;
import com.yelink.dfs.entity.device.DeviceSensorEntity;
import com.yelink.dfs.entity.device.dto.DeviceGroupTypeDTO;
import com.yelink.dfs.entity.device.dto.DeviceImportDTO;
import com.yelink.dfs.entity.device.dto.DeviceImportExcelDTO;
import com.yelink.dfs.entity.device.dto.DeviceScreen;
import com.yelink.dfs.entity.device.dto.DeviceSelectDTO;
import com.yelink.dfs.entity.device.dto.DeviceStateOverviewDTO;
import com.yelink.dfs.entity.device.dto.IndicatorEntityDTO;
import com.yelink.dfs.entity.device.dto.MetricsDocument;
import com.yelink.dfs.entity.device.dto.RecordDeviceDayRunVO;
import com.yelink.dfs.entity.energy.manage.DeviceEnergyConsumptionEntity;
import com.yelink.dfs.entity.energy.manage.DeviceGasConsumptionEntity;
import com.yelink.dfs.entity.energy.manage.DeviceTimePeriodEnergyConsumptionEntity;
import com.yelink.dfs.entity.manufacture.ProductionLineEntity;
import com.yelink.dfs.entity.model.AreaEntity;
import com.yelink.dfs.entity.model.FacilitiesEntity;
import com.yelink.dfs.entity.model.GridEntity;
import com.yelink.dfs.entity.model.ModelEntity;
import com.yelink.dfs.entity.model.WorkCenterDeviceEntity;
import com.yelink.dfs.entity.model.WorkCenterDeviceRelevanceEntity;
import com.yelink.dfs.entity.model.dto.ExcelDeviceDTO;
import com.yelink.dfs.entity.model.vo.ExcelLogVO;
import com.yelink.dfs.entity.order.WorkOrderDeviceRelevanceEntity;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.entity.order.dto.DeviceOperationOrderDTO;
import com.yelink.dfs.entity.order.dto.OperationOrderDTO;
import com.yelink.dfs.entity.order.dto.WorkOrderBasicUnitQueryDTO;
import com.yelink.dfs.entity.product.CraftEntity;
import com.yelink.dfs.entity.product.MaterialEntity;
import com.yelink.dfs.entity.product.ProcedureDeviceTargetReferenceEntity;
import com.yelink.dfs.entity.product.ProcedureDeviceTypeEntity;
import com.yelink.dfs.entity.screen.dto.DeviceStopDTO;
import com.yelink.dfs.entity.screen.dto.RecordDeviceOeeDTO;
import com.yelink.dfs.entity.screen.vo.DeviceDataStatisticsVO;
import com.yelink.dfs.entity.screen.vo.DeviceEnergyConsumptionVO;
import com.yelink.dfs.entity.screen.vo.DeviceScreenInfoVO;
import com.yelink.dfs.entity.sensor.SensorEntity;
import com.yelink.dfs.entity.supplier.SupplierEntity;
import com.yelink.dfs.entity.target.TargetDictEntity;
import com.yelink.dfs.entity.target.TargetModelEntity;
import com.yelink.dfs.entity.target.TargetThresholdEntity;
import com.yelink.dfs.entity.target.dto.BatchAndTargetValDTO;
import com.yelink.dfs.entity.target.dto.DeviceStateToYangzjDTO;
import com.yelink.dfs.entity.target.dto.DeviceTargetMonitorDTO;
import com.yelink.dfs.entity.target.dto.DeviceTargetToYangzjDTO;
import com.yelink.dfs.entity.target.dto.EquipmentAnalysisDTO;
import com.yelink.dfs.entity.target.dto.LinkageToYangzjDTO;
import com.yelink.dfs.entity.target.dto.ManualTargetAnalysisDTO;
import com.yelink.dfs.entity.target.dto.MaterialAndBatchDTO;
import com.yelink.dfs.entity.target.dto.RecordManualCollectionDTO;
import com.yelink.dfs.entity.target.dto.RunningStatePieChartDTO;
import com.yelink.dfs.entity.target.dto.TargetDiagramDTO;
import com.yelink.dfs.entity.target.metrics.MetricsDeviceOeeEntity;
import com.yelink.dfs.entity.target.record.RecordDeviceCurrentEntity;
import com.yelink.dfs.entity.target.record.RecordDeviceDayRunEntity;
import com.yelink.dfs.entity.target.record.RecordDeviceMaintainEntity;
import com.yelink.dfs.entity.target.record.RecordDevicePauseEntity;
import com.yelink.dfs.entity.target.record.RecordDeviceRunEntity;
import com.yelink.dfs.entity.target.record.RecordDeviceStateEntity;
import com.yelink.dfs.entity.target.record.RecordDeviceStopCountEntity;
import com.yelink.dfs.entity.target.record.RecordDeviceStopEntity;
import com.yelink.dfs.entity.target.record.RecordDeviceUtilizationEntity;
import com.yelink.dfs.entity.target.record.RecordDeviceVhtEntity;
import com.yelink.dfs.entity.target.record.RecordDeviceVoltageEntity;
import com.yelink.dfs.entity.target.record.RecordManualCollectionEntity;
import com.yelink.dfs.entity.user.SysUserEntity;
import com.yelink.dfs.mapper.alarm.AlarmMapper;
import com.yelink.dfs.mapper.device.DeviceMapper;
import com.yelink.dfs.mapper.model.AreaMapper;
import com.yelink.dfs.mapper.model.FacilitiesMapper;
import com.yelink.dfs.mapper.model.GridMapper;
import com.yelink.dfs.mapper.model.ModelMapper;
import com.yelink.dfs.mapper.order.WorkOrderDeviceRelevanceMapper;
import com.yelink.dfs.mapper.order.WorkOrderMapper;
import com.yelink.dfs.mapper.sensor.SensorMapper;
import com.yelink.dfs.mapper.target.TargetThresholdMapper;
import com.yelink.dfs.mapper.target.record.RecordDeviceDayRunMapper;
import com.yelink.dfs.mapper.target.record.RecordDevicePauseMapper;
import com.yelink.dfs.mapper.target.record.RecordDeviceRunMapper;
import com.yelink.dfs.mapper.target.record.RecordDeviceStopMapper;
import com.yelink.dfs.mapper.target.record.RecordManualCollectionMapper;
import com.yelink.dfs.mapper.target.record.RecordWorkOrderStateMapper;
import com.yelink.dfs.mapper.user.enums.EnabledEnum;
import com.yelink.dfs.open.v1.device.dto.DeviceDTO;
import com.yelink.dfs.open.v2.model.dto.DeviceQueryDTO;
import com.yelink.dfs.open.v2.model.vo.DeviceVO;
import com.yelink.dfs.open.v2.model.vo.ProductionLineListVO;
import com.yelink.dfs.provider.constant.KafkaMessageTypeEnum;
import com.yelink.dfs.service.barcode.BarCodeAnalysisService;
import com.yelink.dfs.service.barcode.LabelService;
import com.yelink.dfs.service.barcode.LabelTypeConfigService;
import com.yelink.dfs.service.common.AppendixService;
import com.yelink.dfs.service.common.DictService;
import com.yelink.dfs.service.common.ImportDataRecordService;
import com.yelink.dfs.service.common.ModelUploadFileService;
import com.yelink.dfs.service.common.UploadService;
import com.yelink.dfs.service.device.DeviceSensorService;
import com.yelink.dfs.service.device.DeviceService;
import com.yelink.dfs.service.device.IndicatorService;
import com.yelink.dfs.service.energy.manage.DeviceEnergyConsumptionService;
import com.yelink.dfs.service.energy.manage.DeviceGasConsumptionService;
import com.yelink.dfs.service.energy.manage.DeviceTimePeriodEnergyConsumptionService;
import com.yelink.dfs.service.impl.common.WorkPropertise;
import com.yelink.dfs.service.manufacture.ProductionLineService;
import com.yelink.dfs.service.model.FacCalEuiService;
import com.yelink.dfs.service.model.FacilitiesService;
import com.yelink.dfs.service.model.GridService;
import com.yelink.dfs.service.model.ModelService;
import com.yelink.dfs.service.model.WorkCenterDeviceRelevanceService;
import com.yelink.dfs.service.model.WorkCenterDeviceService;
import com.yelink.dfs.service.model.WorkCenterService;
import com.yelink.dfs.service.model.excel.CommonModelExcel;
import com.yelink.dfs.service.order.WorkOrderBasicUnitRelationService;
import com.yelink.dfs.service.product.CraftService;
import com.yelink.dfs.service.product.MaterialService;
import com.yelink.dfs.service.product.ProcedureDeviceTargetReferenceService;
import com.yelink.dfs.service.product.ProcedureDeviceTypeService;
import com.yelink.dfs.service.product.SkuService;
import com.yelink.dfs.service.sensor.SensorService;
import com.yelink.dfs.service.supplier.SupplierService;
import com.yelink.dfs.service.target.TargetDictService;
import com.yelink.dfs.service.target.TargetModelService;
import com.yelink.dfs.service.target.record.RecordDeviceCurrentService;
import com.yelink.dfs.service.target.record.RecordDeviceMaintainService;
import com.yelink.dfs.service.target.record.RecordDeviceOeeService;
import com.yelink.dfs.service.target.record.RecordDevicePauseService;
import com.yelink.dfs.service.target.record.RecordDeviceResumeDailyService;
import com.yelink.dfs.service.target.record.RecordDeviceRunService;
import com.yelink.dfs.service.target.record.RecordDeviceStopCountService;
import com.yelink.dfs.service.target.record.RecordDeviceStopService;
import com.yelink.dfs.service.target.record.RecordDeviceUtilizationService;
import com.yelink.dfs.service.target.record.RecordDeviceVhtService;
import com.yelink.dfs.service.target.record.RecordDeviceVoltageService;
import com.yelink.dfs.service.user.SysUserService;
import com.yelink.dfs.service.work.calendar.WorkCalendarService;
import com.yelink.dfs.utils.EasyExcelDTO;
import com.yelink.dfs.utils.SpringUtil;
import com.yelink.dfs.utils.SqlUtil;
import com.yelink.dfscommon.api.assignment.AssignmentInterface;
import com.yelink.dfscommon.common.excel.BusinessCodeEnum;
import com.yelink.dfscommon.constant.Constants;
import com.yelink.dfscommon.constant.DeviceModelEnum;
import com.yelink.dfscommon.constant.ModelEnum;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.constant.dfs.barcode.LabelInfoEnum;
import com.yelink.dfscommon.constant.dfs.order.WorkOrderStateEnum;
import com.yelink.dfscommon.dto.DeviceReqDTO;
import com.yelink.dfscommon.dto.DeviceTimePeriodEnergyConsumptionDTO;
import com.yelink.dfscommon.dto.ElectronicRackDTO;
import com.yelink.dfscommon.dto.ImportProgressDTO;
import com.yelink.dfscommon.dto.StateEnumDTO;
import com.yelink.dfscommon.dto.TimePeriodEnergyConsumptionDTO;
import com.yelink.dfscommon.dto.dfs.CodeInfoSelectDTO;
import com.yelink.dfscommon.dto.screen.DeviceWorkStateDTO;
import com.yelink.dfscommon.entity.AppendixEntity;
import com.yelink.dfscommon.entity.CommonType;
import com.yelink.dfscommon.entity.PreviewTemplateEntity;
import com.yelink.dfscommon.entity.dfs.SkuEntity;
import com.yelink.dfscommon.entity.dfs.barcode.LabelEntity;
import com.yelink.dfscommon.entity.dfs.barcode.LabelTypeConfigEntity;
import com.yelink.dfscommon.entity.dfs.barcode.PrintDTO;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.provider.MessagePushToKafkaService;
import com.yelink.dfscommon.utils.ColumnUtil;
import com.yelink.dfscommon.utils.DateUtil;
import com.yelink.dfscommon.utils.EasyExcelUtil;
import com.yelink.dfscommon.utils.ExcelUtil;
import com.yelink.dfscommon.utils.JacksonUtil;
import com.yelink.dfscommon.utils.MathUtil;
import com.yelink.dfscommon.utils.WrapperUtil;
import com.yelink.dfscommon.vo.DeviceStateCountVO;
import com.yelink.dfscommon.vo.DeviceStateTimeDetailsVO;
import com.yelink.dfscommon.vo.DeviceStateTimeVO;
import com.yelink.dfscommon.vo.ScreenTargetModelVo;
import com.yelink.inner.common.IotService;
import com.yelink.inner.data.access.SensorIotSetInfo;
import com.yelink.inner.listen.CommandHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpMethod;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Service
 *
 * <AUTHOR>
 * @Date 2021-03-03 14:52
 */
@Slf4j
@Service
public class DeviceServiceImpl extends ServiceImpl<DeviceMapper, DeviceEntity> implements DeviceService {

    private static final String INIT_TIME = "00:00";

    private static final double ONE = 1.0000;
    private static final String WASHING_TARGET_NAME = "bottleWasherProductionNumber";
    private static final String CANNED_TARGET_NAME = "outBottleNumber";
    private static final String DEVICE_TYPE = "deviceCode";

    /**
     * 打印时需要替换的占位符 ${ }
     */
    private static final Pattern PATTERN = Pattern.compile("\\$\\{(.*?)\\}");
    @Autowired
    private MessagePushToKafkaService kafkaService;
    @Autowired
    protected GridService gridService;
    @Autowired
    protected SensorMapper sensorMapper;
    @Autowired
    protected ModelMapper modelMapper;
    @Autowired
    protected CraftService craftService;
    @Autowired
    protected DeviceMapper deviceMapper;
    @Autowired
    protected MaterialService materialService;
    @Autowired
    protected WorkOrderMapper workOrderMapper;
    @Autowired
    protected WorkOrderDeviceRelevanceMapper workOrderDeviceRelevanceMapper;
    @Autowired
    protected IndicatorService indicatorService;
    @Autowired
    protected FacilitiesService facilitiesService;
    @Autowired
    protected TargetDictService targetDictService;
    @Autowired
    protected TargetModelService targetModelService;
    @Autowired
    protected RecordDeviceRunMapper runMapper;
    @Autowired
    protected RecordDeviceStopMapper stopMapper;
    @Autowired
    protected RecordDevicePauseMapper pauseMapper;
    @Autowired
    protected RecordDeviceRunService recordDeviceRunService;
    @Autowired
    protected RecordDeviceVhtService recordDeviceVhtService;
    @Autowired
    protected TargetThresholdMapper targetThresholdMapper;
    @Autowired
    protected RecordDevicePauseService recordDevicePauseService;
    @Autowired
    protected RecordDeviceCurrentService recordDeviceCurrentService;
    @Autowired
    protected RecordDeviceVoltageService recordDeviceVoltageService;
    @Autowired
    protected ProcedureDeviceTypeService procedureDeviceTypeService;
    @Autowired
    protected RecordDeviceUtilizationService recordDeviceUtilizationService;
    @Autowired
    protected ProcedureDeviceTargetReferenceService procedureDeviceTargetReferenceService;
    @Autowired
    protected RecordManualCollectionMapper recordManualCollectionMapper;
    @Autowired
    protected AlarmMapper alarmMapper;
    @Autowired
    protected RecordDeviceDayRunMapper recordDeviceDayRunMapper;
    @Autowired
    protected DictService dictService;
    @Autowired
    protected UploadService uploadService;
    @Autowired
    protected WorkPropertise workPropertise;
    @Autowired
    protected RecordWorkOrderStateMapper recordWorkOrderStateMapper;
    @Autowired
    protected AssignmentInterface assignmentInterface;
    @Autowired
    protected RecordDeviceStopCountService stopCountService;
    @Autowired
    protected AppendixService appendixService;
    @Autowired
    protected DeviceSensorService deviceSensorService;
    @Autowired
    protected RedisTemplate<String, Object> redisTemplate;
    @Autowired
    protected WorkCalendarService workCalendarService;
    @Autowired
    @Lazy
    protected ProductionLineService productionLineService;
    @Autowired
    protected RecordDeviceOeeService deviceOeeService;
    @Resource
    private FacilitiesMapper facilitiesMapper;
    @Autowired
    private AreaMapper areaMapper;
    @Autowired
    private GridMapper gridMapper;
    @Autowired
    private RecordDeviceMaintainService recordDeviceMaintainService;
    @Resource
    private DeviceTimePeriodEnergyConsumptionService deviceTimePeriodEnergyConsumptionService;
    @Resource
    private DeviceGasConsumptionService deviceGasConsumptionService;
    @Autowired
    private WorkCenterDeviceRelevanceService workCenterDeviceRelevanceService;
    @Autowired
    private WorkCenterDeviceService workCenterDeviceService;
    @Autowired
    private WorkCenterService workCenterService;
    @Autowired
    private IotService iotService;
    @Autowired
    protected SensorService sensorService;
    @Autowired
    private CommandHandler commandHandler;
    @Autowired
    private RecordDeviceStopService recordDeviceStopService;
    @Autowired
    private SysUserService sysUserService;
    @Autowired
    protected SkuService skuService;
    @Resource
    private ExcelService excelService;
    @Resource
    private SupplierService supplierService;
    @Resource
    private RecordDeviceResumeDailyService recordDeviceResumeDailyService;
    @Autowired
    private LabelService labelService;
    @Resource
    protected UserAuthenService userAuthenService;
    @Autowired
    private ImportDataRecordService importDataRecordService;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private MessagePushToKafkaService messagePushToKafkaService;
    @Resource
    private ModelUploadFileService modelUploadFileService;
    @Resource
    private LabelTypeConfigService labelTypeConfigService;
    @Resource
    private WorkOrderBasicUnitRelationService workOrderBasicUnitRelationService;

    private static final String TIME = ColumnUtil.getField(MetricsDocument::getTime);
    private static final String DEVICE_ID = ColumnUtil.getField(MetricsDocument::getDeviceId);

    @Override
    public List<CommonType> getDeviceType() {
        CommonType.CommonTypeBuilder builder = CommonType.builder();
        return Arrays.stream(DeviceTypeEnum.values()).map(item -> builder.code(String.valueOf(item.getCode())).type(item.getType()).build()).collect(Collectors.toList());
    }

    @Override
    public Page<DeviceEntity> listByPage(DeviceSelectDTO dto) {
        List<Integer> facIds = null;
        if (StringUtils.isNotBlank(dto.getFacName())) {
            facIds = facilitiesService.lambdaQuery()
                    .like(FacilitiesEntity::getFname, dto.getFacName())
                    .list().stream().map(FacilitiesEntity::getFid).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(facIds)) {
                return new Page<>();
            }
        }
        LambdaQueryWrapper<DeviceEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(dto.getDeviceModelId() != null, DeviceEntity::getModelId, dto.getDeviceModelId());
        queryWrapper.eq(StringUtils.isNotBlank(dto.getTypeCode()), DeviceEntity::getTypeCode, dto.getTypeCode());
        queryWrapper.like(StringUtils.isNotBlank(dto.getName()), DeviceEntity::getDeviceName, dto.getName());
        queryWrapper.like(StringUtils.isNotBlank(dto.getDeviceName()), DeviceEntity::getDeviceName, dto.getDeviceName());
        queryWrapper.like(StringUtils.isNotBlank(dto.getDeviceCode()), DeviceEntity::getDeviceCode, dto.getDeviceCode());
//        queryWrapper.like(StringUtils.isNotBlank(dto.getDeviceTypeName()), DeviceEntity::getTypeName, dto.getDeviceTypeName());
//        queryWrapper.like(StringUtils.isNotBlank(dto.getDeviceTypeName()), DeviceEntity::getTypeName, dto.getDeviceTypeName());
        queryWrapper.eq(dto.getFid() != null, DeviceEntity::getFid, dto.getFid());
        queryWrapper.like(dto.getDeviceExtendOne() != null, DeviceEntity::getDeviceExtendOne, dto.getDeviceExtendOne());
        queryWrapper.like(dto.getDeviceExtendTwo() != null, DeviceEntity::getDeviceExtendTwo, dto.getDeviceExtendTwo());
        queryWrapper.like(dto.getDeviceExtendThree() != null, DeviceEntity::getDeviceExtendThree, dto.getDeviceExtendThree());
        queryWrapper.like(dto.getDeviceExtendFour() != null, DeviceEntity::getDeviceExtendFour, dto.getDeviceExtendFour());
        queryWrapper.like(dto.getDeviceExtendFive() != null, DeviceEntity::getDeviceExtendFive, dto.getDeviceExtendFive());
        queryWrapper.like(dto.getDeviceExtendSix() != null, DeviceEntity::getDeviceExtendSix, dto.getDeviceExtendSix());
        queryWrapper.like(dto.getDeviceExtendSeven() != null, DeviceEntity::getDeviceExtendSeven, dto.getDeviceExtendSeven());
        queryWrapper.like(dto.getDeviceExtendEight() != null, DeviceEntity::getDeviceExtendEight, dto.getDeviceExtendEight());
        queryWrapper.like(dto.getDeviceExtendNine() != null, DeviceEntity::getDeviceExtendNine, dto.getDeviceExtendNine());
        queryWrapper.like(dto.getDeviceExtendTen() != null, DeviceEntity::getDeviceExtendTen, dto.getDeviceExtendTen());
        queryWrapper.in(!CollectionUtils.isEmpty(facIds), DeviceEntity::getFid, facIds);
        queryWrapper.in(!CollectionUtils.isEmpty(dto.getIds()), DeviceEntity::getDeviceId, dto.getIds());

        //工作中心筛选
        if (dto.getWorkCenterId() != null) {
            List<WorkCenterDeviceEntity> workCenterTeamEntityList = workCenterDeviceService.lambdaQuery()
                    .eq(WorkCenterDeviceEntity::getWorkCenterId, dto.getWorkCenterId()).list();
            if (CollectionUtils.isEmpty(workCenterTeamEntityList)) {
                return new Page<>();
            }
            queryWrapper.in(DeviceEntity::getDeviceId, workCenterTeamEntityList.stream().map(WorkCenterDeviceEntity::getDeviceId)
                    .collect(Collectors.toList()));
        }
        ModelService modelService = SpringUtil.getBean(ModelService.class);
        // 设备类型
        if (StringUtils.isNotBlank(dto.getDeviceTypeName())) {
            List<ModelEntity> modelEntities = modelService.lambdaQuery().eq(ModelEntity::getType, ModelEnum.DEVICE.getType())
                    .like(ModelEntity::getName, dto.getDeviceTypeName())
                    .list();
            if (CollectionUtils.isEmpty(modelEntities)) {
                return new Page<>();
            }
            List<Integer> modelIds = modelEntities.stream().map(ModelEntity::getId).collect(Collectors.toList());
            queryWrapper.in(DeviceEntity::getModelId, modelIds);
        }
        if (StringUtils.isNotBlank(dto.getDeviceTypeNames())) {
            List<ModelEntity> modelEntities = modelService.lambdaQuery().eq(ModelEntity::getType, ModelEnum.DEVICE.getType())
                    .in(ModelEntity::getName, dto.getDeviceTypeNames())
                    .list();
            if (CollectionUtils.isEmpty(modelEntities)) {
                return new Page<>();
            }
            List<Integer> modelIds = modelEntities.stream().map(ModelEntity::getId).collect(Collectors.toList());
            queryWrapper.in(DeviceEntity::getModelId, modelIds);
        }
        if (StringUtils.isNotBlank(dto.getUseStates())) {
            List<String> useStatesList = Arrays.asList(dto.getUseStates().split(Constant.SEP));
            queryWrapper.in(DeviceEntity::getUseState, useStatesList);
        }
        if (StringUtils.isNotBlank(dto.getDeviceFullCodes())) {
            List<String> deviceFullCodes = Arrays.asList(dto.getDeviceFullCodes().split(Constant.SEP));
            queryWrapper.in(DeviceEntity::getDeviceCode, deviceFullCodes);
        }
        if (StringUtils.isNotBlank(dto.getGridIds())) {
            List<Integer> gridIds = Arrays.stream(dto.getGridIds().split(Constant.SEP)).map(Integer::valueOf).collect(Collectors.toList());
            queryWrapper.in(DeviceEntity::getGid, gridIds);
        }
        if (StringUtils.isNotBlank(dto.getDeviceTypeCodes())) {
            List<Integer> getDeviceTypeCodes = Arrays.stream(dto.getDeviceTypeCodes().split(Constant.SEP)).map(Integer::valueOf).collect(Collectors.toList());
            queryWrapper.in(DeviceEntity::getTypeCode, getDeviceTypeCodes);
        }
        if (StringUtils.isNotBlank(dto.getDeviceModelIds())) {
            List<String> deviceModelIds = Arrays.asList(dto.getDeviceModelIds().split(Constant.SEP));
            queryWrapper.in(DeviceEntity::getModelId, deviceModelIds);
        }
        if (StringUtils.isNotBlank(dto.getModelCode())) {
            LambdaQueryWrapper<ModelEntity> modelWrapper = new LambdaQueryWrapper<>();
            modelWrapper.eq(ModelEntity::getCode, dto.getModelCode());
            ModelEntity modelEntity = modelMapper.selectOne(modelWrapper);
            if (modelEntity == null) {
                return new Page<>();
            }
            queryWrapper.eq(DeviceEntity::getModelId, modelEntity.getId());
        }
        // 车间
        if (StringUtils.isNotBlank(dto.getGname())) {
            List<GridEntity> grids = gridService.lambdaQuery().like(GridEntity::getGname, dto.getGname()).list();
            if (CollectionUtils.isEmpty(grids)) {
                return new Page<>();
            }
            List<Integer> gridIds = grids.stream().map(GridEntity::getGid).collect(Collectors.toList());
            queryWrapper.in(DeviceEntity::getGid, gridIds);
        }
        // 备注
        queryWrapper.and(StringUtils.isNotBlank(dto.getNeRemark()), e -> e.ne(DeviceEntity::getRemark, dto.getNeRemark()).or().isNull(DeviceEntity::getRemark));
        queryWrapper.like(StringUtils.isNotBlank(dto.getBrandModel()), DeviceEntity::getBrandModel, dto.getBrandModel());
        queryWrapper.like(StringUtils.isNotBlank(dto.getSupplier()), DeviceEntity::getSupplier, dto.getSupplier());
        queryWrapper.orderByDesc(DeviceEntity::getCreateTime).orderByDesc(DeviceEntity::getDeviceId);
        if (dto.getCurrentPage() == null || dto.getPageSize() == null) {
            dto.setCurrentPage(1);
            dto.setPageSize(Integer.MAX_VALUE);
        }
        Page<DeviceEntity> page = deviceMapper.selectPage(new Page<>(dto.getCurrentPage(), dto.getPageSize()), queryWrapper);
        showName(page.getRecords());
        return page;
    }

    @Override
    public void showName(List<DeviceEntity> originList) {
        if (CollectionUtils.isEmpty(originList)) {
            return;
        }
        List<DeviceEntity> list = originList.stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Map<String, String> nickNames = sysUserService.getUserNameNickMap(null);
        Map<Integer, String> gridMap = gridService.lambdaQuery().list().stream()
                .collect(Collectors.toMap(GridEntity::getGid, GridEntity::getGname));
        Map<Integer, String> facilitiesMap = facilitiesService.lambdaQuery().list().stream()
                .collect(Collectors.toMap(FacilitiesEntity::getFid, FacilitiesEntity::getFname));
        Map<Integer, String> lineMap = productionLineService.lambdaQuery().list().stream()
                .collect(Collectors.toMap(ProductionLineEntity::getProductionLineId, ProductionLineEntity::getName));
        for (DeviceEntity entity : list) {
            Integer deviceId = entity.getDeviceId();
            entity.setCreateByName(nickNames.get(entity.getCreateBy()));
            entity.setUpdateByName(nickNames.get(entity.getUpdateBy()));
            entity.setGname(gridMap.get(entity.getGid()));
            entity.setFname(facilitiesMap.get(entity.getFid()));
            entity.setProductionLineName(lineMap.get(entity.getProductionLineId()));
            entity.setRelateTypeName(DeviceModelEnum.getNameByType(entity.getRelateType()));
            entity.setUseStateName(entity.getUseState() != null ? DevicesUseStateEnum.getNameByCode(entity.getUseState()) : null);
            entity.setStateName(entity.getState() != null ? DevicesStateEnum.getNameByCode(entity.getState()) : null);
            entity.setMagNickname(nickNames.get(entity.getMagName()));
            entity.setMaintainerName(nickNames.get(entity.getMaintainer()));
            SupplierEntity supplierEntity = supplierService.getByName(entity.getSupplier());
            if (supplierEntity != null) {
                entity.setSupplierContactWay(supplierEntity.getPhone());
            }
            // 获取模型名称
            LambdaQueryWrapper<ModelEntity> modelWrapper = new LambdaQueryWrapper<>();
            modelWrapper.eq(ModelEntity::getId, entity.getModelId());
            ModelEntity modelEntity = modelMapper.selectOne(modelWrapper);
//            entity.setModelImg(modelEntity.getModelImg());
//            entity.setSimulationImg(modelEntity.getSimulationImg());
            entity.setModelName(modelEntity.getName());

            List<DeviceSensorEntity> deviceSensorEntities = getDeviceSensorEntities(deviceId);
            if (!CollectionUtils.isEmpty(deviceSensorEntities)) {
                List<String> euis = deviceSensorEntities.stream().map(DeviceSensorEntity::getEui).collect(Collectors.toList());
                String euiNames = sensorService.lambdaQuery().in(SensorEntity::getEui, euis).list()
                        .stream().map(o -> StringUtils.isBlank(o.getName()) ? o.getEui() : o.getName()).collect(Collectors.joining(Constants.SEP));
                entity.setEuiNames(euiNames);
            }
            entity.setSensorList(deviceSensorEntities);
            //展示附件
            entity.setAppendixEntity(appendixService.listByRelateId(String.valueOf(deviceId), AppendixTypeEnum.DEVICE_APPENDIX.getCode()));
            // 获取关联的工作中心
            List<Integer> workCenterIds = workCenterDeviceService.lambdaQuery().eq(WorkCenterDeviceEntity::getDeviceId, entity.getDeviceId())
                    .list().stream()
                    .map(WorkCenterDeviceEntity::getWorkCenterId).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(workCenterIds)) {
                entity.setWorkCenterEntities(workCenterService.listByIds(workCenterIds));
            }
        }
    }

    private List<DeviceSensorEntity> getDeviceSensorEntities(Integer deviceId) {
        return deviceSensorService.lambdaQuery()
                .eq(DeviceSensorEntity::getDeviceId, deviceId).list();
    }

    @Override
    public DeviceEntity getEntityById(Integer id) {
        DeviceEntity deviceEntity = deviceMapper.selectById(id);
        showName(Stream.of(deviceEntity).collect(Collectors.toList()));
        return deviceEntity;
    }

    @Override
    public Integer getModelByDeviceId(Integer deviceId) {
        String deviceModelKey = RedisKeyPrefix.getDeviceModelKey(deviceId);
        Integer modelId = (Integer) redisTemplate.opsForValue().get(deviceModelKey);
        if (modelId == null) {
            DeviceEntity deviceEntity = this.lambdaQuery().select(DeviceEntity::getModelId).eq(DeviceEntity::getDeviceId, deviceId).one();
            if (deviceEntity == null) {
                return null;
            }
            modelId = deviceEntity.getModelId();
            redisTemplate.opsForValue().set(deviceModelKey, modelId, 1, TimeUnit.HOURS);
        }
        return modelId;
    }

    @Override
    public DeviceEntity getDeviceByRedis(Integer deviceId) {
        String deviceEntityKey = RedisKeyPrefix.getDeviceEntityKey(deviceId);
        DeviceEntity entity = JacksonUtil.parseObject((String) redisTemplate.opsForValue().get(deviceEntityKey), DeviceEntity.class);
        if (entity == null) {
            entity = this.getById(deviceId);
            redisTemplate.opsForValue().set(deviceEntityKey, JSON.toJSONString(entity), 3, TimeUnit.MINUTES);
        }
        return entity;
    }


    @Override
    public List<DeviceScreen> screen() {
        List<DeviceEntity> list = list();
        list.forEach(deviceEntity -> deviceEntity.setStateName(DevicesStateEnum.getNameByCode(deviceEntity.getState())));
        Map<Integer, List<DeviceEntity>> collect = list.stream().collect(Collectors.groupingBy(DeviceEntity::getTypeCode));
        List<DeviceEntity> productionList = collect.get(DeviceTypeEnum.PRODUCTION.getCode());
        List<DeviceEntity> keyList = collect.get(DeviceTypeEnum.KEY.getCode());
        List<DeviceEntity> environmentList = collect.get(DeviceTypeEnum.ENVIRONMENT.getCode());
        long productionRunningCount = 0L;
        long productionStopCount = 0L;
        if (!CollectionUtils.isEmpty(productionList)) {
            productionRunningCount = productionList.stream().filter(deviceEntity -> deviceEntity.getState().equals(DevicesStateEnum.RUNNING.getCode())).count();
            productionStopCount = productionList.stream().filter(deviceEntity -> deviceEntity.getState().equals(DevicesStateEnum.SUSPEND.getCode())
                    || deviceEntity.getState().equals(DevicesStateEnum.STOP.getCode())).count();
        }
        long keyRunningCount = 0L;
        long keyStopCount = 0L;
        if (!CollectionUtils.isEmpty(keyList)) {
            keyRunningCount = keyList.stream().filter(deviceEntity -> deviceEntity.getState().equals(DevicesStateEnum.RUNNING.getCode())).count();
            keyStopCount = keyList.stream().filter(deviceEntity -> deviceEntity.getState().equals(DevicesStateEnum.SUSPEND.getCode())
                    || deviceEntity.getState().equals(DevicesStateEnum.STOP.getCode())).count();
        }

        long environmentRunningCount = 0L;
        long environmentStopCount = 0L;
        if (!CollectionUtils.isEmpty(environmentList)) {
            environmentRunningCount = environmentList.stream().filter(deviceEntity -> deviceEntity.getState().equals(DevicesStateEnum.RUNNING.getCode())).count();
            environmentStopCount = environmentList.stream().filter(deviceEntity -> deviceEntity.getState().equals(DevicesStateEnum.SUSPEND.getCode())
                    || deviceEntity.getState().equals(DevicesStateEnum.STOP.getCode())).count();
        }

        DeviceScreen production = DeviceScreen.builder()
                .type(DeviceTypeEnum.PRODUCTION.getCode())
                .typeName(DeviceTypeEnum.PRODUCTION.getType())
                .running((int) productionRunningCount)
                .stop((int) productionStopCount)
                .unusual(0)
                .list(productionList)
                .build();
        DeviceScreen key = DeviceScreen.builder()
                .type(DeviceTypeEnum.KEY.getCode())
                .typeName(DeviceTypeEnum.KEY.getType())
                .running((int) keyRunningCount)
                .stop((int) keyStopCount)
                .unusual(0)
                .list(keyList)
                .build();
        DeviceScreen environment = DeviceScreen.builder()
                .type(DeviceTypeEnum.ENVIRONMENT.getCode())
                .typeName(DeviceTypeEnum.ENVIRONMENT.getType())
                .running((int) environmentRunningCount)
                .stop((int) environmentStopCount)
                .unusual(0)
                .list(environmentList)
                .build();

        List<DeviceScreen> result = new ArrayList<>();
        result.add(production);
        result.add(key);
        result.add(environment);
        return result;
    }

    /**
     * 设备指标曲线
     *
     * @return
     */
    @Override
    public JSONObject curve(Integer deviceId, Date start, Date end) {
        if (start == null || end == null) {
            end = new Date();
            Calendar cal = Calendar.getInstance();
            cal.setTime(end);
            cal.add(Calendar.HOUR, -1);
            start = cal.getTime();
        }
        // 创建Json对象
        JSONObject object = new JSONObject();
        //工作状态
        /*LambdaQueryWrapper<RecordDeviceStateEntity> stateWrapper = new LambdaQueryWrapper<>();
        stateWrapper.eq(RecordDeviceStateEntity::getDeviceId, deviceId)
                .between(RecordDeviceStateEntity::getTime, start, end);
        List<RecordDeviceStateEntity> states = recordDeviceStateService.list(stateWrapper);*/

        List<RecordDeviceStateEntity> states = getRecordDeviceStateEntities(deviceId, start, end);

        ArrayList<RecordDeviceStateEntity> stateEntities = new ArrayList<>();
        if (CollectionUtils.isEmpty(states)) {
            object.put("state", states);
        } else {
            stateEntities.add(states.get(0));
            for (int i = 1; i < states.size(); i++) {
                if (stateEntities.get(stateEntities.size() - 1).getState().equals(states.get(i).getState())) {
                    continue;
                }
                stateEntities.add(states.get(i));
            }
            if (stateEntities.size() == 1) {
                stateEntities.add(states.get(states.size() - 1));
            }
            object.put("state", stateEntities);
        }
        //暂停时长
        LambdaQueryWrapper<RecordDevicePauseEntity> pauseWrapper = new LambdaQueryWrapper<>();
        pauseWrapper.eq(RecordDevicePauseEntity::getDeviceId, deviceId)
                .between(RecordDevicePauseEntity::getTime, start, end);
        List<RecordDevicePauseEntity> pauses = recordDevicePauseService.list(pauseWrapper);
        if (CollectionUtils.isEmpty(pauses)) {
            object.put("pause", pauses);
        } else {
            List<RecordDevicePauseEntity> pauseEntities = new ArrayList<>();
            int pauseTime = 0;
            for (int i = 0; i < pauses.size(); i++) {
                pauseTime += pauses.get(i).getPause();
                if ((i + 1) / 10 != 0 && (i + 1) % 10 == 0) {
                    int i1 = pauseTime / 10;
                    RecordDevicePauseEntity pauseEntity = pauses.get(i);
                    pauseEntity.setPause(i1);
                    pauseEntities.add(pauseEntity);
                    pauseTime = 0;
                }
                if (i == pauses.size() - 1 && pauses.size() % 10 != 0) {
                    int i2 = pauseTime / (pauses.size() % 10);
                    RecordDevicePauseEntity pauseEntity = pauses.get(i);
                    pauseEntity.setPause(i2);
                    pauseEntities.add(pauseEntity);
                }
            }
            object.put("pause", pauseEntities);
        }

        //工作时长
        LambdaQueryWrapper<RecordDeviceRunEntity> runWrapper = new LambdaQueryWrapper<>();
        runWrapper.eq(RecordDeviceRunEntity::getDeviceId, deviceId)
                .between(RecordDeviceRunEntity::getTime, start, end);
        List<RecordDeviceRunEntity> runs = recordDeviceRunService.list(runWrapper);
        if (CollectionUtils.isEmpty(runs)) {
            object.put("run", runs);
        } else {
            List<RecordDeviceRunEntity> runEntities = new ArrayList<>();
            int runTime = 0;
            for (int i = 0; i < runs.size(); i++) {
                runTime += runs.get(i).getRunning();
                if ((i + 1) / 10 != 0 && (i + 1) % 10 == 0) {
                    int i1 = runTime / 10;
                    RecordDeviceRunEntity runEntity = runs.get(i);
                    runEntity.setRunning(i1);
                    runEntities.add(runEntity);
                    runTime = 0;
                }
                if (i == runs.size() - 1 && runs.size() % 10 != 0) {
                    int i2 = runTime / (runs.size() % 10);
                    RecordDeviceRunEntity runEntity = runs.get(i);
                    runEntity.setRunning(i2);
                    runEntities.add(runEntity);
                }
            }
            object.put("run", runEntities);
        }
        //当天稼动率
        LambdaQueryWrapper<RecordDeviceUtilizationEntity> utilizationWrapper = new LambdaQueryWrapper<>();
        utilizationWrapper.eq(RecordDeviceUtilizationEntity::getDeviceId, deviceId)
                .between(RecordDeviceUtilizationEntity::getTime, start, end);
        List<RecordDeviceUtilizationEntity> utilizations = recordDeviceUtilizationService.list(utilizationWrapper);
        if (CollectionUtils.isEmpty(utilizations)) {
            object.put("utilization", utilizations);
        } else {
            List<RecordDeviceUtilizationEntity> utilizationEntities = new ArrayList<>();
            double utilizationTime = 0;
            for (int i = 0; i < utilizations.size(); i++) {
                utilizationTime += utilizations.get(i).getUtilization();
                if ((i + 1) / 10 != 0 && (i + 1) % 10 == 0) {
                    double v = utilizationTime / 10;
                    RecordDeviceUtilizationEntity utilizationEntity = utilizations.get(i);
                    utilizationEntity.setUtilization(v);
                    utilizationEntities.add(utilizationEntity);
                    utilizationTime = 0.0;
                }
                if (i == utilizations.size() - 1 && utilizations.size() % 10 != 0) {
                    double v2 = utilizationTime / (utilizations.size() % 10);
                    RecordDeviceUtilizationEntity utilizationEntity = utilizations.get(i);
                    utilizationEntity.setUtilization(v2);
                    utilizationEntities.add(utilizationEntity);
                }
            }
            object.put("utilization", utilizationEntities);
        }

        //设备电流
        LambdaQueryWrapper<RecordDeviceCurrentEntity> currentWrapper = new LambdaQueryWrapper<>();
        currentWrapper.eq(RecordDeviceCurrentEntity::getDeviceId, deviceId)
                .between(RecordDeviceCurrentEntity::getTime, start, end);
        List<RecordDeviceCurrentEntity> currents = recordDeviceCurrentService.list(currentWrapper);
        if (CollectionUtils.isEmpty(currents)) {
            object.put("current", currents);
        } else {
            List<RecordDeviceCurrentEntity> currentEntities = new ArrayList<>();
            for (int i = 0; i < currents.size(); i++) {
                if ((i + 1) / 10 != 0 && (i + 1) % 10 == 0) {
                    currentEntities.add(currents.get(i));
                }
                if (i == currents.size() - 1 && currents.size() % 10 != 0) {
                    currentEntities.add(currents.get(i));
                }
            }
            object.put("current", currentEntities);
        }

        //设备电压
        LambdaQueryWrapper<RecordDeviceVoltageEntity> voltageWrapper = new LambdaQueryWrapper<>();
        voltageWrapper.eq(RecordDeviceVoltageEntity::getDeviceId, deviceId)
                .between(RecordDeviceVoltageEntity::getTime, start, end);
        List<RecordDeviceVoltageEntity> voltages = recordDeviceVoltageService.list(voltageWrapper);
        if (CollectionUtils.isEmpty(voltages)) {
            object.put("voltage", voltages);
        } else {
            List<RecordDeviceVoltageEntity> voltageEntities = new ArrayList<>();
            for (int i = 0; i < voltages.size(); i++) {
                if ((i + 1) / 10 != 0 && (i + 1) % 10 == 0) {
                    voltageEntities.add(voltages.get(i));
                }
                if (i == voltages.size() - 1 && voltages.size() % 10 != 0) {
                    voltageEntities.add(voltages.get(i));
                }
            }
            object.put("voltage", voltageEntities);
        }

        //设备设备谐波畸变
        LambdaQueryWrapper<RecordDeviceVhtEntity> vhtWrapper = new LambdaQueryWrapper<>();
        vhtWrapper.eq(RecordDeviceVhtEntity::getDeviceId, deviceId)
                .between(RecordDeviceVhtEntity::getTime, start, end);
        List<RecordDeviceVhtEntity> vhts = recordDeviceVhtService.list(vhtWrapper);
        if (CollectionUtils.isEmpty(vhts)) {
            object.put("vht", vhts);
        } else {
            List<RecordDeviceVhtEntity> vhtEntities = new ArrayList<>();
            for (int i = 0; i < vhts.size(); i++) {
                if ((i + 1) / 10 != 0 && (i + 1) % 10 == 0) {
                    vhtEntities.add(vhts.get(i));
                }
                if (i == vhts.size() - 1 && vhts.size() % 10 != 0) {
                    vhtEntities.add(vhts.get(i));
                }
            }
            object.put("vht", vhtEntities);
        }

        return object;
    }

    /**
     * 获取产线的所有设备
     *
     * @param lineId
     * @return
     */
    @Override
    public List<DeviceEntity> getAllDevicesInLine(Integer lineId) {
        LambdaQueryWrapper<FacilitiesEntity> facWrapper = new LambdaQueryWrapper<>();
        facWrapper.eq(FacilitiesEntity::getProductionLineId, lineId);
        List<FacilitiesEntity> facilitiesEntities = facilitiesService.list(facWrapper);
        if (CollectionUtils.isEmpty(facilitiesEntities)) {
            return new ArrayList<>();
        }
        List<Integer> fids = facilitiesEntities.stream().map(FacilitiesEntity::getFid).collect(Collectors.toList());
        LambdaQueryWrapper<DeviceEntity> deviceWrapper = new LambdaQueryWrapper<>();
        deviceWrapper.in(DeviceEntity::getFid, fids);
        return this.list(deviceWrapper);

    }


    @Override
    public TargetDiagramDTO getTargetDiagram(Integer deviceId, String targetName, Integer modelId) {
        //实时数据
        List<IndicatorEntityDTO> records = indicatorService.getTenRecords(deviceId, targetName);
        List<IndicatorEntityDTO> list = records.stream().sorted(Comparator.comparing(IndicatorEntityDTO::getTime)).collect(Collectors.toList());
        //获取指标参考值、平均值、指标相对应的记录
        return getTargetThreshold(list, targetName, deviceId);
    }

    /**
     * 获取设备相关参考值
     */
    @Override
    public TargetDiagramDTO getTargetThreshold(List<IndicatorEntityDTO> list, String targetName, Integer instanceId) {
        Double referenceValue = 0.00;
        Double referenceUpper = 0.00;
        Double referenceLower = 0.00;
        List<BatchAndTargetValDTO> batchAndTargetValList = new ArrayList<>();
        //获取指标参考值
        QueryWrapper<TargetThresholdEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(TargetThresholdEntity::getTargetName, targetName)
                .eq(TargetThresholdEntity::getModelType, ModelEnum.DEVICE.getType())
                .eq(TargetThresholdEntity::getInstanceId, instanceId);
        TargetThresholdEntity targetThresholdEntity = targetThresholdMapper.selectOne(wrapper);
        if (targetThresholdEntity != null) {
            referenceValue = targetThresholdEntity.getReferenceValue();
            referenceUpper = targetThresholdEntity.getUpperLimit();
            referenceLower = targetThresholdEntity.getLowerLimit();
        }
        BatchAndTargetValDTO batchAndTargetValDTO = BatchAndTargetValDTO.builder()
                .batch(null)
                .referenceUpper(referenceUpper)
                .referenceLower(referenceLower)
                .referenceValue(referenceValue)
                .targetList(list)
                .build();
        batchAndTargetValList.add(batchAndTargetValDTO);

        TargetDictEntity targetDictEntity = getTargetDictEntity(targetName);
        //指标单位
        String unit = targetDictEntity == null ? null : targetDictEntity.getUnit();
        //指标名称
        String targetCname = targetDictEntity == null ? null : targetDictEntity.getTargetCnname();
        //获取平均值
        Double avg = getAvg(list);
        TargetDiagramDTO build = TargetDiagramDTO.builder()
                .targetName(targetCname)
                .average(avg)
                .unit(unit)
                .batchAndTarget(batchAndTargetValList)
                .build();
        return build;

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRunRecordTableByReport(Integer deviceId, Integer state, String username) {
        DeviceEntity oldDevice = BeanUtil.copyProperties(this.getById(deviceId), DeviceEntity.class);
        if (oldDevice == null) {
            throw new ResponseException("找不到该设备");
        }

        Date now = new Date();
        // 获取最新一条设备的运行记录的数据
        LambdaQueryWrapper<RecordDeviceDayRunEntity> runWrapper = new LambdaQueryWrapper<>();
        runWrapper.eq(RecordDeviceDayRunEntity::getDeviceId, deviceId)
                .isNull(RecordDeviceDayRunEntity::getEndTime);
        RecordDeviceDayRunEntity entity = recordDeviceDayRunMapper.selectOne(runWrapper);
        // 记录表如果为空则添加一条新数据
        if (entity == null) {
            // 获取状态为投入的最新工单
            // 插入设备的运行记录数据
            insertRecordByDevice(oldDevice, state, username, null, now);
        } else {
            // 状态相同时，不更新时间
            if (!oldDevice.getState().equals(state)) {
                // 判断是否为当天时间，如果是，则更新结束时间，同时新增数据，更新目前的状态。
                // 如果跨天，则按天填充数据，同时新增数据，更新目前的状态。
                if (entity.getRecordDate().getTime() == dictService.getRecordDate(now).getTime()) {
                    String endTime = DateUtil.format(now, DateUtil.HOUR_MIN_FORMAT);
                    LambdaUpdateWrapper<RecordDeviceDayRunEntity> runUw = new LambdaUpdateWrapper<>();
                    runUw.eq(RecordDeviceDayRunEntity::getDeviceId, deviceId)
                            .eq(RecordDeviceDayRunEntity::getId, entity.getId())
                            .set(RecordDeviceDayRunEntity::getEndTime, endTime)
                            .set(RecordDeviceDayRunEntity::getDuration, DateUtil.getDurationDaySpan(entity.getStartTime(), endTime))
                            .set(RecordDeviceDayRunEntity::getUpdateTime, now)
                            .isNull(RecordDeviceDayRunEntity::getEndTime);
                    recordDeviceDayRunMapper.update(entity, runUw);
                } else {
                    // 跨天，填充之前的数据
                    String startTime = getStartTime();
                    String lastTime = getLastTime();
                    LambdaUpdateWrapper<RecordDeviceDayRunEntity> runUw = new LambdaUpdateWrapper<>();
                    runUw.eq(RecordDeviceDayRunEntity::getDeviceId, deviceId)
                            .set(RecordDeviceDayRunEntity::getEndTime, lastTime)
                            .set(RecordDeviceDayRunEntity::getDuration, DateUtil.getDurationDaySpan(entity.getStartTime(), lastTime))
                            .isNull(RecordDeviceDayRunEntity::getEndTime);
                    recordDeviceDayRunMapper.update(entity, runUw);
                    // 获取相隔天数
                    Long days = DateUtil.getDaysBetweenDays(entity.getRecordDate(), dictService.getRecordDate(now));
                    for (int i = 1; i <= days; i++) {
                        RecordDeviceDayRunEntity build = RecordDeviceDayRunEntity.builder()
                                .orderName(entity.getOrderName())
                                .orderNumber(entity.getOrderNumber())
                                .addType(entity.getAddType())
                                .deviceId(deviceId)
                                .recordDate(DateUtil.formatToDate(DateUtil.addDate(entity.getRecordDate(), i), DateUtil.DATETIME_FORMAT_ZERO))
                                .state(entity.getState())
                                .startTime(startTime)
                                .endTime(days > 1 && i != days ? lastTime : DateUtil.format(now, DateUtil.HOUR_MIN_FORMAT))
                                .createTime(now)
                                .updateTime(now)
                                .createBy(username)
                                .updateBy(username)
                                .build();
                        build.setDuration(DateUtil.getDurationDaySpan(build.getStartTime(), build.getEndTime()));
                        recordDeviceDayRunMapper.insert(build);
                    }
                }
                // 获取状态为投入的最新工单
                WorkOrderEntity workOrderEntity = getLatestInvestmentWorkOrder(deviceId);
                // 插入设备的运行记录数据
                insertRecordByDevice(oldDevice, state, username, workOrderEntity, now);
            }
        }
        // 履历
        recordDeviceResumeDailyService.handleDeviceResumeDaily(oldDevice, oldDevice.getState(), state);
    }

    @Override
    public List<RecordDeviceDayRunVO> getRunRecordTimeByDeviceId(Integer deviceId) {
        List<RecordDeviceDayRunVO> dto = new ArrayList<>();
        List<RecordDeviceDayRunEntity> runList = new ArrayList<>();
        LambdaQueryWrapper<RecordDeviceDayRunEntity> runWrapper = new LambdaQueryWrapper<>();
        runWrapper.eq(RecordDeviceDayRunEntity::getDeviceId, deviceId)
                .ne(RecordDeviceDayRunEntity::getAddType, DeviceAddTypeEnum.DEVICE_FINISH.getType());
        List<RecordDeviceDayRunEntity> list = recordDeviceDayRunMapper.selectList(runWrapper);
        // 组装数据，获取上报时间（年月日时分秒）,根据组装数据排序
        for (RecordDeviceDayRunEntity entity : list) {
            SimpleDateFormat dateFormat = new SimpleDateFormat(DateUtil.DATE_FORMAT);
            SimpleDateFormat timeFormat = new SimpleDateFormat(DateUtil.DATETIME_FORMAT);
            String date = dateFormat.format(entity.getRecordDate());
            String startStr = date + " " + entity.getStartTime() + ":00";
            try {
                Date startDate = timeFormat.parse(startStr);
                entity.setStartDate(startDate);
                runList.add(entity);
            } catch (ParseException e) {
                log.error("", e);
            }
        }
        List<RecordDeviceDayRunEntity> sortedList = runList.stream()
                .sorted(Comparator.comparing(RecordDeviceDayRunEntity::getStartDate).reversed())
                .sorted(Comparator.comparing(RecordDeviceDayRunEntity::getCreateTime).reversed())
                .collect(Collectors.toList());
        for (RecordDeviceDayRunEntity recordDeviceDayRunEntity : sortedList) {
            RecordDeviceDayRunVO vo = RecordDeviceDayRunVO.builder()
                    .type(recordDeviceDayRunEntity.getAddType())
                    .typeName(DeviceAddTypeEnum.getNameByType(recordDeviceDayRunEntity.getAddType()))
                    .time(DateUtil.format(recordDeviceDayRunEntity.getStartDate(), DateUtil.DATETIME_FORMAT))
                    .build();
            dto.add(vo);
        }
        return dto;
    }

    /**
     * 插入设备的运行记录数据
     *
     * @param
     * @return
     */
    private void insertRecordByDevice(DeviceEntity deviceEntity, Integer state, String username, WorkOrderEntity workOrderEntity, Date now) {
        Date recordDate = dictService.getRecordDate(now);
        RecordDeviceDayRunEntity build = RecordDeviceDayRunEntity.builder()
                .orderName(workOrderEntity == null ? null : workOrderEntity.getWorkOrderName())
                .orderNumber(workOrderEntity == null ? null : workOrderEntity.getWorkOrderNumber())
                .addType(DeviceAddTypeEnum.getTypeByState(state))
                .deviceId(deviceEntity.getDeviceId())
                .recordDate(recordDate)
                .state(state)
                .startTime(DateUtil.format(now, DateUtil.HOUR_MIN_FORMAT))
                .endTime(null)
                .duration(null)
                .createTime(now)
                .updateTime(now)
                .createBy(username)
                .updateBy(username)
                .build();
        // 同时更新设备状态
        LambdaUpdateWrapper<DeviceEntity> deviceWrapper = new LambdaUpdateWrapper<>();
        deviceWrapper.eq(DeviceEntity::getDeviceId, deviceEntity.getDeviceId())
                .set(DeviceEntity::getState, state)
                .set(DeviceEntity::getStateUpdateTime, new Date());
        this.update(deviceWrapper);
        //如果状态变更发送消息
        if (!deviceEntity.getState().equals(state)) {
            deviceEntity.setState(state);
            deviceEntity.setStateName(DevicesStateEnum.getNameByCode(deviceEntity.getState()));
            messagePushToKafkaService.pushNewMessage(deviceEntity, Constants.KAFKA_VALUE_CHAIN_STATE_TOPIC, KafkaMessageTypeEnum.PRODUCTION_EQUIPMENT_STATUS_CHANGE_MESSAGE);
        }
        recordDeviceDayRunMapper.insert(build);
    }


    private String getStartTime() {
        String outputBeginTime = dictService.getBeginTimeOfDay();
        String[] time = outputBeginTime.split(":");
        LocalTime localTime = LocalTime.of(Integer.parseInt(time[0]), Integer.parseInt(time[1]));
        return localTime.toString();
    }

    private String getLastTime() {
        String outputBeginTime = dictService.getBeginTimeOfDay();
        String[] time = outputBeginTime.split(":");
        LocalTime localTime = LocalTime.of(Integer.parseInt(time[0]), Integer.parseInt(time[1]));
        localTime = localTime.minusMinutes(1);
        return localTime.toString();
    }

    /**
     * 获取状态为投入的最新工单
     *
     * @param
     * @return
     */
    private WorkOrderEntity getLatestInvestmentWorkOrder(Integer deviceId) {
        // 获取状态为投入的最新工单
        if (deviceId == null) {
            return null;
        }
        DeviceEntity deviceEntity = deviceMapper.selectById(deviceId);
        FacilitiesEntity facilitiesEntity = facilitiesService.getById(deviceEntity.getFid());
        if (Objects.isNull(facilitiesEntity)) {
            return null;
        }
        LambdaQueryWrapper<WorkOrderEntity> workOrderWrapper = new LambdaQueryWrapper<>();
        workOrderWrapper.eq(WorkOrderEntity::getLineId, facilitiesEntity.getProductionLineId())
                .eq(WorkOrderEntity::getState, WorkOrderStateEnum.INVESTMENT.getCode())
                .orderByDesc(WorkOrderEntity::getActualStartDate)
                .orderByDesc(WorkOrderEntity::getWorkOrderId)
                .last("limit 1");
        return workOrderMapper.selectOne(workOrderWrapper);
    }

    /**
     * 通过设备编号获工单列表
     *
     * @param
     * @return
     */
    @Override
    public List<WorkOrderEntity> getWorkOrdersByDeviceCode(String deviceCode) {
        if (StringUtils.isBlank(deviceCode)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<DeviceEntity> deviceWrapper = new LambdaQueryWrapper<>();
        deviceWrapper.eq(DeviceEntity::getDeviceCode, deviceCode);
        DeviceEntity deviceEntity = deviceMapper.selectOne(deviceWrapper);
        if (deviceEntity == null || deviceEntity.getFid() == null) {
            return new ArrayList<>();
        }
        FacilitiesEntity facilitiesEntity = facilitiesService.getById(deviceEntity.getFid());
        List<Integer> states = new ArrayList<>();
        states.add(WorkOrderStateEnum.RELEASED.getCode());
        states.add(WorkOrderStateEnum.INVESTMENT.getCode());
        states.add(WorkOrderStateEnum.HANG_UP.getCode());
        LambdaQueryWrapper<WorkOrderEntity> workOrderWrapper = new LambdaQueryWrapper<>();
        workOrderWrapper.eq(WorkOrderEntity::getLineId, facilitiesEntity.getProductionLineId())
                .in(WorkOrderEntity::getState, states);
        List<WorkOrderEntity> list = workOrderMapper.selectList(workOrderWrapper);
        if (!CollectionUtils.isEmpty(list)) {
            List<String> materialCodes = list.stream().map(WorkOrderEntity::getMaterialCode).distinct().collect(Collectors.toList());
            List<MaterialEntity> materials = materialService.lambdaQuery().in(MaterialEntity::getCode, materialCodes).list();
            Map<String, MaterialEntity> materialMap = materials.stream().collect(Collectors.toMap(MaterialEntity::getCode, o -> o));
            // 查询sku信息
            Set<Integer> skuIds = list.stream().map(WorkOrderEntity::getSkuId).filter(Objects::nonNull).collect(Collectors.toSet());
            Map<Integer, SkuEntity> skuMap = skuService.getSkusByIds(skuIds).stream().collect(Collectors.toMap(SkuEntity::getSkuId, v -> v));
            for (WorkOrderEntity entity : list) {
                MaterialEntity materialEntity = JacksonUtil.convertObject(materialMap.get(entity.getMaterialCode()), MaterialEntity.class);
                if (materialEntity != null) {
                    materialEntity.setSkuEntity(skuMap.get(entity.getSkuId()));
                }
                entity.setMaterialFields(materialEntity);
            }
        }
        return list;
    }

    /**
     * 获取单个或多个批次指标参考值指标相对应的记录
     *
     * @param targetName
     * @return
     */
    @Override
    public TargetDiagramDTO getSpecifyValues(List<IndicatorEntityDTO> list, String targetName, Integer deviceId, Integer modelId) {
        Double referenceValue = 0.00;
        Double referenceUpper = 0.00;
        Double referenceLower = 0.00;
        List<BatchAndTargetValDTO> batchAndTargetValList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(list)) {
            IndicatorEntityDTO indicatorEntityDTO = list.get(0);
            String batch = indicatorEntityDTO.getBatch();
            ProcedureDeviceTargetReferenceEntity referenceEntity = getReferenceValue(batch, modelId, targetName);
            if (referenceEntity != null) {
                referenceValue = referenceEntity.getReferenceVal() == null ? null : Double.valueOf(referenceEntity.getReferenceVal());
                referenceUpper = referenceEntity.getReferenceUpper() == null ? null : Double.valueOf(referenceEntity.getReferenceUpper());
                referenceLower = referenceEntity.getReferenceLower() == null ? null : Double.valueOf(referenceEntity.getReferenceLower());
            }
            BatchAndTargetValDTO batchAndTargetValDTO = BatchAndTargetValDTO.builder()
                    .batch(batch)
                    .referenceUpper(referenceUpper)
                    .referenceLower(referenceLower)
                    .referenceValue(referenceValue)
                    .targetList(list)
                    .build();
            batchAndTargetValList.add(batchAndTargetValDTO);
        }
        TargetDictEntity targetDictEntity = getTargetDictEntity(targetName);
        //指标单位
        String unit = targetDictEntity == null ? null : targetDictEntity.getUnit();
        //指标名称
        String targetCname = targetDictEntity == null ? null : targetDictEntity.getTargetCnname();
        //获取平均值
        Double avg = getAvg(list);
        return TargetDiagramDTO.builder()
                .targetName(targetCname)
                .average(avg)
                .unit(unit)
                .batchAndTarget(batchAndTargetValList)
                .build();
    }

    /**
     * 获取产品相关参考值
     */
    public ProcedureDeviceTargetReferenceEntity getReferenceValue(String batch, Integer modelId, String targetName) {
        WorkOrderEntity workOrder = workOrderMapper.getWorkOrderByName(batch);
        if (Objects.isNull(workOrder)) {
            throw new ResponseException(RespCodeEnum.WORK_ORDER_NAME_DUPLICATE.fmtDes(batch));
        }
        CraftEntity craftEntity = craftService.getCraftByMaterialCode(workOrder.getMaterialCode());
        ProcedureDeviceTypeEntity procedureDeviceTypeEntity = procedureDeviceTypeService.getProcedureId(craftEntity.getCraftId(), modelId);
        if (procedureDeviceTypeEntity == null) {
            return null;
        }
        return procedureDeviceTargetReferenceService
                .getReferenceValue(craftEntity.getCraftId(), procedureDeviceTypeEntity.getProcedureId(), modelId, targetName);
    }

    /**
     * 获取平均值
     *
     * @param list
     * @return
     */
    private Double getAvg(List<IndicatorEntityDTO> list) {
        double avg = 0.00;
        if (!CollectionUtils.isEmpty(list)) {
            List<Integer> integers = new ArrayList<>();
            for (IndicatorEntityDTO indicatorEntityDTO : list) {
                //可能存在没有指标数值的情况 则平均值直接返回默认值
                if (indicatorEntityDTO.getDataVal() == null) {
                    return avg;
                }
                double value = Double.parseDouble(indicatorEntityDTO.getDataVal());
                integers.add((int) value);
            }
            avg = MathUtil.round(integers.stream().mapToDouble(Integer::intValue).average().getAsDouble(), 1);
        }
        return avg;
    }

    @Override
    public List<DeviceEntity> getDevicesByFids(List<Integer> fids) {
        if (CollectionUtils.isEmpty(fids)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<DeviceEntity> lambda = new LambdaQueryWrapper<>();
        lambda.in(DeviceEntity::getFid, fids);
        return this.list(lambda);
    }

    @Override
    public EquipmentAnalysisDTO getMoreTargetDiagram(Integer deviceId, String targetName, String startDate, String endDate,
                                                     String batch, Integer cycle, Integer modelId) {
        Double referenceValue = 0.00;
        Double referenceUpper = 0.00;
        Double referenceLower = 0.00;
        List<IndicatorEntityDTO> records;
        String[] batchList = {};
        if (StringUtils.isNotBlank(batch)) {
            batchList = batch.split(Constant.SEP);
        }
        if ((batchList.length <= 1)) {
            //只选单个批次数据处理如下
            //传时间范围 并且传周期
            IndicatorEntityDTO dto = indicatorService.getFieldNameAndTableName(targetName, cycle);
            String tableName = dto.getTableName();
            records = indicatorService.getIndicatorEntityByCycle(deviceId, tableName, startDate, endDate, cycle, batchList);
            //获取参考值 上、下限
            TargetDiagramDTO targetDiagramDTO = getSpecifyValues(records, targetName, deviceId, modelId);
            List<BatchAndTargetValDTO> batchAndTarget = targetDiagramDTO.getBatchAndTarget();
            if (!CollectionUtils.isEmpty(batchAndTarget)) {
                referenceValue = targetDiagramDTO.getBatchAndTarget().get(0).getReferenceValue();
                referenceUpper = targetDiagramDTO.getBatchAndTarget().get(0).getReferenceUpper();
                referenceLower = targetDiagramDTO.getBatchAndTarget().get(0).getReferenceLower();
            }
            //组装给前端传的数据实体
            return EquipmentAnalysisDTO.builder()
                    .targetName(targetDiagramDTO.getTargetName())
                    .unit(targetDiagramDTO.getUnit())
                    .average(targetDiagramDTO.getAverage())
                    .referenceValue(referenceValue)
                    .reference_upper(referenceUpper)
                    .reference_lower(referenceLower)
                    .batchAndTarget(targetDiagramDTO.getBatchAndTarget())
                    .build();
        }
        //选多个批次 不传时间 不传周期
        //指标记录
        records = getBatchRecords(batchList, targetName, deviceId);
        //按批次分组后的指标记录
        Map<String, List<IndicatorEntityDTO>> collect = records.stream().collect(Collectors.groupingBy(IndicatorEntityDTO::getBatch));
        for (Map.Entry<String, List<IndicatorEntityDTO>> entry : collect.entrySet()) {
            //转换前端的横坐标
            List<IndicatorEntityDTO> value = entry.getValue();
            IndicatorEntityDTO first = value.get(0);
            for (int i = 1; i < value.size(); i++) {
                IndicatorEntityDTO indicatorEntityDTO = value.get(i);
                long timeStamp = DateUtil.parse(indicatorEntityDTO.getTime(), DateUtil.DATETIME_FORMAT).getTime() - DateUtil.parse(first.getTime(), DateUtil.DATETIME_FORMAT).getTime();
                String hoursStr = DateUtil.convertToHours(timeStamp, Constant.COLON);
                indicatorEntityDTO.setTime(hoursStr);
            }
            first.setTime(INIT_TIME);
        }
        TargetDictEntity targetDictEntity = getTargetDictEntity(targetName);
        //指标单位
        String unit = targetDictEntity == null ? null : targetDictEntity.getUnit();
        //指标名称
        String targetCname = targetDictEntity == null ? null : targetDictEntity.getTargetCnname();
        //获取平均值
        Double avg = getAvg(records);
        //获取参考值 上、下限
        TargetDiagramDTO targetDiagramDTO = getSpecifyValues(records, targetName, deviceId, modelId);
        List<BatchAndTargetValDTO> batchAndTarget = targetDiagramDTO.getBatchAndTarget();
        if (!CollectionUtils.isEmpty(batchAndTarget)) {
            referenceValue = targetDiagramDTO.getBatchAndTarget().get(0).getReferenceValue();
            referenceUpper = targetDiagramDTO.getBatchAndTarget().get(0).getReferenceUpper();
            referenceLower = targetDiagramDTO.getBatchAndTarget().get(0).getReferenceLower();
        }
        //组装给前端传的数据实体
        return EquipmentAnalysisDTO.builder()
                .targetName(targetCname)
                .average(avg)
                .unit(unit)
                .reference_upper(referenceUpper)
                .reference_lower(referenceLower)
                .referenceValue(referenceValue)
                .moreBatchAndTarget(collect)
                .build();

    }

//    private List<IndicatorEntityDTO> getIndicatorEntityByCycle(Integer deviceId, String startDate, String endDate, Integer cycle, String[] batchList, String fieldName, String tableName) {
//        if (cycle == DeviceProductionEnum.ONE_MIN.getCode()) {
//            //原始值数据
//            return indicatorService.getTimeRecords(deviceId, tableName, fieldName, startDate, endDate, batchList);
//        } else if (cycle == DeviceProductionEnum.FIVE_MIN.getCode()) {
//            //五分钟数据
//            return indicatorService.getFiveMinRecords(deviceId, tableName, fieldName, startDate, endDate, batchList);
//        } else if (cycle == DeviceProductionEnum.TWENTY_MIN.getCode()) {
//            //20分钟指标数据
//            return indicatorService.getTwentyMinRecords(deviceId, tableName, fieldName, startDate, endDate, batchList);
//        } else if (cycle == DeviceProductionEnum.ONE_HOURS.getCode()) {
//            //1小时指标数据
//            return indicatorService.getOneHoursRecords(deviceId, tableName, fieldName, startDate, endDate, batchList);
//        }
//        return new ArrayList<>();
//    }

    @Override
    public ManualTargetAnalysisDTO getManualTarget(Integer deviceId, String targetName, String startDate, String endDate) {
        //不传时间则默认查询近当前时间七天的的数据
        if (StringUtils.isEmpty(startDate) || StringUtils.isEmpty(endDate)) {
            QueryWrapper<RecordManualCollectionEntity> wrapper = new QueryWrapper<>();
            wrapper.lambda().between(RecordManualCollectionEntity::getReportTime, DateUtil.getLast7DaysStartTime(), DateUtil.getCurrentTime())
                    .eq(RecordManualCollectionEntity::getDeviceId, deviceId);
        }
        QueryWrapper<RecordManualCollectionEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().between(RecordManualCollectionEntity::getReportTime, startDate, endDate)
                .eq(RecordManualCollectionEntity::getDeviceId, deviceId);
        List<RecordManualCollectionEntity> list = recordManualCollectionMapper.selectList(wrapper);
        if (!CollectionUtils.isEmpty(list)) {
            Map<Date, List<RecordManualCollectionEntity>> collect = list.stream().collect(Collectors.groupingBy(RecordManualCollectionEntity::getReportTime));
            //存在一天有多个批次 求平均值
            ArrayList<IndicatorEntityDTO> indicatorEntityDTOS = new ArrayList<>();
            for (Map.Entry<Date, List<RecordManualCollectionEntity>> entry : collect.entrySet()) {
                SimpleDateFormat formatter = new SimpleDateFormat(DateUtil.DATE_FORMAT);
                String dateString = formatter.format(entry.getKey());
                List<RecordManualCollectionEntity> manualCollectionList = entry.getValue();
                List<String> reportContentList = manualCollectionList.stream().map(RecordManualCollectionEntity::getReportContent).collect(Collectors.toList());
                List<RecordManualCollectionDTO> dtos = new ArrayList<>();
                reportContentList.stream().map(s -> JSONObject.parseArray(s, RecordManualCollectionDTO.class)).forEachOrdered(dtos::addAll);
                Map<String, List<RecordManualCollectionDTO>> listMap = dtos.stream().collect(Collectors.groupingBy(RecordManualCollectionDTO::getEname));
                if (listMap.containsKey(targetName)) {
                    List<RecordManualCollectionDTO> dtoList = listMap.get(targetName);
                    double v = dtoList.stream().filter(o -> o.getValue() != null).mapToDouble(RecordManualCollectionDTO::getValue).average().orElse(0.0);
                    for (RecordManualCollectionDTO recordManualCollectionDTO : dtoList) {
                        indicatorEntityDTOS.add(IndicatorEntityDTO.builder()
                                .targetName(targetName)
                                .deviceId(deviceId)
                                .targetCname(recordManualCollectionDTO.getName())
                                .dataVal(String.valueOf(v))
                                .unit(recordManualCollectionDTO.getUnit())
                                .time(dateString)
                                .build());
                    }
                }
            }
            return ManualTargetAnalysisDTO.builder()
                    .targetRecord(indicatorEntityDTOS)
                    .build();
        }
        return null;
    }

    @Override
    public List<StateEnumDTO> getDeviceStatusList() {
        return Stream.of(DevicesStateEnum.values())
                .map(devicesStateEnum -> StateEnumDTO.builder().code(devicesStateEnum.getCode()).name(devicesStateEnum.getName()).build())
                .collect(Collectors.toList());
    }

    /*@Override
    public DeviceOrdersRespDTO getWorkOrderInfo(String deviceId, String startDate, String endDate) {
        DeviceEntity deviceEntity = getById(deviceId);
        Integer fid = deviceEntity.getFid();
        if (fid == null) {
            return DeviceOrdersRespDTO.builder().build();
        }
        FacilitiesEntity facilitiesEntity = facilitiesService.getById(fid);
        Integer lineId = facilitiesEntity.getProductionLineId();

        //查询在该时间段内投产的工单
        List<String> collect = recordWorkOrderStateMapper.selectWorkingOrderList(WorkOrderStateEnum.INVESTMENT.getCode(), startDate, endDate);

        //作业工单情况
        if (workCenterService.isOperationByLineId(lineId)) {
            DeviceOrderDTO build = DeviceOrderDTO.builder().lineId(lineId).collect(collect).build();
            ResponseData responseData = assignmentInterface.selectByLineIdAndCollect(build);
            if (responseData != null) {
                List<OperationOrderDTO> orderDTOS = JSON.parseArray(JSON.toJSONString(responseData.getData()), OperationOrderDTO.class);
                return DeviceOrdersRespDTO.builder().operationOrder(orderDTOS).build();
            }
        }

        if (CollectionUtils.isEmpty(collect)) {
            return DeviceOrdersRespDTO.builder().build();
        }
        //过滤出该产线的工单
        LambdaQueryWrapper<WorkOrderEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WorkOrderEntity::getLineId, lineId).in(WorkOrderEntity::getWorkOrderNumber, collect);
        return DeviceOrdersRespDTO.builder().workOrder(workOrderMapper.selectList(wrapper)).build();
    }*/


    private TargetDictEntity getTargetDictEntity(String targetName) {
        QueryWrapper<TargetDictEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(TargetDictEntity::getTargetName, targetName);
        return targetDictService.getOne(queryWrapper);
    }


    @Override
    public List<MaterialAndBatchDTO> getMaterialAndBatch(Integer deviceId) {
        //六个月内设备生产的所有产品
        DeviceEntity deviceEntity = this.getById(deviceId);
        List<WorkOrderEntity> workOrderList;
        if (deviceEntity.getFid() != null) {
            //通过设备绑定的工位查询工位信息
            FacilitiesEntity facilitiesEntity = facilitiesService.getById(deviceEntity.getFid());
            workOrderList = workOrderMapper.getFinishWorkOrder(facilitiesEntity.getProductionLineId());
        } else {
            return null;
        }
        //近六个月完成状态的工单
        if (!CollectionUtils.isEmpty(workOrderList)) {
            Map<String, List<WorkOrderEntity>> collect = workOrderList.stream().collect(Collectors.groupingBy(WorkOrderEntity::getMaterialCode));
            ArrayList<MaterialAndBatchDTO> materialAndBatchList = new ArrayList<>();
            for (Map.Entry<String, List<WorkOrderEntity>> entry : collect.entrySet()) {
                String materialCode = entry.getKey();
                MaterialEntity materialEntity = materialService.getSimpleMaterialByCode(materialCode);
                String name = materialEntity.getName();
                List<WorkOrderEntity> workOrderEntities = entry.getValue();
                MaterialAndBatchDTO materialAndBatch = MaterialAndBatchDTO.builder()
                        .materialName(name)
                        .materialCode(materialCode)
                        .batchList(workOrderEntities)
                        .build();
                materialAndBatchList.add(materialAndBatch);
            }
            return materialAndBatchList;
        }
        return null;
    }

    @Override
    public List<TargetDictEntity> getTargetSetIds(Integer deviceId, Integer targetType) {
        List<TargetDictEntity> list = new ArrayList<>();
        DeviceEntity deviceEntity = this.getById(deviceId);
        if (targetType.equals(TargetTypeEnum.PRODUCTION.getCode())) {
            //################返回属于生产数据指标#######################
            if (!StringUtils.isEmpty(deviceEntity.getProTargetId())) {
                String[] proTargetIds = deviceEntity.getProTargetId().split(",");
                list = Arrays.stream(proTargetIds).map(proTargetId -> targetDictService.getById(proTargetId)).filter(Objects::nonNull).collect(Collectors.toList());
            } else {
                //没有设置指定查看的指标则返回该设备的所有指标
                Page<TargetModelEntity> targetModels = targetModelService.getList(deviceEntity.getModelId(), deviceId, null, null);
                List<TargetModelEntity> targetModelList = targetModels.getRecords();
                List<String> targetNames = targetModelList.stream().map(TargetModelEntity::getTargetName).collect(Collectors.toList());
                for (String targetName : targetNames) {
                    QueryWrapper<TargetDictEntity> queryWrapper = new QueryWrapper<>();
                    queryWrapper.lambda().eq(TargetDictEntity::getTargetName, targetName);
                    TargetDictEntity targetDictEntity = targetDictService.getOne(queryWrapper);
                    if (targetDictEntity != null && StringUtils.isNumeric(targetDictEntity.getTargetType())) {
                        int type = Integer.parseInt(targetDictEntity.getTargetType());
                        if (type == TargetTypeEnum.PRODUCTION.getCode() || type == TargetTypeEnum.MERGE.getCode()) {
                            list.add(targetDictEntity);
                        }
                    }
                }
            }
        } else if (targetType.equals(TargetTypeEnum.ADMINISTRATION.getCode())) {
            //#########################返回属于设备管理指标########################
            if (!StringUtils.isEmpty(deviceEntity.getAdmTargetId())) {
                String[] admTargetIds = deviceEntity.getAdmTargetId().split(",");
                for (String admTargetId : admTargetIds) {
                    TargetDictEntity targetDictEntity = targetDictService.getById(admTargetId);
                    if (targetDictEntity != null) {
                        list.add(targetDictEntity);
                    }
                }
            } else {
                //没有设置指定查看的指标则返回该设备的所有指标
                Page<TargetModelEntity> targetModels = targetModelService.getList(deviceEntity.getModelId(), deviceId, null, null);
                List<TargetModelEntity> targetModelList = targetModels.getRecords();
                List<String> targetNames = targetModelList.stream().map(TargetModelEntity::getTargetName).collect(Collectors.toList());
                for (String targetName : targetNames) {
                    QueryWrapper<TargetDictEntity> queryWrapper = new QueryWrapper<>();
                    queryWrapper.lambda().eq(TargetDictEntity::getTargetName, targetName);
                    TargetDictEntity targetDictEntity = targetDictService.getOne(queryWrapper);
                    if (targetDictEntity != null && targetDictEntity.getTargetType() != null) {
                        int type = Integer.parseInt(targetDictEntity.getTargetType());
                        if (type == TargetTypeEnum.ADMINISTRATION.getCode() || type == TargetTypeEnum.MERGE.getCode()) {
                            list.add(targetDictEntity);
                        }
                    }
                }

            }
        }
        return list;
    }

    @Override
    public void updateTargetBatch(RecordDeviceDayRunEntity entity) {
        DeviceEntity deviceEntity = this.getById(entity.getDeviceId());
        ModelEntity modelEntity = modelMapper.selectById(deviceEntity.getModelId());
        //灯检机不允许修改批次号
        if (modelEntity.getCode().equals(ModelIdEnum.LAMP_INSPECTION.getCode())) {
            return;
        }
        //如果新增或修改的是有效时间则更新这段时间内指标记录的批次号
        if (entity.getAddType().equals(DeviceAddTypeEnum.DEVICE_FINISH.getType())) {
            SimpleDateFormat format = new SimpleDateFormat(DateUtil.DATE_FORMAT);
            String dateStr = format.format(entity.getRecordDate());
            LocalDate localDate = LocalDate.parse(dateStr);
            //开始时间
            LocalTime start = LocalTime.parse(entity.getStartTime());
            LocalDateTime startDate = LocalDateTime.of(localDate, start);
            String startSrt = startDate.format(DateTimeFormatter.ofPattern(DateUtil.DATETIME_FORMAT));
            //结束时间
            LocalTime end = LocalTime.parse(entity.getEndTime());
            LocalDateTime endDate = LocalDateTime.of(localDate, end);
            String endSrt = endDate.format(DateTimeFormatter.ofPattern(DateUtil.DATETIME_FORMAT));
            Page<TargetModelEntity> targetList = targetModelService.getList(deviceEntity.getModelId(), null, null, null);
            List<TargetModelEntity> records = targetList.getRecords();
            for (TargetModelEntity targetModelEntity : records) {
                IndicatorEntityDTO dto = indicatorService.getFieldNameAndTableName(targetModelEntity.getTargetName());
                String fieldName = dto.getFieldName();
                String tableNameOneMin = dto.getTableName();

                String tableNameFiveMin = tableNameOneMin + Constant.FIVE_MIN;
                String tableNameTwentyMin = tableNameOneMin + Constant.TWENTY_MIN;
                String tableNameOneHours = tableNameOneMin + Constant.ONE_HOURS;
                indicatorService.updateRecord(entity.getDeviceId(), tableNameOneMin, fieldName, entity.getOrderName(), startSrt, endSrt);
                indicatorService.updateRecord(entity.getDeviceId(), tableNameFiveMin, fieldName, entity.getOrderName(), startSrt, endSrt);
                indicatorService.updateRecord(entity.getDeviceId(), tableNameTwentyMin, fieldName, entity.getOrderName(), startSrt, endSrt);
                indicatorService.updateRecord(entity.getDeviceId(), tableNameOneHours, fieldName, entity.getOrderName(), startSrt, endSrt);
            }
        }
    }

    @Override
    public void deleteTargetBatch(RecordDeviceDayRunEntity entity) {
        DeviceEntity deviceEntity = this.getById(entity.getDeviceId());
        ModelEntity modelEntity = modelMapper.selectById(deviceEntity.getModelId());
        //灯检机不允许修改批次号
        if (modelEntity.getCode().equals(ModelIdEnum.LAMP_INSPECTION.getCode())) {
            return;
        }
        //如果新增或修改的是有效时间则更新这段时间内指标记录的批次号
        if (entity.getAddType().equals(DeviceAddTypeEnum.DEVICE_FINISH.getType())) {
            SimpleDateFormat format = new SimpleDateFormat(DateUtil.DATE_FORMAT);
            String dateStr = format.format(entity.getRecordDate());
            LocalDate localDate = LocalDate.parse(dateStr);
            //开始时间
            LocalTime start = LocalTime.parse(entity.getStartTime());
            LocalDateTime startDate = LocalDateTime.of(localDate, start);
            String startSrt = startDate.format(DateTimeFormatter.ofPattern(DateUtil.DATETIME_FORMAT));
            //结束时间
            LocalTime end = LocalTime.parse(entity.getEndTime());
            LocalDateTime endDate = LocalDateTime.of(localDate, end);
            String endSrt = endDate.format(DateTimeFormatter.ofPattern(DateUtil.DATETIME_FORMAT));
            Page<TargetModelEntity> targetList = targetModelService.getList(deviceEntity.getModelId(), null, null, null);
            List<TargetModelEntity> records = targetList.getRecords();
            for (TargetModelEntity targetModelEntity : records) {
                IndicatorEntityDTO dto = indicatorService.getFieldNameAndTableName(targetModelEntity.getTargetName());
                String fieldName = dto.getFieldName();
                String tableNameOneMin = dto.getTableName();
                String tableNameFiveMin = tableNameOneMin + Constant.FIVE_MIN;
                String tableNameTwentyMin = tableNameOneMin + Constant.TWENTY_MIN;
                String tableNameOneHours = tableNameOneMin + Constant.ONE_HOURS;
                indicatorService.deleteRecord(entity.getDeviceId(), tableNameOneMin, fieldName, entity.getOrderName(), startSrt, endSrt);
                indicatorService.deleteRecord(entity.getDeviceId(), tableNameFiveMin, fieldName, entity.getOrderName(), startSrt, endSrt);
                indicatorService.deleteRecord(entity.getDeviceId(), tableNameTwentyMin, fieldName, entity.getOrderName(), startSrt, endSrt);
                indicatorService.deleteRecord(entity.getDeviceId(), tableNameOneHours, fieldName, entity.getOrderName(), startSrt, endSrt);
            }
        }

    }

    @Override
    public Map<String, List<IndicatorEntityDTO>> getLampTvOneToTvNineDataAboutTime(Integer deviceId) {
        Map<String, List<IndicatorEntityDTO>> map = new LinkedHashMap<>();
        List<IndicatorEntityDTO> tvOne = indicatorService.getRecordInmMinute(deviceId, SensorParamEnum.TV_ONE_SCRAP_NUMBER.getEname());
        //排序
        tvOne.sort(Comparator.comparing(IndicatorEntityDTO::getTime));
        getUnit(SensorParamEnum.TV_ONE_SCRAP_NUMBER, tvOne);
        List<IndicatorEntityDTO> tvTwo = indicatorService.getRecordInmMinute(deviceId, SensorParamEnum.TV_TWO_SCRAP_NUMBER.getEname());
        tvTwo.sort(Comparator.comparing(IndicatorEntityDTO::getTime));
        getUnit(SensorParamEnum.TV_TWO_SCRAP_NUMBER, tvTwo);
        List<IndicatorEntityDTO> tvThree = indicatorService.getRecordInmMinute(deviceId, SensorParamEnum.TV_THREE_SCRAP_NUMBER.getEname());
        tvThree.sort(Comparator.comparing(IndicatorEntityDTO::getTime));
        getUnit(SensorParamEnum.TV_THREE_SCRAP_NUMBER, tvThree);
        List<IndicatorEntityDTO> tvFour = indicatorService.getRecordInmMinute(deviceId, SensorParamEnum.TV_FOUR_SCRAP_NUMBER.getEname());
        tvFour.sort(Comparator.comparing(IndicatorEntityDTO::getTime));
        getUnit(SensorParamEnum.TV_FOUR_SCRAP_NUMBER, tvFour);
        List<IndicatorEntityDTO> tvFive = indicatorService.getRecordInmMinute(deviceId, SensorParamEnum.TV_FIVE_SCRAP_NUMBER.getEname());
        tvFive.sort(Comparator.comparing(IndicatorEntityDTO::getTime));
        getUnit(SensorParamEnum.TV_FIVE_SCRAP_NUMBER, tvFive);
        List<IndicatorEntityDTO> tvSix = indicatorService.getRecordInmMinute(deviceId, SensorParamEnum.TV_SIX_SCRAP_NUMBER.getEname());
        tvSix.sort(Comparator.comparing(IndicatorEntityDTO::getTime));
        getUnit(SensorParamEnum.TV_SIX_SCRAP_NUMBER, tvSix);
        List<IndicatorEntityDTO> tvSeven = indicatorService.getRecordInmMinute(deviceId, SensorParamEnum.TV_SEVEN_SCRAP_NUMBER.getEname());
        tvSeven.sort(Comparator.comparing(IndicatorEntityDTO::getTime));
        getUnit(SensorParamEnum.TV_SEVEN_SCRAP_NUMBER, tvSeven);
        List<IndicatorEntityDTO> tvEight = indicatorService.getRecordInmMinute(deviceId, SensorParamEnum.TV_EIGHT_SCRAP_NUMBER.getEname());
        tvEight.sort(Comparator.comparing(IndicatorEntityDTO::getTime));
        getUnit(SensorParamEnum.TV_EIGHT_SCRAP_NUMBER, tvEight);
        List<IndicatorEntityDTO> tvNine = indicatorService.getRecordInmMinute(deviceId, SensorParamEnum.TV_NINE_SCRAP_NUMBER.getEname());
        tvNine.sort(Comparator.comparing(IndicatorEntityDTO::getTime));
        getUnit(SensorParamEnum.TV_NINE_SCRAP_NUMBER, tvNine);
        lampdataAssembly(map, tvOne, tvTwo, tvThree, tvFour, tvFive, tvSix, tvSeven, tvEight, tvNine);
        return map;
    }

    private void getUnit(SensorParamEnum tvTwoScrapNumber, List<IndicatorEntityDTO> list) {
        LambdaQueryWrapper<TargetDictEntity> qw = new LambdaQueryWrapper<>();
        qw.eq(TargetDictEntity::getTargetName, tvTwoScrapNumber.getEname());
        TargetDictEntity one = targetDictService.getOne(qw);
        list.forEach(indicatorEntityDTO -> indicatorEntityDTO.setUnit(one.getUnit()));
    }

    /**
     * 灯检数据组装
     *
     * @param
     * @return
     */
    private void lampdataAssembly(Map<String, List<IndicatorEntityDTO>> map, List<IndicatorEntityDTO> tvOne, List<IndicatorEntityDTO> tvTwo, List<IndicatorEntityDTO> tvThree, List<IndicatorEntityDTO> tvFour, List<IndicatorEntityDTO> tvFive, List<IndicatorEntityDTO> tvSix, List<IndicatorEntityDTO> tvSeven, List<IndicatorEntityDTO> tvEight, List<IndicatorEntityDTO> tvNine) {
        map.put(ScreenConstant.TV_ONE, tvOne);
        map.put(ScreenConstant.TV_TWO, tvTwo);
        map.put(ScreenConstant.TV_THREE, tvThree);
        map.put(ScreenConstant.TV_FOUR, tvFour);
        map.put(ScreenConstant.TV_FIVE, tvFive);
        map.put(ScreenConstant.TV_SIX, tvSix);
        map.put(ScreenConstant.TV_SEVEN, tvSeven);
        map.put(ScreenConstant.TV_EIGHT, tvEight);
        map.put(ScreenConstant.TV_NINE, tvNine);
    }

    @Override
    public Map<String, List<IndicatorEntityDTO>> getLampTvOneToTvNineDataAboutWorkOrder(Integer deviceId) {
        Map<String, List<IndicatorEntityDTO>> map = new LinkedHashMap<>();
        List<IndicatorEntityDTO> tvOne = indicatorService.getLatestFiveBatchOnTargetName(deviceId, SensorParamEnum.TV_ONE_SCRAP_NUMBER.getEname());
        List<IndicatorEntityDTO> tvTwo = indicatorService.getLatestFiveBatchOnTargetName(deviceId, SensorParamEnum.TV_TWO_SCRAP_NUMBER.getEname());
        List<IndicatorEntityDTO> tvThree = indicatorService.getLatestFiveBatchOnTargetName(deviceId, SensorParamEnum.TV_THREE_SCRAP_NUMBER.getEname());
        List<IndicatorEntityDTO> tvFour = indicatorService.getLatestFiveBatchOnTargetName(deviceId, SensorParamEnum.TV_FOUR_SCRAP_NUMBER.getEname());
        List<IndicatorEntityDTO> tvFive = indicatorService.getLatestFiveBatchOnTargetName(deviceId, SensorParamEnum.TV_FIVE_SCRAP_NUMBER.getEname());
        List<IndicatorEntityDTO> tvSix = indicatorService.getLatestFiveBatchOnTargetName(deviceId, SensorParamEnum.TV_SIX_SCRAP_NUMBER.getEname());
        List<IndicatorEntityDTO> tvSeven = indicatorService.getLatestFiveBatchOnTargetName(deviceId, SensorParamEnum.TV_SEVEN_SCRAP_NUMBER.getEname());
        List<IndicatorEntityDTO> tvEight = indicatorService.getLatestFiveBatchOnTargetName(deviceId, SensorParamEnum.TV_EIGHT_SCRAP_NUMBER.getEname());
        List<IndicatorEntityDTO> tvNine = indicatorService.getLatestFiveBatchOnTargetName(deviceId, SensorParamEnum.TV_NINE_SCRAP_NUMBER.getEname());
        lampdataAssembly(map, tvOne, tvTwo, tvThree, tvFour, tvFive, tvSix, tvSeven, tvEight, tvNine);
        return map;
    }

    private List<IndicatorEntityDTO> getBatchRecords(String[] batchList, String targetName, Integer deviceId) {
        IndicatorEntityDTO dto = indicatorService.getFieldNameAndTableName(targetName);
        String fieldName = dto.getFieldName();
        String tableName = dto.getTableName();
        List<IndicatorEntityDTO> indicatorList = new ArrayList<>();
        Arrays.stream(batchList).map(code -> indicatorService.getMoreBatchRecords(deviceId, tableName, fieldName, code)).forEachOrdered(indicatorList::addAll);
        return indicatorList;
    }


    @Override
    public List<CommonState> getAllCycle() {
        CycleEnum[] values = CycleEnum.values();
        List<CommonState> cycleEnums = new ArrayList<>();
        for (CycleEnum cycleEnum : values) {
            cycleEnums.add(CommonState.builder().code(cycleEnum.getCode()).name(cycleEnum.getName()).build());
        }
        return cycleEnums;
    }

    @Override
    public DeviceEntity setDisplayById(DeviceEntity entity) {
        if (entity.getProTargetId() != null) {
            //修改生产数据指标
            UpdateWrapper<DeviceEntity> wrapper = new UpdateWrapper<>();
            wrapper.lambda().set(DeviceEntity::getProTargetId, entity.getProTargetId())
                    .eq(DeviceEntity::getDeviceId, entity.getDeviceId());
            update(wrapper);
        } else if (entity.getAdmTargetId() != null) {
            //修改设备管理指标
            UpdateWrapper<DeviceEntity> wrapper = new UpdateWrapper<>();
            wrapper.lambda().set(DeviceEntity::getAdmTargetId, entity.getAdmTargetId())
                    .eq(DeviceEntity::getDeviceId, entity.getDeviceId());
            update(wrapper);
        }
        return entity;
    }


    @Override
    public List<TargetDictEntity> getShowTargetId(Integer deviceId, Integer targetType) {
        DeviceEntity deviceEntity = this.getById(deviceId);
        List<TargetDictEntity> list = new ArrayList<>();
        //生产数据指标
        if (targetType == TargetTypeEnum.PRODUCTION.getCode()) {
            if (StringUtils.isNotEmpty(deviceEntity.getProTargetId())) {
                String[] targetIds = deviceEntity.getProTargetId().split(",");
                if (targetIds.length == 0) {
                    return null;
                }
                list = Arrays.stream(targetIds).map(targetId -> targetDictService.getById(targetId)).filter(Objects::nonNull).collect(Collectors.toList());
            }
        } else if (targetType == TargetTypeEnum.ADMINISTRATION.getCode()) {
            //设备管理指标
            if (StringUtils.isNotEmpty(deviceEntity.getAdmTargetId())) {
                String[] targetIds = deviceEntity.getAdmTargetId().split(",");
                if (targetIds.length == 0) {
                    return null;
                }
                for (String targetId : targetIds) {
                    TargetDictEntity targetDictEntity = targetDictService.getById(targetId);
                    if (targetDictEntity != null) {
                        list.add(targetDictEntity);
                    }
                }
            }
        }
        return list;
    }


    @Override
    public DeviceEntity saveEntity(DeviceEntity deviceEntity) {
        if (StringUtils.isBlank(deviceEntity.getDeviceCode())) {
            deviceEntity.setDeviceCode(RandomUtil.randomString(10));
        }
        // 判断设备编码重复
        LambdaQueryWrapper<DeviceEntity> wrapper1 = new LambdaQueryWrapper<>();
        wrapper1.eq(DeviceEntity::getDeviceCode, deviceEntity.getDeviceCode());
        if (count(wrapper1) > 0) {
            throw new ResponseException(RespCodeEnum.DEVICE_CODE_DUPLICATE);
        }
        // 判断设备名称重复，3.6.1取消名称重复校验
        /*LambdaQueryWrapper<DeviceEntity> wrapper2 = new LambdaQueryWrapper<>();
        wrapper2.eq(DeviceEntity::getDeviceName, deviceEntity.getDeviceName());
        DeviceEntity entity2 = deviceMapper.selectOne(wrapper2);
        if (entity2 != null) {
            throw new ResponseException(RespCodeEnum.DEVICE_NAME_DUPLICATE);
        }*/
        if (deviceEntity.getGid() != null) {
            LambdaQueryWrapper<GridEntity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GridEntity::getGid, deviceEntity.getGid());
            GridEntity gridEntity = gridService.getOne(queryWrapper);
            deviceEntity.setCid(gridEntity.getCid());
            deviceEntity.setAid(gridEntity.getAid());
        }
        if (deviceEntity.getModelId() != null) {
            LambdaQueryWrapper<ModelEntity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ModelEntity::getId, deviceEntity.getModelId());
            ModelEntity modelEntity = modelMapper.selectOne(queryWrapper);
            if (modelEntity.getModelImg() != null && deviceEntity.getModelImg() == null) {
                deviceEntity.setModelImg(modelEntity.getModelImg());
            }
            if (modelEntity.getSimulationImg() != null && deviceEntity.getSimulationImg() == null) {
                deviceEntity.setSimulationImg(modelEntity.getSimulationImg());
            }
        }
        //判断设备关联模型，逐层设置模型数据
        formatFactoryModel(deviceEntity);
        // 设备类型
        deviceEntity.setTypeCode(DeviceTypeEnum.PRODUCTION.getCode());
        deviceEntity.setTypeName(DeviceTypeEnum.PRODUCTION.getType());
        deviceMapper.insert(deviceEntity);
        saveOrUpdateFile(deviceEntity, null);
        uploadService.markUploadFile(deviceEntity.getModelImg(), deviceEntity);
        uploadService.markUploadFile(deviceEntity.getSimulationImg(), deviceEntity);

        redisTemplate.delete(redisTemplate.keys(RedisKeyPrefix.INSTANCE_TREE + "*"));
        // 推送消息
        kafkaService.pushNewMessage(deviceEntity, Constants.KAFKA_MAIN_DATA_TOPIC, KafkaMessageTypeEnum.PRODUCTION_EQUIPMENT_ADD_MESSAGE);
        return this.getByDeviceCode(deviceEntity.getDeviceCode());
    }

    @Override
    public void excelImport(MultipartFile file) {
        //加锁
        Boolean lockStatus = redisTemplate.opsForValue().setIfAbsent(RedisKeyPrefix.DEVICE_IMPORT_LOCK, new Date(), 5, TimeUnit.MINUTES);
        if (lockStatus == null || !lockStatus) {
            log.info("获取redis同步锁失败");
            throw new ResponseException(RespCodeEnum.IMPORT_OPREATION_NOT_RE);
        }
        ImportProgressDTO build = ImportProgressDTO.builder().progress(Double.valueOf(Constant.ZERO_VALUE)).build();
        redisTemplate.opsForValue().set(RedisKeyPrefix.DEVICE_IMPORT_PROGRESS, JSONObject.toJSONString(build), 5, TimeUnit.MINUTES);
        String filename = file.getOriginalFilename();
        //1、判断导入文件的合法性
        ExcelUtil.checkImportExcelFile(file);
        try (InputStream inputStream = file.getInputStream()) {
            List<DeviceImportDTO> deviceImports = EasyExcelUtil.dynamicColumnRead(inputStream, DeviceImportDTO.class, 0, 2);
            dealDeviceImport(deviceImports);
            int successCount = (int) deviceImports.stream().filter(e -> StringUtils.isEmpty(e.getReason())).count();
            String logString = EasyExcelDTO.getLogString(deviceImports, 3);
            int totalCount = deviceImports.size();
            int failCount = totalCount - successCount;
            // 创建并上传日志
            String type = ImportTypeEnum.DEVICE_IMPORT.getType();
            String url = importDataRecordService.writeLogData(logString, userAuthenService.getUsername(), filename, type, successCount, failCount);
            importDataRecordService.addImportProgress(RedisKeyPrefix.DEVICE_IMPORT_PROGRESS, "成功" + successCount + "条，" + "失败" + failCount + "条。", 1.0, true, url);

        } catch (Exception e) {
            importDataRecordService.addImportProgress(RedisKeyPrefix.DEVICE_IMPORT_PROGRESS, "转换excel失败：" + e.getMessage(), 1.0, true, null);
            log.error("设备导入错误", e);
        } finally {
            redisTemplate.delete(RedisKeyPrefix.DEVICE_IMPORT_LOCK);
        }
    }

    @Override
    public PrintDTO print(DeviceSelectDTO selectDTO) {
        List<PrintDTO.ParamDto> printDTOS = new ArrayList<>();
        // 必填项如果未填，则直接返回，防止前端误传导致CPU飙高、以及未打印的数据显示已打印
        boolean pageParams = Objects.isNull(selectDTO.getCurrentPage()) || Objects.isNull(selectDTO.getPageSize());
        if (StringUtils.isBlank(selectDTO.getDeviceFullCodes()) && pageParams) {
            throw new ResponseException(RespCodeEnum.PRINT_IS_REQUIRE);
        }
        Page<DeviceEntity> page = listByPage(selectDTO);
        List<DeviceEntity> deviceEntities = page.getRecords();

        // 新版本通过前端选择的标签规则进行打印
        LabelEntity labelEntity = labelService.lambdaQuery().eq(LabelEntity::getRuleId, selectDTO.getRuleId()).one();
        if (labelEntity == null) {
            throw new ResponseException(RespCodeEnum.NOT_FIND_BAR_CODE_RULE);
        }
        LabelTypeConfigEntity labelTypeConfigEntity = labelTypeConfigService.detail(labelEntity.getCodeType());
        BarCodeAnalysisService barCodeAnalysisService = SpringUtil.getBean(BarCodeAnalysisService.class);
        // 每次打印，序号初始化为1
        int i = 1;
        // 获取打印模板, 替换打印模板里${ }的数据
        for (DeviceEntity deviceEntity : deviceEntities) {
            PrintDTO printDTO = JSON.parseObject(labelEntity.getContent(), PrintDTO.class);
            List<PrintDTO.ParamDto.Content> printElements = printDTO.getPanels().get(0).getPrintElements();
            for (PrintDTO.ParamDto.Content content : printElements) {
                String data = content.getOptions().getTitle();
                if (StringUtils.isBlank(data)) {
                    continue;
                }
                // 替换占位符, 替换值为查询对象的某个属性值
                Matcher matcher = PATTERN.matcher(data);
                while (matcher.find()) {
                    String placeholder = matcher.group();
                    // 序号加1
                    if (placeholder.equals(LabelInfoEnum.SERIAL_NO.getPlaceholder())) {
                        data = String.valueOf(i);
                        i++;
                        continue;
                    }
                    CodeInfoSelectDTO build = CodeInfoSelectDTO.builder()
                            .ruleType(labelEntity.getCodeType())
                            .relateType(labelTypeConfigEntity.getRelateType())
                            .deviceCode(deviceEntity.getDeviceCode())
                            .supplierName(deviceEntity.getSupplier())
                            .placeholder(placeholder)
                            .sliceDigits(content.getOptions().getSliceDigits())
                            .build();
                    // 判断该标签是否存在公式，如果存在运算公式，将对数据进行四则运算
                    // 如果公式为空，则直接进行真实值替换占位符的逻辑
                    String placeholderValue = barCodeAnalysisService.replacePlaceholder(content, build);
                    data = data.replace(placeholder, placeholderValue);
                }
                content.getOptions().setTitle(data);
                // 字体大小如果为空,则设置默认值
                if (content.getOptions().getFontSize() == null) {
                    content.getOptions().setFontSize(9.0);
                }
            }
            printDTOS.addAll(printDTO.getPanels());
        }
        List<String> deviceCodes = deviceEntities.stream().map(DeviceEntity::getDeviceCode).collect(Collectors.toList());
        PrintDTO printDTO = PrintDTO.builder().ruleType(labelEntity.getCodeType()).ruleCode(labelEntity.getRuleCode()).panels(printDTOS).build();
        PrintDTO dto = labelService.getPrintDtoByOpenApi(printDTO);
        // 记录打印的编号
        dto.setPrintCodes(StringUtils.join(deviceCodes, Constant.SEP));
        return dto;
    }


    /**
     * 校验导入的数据, 不成功的赋予reason字段
     */
    private void dealDeviceImport(List<DeviceImportDTO> deviceImports) {
        Date now = new Date();
        String username = userAuthenService.getUsername();
        if (username == null) {
            username = CommonModelExcel.USER_NAME.get();
        }
        for (DeviceImportDTO dto : deviceImports) {
            DeviceEntity device = DeviceEntity.builder()
                    .deviceCode(dto.getDeviceCode())
                    .deviceName(dto.getDeviceName())
                    .place(dto.getPlace())
                    .remark(dto.getRemark())
                    .brandModel(dto.getBrandModel())
                    .purchaseDate(dto.getPurchaseDate())
                    .createTime(Objects.nonNull(dto.getCreateTime()) ? dto.getCreateTime() : now)
                    .createBy(username)
                    .updateTime(now)
                    .updateBy(username)
                    .build();
            StringBuilder reason = new StringBuilder();
            // 必填
            if (StringUtils.isEmpty(dto.getDeviceName())) {
                reason.append("设备名称不能为空;");
            }
//            else {
//                // 设备名称不能重复
//                boolean exists = this.lambdaQuery().eq(DeviceEntity::getDeviceName, dto.getDeviceName()).exists();
//                if (exists) {
//                    reason.append("系统已存在名称相同的设备;");
//                }
//            }
            if (StringUtils.isEmpty(dto.getDeviceCode())) {
                device.setDeviceCode(RandomUtil.randomString(10));
            }
            if (StringUtils.isEmpty(dto.getModelName())) {
                reason.append("设备类型不能为空;");
            } else {
                LambdaQueryWrapper<ModelEntity> modelWrapper = new LambdaQueryWrapper<>();
                modelWrapper.eq(ModelEntity::getName, dto.getModelName()).eq(ModelEntity::getType, ModelEnum.DEVICE.getType()).last("limit 1");
                ModelEntity model = modelMapper.selectOne(modelWrapper);
                if (model == null) {
                    reason.append("未找到该设备类型:").append(dto.getModelName()).append(";");
                } else {
                    device.setModelId(model.getId());
                }

            }
            // 非必填
            if (StringUtils.isNotEmpty(dto.getMagNickname()) || StringUtils.isNotEmpty(dto.getMaintainerNickname())) {
                List<SysUserEntity> users = sysUserService.getListByNickNames(Arrays.asList(dto.getMagNickname(), dto.getMaintainerNickname()));
                Map<String, String> nickUserMap = users.stream().collect(Collectors.toMap(SysUserEntity::getNickname, SysUserEntity::getUsername));
                if (StringUtils.isNotEmpty(dto.getMagNickname())) {
                    if (!nickUserMap.containsKey(dto.getMagNickname())) {
                        reason.append("未找到该负责人员信息或已被禁用:").append(dto.getMagNickname()).append(";");
                    } else {
                        device.setMagName(nickUserMap.get(dto.getMagNickname()));
                    }
                }
                if (StringUtils.isNotEmpty(dto.getMaintainerNickname())) {
                    if (!nickUserMap.containsKey(dto.getMaintainerNickname())) {
                        reason.append("未找到该维护人员信息或已被禁用:").append(dto.getMaintainerNickname()).append(";");
                    } else {
                        device.setMaintainer(nickUserMap.get(dto.getMaintainerNickname()));
                    }
                }
            }
            String useStateName = dto.getUseStateName();
            if (StringUtils.isNotBlank(useStateName)) {
                Integer useState = DevicesUseStateEnum.getCodeByName(useStateName);
                if (useState == null) {
                    reason.append("使用状态填写错误; ");
                } else {
                    device.setUseState(useState);
                }
            }
            // 供应商
            if (StringUtils.isNotEmpty(dto.getSupplier())) {
                SupplierEntity supplier = supplierService.getByName(dto.getSupplier());
                if (supplier == null) {
                    reason.append("未找到该供应商:").append(dto.getSupplier()).append(";");
                } else {
                    device.setSupplier(supplier.getName());
                    device.setSupplierContactWay(supplier.getPhone());
                }
            }
            // 最终保存
            if (StringUtils.isBlank(reason)) {
                try {
                    saveEntity(device);
                } catch (Exception e) {
                    dto.setReason(e.getMessage());
                }
            } else {
                dto.setReason(reason.toString());
            }
        }
    }

    /**
     * 根据设备关联的模型设置模型id
     * 设备只能让用户选择关联车间和工位  现在是不能关联产线
     *
     * @param deviceEntity
     */
    private void formatFactoryModel(DeviceEntity deviceEntity) {
        //关联工位->直接从工位数据里面取模型数据
        if (deviceEntity.getFid() != null) {
            FacilitiesEntity facilitiesEntity = facilitiesMapper.selectById(deviceEntity.getFid());
            if (facilitiesEntity != null) {
                deviceEntity.setProductionLineId(facilitiesEntity.getProductionLineId());
                deviceEntity.setGid(facilitiesEntity.getGid());
                deviceEntity.setAid(facilitiesEntity.getAid());
                deviceEntity.setCid(facilitiesEntity.getCid());
                deviceEntity.setRelateType(DeviceModelEnum.FACILITY.getType());
            }
        } else if (deviceEntity.getGid() != null) {
            //关联车间
            GridEntity gridEntity = gridMapper.selectById(deviceEntity.getGid());
            deviceEntity.setAid(gridEntity != null ? gridEntity.getAid() : null);
            AreaEntity areaEntity = areaMapper.selectById(deviceEntity.getAid());
            deviceEntity.setCid(areaEntity != null ? areaEntity.getCid() : null);
            deviceEntity.setRelateType(DeviceModelEnum.GRID.getType());
        }

    }

    /**
     * 保存/更新设备附件
     *
     * @param deviceEntity
     * @param oldEntity
     */
    private void saveOrUpdateFile(DeviceEntity deviceEntity, DeviceEntity oldEntity) {
        List<AppendixEntity> appendixEntities = deviceEntity.getAppendixEntity();
        if (oldEntity == null) {
            // 新增 保存文件
            saveFile(deviceEntity, appendixEntities);
        } else {
            // 编辑 删除旧文件
            appendixService.removeByRelateId(String.valueOf(deviceEntity.getDeviceId()), AppendixTypeEnum.DEVICE_APPENDIX.getCode());
            // 编辑 保存新文件
            saveFile(deviceEntity, appendixEntities);
        }
    }

    /**
     * 保存设备文件
     *
     * @param deviceEntity
     * @param appendixEntities
     */
    private void saveFile(DeviceEntity deviceEntity, List<AppendixEntity> appendixEntities) {
        if (!CollectionUtils.isEmpty(appendixEntities)) {
            String relateId = String.valueOf(deviceEntity.getDeviceId());
            String username = deviceEntity.getCreateBy();
            appendixService.addAppendix(appendixEntities, AppendixTypeEnum.DEVICE_APPENDIX, relateId, deviceEntity.getDeviceCode(), username);
        }
    }


    @Override
    public void updateEntity(DeviceEntity deviceEntity) {
        DeviceEntity old = getById(deviceEntity.getDeviceId());

        // 如果设备选择关联车间，则置空工位ID字段
        // 如果设备选择关联工位，则置空单位ID、厂区ID、车间ID字段
        UpdateWrapper<DeviceEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().set(DeviceEntity::getCid, deviceEntity.getCid())
                .set(DeviceEntity::getAid, deviceEntity.getAid());
        if (deviceEntity.getGid() != null) {
            LambdaQueryWrapper<GridEntity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GridEntity::getGid, deviceEntity.getGid());
            GridEntity gridEntity = gridService.getOne(queryWrapper);
            if (gridEntity != null) {
                updateWrapper.lambda().set(DeviceEntity::getCid, gridEntity.getCid())
                        .set(DeviceEntity::getAid, gridEntity.getAid());
            }
        }
        //判断设备关联模型，逐层设置模型数据
        formatFactoryModel(deviceEntity);
        updateWrapper.lambda()
                .eq(DeviceEntity::getDeviceId, deviceEntity.getDeviceId())
                .set(DeviceEntity::getDeviceName, deviceEntity.getDeviceName())
                .set(DeviceEntity::getPlace, deviceEntity.getPlace())
                .set(DeviceEntity::getModelId, deviceEntity.getModelId())
                .set(DeviceEntity::getMagName, deviceEntity.getMagName())
                .set(DeviceEntity::getMagNickname, deviceEntity.getMagNickname())
                .set(DeviceEntity::getMagPhone, deviceEntity.getMagPhone())
                .set(DeviceEntity::getRemark, deviceEntity.getRemark())
                .set(DeviceEntity::getGid, deviceEntity.getGid())
                .set(DeviceEntity::getFid, deviceEntity.getFid())
                .set(DeviceEntity::getProductionLineId, deviceEntity.getProductionLineId())
                .set(DeviceEntity::getAid, deviceEntity.getAid())
                .set(DeviceEntity::getUseState, deviceEntity.getUseState())
                .set(DeviceEntity::getBrandModel, deviceEntity.getBrandModel())
                .set(DeviceEntity::getPurchaseDate, deviceEntity.getPurchaseDate())
                .set(DeviceEntity::getMaintainer, deviceEntity.getMaintainer())
                .set(DeviceEntity::getBindingDate, deviceEntity.getBindingDate())
                .set(DeviceEntity::getSupplier, deviceEntity.getSupplier())
                .set(DeviceEntity::getCid, deviceEntity.getCid());
        deviceMapper.update(deviceEntity, updateWrapper);

        //标记上传文件
        saveOrUpdateFile(deviceEntity, old);
        uploadService.markUploadFile(old.getModelImg(), deviceEntity.getModelImg(), deviceEntity);
        uploadService.markUploadFile(old.getSimulationImg(), deviceEntity.getSimulationImg(), deviceEntity);
        FacCalEuiService facCalEuiService = SpringUtil.getBean(FacCalEuiService.class);
        if (deviceEntity.getFid() != null) {
            facCalEuiService.isKeyFac(facilitiesService.getById(deviceEntity.getFid()));
        }
        if (old.getFid() != null && !old.getFid().equals(deviceEntity.getFid())) {
            facCalEuiService.isKeyFac(facilitiesService.getById(old.getFid()));
        }

        redisTemplate.delete(redisTemplate.keys(RedisKeyPrefix.INSTANCE_TREE + "*"));
        redisTemplate.delete(RedisKeyPrefix.getDeviceModelKey(deviceEntity.getDeviceId()));
        redisTemplate.delete(RedisKeyPrefix.getDeviceEntityKey(deviceEntity.getDeviceId()));
        targetModelService.resetCountTargetCache();

        // 推送消息
        kafkaService.pushNewMessage(deviceEntity, Constants.KAFKA_MAIN_DATA_TOPIC, KafkaMessageTypeEnum.PRODUCTION_EQUIPMENT_UPDATE_MESSAGE);
    }

    @Override
    public Page<DeviceEntity> deviceByGid(String gid, Integer current, Integer size) {
        QueryWrapper<DeviceEntity> queryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<DeviceEntity> lambda = queryWrapper.lambda();
        WrapperUtil.eq(lambda, DeviceEntity::getGid, gid);
        return this.page(new Page<>(current, size), queryWrapper);
    }

    @Override
    public List<DeviceEntity> deviceByLineId(Integer productionLineId) {
        //获取产线下的所有设施，拿到fid列表
        List<FacilitiesEntity> list = getFacilitiesEntities(productionLineId);
        List<DeviceEntity> resultList = new ArrayList<>();
        list.forEach(facilitiesEntity -> {
            List<DeviceEntity> deviceEntityList = deviceMapper.getlistByFid(facilitiesEntity.getFid());
            // 获取状态名称
            deviceEntityList.forEach(deviceEntity -> deviceEntity.setStateName(DevicesStateEnum.getNameByCode(deviceEntity.getState())));
            resultList.addAll(deviceEntityList);
        });
        return resultList.stream().sorted(Comparator.comparing(DeviceEntity::getSeq, Comparator.nullsLast(Integer::compareTo))).collect(Collectors.toList());
    }

    @Override
    public void bindSensor(String euis, Integer deviceId) {
        // 先删除绑定的传感器
        LambdaQueryWrapper<DeviceSensorEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DeviceSensorEntity::getDeviceId, deviceId);
        deviceSensorService.remove(queryWrapper);
        // 新增
        if (StringUtils.isNotBlank(euis)) {
            String[] euiArr = euis.split(",");
            Arrays.stream(euiArr).map(s -> DeviceSensorEntity.builder()
                    .eui(s)
                    .deviceId(deviceId)
                    .createDate(new Date())
                    .build()).forEachOrdered(deviceSensorEntity -> deviceSensorService.save(deviceSensorEntity));
        }
        dealAfterBindSensor(deviceId);
    }

    @Override
    public List<SensorEntity> getSensorList() {
        LambdaQueryWrapper<SensorEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ne(SensorEntity::getSensorType, SensorTypeCodeEnum.CUBICLE_BOARD.getCode())
                .ne(SensorEntity::getSensorType, SensorTypeCodeEnum.LINE_BOARD.getCode())
                .isNotNull(SensorEntity::getSensorType);
        return sensorMapper.selectList(queryWrapper);
    }


    @Override
    public DeviceEntity getByDeviceCode(String code) {
        DeviceEntity deviceEntity = this.lambdaQuery().eq(DeviceEntity::getDeviceCode, code).one();
        showName(Stream.of(deviceEntity).collect(Collectors.toList()));
        return deviceEntity;
    }

    @Override
    public List<DeviceEntity> deviceByLineIdAndUsername(Integer instanceId, String username, String type) {
        List<FacilitiesEntity> list = new ArrayList<>();
        List<DeviceEntity> resultList = new ArrayList<>();
        if (type.equals(ModelEnum.LINE.getType())) {
            //获取产线下的所有工位
            list = getFacilitiesEntities(instanceId);
        } else if (type.equals(ModelEnum.GRID.getType())) {
            //获取车间下的所有工位
            LambdaQueryWrapper<FacilitiesEntity> facQw = new LambdaQueryWrapper<>();
            facQw.eq(FacilitiesEntity::getGid, instanceId);
            list = facilitiesService.list(facQw);
            // 获取车间下的设备
            LambdaQueryWrapper<DeviceEntity> deviceWrapper = new LambdaQueryWrapper<>();
            deviceWrapper.eq(DeviceEntity::getGid, instanceId);
            List<DeviceEntity> deviceEntityList = this.list(deviceWrapper);
            resultList.addAll(deviceEntityList);
        }

        for (FacilitiesEntity facilitiesEntity : list) {
            // 获取用户负责的工位列表
            List<Integer> facList = deviceMapper.getFacListByUsername(username);
            if (CollectionUtils.isEmpty(facList)) {
                // 没有数据则添加无用的默认数据
                facList.add(0);
            }
            // 获取员工绑定的工位
            List<Integer> facIds = deviceMapper.getFidByUsername(username);
            if (CollectionUtils.isEmpty(facIds)) {
                // 没有数据则添加无用的默认数据
                facIds.add(0);
            }
            LambdaQueryWrapper<DeviceEntity> deviceWrapper = new LambdaQueryWrapper<>();
            // 数据隔离：只有设备的负责人、设备所在的工位负责人、工位的员工能够看到
            deviceWrapper.eq(DeviceEntity::getFid, facilitiesEntity.getFid())
                    .and(wrapper -> wrapper.eq(DeviceEntity::getMagName, username)
                            .or()
                            .in(DeviceEntity::getFid, facList)
                            .or()
                            .in(DeviceEntity::getFid, facIds));
            List<DeviceEntity> deviceEntityList = this.list(deviceWrapper);
//            deviceEntityList = deviceMapper.getListByFidAndUsername(facilitiesEntity.getFid(), username, facList, users);
            // 获取状态名称
            deviceEntityList.forEach(deviceEntity -> deviceEntity.setStateName(DevicesStateEnum.getNameByCode(deviceEntity.getState())));
            resultList.addAll(deviceEntityList);
        }
        return resultList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Integer id) {
        DeviceEntity deviceEntity = this.getById(id);
        // 工单相关
        //工单相关
        List<String> relateWorkOrderNumbers = workOrderBasicUnitRelationService.queryWorkOrderNumbers(WorkOrderBasicUnitQueryDTO.buildDevice(Collections.singletonList(id), null));
        if (!CollectionUtils.isEmpty(relateWorkOrderNumbers)) {
            throw new ResponseException("已有关联工单，无法删除");
        }
        Long count2 = workOrderDeviceRelevanceMapper.selectCount(Wrappers.lambdaQuery(WorkOrderDeviceRelevanceEntity.class).eq(WorkOrderDeviceRelevanceEntity::getDeviceId, id));
        if (count2 > 0) {
            throw new ResponseException("已成为工单'关联资源'，无法删除");
        }

        // 解绑 工作中心关联
        List<WorkCenterDeviceEntity> list = workCenterDeviceService.lambdaQuery().eq(WorkCenterDeviceEntity::getDeviceId, id).list();
        List<WorkCenterDeviceRelevanceEntity> relevanceEntities = workCenterDeviceRelevanceService.lambdaQuery().eq(WorkCenterDeviceRelevanceEntity::getDeviceId, id).list();
        List<Integer> workCenterIds = list.stream().map(WorkCenterDeviceEntity::getWorkCenterId).collect(Collectors.toList());
        workCenterIds.addAll(relevanceEntities.stream().map(WorkCenterDeviceRelevanceEntity::getWorkCenterId).collect(Collectors.toList()));

        workCenterDeviceService.lambdaUpdate().eq(WorkCenterDeviceEntity::getDeviceId, id).remove();
        workCenterDeviceRelevanceService.lambdaUpdate().eq(WorkCenterDeviceRelevanceEntity::getDeviceId, id).remove();
        // 删除
        deviceMapper.deleteById(id);
        QueryWrapper<DeviceSensorEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DeviceSensorEntity::getDeviceId, id);
        deviceSensorService.remove(queryWrapper);
        deviceSensorService.resetDeviceSensorIntoRedis();
        // 删除告警信息
        LambdaQueryWrapper<AlarmEntity> alarmWrapper = new LambdaQueryWrapper<>();
        alarmWrapper.eq(AlarmEntity::getDeviceId, id);
        alarmMapper.delete(alarmWrapper);

        redisTemplate.delete(redisTemplate.keys(RedisKeyPrefix.INSTANCE_TREE + "*"));
        for (Integer workCenterId : workCenterIds) {
            redisTemplate.delete(RedisKeyPrefix.getWorkCenterDetail(workCenterId));
        }
        // 推送消息
        kafkaService.pushNewMessage(deviceEntity, Constants.KAFKA_MAIN_DATA_TOPIC, KafkaMessageTypeEnum.PRODUCTION_EQUIPMENT_DELETE_MESSAGE);
    }

    @Override
    public List<DeviceEntity> getListByModelId(Integer modelId) {
        QueryWrapper<DeviceEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(DeviceEntity::getModelId, modelId);
        return this.list(wrapper);
    }

    @Override
    public LinkageToYangzjDTO getLinkageList(Integer device) {
        //传入的device不能为空
        if (device != null) {
            List<RunningStatePieChartDTO> list;
            if (device.equals(Constant.START)) {
                //洗瓶机 device=0
                QueryWrapper<ModelEntity> queryWrapper = new QueryWrapper<>();
                queryWrapper.lambda().eq(ModelEntity::getCode, ModelIdEnum.WASHING.getCode());
                ModelEntity modelEntity = modelMapper.selectOne(queryWrapper);
                ArrayList<DeviceTargetToYangzjDTO> xpjList = new ArrayList<>();
                //洗瓶机设备类型下的所有设备（曲线图）
                addTargetRecords(xpjList, modelEntity, WASHING_TARGET_NAME);
                //饼图
                list = getRunningState(modelEntity.getId());
                return LinkageToYangzjDTO.builder().xpjRecord(xpjList).pieChar(list).build();
            } else if (device.equals(Constant.END)) {
                //罐装机 device=1
                QueryWrapper<ModelEntity> queryWrapper = new QueryWrapper<>();
                queryWrapper.lambda().eq(ModelEntity::getCode, ModelIdEnum.CANNED.getCode());
                ModelEntity modelEntity = modelMapper.selectOne(queryWrapper);
                ArrayList<DeviceTargetToYangzjDTO> gzjList = new ArrayList<>();
                //罐装机设备类型下的所有设备（曲线图）
                addTargetRecords(gzjList, modelEntity, CANNED_TARGET_NAME);
                //饼图
                list = getRunningState(modelEntity.getId());
                return LinkageToYangzjDTO.builder().xpjRecord(gzjList).pieChar(list).build();
            }
        }
        return null;
    }

    private List<RunningStatePieChartDTO> getRunningState(Integer modelId) {
        List<RunningStatePieChartDTO> list = new ArrayList<>();
        //同一设备类型下的所有设备
        List<DeviceEntity> deviceList = this.getListByModelId(modelId);
        if (!CollectionUtils.isEmpty(deviceList)) {
            ArrayList<DeviceStateToYangzjDTO> stateToYangzjDTOS = new ArrayList<>();
            for (DeviceEntity deviceEntity : deviceList) {
                Integer deviceId = deviceEntity.getDeviceId();
                String deviceName = deviceEntity.getDeviceName();
                List<RecordDeviceRunEntity> deviceRunList = runMapper.getRunningState(deviceId, new Date());
                List<RecordDeviceStopEntity> deviceStopList = stopMapper.getStopState(deviceId, new Date());
                List<RecordDevicePauseEntity> devicePauseList = pauseMapper.getPauseState(deviceId, new Date());
                if (!CollectionUtils.isEmpty(deviceRunList) && !CollectionUtils.isEmpty(deviceStopList) && !CollectionUtils.isEmpty(devicePauseList)) {
                    Integer running = deviceRunList.get(0).getRunning();
                    Integer stop = deviceStopList.get(0).getStop();
                    Integer pause = devicePauseList.get(0).getPause();
                    int total = running + stop + pause;
                    double run;
                    double standby;
                    if (running == 0) {
                        run = 0.0000;
                    } else {
                        run = MathUtil.divideDouble(running.doubleValue(), (double) total, 4);
                    }
                    standby = MathUtil.sub(ONE, run);
                    stateToYangzjDTOS.add(DeviceStateToYangzjDTO.builder().deviceName(deviceName).run(run).standby(standby).build());
                }
            }
            list.add(RunningStatePieChartDTO.builder().pieChar(stateToYangzjDTOS).build());
            return list;
        }
        return null;

    }

    private void addTargetRecords(ArrayList<DeviceTargetToYangzjDTO> list, ModelEntity modelEntity, String cannedTargetName) {
        List<DeviceEntity> deviceList = this.getListByModelId(modelEntity.getId());
        for (DeviceEntity deviceEntity : deviceList) {
            Integer deviceId = deviceEntity.getDeviceId();
            String deviceName = deviceEntity.getDeviceName();
            List<IndicatorEntityDTO> records = indicatorService.getDayRecords(deviceId, cannedTargetName);
            list.add(DeviceTargetToYangzjDTO.builder().deviceName(deviceName).targetRecord(records).build());
        }
    }


    private List<FacilitiesEntity> getFacilitiesEntities(Integer productionLineId) {
        LambdaQueryWrapper<FacilitiesEntity> queryFacilitiesWrapper = new LambdaQueryWrapper<>();
        queryFacilitiesWrapper.eq(FacilitiesEntity::getProductionLineId, productionLineId);
        queryFacilitiesWrapper.eq(FacilitiesEntity::getTypeOneCode, FacilitiesTypeEnum.CUBICLE.getCode());
        return facilitiesService.list(queryFacilitiesWrapper);
    }

    /**
     * 通过产线获取设备的故障次数,停机时长列表
     *
     * @param productionLineId
     * @return
     */
    @Override
    public List<DeviceStopDTO> getDevicesStopAndTimes(Integer productionLineId) {
        Date now = new Date();
        Date beginTime = dictService.getDayOutputBeginTime(now);
        return getDeviceStopDtos(productionLineId, beginTime, now);
    }

    /**
     * 通过产线获取指定时间内设备的故障次数,停机时长列表
     *
     * @param productionLineId
     * @param start
     * @param end
     * @return
     */
    @Override
    public List<DeviceStopDTO> getDeviceStopDtos(Integer productionLineId, Date start, Date end) {
        List<FacilitiesEntity> facilitiesEntities = getFacilitiesEntities(productionLineId);
        //设备名称 故障次数,停机时长
        List<DeviceEntity> deviceEntities = new ArrayList<>();
        facilitiesEntities.forEach(facilitiesEntity -> {
            List<DeviceEntity> deviceEntityList = deviceMapper.getlistByFid(facilitiesEntity.getFid());
            deviceEntities.addAll(deviceEntityList);
        });
        List<DeviceStopDTO> dtos = new ArrayList<>();
        for (DeviceEntity deviceEntity : deviceEntities) {
            Integer deviceId = deviceEntity.getDeviceId();
            Integer typeCode = deviceEntity.getTypeCode();
            //停机时长
            int stopTime = getStopTime(start, end, deviceId, typeCode);
            //停机次数
            LambdaQueryWrapper<RecordDeviceStopCountEntity> stopCountWrapper = new LambdaQueryWrapper<>();
            stopCountWrapper.eq(RecordDeviceStopCountEntity::getDeviceId, deviceId)
                    .eq(RecordDeviceStopCountEntity::getTypeCode, typeCode)
                    .between(RecordDeviceStopCountEntity::getTime, start, end);
            List<RecordDeviceStopCountEntity> stopCountEntities = stopCountService.list(stopCountWrapper);
            int stopCount = stopCountEntities.size();
            Double stopHours = MathUtil.divideDouble(stopTime, 60, 2);
            dtos.add(DeviceStopDTO.builder()
                    .deviceName(deviceEntity.getDeviceName())
                    .stopTime(stopHours)
                    .stopCount(stopCount)
                    .build());
        }
        return dtos;
    }

    /**
     * 获取停机时长
     *
     * @param start
     * @param end
     * @param deviceId
     * @param typeCode
     * @return
     */
    private int getStopTime(Date start, Date end, Integer deviceId, Integer typeCode) {
        //获取查询的时间范围
        Date beginTime = start;
        Date nextDay = DateUtil.addDate(dictService.getDayOutputBeginTime(start), 1);
        int stopTime = 0;
        //如果次日首班时间较早于end,分次查询，直到晚于end
        while (nextDay.before(end)) {
            RecordDeviceStopEntity startTime = recordDeviceStopService.getByDate(deviceId, beginTime, nextDay);
            if (startTime != null && startTime.getStopOnWork() != null) {
                stopTime += startTime.getStopOnWork();
            }
            beginTime = nextDay;
            nextDay = DateUtil.addDate(nextDay, 1);
        }
        //查询截至end的时间
        RecordDeviceStopEntity endTime = recordDeviceStopService.getByDate(deviceId, beginTime, end);
        if (endTime != null && endTime.getStopOnWork() != null) {
            stopTime += endTime.getStopOnWork();
        }
        return stopTime;
    }

    /**
     * 获取eui绑定设备
     *
     * @param eui
     * @return
     */
    @Override
    public List<DeviceEntity> getDeviceByEui(String eui) {
        List<DeviceEntity> resultList = new ArrayList<>();
        List<DeviceSensorEntity> deviceSensorEntities = deviceSensorService.getDeviceSensorEntity(eui);
        if (CollectionUtils.isEmpty(deviceSensorEntities)) {
            return resultList;
        }
        return this.listByIds(deviceSensorEntities.stream().map(DeviceSensorEntity::getDeviceId).collect(Collectors.toList()));
    }

    @Override
    public List<DeviceScreenInfoVO> getDeviceOeeByGid(Integer gid, String startDate, String endDate) throws ParseException {
        List<DeviceScreenInfoVO> list = new ArrayList<>();
        //获取车间下参与产线计数的工位下的设备
        List<DeviceEntity> deviceEntities = getDeviceListByGid(gid);
        if (CollectionUtils.isEmpty(deviceEntities)) {
            return list;
        }
        if (StringUtils.isBlank(startDate) || StringUtils.isBlank(endDate)) {
            List<RecordDeviceOeeDTO> deviceInfoList = getRecordDeviceInfo(deviceEntities);
            if (CollectionUtils.isEmpty(deviceInfoList)) {
                return list;
            }
            for (RecordDeviceOeeDTO recordDeviceOeeDTO : deviceInfoList) {
                //获取该设备的标准值
                Double threshold = getTargetThresholdByDeviceId(TargetConst.DEVICE_OEE, recordDeviceOeeDTO.getDeviceId(), ModelEnum.DEVICE.getType());
                list.add(DeviceScreenInfoVO.builder()
                        .deviceName(recordDeviceOeeDTO.getDeviceName())
                        .lastValue(recordDeviceOeeDTO.getLastDayData() != null ? recordDeviceOeeDTO.getLastDayData().getOee() : 0)
                        .todayValue(recordDeviceOeeDTO.getTodayData() != null ? recordDeviceOeeDTO.getTodayData().getOee() : 0)
                        .standardValue(threshold)
                        .build());
            }
        } else {
            List<RecordDeviceOeeDTO> avgList = getRecordDeviceInfoByDate(startDate, endDate, deviceEntities);
            if (CollectionUtils.isEmpty(avgList)) {
                return list;
            }
            String targetName = TargetConst.DEVICE_OEE;
            for (RecordDeviceOeeDTO recordDeviceOeeDTO : avgList) {
                //获取该设备的标准值
                Double threshold = getTargetThresholdByDeviceId(targetName, recordDeviceOeeDTO.getDeviceId(), ModelEnum.DEVICE.getType());
                list.add(DeviceScreenInfoVO.builder()
                        .deviceName(recordDeviceOeeDTO.getDeviceName())
                        .avgValue(recordDeviceOeeDTO.getAvgData() != null ? recordDeviceOeeDTO.getAvgData().getOee() : 0)
                        .standardValue(threshold)
                        .build());
            }
        }
        return list;
    }

    /**
     * 获取参考值
     *
     * @param targetName
     * @param instanceId
     * @param modelType
     * @return
     */
    private Double getTargetThresholdByDeviceId(String targetName, Integer instanceId, String modelType) {
        double referenceValue = 0;
        //获取指标参考值
        QueryWrapper<TargetThresholdEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(TargetThresholdEntity::getTargetName, targetName)
                .eq(TargetThresholdEntity::getModelType, modelType)
                .eq(TargetThresholdEntity::getInstanceId, instanceId);
        TargetThresholdEntity targetThresholdEntity = targetThresholdMapper.selectOne(wrapper);
        if (targetThresholdEntity != null) {
            referenceValue = targetThresholdEntity.getReferenceValue();
        }
        return referenceValue;
    }

    /**
     * 获取时间段的平均数
     *
     * @param startDate
     * @param endDate
     * @param deviceEntities
     * @return
     * @throws ParseException
     */
    private List<RecordDeviceOeeDTO> getRecordDeviceInfoByDate(String startDate, String endDate, List<DeviceEntity> deviceEntities) throws ParseException {
        List<RecordDeviceOeeDTO> list = new ArrayList<>();
        SimpleDateFormat dateFormat = new SimpleDateFormat(DateUtil.DATE_FORMAT);
        List<Date> betweenDate = DateUtil.getBetweenDate(dateFormat.parse(startDate), dateFormat.parse(endDate));
        for (DeviceEntity deviceEntity : deviceEntities) {
            List<MetricsDeviceOeeEntity> collect = new ArrayList<>();
            for (Date date : betweenDate) {
                // 求工作日的数据 如果没有配置日历就认为全年无休
                Date beginTimeByReportDate = dictService.getBeginTimeByReportDate(date);
                Double workDuration = workCalendarService.traceWorkDuration(deviceEntity.getDeviceId(), ModelEnum.DEVICE.getType(), beginTimeByReportDate);
                if (workDuration != null && workDuration == 0) {
                    continue;
                }
                MetricsDeviceOeeEntity listByDate = deviceOeeService.getOneByDate(deviceEntity.getDeviceId(), date);
                if (listByDate != null) {
                    collect.add(listByDate);
                }
            }
            if (!CollectionUtils.isEmpty(collect)) {
                double amount = MathUtil.round(collect.stream().mapToDouble(MetricsDeviceOeeEntity::getAmount).average().orElse(0), 2);
                double theoreticalSpeed = MathUtil.round(collect.stream().mapToDouble(MetricsDeviceOeeEntity::getTheoreticalSpeed).average().orElse(0), 2);
                double unqualified = MathUtil.round(collect.stream().mapToDouble(MetricsDeviceOeeEntity::getUnqualified).average().orElse(0), 2);
                double runTime = MathUtil.round(collect.stream().mapToDouble(MetricsDeviceOeeEntity::getRunTime).average().orElse(0), 2);
                double loadTime = MathUtil.round(collect.stream().mapToDouble(MetricsDeviceOeeEntity::getLoadTime).average().orElse(0), 2);
                double yield = MathUtil.round(collect.stream().mapToDouble(MetricsDeviceOeeEntity::getYield).average().orElse(0), 2);
                double performance = MathUtil.round(collect.stream().mapToDouble(MetricsDeviceOeeEntity::getPerformance).average().orElse(0), 2);
                double timeEfficiency = MathUtil.round(collect.stream().mapToDouble(MetricsDeviceOeeEntity::getTimeEfficiency).average().orElse(0), 2);
                double oee = MathUtil.round(collect.stream().mapToDouble(MetricsDeviceOeeEntity::getOee).average().orElse(0), 2);
                MetricsDeviceOeeEntity build = MetricsDeviceOeeEntity.builder()
                        .deviceId(deviceEntity.getDeviceId())
                        .deviceName(deviceEntity.getDeviceName())
                        .theoreticalSpeed(theoreticalSpeed)
                        .amount(amount)
                        .unqualified(unqualified)
                        .runTime(runTime)
                        .loadTime(loadTime)
                        .yield(yield)
                        .performance(performance)
                        .timeEfficiency(timeEfficiency)
                        .oee(oee)
                        .build();
                list.add(RecordDeviceOeeDTO.builder()
                        .deviceId(deviceEntity.getDeviceId())
                        .deviceName(deviceEntity.getDeviceName())
                        .avgData(build)
                        .build());
            }
        }
        return list;
    }

    /**
     * 获取当天的设备数据
     *
     * @param deviceEntities
     * @return
     */
    private List<RecordDeviceOeeDTO> getRecordDeviceInfo(List<DeviceEntity> deviceEntities) {
        List<RecordDeviceOeeDTO> list = new ArrayList<>();
        Date now = new Date();
        for (DeviceEntity deviceEntity : deviceEntities) {
            //判断每个设备当天以及上一天是否是工作日
            Date date = getLastDayDate(now, deviceEntity);
            //获取当天设备的数据
            MetricsDeviceOeeEntity todayData = deviceOeeService.getOneByDate(deviceEntity.getDeviceId(), DateUtil.getDayBegin(now));
            //获取上一天的数据
            MetricsDeviceOeeEntity lastDayData = deviceOeeService.getOneByDate(deviceEntity.getDeviceId(), DateUtil.getDayBegin(date));
            list.add(RecordDeviceOeeDTO.builder()
                    .deviceId(deviceEntity.getDeviceId())
                    .deviceName(deviceEntity.getDeviceName())
                    .todayData(todayData)
                    .lastDayData(lastDayData)
                    .build());
        }
        return list;
    }

    /**
     * 获取上一个工作日的日期
     *
     * @param now
     * @param deviceEntity
     * @return
     */
    private Date getLastDayDate(Date now, DeviceEntity deviceEntity) {
        Date date = now;
        double lastDuration = 0;
        while (lastDuration == 0) {
            date = DateUtil.addDate(date, -1);
            Double workDuration = workCalendarService.traceWorkDuration(deviceEntity.getDeviceId(), ModelEnum.DEVICE.getType(), date);
            if (workDuration != null && workDuration == 0) {
                continue;
            }
            if (workDuration == null) {
                break;
            }
            lastDuration = workDuration;
        }
        return date;
    }

    /**
     * 获取车间所有的设备
     *
     * @param gid
     * @return
     */
    private List<DeviceEntity> getAllDeviceListByGid(Integer gid) {
        List<ProductionLineEntity> list = productionLineService.listLineByGid(gid);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        List<DeviceEntity> deviceEntities = new ArrayList<>();
        for (ProductionLineEntity lineEntity : list) {
            //过滤掉不参与产线计数的工位FacilitiesCountTypeEnum.NONE
            List<Integer> fids = facilitiesService.getFidsByLineId(lineEntity.getProductionLineId());
            if (CollectionUtils.isEmpty(fids)) {
                continue;
            }
            deviceEntities.addAll(getDevicesByFids(fids));
        }
        return deviceEntities;
    }


    /**
     * 获取车间下参与产线计数的工位设备
     *
     * @param gid
     * @return
     */
    private List<DeviceEntity> getDeviceListByGid(Integer gid) {
        List<ProductionLineEntity> list = productionLineService.listLineByGid(gid);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        List<DeviceEntity> deviceEntities = new ArrayList<>();
        for (ProductionLineEntity lineEntity : list) {
            //过滤掉不参与产线计数的工位FacilitiesCountTypeEnum.NONE
            List<Integer> fids = facilitiesMapper.filterByLineId(lineEntity.getProductionLineId(), FacilitiesCountTypeEnum.NONE.getCode());
            if (CollectionUtils.isEmpty(fids)) {
                continue;
            }
            deviceEntities.addAll(getDevicesByFids(fids));
        }
        return deviceEntities;
    }

    @Override
    public List<DeviceScreenInfoVO> getDeviceTimeEfficiencyByGid(Integer gid, String startDate, String endDate) throws ParseException {
        List<DeviceScreenInfoVO> list = new ArrayList<>();
        //获取车间下参与产线计数的工位设备
        List<DeviceEntity> deviceEntities = getDeviceListByGid(gid);
        if (CollectionUtils.isEmpty(deviceEntities)) {
            return list;
        }
        if (StringUtils.isBlank(startDate) || StringUtils.isBlank(endDate)) {
            List<RecordDeviceOeeDTO> deviceInfoList = getRecordDeviceInfo(deviceEntities);
            if (CollectionUtils.isEmpty(deviceInfoList)) {
                return list;
            }
            for (RecordDeviceOeeDTO recordDeviceOeeDTO : deviceInfoList) {
                list.add(DeviceScreenInfoVO.builder()
                        .deviceName(recordDeviceOeeDTO.getDeviceName())
                        .lastValue(recordDeviceOeeDTO.getLastDayData() != null ? recordDeviceOeeDTO.getLastDayData().getTimeEfficiency() : 0)
                        .todayValue(recordDeviceOeeDTO.getTodayData() != null ? recordDeviceOeeDTO.getTodayData().getTimeEfficiency() : 0)
                        .build());
            }
        } else {
            List<RecordDeviceOeeDTO> avgList = getRecordDeviceInfoByDate(startDate, endDate, deviceEntities);
            if (CollectionUtils.isEmpty(avgList)) {
                return list;
            }
            for (RecordDeviceOeeDTO recordDeviceOeeDTO : avgList) {
                list.add(DeviceScreenInfoVO.builder()
                        .deviceName(recordDeviceOeeDTO.getDeviceName())
                        .avgValue(recordDeviceOeeDTO.getAvgData() != null ? recordDeviceOeeDTO.getAvgData().getTimeEfficiency() : 0)
                        .build());
            }
        }
        return list;
    }

    @Override
    public List<DeviceScreenInfoVO> getDeviceYieldByGid(Integer gid, String startDate, String endDate) throws ParseException {
        List<DeviceScreenInfoVO> list = new ArrayList<>();
        //获取车间下参与产线计数的工位设备
        List<DeviceEntity> deviceEntities = getDeviceListByGid(gid);
        if (CollectionUtils.isEmpty(deviceEntities)) {
            return list;
        }
        if (StringUtils.isBlank(startDate) || StringUtils.isBlank(endDate)) {
            List<RecordDeviceOeeDTO> deviceInfoList = getRecordDeviceInfo(deviceEntities);
            if (CollectionUtils.isEmpty(deviceInfoList)) {
                return list;
            }
            for (RecordDeviceOeeDTO recordDeviceOeeDTO : deviceInfoList) {
                list.add(DeviceScreenInfoVO.builder()
                        .deviceName(recordDeviceOeeDTO.getDeviceName())
                        .lastValue(recordDeviceOeeDTO.getLastDayData() != null ? recordDeviceOeeDTO.getLastDayData().getYield() : 0)
                        .todayValue(recordDeviceOeeDTO.getTodayData() != null ? recordDeviceOeeDTO.getTodayData().getYield() : 0)
                        .build());
            }
        } else {
            List<RecordDeviceOeeDTO> avgList = getRecordDeviceInfoByDate(startDate, endDate, deviceEntities);
            if (CollectionUtils.isEmpty(avgList)) {
                return list;
            }
            for (RecordDeviceOeeDTO recordDeviceOeeDTO : avgList) {
                list.add(DeviceScreenInfoVO.builder()
                        .deviceName(recordDeviceOeeDTO.getDeviceName())
                        .avgValue(recordDeviceOeeDTO.getAvgData() != null ? recordDeviceOeeDTO.getAvgData().getYield() : 0)
                        .build());
            }
        }
        return list;
    }

    @Override
    public List<DeviceScreenInfoVO> getPerformanceEfficiency(Integer gid, String startDate, String endDate) throws ParseException {
        List<DeviceScreenInfoVO> list = new ArrayList<>();
        //获取车间下参与产线计数的工位设备
        List<DeviceEntity> deviceEntities = getDeviceListByGid(gid);
        if (CollectionUtils.isEmpty(deviceEntities)) {
            return list;
        }
        if (StringUtils.isBlank(startDate) || StringUtils.isBlank(endDate)) {
            List<RecordDeviceOeeDTO> deviceInfoList = getRecordDeviceInfo(deviceEntities);
            if (CollectionUtils.isEmpty(deviceInfoList)) {
                return list;
            }
            for (RecordDeviceOeeDTO recordDeviceOeeDTO : deviceInfoList) {
                list.add(DeviceScreenInfoVO.builder()
                        .deviceName(recordDeviceOeeDTO.getDeviceName())
                        .lastValue(recordDeviceOeeDTO.getLastDayData() != null ? recordDeviceOeeDTO.getLastDayData().getPerformance() : 0)
                        .todayValue(recordDeviceOeeDTO.getTodayData() != null ? recordDeviceOeeDTO.getTodayData().getPerformance() : 0)
                        .build());
            }
        } else {
            List<RecordDeviceOeeDTO> avgList = getRecordDeviceInfoByDate(startDate, endDate, deviceEntities);
            if (CollectionUtils.isEmpty(avgList)) {
                return list;
            }
            for (RecordDeviceOeeDTO recordDeviceOeeDTO : avgList) {
                list.add(DeviceScreenInfoVO.builder()
                        .deviceName(recordDeviceOeeDTO.getDeviceName())
                        .avgValue(recordDeviceOeeDTO.getAvgData() != null ? recordDeviceOeeDTO.getAvgData().getPerformance() : 0)
                        .build());
            }
        }
        return list;
    }

    @Override
    public List<DeviceEnergyConsumptionVO> getEnergyConsumptionByGid(Integer gid, String startDate, String endDate) {
        List<DeviceEnergyConsumptionVO> list = new ArrayList<>();
        DeviceEnergyConsumptionService deviceEnergyConsumptionService = SpringUtil.getBean(DeviceEnergyConsumptionService.class);
        // 获取该车间的设备列表
        List<DeviceEntity> devices = getAllDeviceListByGid(gid);
        if (CollectionUtils.isEmpty(devices)) {
            return list;
        }
        Map<Integer, DeviceEntity> deviceIdCodeMap = devices.stream().collect(Collectors.toMap(DeviceEntity::getDeviceId, v -> v));
        if (!ObjectUtils.isEmpty(startDate) && !ObjectUtils.isEmpty(endDate)) {
            // 时段统计
            // yyyy-MM-dd
            Date start = DateUtil.parse(startDate, DateUtil.DATE_FORMAT);
            Date end = DateUtil.parse(endDate, DateUtil.DATE_FORMAT);
            // 查询这段时间的内的所有能耗记录
            List<DeviceEnergyConsumptionEntity> deviceEnergyConsumptions = deviceEnergyConsumptionService.lambdaQuery()
                    .in(DeviceEnergyConsumptionEntity::getDeviceId, deviceIdCodeMap.keySet())
                    .ge(DeviceEnergyConsumptionEntity::getRecordDate, start)
                    .le(DeviceEnergyConsumptionEntity::getRecordDate, end)
                    .list();
            Map<Integer, Double> deviceIdConsumptionMap = deviceEnergyConsumptions.stream()
                    .collect(Collectors.groupingBy(DeviceEnergyConsumptionEntity::getDeviceId, Collectors.summingDouble(value -> value.getConsumption() != null ? value.getConsumption() : 0)));

            // 排序
            deviceIdCodeMap.forEach((deviceId, tem) -> list.add(DeviceEnergyConsumptionVO.builder()
                    .deviceId(tem.getDeviceId())
                    .deviceCode(tem.getDeviceCode())
                    .deviceName(tem.getDeviceName())
                    .sumValue(MathUtil.round(deviceIdConsumptionMap.getOrDefault(deviceId, 0.0), 1))
                    .build()));
            list.sort(Comparator.comparing(DeviceEnergyConsumptionVO::getDeviceCode));
            return list;
        }

        //设备的今日能耗, 上一日能耗, 能耗目标值：DFS配阈值
        Date now = new Date();
        // yyyy-MM-dd 00:00:00
        Date nowDate = DateUtil.getDayBegin(now);
        for (DeviceEntity device : devices) {
            //判断每个设备当天以及上一天是否是工作日
            Date lastDay = getLastDayDate(now, device);
            //获取当天设备的数据
            DeviceEnergyConsumptionEntity today = deviceEnergyConsumptionService.lambdaQuery()
                    .eq(DeviceEnergyConsumptionEntity::getDeviceId, device.getDeviceId())
                    .eq(DeviceEnergyConsumptionEntity::getRecordDate, nowDate)
                    .one();
            //获取上一天的数据
            DeviceEnergyConsumptionEntity last = deviceEnergyConsumptionService.lambdaQuery()
                    .eq(DeviceEnergyConsumptionEntity::getDeviceId, device.getDeviceId())
                    .eq(DeviceEnergyConsumptionEntity::getRecordDate, DateUtil.getDayBegin(lastDay))
                    .one();
            // 获取能耗目标值
            Double standardValue = getTargetThresholdByDeviceId(TargetConst.DAILY_ENERGY_CONSUMPTION, device.getDeviceId(), ModelEnum.DEVICE.getType());

            list.add(DeviceEnergyConsumptionVO.builder()
                    .deviceId(device.getDeviceId())
                    .deviceCode(device.getDeviceCode())
                    .deviceName(device.getDeviceName())
                    .todayValue(today != null ? MathUtil.round(today.getConsumption(), 1) : 0)
                    .lastValue(last != null ? MathUtil.round(last.getConsumption(), 1) : 0)
                    .standardValue(standardValue)
                    .build());
            list.sort(Comparator.comparing(DeviceEnergyConsumptionVO::getDeviceCode));
        }
        return list;
    }

    @Override
    public DeviceDataStatisticsVO todayDataAnalysis(Integer gridId) {
        //设备总数、运行中、待机中、已停机、维修中  计算得到=》合格率、设备开机率、设备故障率
        long runningCount = this.lambdaQuery()
                .eq(DeviceEntity::getGid, gridId)
                .eq(DeviceEntity::getState, DevicesStateEnum.RUNNING.getCode()).count();
        long suspendCount = this.lambdaQuery()
                .eq(DeviceEntity::getGid, gridId)
                .eq(DeviceEntity::getState, DevicesStateEnum.SUSPEND.getCode()).count();
        long stopCount = this.lambdaQuery()
                .eq(DeviceEntity::getGid, gridId)
                .eq(DeviceEntity::getState, DevicesStateEnum.STOP.getCode()).count();
        //维修中:拥有维修生效的维修工单关联的设备
        long maintenancingCount = this.lambdaQuery()
                .eq(DeviceEntity::getGid, gridId)
                .eq(DeviceEntity::getMaintenanceState, true).count();

        long faultCount = this.lambdaQuery()
                .eq(DeviceEntity::getGid, gridId)
                .eq(DeviceEntity::getState, DevicesStateEnum.FAULT.getCode()).count();
        long count = runningCount + suspendCount + stopCount + faultCount;
        String passRate = "100%";
        String powerOnRate = "0%";
        String failureRate = "0%";
        if (count != 0) {
            double temp = MathUtil.divideDouble((double) faultCount, (double) count, 2);
            failureRate = MathUtil.doubleToPercent(temp);
            passRate = MathUtil.doubleToPercent(MathUtil.sub(1.0, temp));
            powerOnRate = MathUtil.doubleToPercent(MathUtil.divideDouble((double) (runningCount + suspendCount), (double) count, 2));
        }
        return DeviceDataStatisticsVO.builder()
                .count(count)
                .runningCount(runningCount)
                .suspendCount(suspendCount)
                .stopCount(stopCount)
                .faultCount(maintenancingCount)
                .passRate(passRate)
                .powerOnRate(powerOnRate)
                .failureRate(failureRate)
                .build();
    }

    @Override
    public DeviceStateCountVO getDeviceStateCount(DeviceReqDTO dto) {
        //设备总数、运行中、待机中、已停机、维修中
        String targetParams = dto.getTargetParams();
        //不传默认查全部指标
        if (StringUtils.isBlank(targetParams)) {
            targetParams = "count,runCount,pauseCount,stopCount,faultCount,maintainCount";
        }
        long count = 0, runCount = 0, pauseCount = 0, stopCount = 0, faultCount = 0, maintainCount = 0;
        if (targetParams.contains(Constant.PARAM_RUN_COUNT)) {
            runCount = getDeviceStateCount(dto, DevicesStateEnum.RUNNING.getCode(), null);
        }
        if (targetParams.contains(Constant.PARAM_PAUSE_COUNT)) {
            pauseCount = getDeviceStateCount(dto, DevicesStateEnum.SUSPEND.getCode(), null);
        }
        if (targetParams.contains(Constant.PARAM_STOP_COUNT)) {
            stopCount = getDeviceStateCount(dto, DevicesStateEnum.STOP.getCode(), null);
        }
        if (targetParams.contains(Constant.PARAM_FAULT_COUNT)) {
            faultCount = getDeviceStateCount(dto, DevicesStateEnum.FAULT.getCode(), null);
        }
        if (targetParams.contains(Constant.PARAM_COUNT)) {
            count = getDeviceStateCount(dto, null, null);
        }
        if (targetParams.contains(Constant.PARAM_MAINTAIN_COUNT)) {
            maintainCount = getDeviceStateCount(dto, null, true);
        }
        return DeviceStateCountVO.builder().runCount(runCount).pauseCount(pauseCount).stopCount(stopCount)
                .faultCount(faultCount).maintainCount(maintainCount).count(count).build();
    }

    private long getDeviceStateCount(DeviceReqDTO dto, Integer state, Boolean maintenanceState) {
        return this.lambdaQuery()
                .eq(dto.getAid() != null, DeviceEntity::getAid, dto.getAid())
                .eq(dto.getGridId() != null, DeviceEntity::getGid, dto.getGridId())
                .eq(dto.getLineId() != null, DeviceEntity::getProductionLineId, dto.getLineId())
                .eq(dto.getModelId() != null, DeviceEntity::getModelId, dto.getModelId())
                .eq(dto.getDeviceId() != null, DeviceEntity::getDeviceId, dto.getDeviceId())
                .eq(state != null, DeviceEntity::getState, state)
                .eq(maintenanceState != null, DeviceEntity::getMaintenanceState, maintenanceState)
                .count();
    }

    @Override
    public Page<DeviceEntity> deviceOpenList(DeviceDTO dto) {
        Integer current = dto.getCurrent();
        Integer size = dto.getSize();
        String deviceCodes = dto.getDeviceCodes();
        LambdaQueryWrapper<DeviceEntity> wrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(deviceCodes)) {
            wrapper.in(DeviceEntity::getDeviceCode, Arrays.asList(deviceCodes.split(Constant.SEP)));
        }
        wrapper.eq(dto.getDeviceId() != null, DeviceEntity::getDeviceId, dto.getDeviceId())
                .in(!CollectionUtils.isEmpty(dto.getDeviceModelIds()), DeviceEntity::getModelId, dto.getDeviceModelIds())
                .like(StringUtils.isNotBlank(dto.getDeviceCode()), DeviceEntity::getDeviceCode, dto.getDeviceCode())
                .like(StringUtils.isNotBlank(dto.getDeviceName()), DeviceEntity::getDeviceName, dto.getDeviceName())
                .like(dto.getDeviceExtendOne() != null, DeviceEntity::getDeviceExtendOne, dto.getDeviceExtendOne())
                .like(dto.getDeviceExtendTwo() != null, DeviceEntity::getDeviceExtendTwo, dto.getDeviceExtendTwo())
                .like(dto.getDeviceExtendThree() != null, DeviceEntity::getDeviceExtendThree, dto.getDeviceExtendThree())
                .like(dto.getDeviceExtendFour() != null, DeviceEntity::getDeviceExtendFour, dto.getDeviceExtendFour())
                .like(dto.getDeviceExtendFive() != null, DeviceEntity::getDeviceExtendFive, dto.getDeviceExtendFive())
                .like(dto.getDeviceExtendSix() != null, DeviceEntity::getDeviceExtendSix, dto.getDeviceExtendSix())
                .like(dto.getDeviceExtendSeven() != null, DeviceEntity::getDeviceExtendSeven, dto.getDeviceExtendSeven())
                .like(dto.getDeviceExtendEight() != null, DeviceEntity::getDeviceExtendEight, dto.getDeviceExtendEight())
                .like(dto.getDeviceExtendNine() != null, DeviceEntity::getDeviceExtendNine, dto.getDeviceExtendNine())
                .like(dto.getDeviceExtendTen() != null, DeviceEntity::getDeviceExtendTen, dto.getDeviceExtendTen());
        if (StringUtils.isNotBlank(dto.getGridCode())) {
            //通过车间查询所有设备
            GridEntity gridEntity = gridService.getDetailByCode(dto.getGridCode());
            List<Integer> facIds = facilitiesService.getFidsByGid(gridEntity.getGid());
            wrapper.and(
                    o -> o.in(DeviceEntity::getGid, gridEntity.getGid()).or().in(DeviceEntity::getFid, facIds)
            );

        } else if (StringUtils.isNotBlank(dto.getLineCode())) {
            //通过产线查询所有设备
            ProductionLineEntity lineEntity = productionLineService.getEntityByLineCode(dto.getLineCode());
            List<Integer> facIds = facilitiesService.getFidsByLineId(lineEntity.getProductionLineId());
            wrapper.in(DeviceEntity::getFid, facIds);
        }
        //工作中心筛选
        if (dto.getWorkCenterId() != null) {
            List<WorkCenterDeviceEntity> workCenterTeamEntityList = workCenterDeviceService.lambdaQuery()
                    .eq(WorkCenterDeviceEntity::getWorkCenterId, dto.getWorkCenterId()).list();
            if (CollectionUtils.isEmpty(workCenterTeamEntityList)) {
                return new Page<>();
            }
            wrapper.in(DeviceEntity::getDeviceId, workCenterTeamEntityList.stream().map(WorkCenterDeviceEntity::getDeviceId)
                    .collect(Collectors.toList()));
        }
        Page<DeviceEntity> page = this.page(new Page<>(current, size), wrapper);
        Map<Integer, String> areaMap = areaMapper.selectList(null).stream()
                .collect(Collectors.toMap(AreaEntity::getAid, AreaEntity::getAname));
        Map<Integer, String> gridMap = gridService.lambdaQuery().list().stream()
                .collect(Collectors.toMap(GridEntity::getGid, GridEntity::getGname));
        Map<Integer, String> lineMap = productionLineService.lambdaQuery().list().stream()
                .collect(Collectors.toMap(ProductionLineEntity::getProductionLineId, ProductionLineEntity::getName));
        for (DeviceEntity deviceEntity : page.getRecords()) {
            Integer deviceId = deviceEntity.getDeviceId();
            // 获取模型名称
            LambdaQueryWrapper<ModelEntity> modelWrapper = new LambdaQueryWrapper<>();
            modelWrapper.eq(ModelEntity::getId, deviceEntity.getModelId());
            ModelEntity modelEntity = modelMapper.selectOne(modelWrapper);
            deviceEntity.setModelName(modelEntity != null ? modelEntity.getName() : null);
            // 设置传感器
            List<DeviceSensorEntity> deviceSensorEntities = getDeviceSensorEntities(deviceId);
            deviceEntity.setSensorList(deviceSensorEntities);

            deviceEntity.setAname(areaMap.get(deviceEntity.getAid()));
            deviceEntity.setGname(gridMap.get(deviceEntity.getGid()));
            deviceEntity.setProductionLineName(lineMap.get(deviceEntity.getProductionLineId()));
            //展示附件
            deviceEntity.setAppendixEntity(appendixService.getFilesById(AppendixTypeEnum.DEVICE_APPENDIX.getCode(), String.valueOf(deviceId), null));
        }
        return page;
    }

    @Override
    public List<DeviceEntity> getByDeviceCodes(String deviceCodes) {
        if (StringUtils.isBlank(deviceCodes)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<DeviceEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(DeviceEntity::getDeviceCode, Arrays.asList(deviceCodes.split(Constant.SEP)));
        return list(wrapper);
    }

    @Override
    public Boolean updateMaintenanceState(String deviceCode, Boolean maintenanceState) {
        return lambdaUpdate()
                .eq(DeviceEntity::getDeviceCode, deviceCode)
                .set(DeviceEntity::getMaintenanceState, maintenanceState).update();
    }

    @Override
    public List<DeviceTimePeriodEnergyConsumptionEntity> listDeviceTimePeriodEnergyConsumption(List<Integer> deviceIds, Date start, Date end, String targetParams) {
        if (CollectionUtils.isEmpty(deviceIds)) {
            return new ArrayList<>();
        }
        return deviceTimePeriodEnergyConsumptionService.lambdaQuery()
                .in(DeviceTimePeriodEnergyConsumptionEntity::getDeviceId, deviceIds)
                .ge(DeviceTimePeriodEnergyConsumptionEntity::getRecordDate, start)
                .le(DeviceTimePeriodEnergyConsumptionEntity::getRecordDate, end)
                .list();
    }

    @Override
    public List<MetricsDeviceOeeEntity> getDeviceOeeList(List<Integer> deviceIds, String targetParams) {
        if (StringUtils.isBlank(targetParams)) {
            throw new ResponseException(RespCodeEnum.TARGET_PARAMS_IS_BLANK);
        }
        Date now = new Date();
        String selectColumns = null;
        try {
            selectColumns = ColumnUtil.getTableFieldByField(targetParams, MetricsDeviceOeeEntity.class);
        } catch (NoSuchFieldException e) {
            log.error("指标参数字段转换失败", e);
            throw new ResponseException(RespCodeEnum.TARGET_PARAMS_ERROR);
        }
        return deviceOeeService.getListByDate(deviceIds, DateUtil.getDayBegin(now), selectColumns);
    }

    @Override
    public DeviceStateTimeVO getTodayDeviceStateTime(Integer deviceId, String targetParams) {
        if (StringUtils.isBlank(targetParams)) {
            throw new ResponseException(RespCodeEnum.TARGET_PARAMS_IS_BLANK);
        }
        DeviceEntity deviceEntity = this.lambdaQuery().eq(DeviceEntity::getDeviceId, deviceId).one();
        if (deviceEntity == null) {
            return DeviceStateTimeVO.builder().stopTime(0).runTime(0).pauseTime(0).maintainTime(0).build();
        }
        Date now = new Date();
        Date beginTime = dictService.getDayOutputBeginTime(now);
        int stopTime = 0, runTime = 0, pauseTime = 0, maintainTime = 0;
        if (targetParams.contains(Constant.PARAM_STOP_TIME)) {
            stopTime = getStopTime(beginTime, now, deviceId, deviceEntity.getTypeCode());
        }
        if (targetParams.contains(Constant.PARAM_RUN_TIME)) {
            runTime = getRunTime(beginTime, now, deviceId);
        }
        if (targetParams.contains(Constant.PARAM_PAUSE_TIME)) {
            pauseTime = getPauseTime(beginTime, now, deviceId);
        }
        if (targetParams.contains(Constant.PARAM_MAINTAIN_TIME)) {
            maintainTime = getMaintainTime(beginTime, now, deviceId);
        }
        return DeviceStateTimeVO.builder().stopTime(stopTime).runTime(runTime).pauseTime(pauseTime).maintainTime(maintainTime).build();
    }

    /**
     * 获取运行时长
     */
    private int getRunTime(Date start, Date end, Integer deviceId) {
        //获取查询的时间范围
        Date beginTime = start;
        Date nextDay = DateUtil.addDate(dictService.getDayOutputBeginTime(start), 1);
        int runTime = 0;
        //如果次日首班时间较早于end,分次查询，直到晚于end
        while (nextDay.before(end)) {
            RecordDeviceRunEntity startTime = recordDeviceRunService.getByDate(deviceId, beginTime, nextDay);
            if (startTime != null && startTime.getRunning() != null) {
                runTime += startTime.getRunning();
            }
            beginTime = nextDay;
            nextDay = DateUtil.addDate(nextDay, 1);
        }
        //查询截至end的时间
        RecordDeviceRunEntity endTime = recordDeviceRunService.getByDate(deviceId, beginTime, end);
        if (endTime != null && endTime.getRunning() != null) {
            runTime += endTime.getRunning();
        }
        return runTime;
    }

    /**
     * 获取待机时长
     */
    private int getPauseTime(Date start, Date end, Integer deviceId) {
        //获取查询的时间范围
        Date beginTime = start;
        Date nextDay = DateUtil.addDate(dictService.getDayOutputBeginTime(start), 1);
        int pauseTime = 0;
        //如果次日首班时间较早于end,分次查询，直到晚于end
        while (nextDay.before(end)) {
            RecordDevicePauseEntity startTime = recordDevicePauseService.getByDate(deviceId, beginTime, nextDay);
            if (startTime != null && startTime.getPause() != null) {
                pauseTime += startTime.getPause();
            }
            beginTime = nextDay;
            nextDay = DateUtil.addDate(nextDay, 1);
        }
        //查询截至end的时间
        RecordDevicePauseEntity endTime = recordDevicePauseService.getByDate(deviceId, beginTime, end);
        if (endTime != null && endTime.getPause() != null) {
            pauseTime += endTime.getPause();
        }
        return pauseTime;
    }

    /**
     * 获取维修时长
     */
    private int getMaintainTime(Date start, Date end, Integer deviceId) {
        //获取查询的时间范围
        Date beginTime = start;
        Date nextDay = DateUtil.addDate(dictService.getDayOutputBeginTime(start), 1);
        int maintainTime = 0;
        //如果次日首班时间较早于end,分次查询，直到晚于end
        while (nextDay.before(end)) {
            RecordDeviceMaintainEntity startTime = recordDeviceMaintainService.getByDate(deviceId, beginTime, nextDay);
            if (startTime != null && startTime.getMaintain() != null) {
                maintainTime += startTime.getMaintain();
            }
            beginTime = nextDay;
            nextDay = DateUtil.addDate(nextDay, 1);
        }
        //查询截至end的时间
        RecordDeviceMaintainEntity endTime = recordDeviceMaintainService.getByDate(deviceId, beginTime, end);
        if (endTime != null && endTime.getMaintain() != null) {
            maintainTime += endTime.getMaintain();
        }
        return maintainTime;
    }

    @Override
    public DeviceStateTimeVO getDeviceStateTimeList(Integer deviceId, String targetParams, Date startDate, Date endDate) {
        if (StringUtils.isBlank(targetParams)) {
            throw new ResponseException(RespCodeEnum.TARGET_PARAMS_IS_BLANK);
        }
        //表存储 起始时间-截止时间是截止时间状态时长的累计
        //返回数据 起始时间-截止时间是起始时间状态时长的累计
        Date end = DateUtil.addMin(endDate, 1);
        /*List<RecordDeviceStateEntity> list = recordDeviceStateService.lambdaQuery()
                .eq(RecordDeviceStateEntity::getDeviceId, deviceId)
                .between(RecordDeviceStateEntity::getTime, startDate, end)
                .orderByAsc(RecordDeviceStateEntity::getTime).list();*/

        List<RecordDeviceStateEntity> list = getRecordDeviceStateEntities(deviceId, startDate, end);

        if (CollectionUtils.isEmpty(list) || list.size() == 1) {
            return null;
        }
        //将截止时间状态变更为起始时间状态 -> 至截止时间 变为 起始时间起
        for (int i = 0; i < list.size() - 1; i++) {
            list.get(i).setState(list.get(i + 1).getState());
            list.get(i).setMaintenanceState(list.get(i + 1).getMaintenanceState());
        }
        //移除最后一条辅助记录
        list.remove(list.size() - 1);
        List<DeviceStateTimeDetailsVO> stateList = new ArrayList<>();
        List<DeviceStateTimeDetailsVO> maintenanceStateList = new ArrayList<>();
        RecordDeviceStateEntity firstEntity = list.get(0);
        //添加第一条记录
        if (targetParams.contains(Constant.PARAM_STATE)) {
            stateList.add(DeviceStateTimeDetailsVO.builder().time(firstEntity.getTime()).state(firstEntity.getState())
                    .stateName(DevicesStateEnum.getNameByCode(firstEntity.getState())).build());
        }
        if (targetParams.contains(Constant.PARAM_MAINTENANCE_STATE)) {
            maintenanceStateList.add(DeviceStateTimeDetailsVO.builder().time(firstEntity.getTime())
                    .maintenanceState(firstEntity.getMaintenanceState()).build());
        }
        for (int i = 1; i < list.size(); i++) {
            RecordDeviceStateEntity entity = list.get(i);
            //状态变更，添加一条记录
            if (targetParams.contains(Constant.PARAM_STATE)) {
                Integer lastSate = stateList.get(stateList.size() - 1).getState();
                if (!lastSate.equals(entity.getState())) {
                    stateList.add(DeviceStateTimeDetailsVO.builder().time(entity.getTime()).state(entity.getState())
                            .stateName(DevicesStateEnum.getNameByCode(entity.getState())).build());
                }
            }
            if (targetParams.contains(Constant.PARAM_MAINTENANCE_STATE)) {
                Boolean lastMaintenanceState = maintenanceStateList.get(maintenanceStateList.size() - 1).getMaintenanceState();
                if (!lastMaintenanceState.equals(entity.getMaintenanceState())) {
                    maintenanceStateList.add(DeviceStateTimeDetailsVO.builder().time(entity.getTime())
                            .maintenanceState(entity.getMaintenanceState()).build());
                }
            }

        }
        return DeviceStateTimeVO.builder().stateList(stateList).maintenanceStateList(maintenanceStateList).build();
    }

    @Override
    public DeviceOperationOrderDTO getOperationOrderList(String deviceCode, String state) {
        // 获取生产设备对应的产线，产线对应的作业工单信息（生效、投产、挂起、今日完成，按照计划开始时间的先后顺序）
        LambdaQueryWrapper<DeviceEntity> deviceWrapper = new LambdaQueryWrapper<>();
        deviceWrapper.eq(DeviceEntity::getDeviceCode, deviceCode);
        DeviceEntity deviceEntity = this.getOne(deviceWrapper);
        if (deviceEntity == null) {
            return null;
        }
        // 获取产线模型Id
        ProductionLineEntity productionLineEntity = productionLineService.getById(deviceEntity.getProductionLineId());
        if (productionLineEntity == null) {
            throw new ResponseException("产线不存在，请检查基础数据");
        }
        ResponseData responseData = assignmentInterface.selectListByLineId(deviceEntity.getProductionLineId(), state);
        if (responseData != null) {
            return DeviceOperationOrderDTO.builder()
                    .deviceCode(deviceCode)
                    .deviceName(deviceEntity.getDeviceName())
                    .lineId(deviceEntity.getProductionLineId())
                    .lineModelId(productionLineEntity.getModelId())
                    .operationOrderList(JSON.parseArray(JSON.toJSONString(responseData.getData()), OperationOrderDTO.class))
                    .build();
        }
        return null;
    }

    @Override
    public List<DeviceTimePeriodEnergyConsumptionDTO> getGasConsumption(TimePeriodEnergyConsumptionDTO dto) {
        Date startDate = DateUtil.parse(dto.getStartDate(), DateUtil.DATE_FORMAT);
        Date endDate = DateUtil.parse(dto.getEndDate(), DateUtil.DATE_FORMAT);

        List<Integer> deviceIds = this.lambdaQuery()
                .select(DeviceEntity::getDeviceId)
                .eq(dto.getGridId() != null, DeviceEntity::getGid, dto.getGridId())
                .in(!CollectionUtils.isEmpty(dto.getDeviceCodeList()), DeviceEntity::getDeviceCode, dto.getDeviceCodeList())
                .list()
                .stream()
                .map(DeviceEntity::getDeviceId)
                .collect(Collectors.toList());
        List<DeviceTimePeriodEnergyConsumptionDTO> consumptionList = new ArrayList<>();
        if (CollectionUtils.isEmpty(deviceIds)) {
            return consumptionList;
        }

        //获取数据
        List<DeviceGasConsumptionEntity> list = deviceGasConsumptionService.lambdaQuery()
                .in(DeviceGasConsumptionEntity::getDeviceId, deviceIds)
                .ge(DeviceGasConsumptionEntity::getRecordDate, startDate)
                .le(DeviceGasConsumptionEntity::getRecordDate, endDate)
                .list();
        // 按每天统计
        Map<Date, Double> recordDateMap = list.stream().collect(
                Collectors.groupingBy(DeviceGasConsumptionEntity::getRecordDate, Collectors.summingDouble(DeviceGasConsumptionEntity::getConsumption)));

        while (endDate.compareTo(startDate) >= 0) {
            double consumption = recordDateMap.getOrDefault(startDate, 0.0);
            DeviceTimePeriodEnergyConsumptionDTO build = DeviceTimePeriodEnergyConsumptionDTO.builder().consumption(consumption).recordDate(startDate).build();
            consumptionList.add(build);
            startDate = DateUtil.addDate(startDate, 1);
        }

        return consumptionList;
    }

    @Override
    public BatchAndTargetValDTO getTargetDetailsList(Integer deviceId, String targetName, String startDate, String endDate, Integer cycle) {
        IndicatorEntityDTO nameDTO = indicatorService.getFieldNameAndTableName(targetName, cycle);
        List<IndicatorEntityDTO> records = indicatorService.getIndicatorEntityByCycle(deviceId, nameDTO.getTableName(), startDate, endDate, cycle, null);

        Double referenceValue = 0.00;
        Double referenceUpper = 0.00;
        Double referenceLower = 0.00;
        //获取指标配置
        LambdaQueryWrapper<TargetThresholdEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TargetThresholdEntity::getTargetName, targetName)
                .eq(TargetThresholdEntity::getModelType, ModelEnum.DEVICE.getType())
                .eq(TargetThresholdEntity::getInstanceId, deviceId);
        TargetThresholdEntity targetThresholdEntity = targetThresholdMapper.selectOne(wrapper);
        if (targetThresholdEntity != null) {
            referenceValue = targetThresholdEntity.getReferenceValue();
            referenceUpper = targetThresholdEntity.getUpperLimit();
            referenceLower = targetThresholdEntity.getLowerLimit();
        }

        TargetDictEntity targetDictEntity = getTargetDictEntity(targetName);
        //指标单位
        String unit = targetDictEntity == null ? null : targetDictEntity.getUnit();
        return BatchAndTargetValDTO.builder()
                .referenceValue(referenceValue)
                .referenceUpper(referenceUpper)
                .referenceLower(referenceLower)
                .targetList(records)
                .unit(unit)
                .build();
    }

    @Override
    public List<DeviceEntity> getListByLineModelId(Integer workCenterId, String deviceName) {
        List<ProductionLineEntity> lineEntities = productionLineService.listByWorkCenterId(workCenterId);

        if (CollectionUtils.isEmpty(lineEntities)) {
            return new ArrayList<>();
        }
        List<Integer> lineIds = lineEntities.stream().map(ProductionLineEntity::getProductionLineId).collect(Collectors.toList());
        LambdaQueryWrapper<DeviceEntity> deviceWrapper = new LambdaQueryWrapper<>();
        deviceWrapper.in(DeviceEntity::getProductionLineId, lineIds)
                .like(StringUtils.isNotBlank(deviceName), DeviceEntity::getDeviceName, deviceName)
                .orderByDesc(DeviceEntity::getDeviceCode);
        return this.list(deviceWrapper);
    }

    @Override
    public List<DeviceEntity> getListByWorkCenter(Integer workCenterId, String deviceName) {
        //通过班组查询绑定的所有工作中心，再通过工作中心查询所有关联设备资源
        List<WorkCenterDeviceRelevanceEntity> deviceRelevanceEntities = workCenterDeviceRelevanceService.lambdaQuery()
                .eq(WorkCenterDeviceRelevanceEntity::getWorkCenterId, workCenterId).list();
        List<Integer> deviceIds = deviceRelevanceEntities.stream().map(WorkCenterDeviceRelevanceEntity::getDeviceId).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(deviceIds)) {
            return new ArrayList<>();
        }

        return this.lambdaQuery().in(DeviceEntity::getDeviceId, deviceIds)
                .like(StringUtils.isNotBlank(deviceName), DeviceEntity::getDeviceName, deviceName)
                .list();
    }

    @Override
    public ResponseData getIotProductList(Integer functionType, String deviceCode, Integer modelId) {
        List<DeviceEntity> deviceEntities = this.lambdaQuery()
                .eq(StringUtils.isNotBlank(deviceCode), DeviceEntity::getDeviceCode, deviceCode)
                .in(modelId != null, DeviceEntity::getModelId, modelId)
                .list();

        if (CollectionUtils.isEmpty(deviceEntities)) {
            return ResponseData.success();
        }
        List<Integer> deviceIds = deviceEntities.stream().map(DeviceEntity::getDeviceId).collect(Collectors.toList());
        String euis = deviceSensorService.lambdaQuery().in(DeviceSensorEntity::getDeviceId, deviceIds).list()
                .stream().map(DeviceSensorEntity::getEui).collect(Collectors.joining(Constant.SEP));
        if (StringUtils.isBlank(euis)) {
            return ResponseData.success();
        }
        return getProductKeyByEuis(functionType, euis);
    }

    @Override
    public ResponseData getProductKeyByEuis(Integer functionType, String euis) {
        String suffixUrl = "/device/product/getFunction?functionType=" + functionType + "&devName=" + euis;
        MultiValueMap<String, Object> requestEntity = new LinkedMultiValueMap<>();
        ResponseData responseData = iotService.requestIotService(suffixUrl, HttpMethod.GET, requestEntity);
        if (ResponseData.SUCCESS_CODE.equals(responseData.getCode())) {
            Object result = ((Map) responseData.getData()).get("tsl");
            responseData.setData(result);
        }
        return responseData;
    }

    /**
     * 电子料架点灯功能
     *
     * @param electronicRackDTO
     * @return
     */
    @Override
    public Boolean sendCommand(ElectronicRackDTO electronicRackDTO) {
        //校验当前电子料架是否存在
        SensorEntity one = sensorService.lambdaQuery()
                .eq(SensorEntity::getEui, electronicRackDTO.getDevId()).one();
        if (one == null) {
            throw new ResponseException(RespCodeEnum.ELECTRONIC_RACK_NOT_EXIT);
        }
        if (StringUtils.isBlank(electronicRackDTO.getStorage())) {
            throw new ResponseException(RespCodeEnum.ELECTRONIC_RACK_NOT_EXIT);
        }
        //校验传递的指示灯信息
        boolean contains = Constant.ELECTRONIC_RACK_COLOR.contains(electronicRackDTO.getColor());
        if (!contains) {
            throw new ResponseException(RespCodeEnum.ELECTRONIC_RACK_COLOR_NOT_EXIT);
        }
        SensorIotSetInfo sensorIotSetInfo = SensorIotSetInfo.builder()
                .eui(electronicRackDTO.getDevId())
                .productId(Constant.ELECTRONIC_RACK)
                .setData(JSONObject.toJSONString(electronicRackDTO))
                .build();
        String code = commandHandler.commandMessage(sensorIotSetInfo).getCode();
        return ResponseData.SUCCESS_CODE.equals(code);
    }

    @Override
    public void openBindSensor(Integer deviceId, String euis) {
        // 新增
        if (StringUtils.isNotBlank(euis)) {
            return;
        }

        String[] euiArr = euis.split(",");
        for (String eui : euiArr) {
            DeviceSensorEntity one = deviceSensorService.lambdaQuery()
                    .eq(DeviceSensorEntity::getEui, eui)
                    .eq(DeviceSensorEntity::getDeviceId, deviceId).one();
            if (one != null) {
                continue;
            }
            deviceSensorService.save(
                    DeviceSensorEntity.builder()
                            .eui(eui)
                            .deviceId(deviceId)
                            .createDate(new Date())
                            .build()
            );
        }

        dealAfterBindSensor(deviceId);
    }

    @Override
    public void openUnBindSensor(Integer deviceId, String euis) {
        // 新增
        if (StringUtils.isNotBlank(euis)) {
            return;
        }

        String[] euiArr = euis.split(",");
        for (String eui : euiArr) {
            DeviceSensorEntity one = deviceSensorService.lambdaQuery()
                    .eq(DeviceSensorEntity::getEui, eui)
                    .eq(DeviceSensorEntity::getDeviceId, deviceId).one();
            if (one != null) {
                deviceSensorService.removeById(one.getId());
            }
        }

        dealAfterBindSensor(deviceId);
    }

    @Override
    public List<DeviceGroupTypeDTO> listGroupByType() {
        List<DeviceEntity> list = this.list();
        for (DeviceEntity deviceEntity : list) {
            LambdaQueryWrapper<ModelEntity> modelWrapper = new LambdaQueryWrapper<>();
            modelWrapper.eq(ModelEntity::getId, deviceEntity.getModelId());
            ModelEntity modelEntity = modelMapper.selectOne(modelWrapper);
            deviceEntity.setModelName(modelEntity != null ? modelEntity.getName() : null);

        }
        Map<String, List<DeviceEntity>> collect = list.stream().collect(Collectors.groupingBy(DeviceEntity::getModelName));
        List<DeviceGroupTypeDTO> result = new ArrayList<>();
        for (Map.Entry<String, List<DeviceEntity>> entry : collect.entrySet()) {
            result.add(DeviceGroupTypeDTO.builder().deviceType(entry.getKey()).deviceEntities(entry.getValue()).build());
        }
        for (DeviceGroupTypeDTO deviceGroupTypeDTO : result) {
            List<DeviceEntity> deviceEntities = deviceGroupTypeDTO.getDeviceEntities();
            if (!CollectionUtils.isEmpty(deviceEntities)) {
                deviceGroupTypeDTO.setModelId(deviceGroupTypeDTO.getDeviceEntities().get(0).getModelId());
            }
        }
        return result;
    }

    @Override
    public Page<DeviceEntity> listForPopover(int size, int current, String deviceModelId, String deviceCode, String typeCode, String deviceName) {

        LambdaQueryWrapper<DeviceEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.isNotBlank(deviceName), DeviceEntity::getDeviceName, deviceName);
        queryWrapper.like(StringUtils.isNotBlank(deviceCode), DeviceEntity::getDeviceCode, deviceCode);
        if (StringUtils.isNotBlank(deviceModelId)) {
            List<String> deviceModelIds = Arrays.stream(deviceModelId.split(Constant.SEP)).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(deviceModelIds)) {
                queryWrapper.in(DeviceEntity::getModelId, deviceModelIds);
            }
        }
        if (StringUtils.isNotBlank(typeCode)) {
            List<String> typeCodes = Arrays.stream(typeCode.split(Constant.SEP)).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(typeCodes)) {
                queryWrapper.in(DeviceEntity::getTypeCode, typeCodes);
            }
        }
        queryWrapper.orderByDesc(DeviceEntity::getDeviceId);
        Page<DeviceEntity> page = deviceMapper.selectPage(new Page<>(current, size), queryWrapper);
        for (DeviceEntity deviceEntity : page.getRecords()) {
            // 获取模型名称
            LambdaQueryWrapper<ModelEntity> modelWrapper = new LambdaQueryWrapper<>();
            modelWrapper.eq(ModelEntity::getId, deviceEntity.getModelId());
            ModelEntity modelEntity = modelMapper.selectOne(modelWrapper);
            deviceEntity.setModelName(modelEntity != null ? modelEntity.getName() : null);
        }
        return page;

    }

    @Override
    public ResponseData getDeviceWorkState(Integer deviceId) {
        Date startTime = DateUtil.initDateByDay();
        Date endTime = DateUtil.addDate(startTime, 1);
        /*List<RecordDeviceStateEntity> recordDeviceStateEntities = recordDeviceStateService.lambdaQuery()
                .eq(RecordDeviceStateEntity::getDeviceId, deviceId)
                .between(RecordDeviceStateEntity::getTime, startTime, endTime)
                .orderByAsc(RecordDeviceStateEntity::getTime)
                .list();*/

        List<RecordDeviceStateEntity> recordDeviceStateEntities = getRecordDeviceStateEntities(deviceId, startTime, endTime);

        recordDeviceStateEntities = recordDeviceStateEntities.stream().peek(recordDeviceStateEntity -> {
            if (recordDeviceStateEntity.getState() == DevicesStateEnum.FAULT.getCode()) {
                recordDeviceStateEntity.setState(DevicesStateEnum.STOP.getCode());
            }
        }).collect(Collectors.toList());
        DeviceEntity deviceEntity = this.getById(deviceId);
        //故障状态归于停机一类故将所有的故障状态变更为停机
        List<DeviceWorkStateDTO> workStateDTOS = new ArrayList<>(64);
        for (int i = 0; i < recordDeviceStateEntities.size(); i++) {
            RecordDeviceStateEntity recordDeviceStateEntity = recordDeviceStateEntities.get(i);
            Integer state = recordDeviceStateEntity.getState();
            Date time = recordDeviceStateEntity.getTime();
            //第一个直接插入
            if (i == 0) {
                DeviceWorkStateDTO build = DeviceWorkStateDTO.builder()
                        .deviceName(deviceEntity.getDeviceName())
                        .deviceId(deviceId)
                        .stateName(DevicesStateEnum.getNameByCode(state))
                        .state(state)
                        .stateTime(time)
                        .build();
                workStateDTOS.add(build);
            } else {
                //判断是否和上一个节点状态是否一致,不一致记录
                RecordDeviceStateEntity recordDeviceState = recordDeviceStateEntities.get(i - 1);
                Integer state1 = recordDeviceState.getState();
                if (!state.equals(state1) && i != recordDeviceStateEntities.size()) {
                    Date time1 = recordDeviceState.getTime();
                    DeviceWorkStateDTO deviceWorkStateDTO = workStateDTOS.get(workStateDTOS.size() - 1);
                    deviceWorkStateDTO.setEndTime(time1);
                    deviceWorkStateDTO.setWorkTime((time1.getTime() - deviceWorkStateDTO.getStateTime().getTime()) / 60000);
                    DeviceWorkStateDTO build = DeviceWorkStateDTO.builder()
                            .deviceName(deviceEntity.getDeviceName())
                            .deviceId(deviceId)
                            .stateName(DevicesStateEnum.getNameByCode(state))
                            .state(state)
                            .stateTime(time1)
                            .build();
                    workStateDTOS.add(build);
                }
                //如果是最后一个数据则单独处理
                if (i == recordDeviceStateEntities.size() - 1) {
                    Date time1 = recordDeviceState.getTime();
                    DeviceWorkStateDTO deviceWorkStateDTO = workStateDTOS.get(workStateDTOS.size() - 1);
                    deviceWorkStateDTO.setEndTime(time1);
                    deviceWorkStateDTO.setWorkTime((time1.getTime() - deviceWorkStateDTO.getStateTime().getTime()) / 60000);
                    if (!state.equals(state1)) {
                        DeviceWorkStateDTO build = DeviceWorkStateDTO.builder()
                                .deviceName(deviceEntity.getDeviceName())
                                .deviceId(deviceId)
                                .stateName(DevicesStateEnum.getNameByCode(state))
                                .state(state)
                                .workTime(Constants.ONE)
                                .stateTime(time1)
                                .build();
                        workStateDTOS.add(build);
                    }
                }
            }
        }
        return ResponseData.success(workStateDTOS);
    }

    private List<RecordDeviceStateEntity> getRecordDeviceStateEntities(Integer deviceId, Date startTime, Date endTime) {
        Query query = new Query();
        query.addCriteria(Criteria.where(DEVICE_ID).is(deviceId));
        query.addCriteria(Criteria.where(TIME).gte(startTime).andOperator(Criteria.where(TIME).lte(endTime)));
        query.with(Sort.by(Sort.Order.asc(TIME)));
        query.fields().exclude("_id");
        List<Map> maps = mongoTemplate.find(query, Map.class, ColumnUtil.getTableName(new RecordDeviceStateEntity()));
        return JSON.parseArray(JSON.toJSONString(maps), RecordDeviceStateEntity.class);
    }

    @Override
    public PreviewTemplateEntity analysisImport(MultipartFile file) {
        // 判断导入的文件
        if (!ExcelUtil.isExcelFile(file.getOriginalFilename())) {
            throw new ResponseException(RespCodeEnum.PARSING_DATA_IS_EMPTY);
        }
        List<DeviceImportExcelDTO> deviceImportExcelDTOList = EasyExcelUtil.analysisExcel(file, DeviceImportExcelDTO.class);
        if (CollectionUtils.isEmpty(deviceImportExcelDTOList)) {
            return PreviewTemplateEntity.builder().build();
        }

        // 不导入提示行数据
        deviceImportExcelDTOList.remove(deviceImportExcelDTOList.get(0));
        deviceImportExcelDTOList.remove(deviceImportExcelDTOList.get(1));

        List<DeviceImportExcelDTO> deviceImportExcelDTOs = importCheck(deviceImportExcelDTOList);

        return PreviewTemplateEntity.builder()
                .wrongResult(deviceImportExcelDTOList)
                .importResult(deviceImportExcelDTOs)
                .build();
    }

    @Override
    public Boolean importList(List<DeviceImportExcelDTO> importList, String username) {
        Date time = new Date();
        List<DeviceEntity> deviceEntities = new ArrayList<>();
        List<DeviceSensorEntity> deviceSensorEntities = new ArrayList<>();
        for (DeviceImportExcelDTO excelDTO : importList) {
            SysUserEntity sysUserEntity = sysUserService.lambdaQuery().eq(SysUserEntity::getNickname, excelDTO.getMagNickname()).one();
            excelDTO.setMagName(Objects.nonNull(sysUserEntity) ? sysUserEntity.getUsername() : null);
            DeviceEntity deviceEntity = JSON.parseObject(JSON.toJSONString(excelDTO), DeviceEntity.class);
            deviceEntity.setCreateBy(username);
            deviceEntity.setCreateTime(Objects.nonNull(deviceEntity.getCreateTime()) ? deviceEntity.getCreateTime() : time);
            deviceEntities.add(deviceEntity);
        }
        // 导入设备
        boolean saveDevice = this.saveBatch(deviceEntities);
        for (DeviceEntity deviceEntity : deviceEntities) {
            String euiNames = deviceEntity.getEuiNames();
            List<SensorEntity> byEuiNames = sensorService.getByEuiNames(euiNames);
            for (SensorEntity sensorEntity : byEuiNames) {
                DeviceSensorEntity build = DeviceSensorEntity.builder()
                        .deviceId(deviceEntity.getDeviceId())
                        .eui(sensorEntity.getEui())
                        .createDate(deviceEntity.getCreateTime())
                        .build();
                deviceSensorEntities.add(build);
            }
        }
        // 导入设备采集器
        boolean saveDeviceSenor = deviceSensorService.saveBatch(deviceSensorEntities);
        return saveDevice && saveDeviceSenor;
    }

    @Override
    public byte[] downloadImportTemplate() throws IOException {
        Workbook templateWorkbook = null;
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        InputStream inputStream = null;
        try {
            //默认模板
            ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
            org.springframework.core.io.Resource resource = resolver.getResource("classpath:template/DeviceTemplate.xlsx");
            inputStream = resource.getInputStream();
            templateWorkbook = WorkbookFactory.create(inputStream);
            templateWorkbook.write(byteArrayOutputStream);
            return byteArrayOutputStream.toByteArray();
        } catch (IOException e) {
            throw new IOException(e);
        } finally {
            ExcelUtil.closeWorkBook(templateWorkbook);
            IOUtils.closeQuietly(byteArrayOutputStream);
            IOUtils.closeQuietly(inputStream);
        }
    }

    @Override
    public Long exportTask(DeviceSelectDTO dto, String username) {
        DataExportParam<DeviceSelectDTO> dataExportParam = new DataExportParam<>();
        dataExportParam.setExportFileName(BusinessCodeEnum.DEVICE_EXPORT_REPORT.getCodeName());
        dataExportParam.setLimit(10000);
        dataExportParam.setBusinessCode(BusinessCodeEnum.DEVICE_EXPORT_REPORT.name());
        dataExportParam.setCreateUserCode(username);
        dto.setTemplateSheetName("数据源");
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(DeviceSelectDTO.class.getName(), dto);
        parameters.put("templateId", dto.getTemplateId());
        dataExportParam.setParameters(parameters);
        return excelService.doExport(dataExportParam, DeviceExportHandler.class);
    }

    @Override
    public IPage<ExcelTask> taskPage(Integer current, Integer size) {
        ExcelTask excelTask = new ExcelTask();
        excelTask.setBusinessCode(BusinessCodeEnum.DEVICE_EXPORT_REPORT.name());
        return excelService.listPage(excelTask, current, size);
    }

    @Override
    public ExcelTask taskById(Long taskId) {
        ExcelTask excelTask = new ExcelTask();
        excelTask.setId(taskId);
        excelTask.setBusinessCode(BusinessCodeEnum.DEVICE_EXPORT_REPORT.name());
        IPage<ExcelTask> excelTaskIPage = excelService.listPage(excelTask, 1, 1);
        List<ExcelTask> records = excelTaskIPage.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            throw new ResponseException("没有查询到对应的导出任务详情");
        }
        return records.get(0);
    }

    @Override
    public DeviceTargetMonitorDTO getParameterListByDeviceId(Integer deviceId) {
        DeviceTargetMonitorDTO dto = DeviceTargetMonitorDTO.builder().build();
        DeviceEntity entity = this.getById(deviceId);
        if (entity == null) {
            return dto;
        }
        dto.setDeviceEntity(entity);
        Page<TargetModelEntity> list = targetModelService.getList(entity.getModelId(), entity.getDeviceId(), null, null);
        List<TargetModelEntity> records = list.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return dto;
        }
        String collect = records.stream().map(TargetModelEntity::getTargetName).collect(Collectors.joining(Constants.SEP));
        List<ScreenTargetModelVo> targetModelListBySelect = targetModelService.getTargetModelListBySelect(entity, collect);
        dto.setScreenTargetModelVos(targetModelListBySelect);
        return dto;
    }

    @Override
    public Page<DeviceEntity> getRecordDeviceDayRunList(DeviceSelectDTO dto) {
        // 获取所有的设备信息
        Page<DeviceEntity> page = this.page(new Page<>(dto.getCurrentPage(), dto.getPageSize()));
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return new Page<>();
        }
        for (DeviceEntity record : page.getRecords()) {
            List<DeviceWorkStateDTO> deviceWorkStateDTOS = JacksonUtil.getResponseArray(getDeviceWorkState(record.getDeviceId()), DeviceWorkStateDTO.class);
            record.setDeviceWorkStateDTOS(deviceWorkStateDTOS);
        }
        return page;
//        Page<MetricsDeviceStateDailyEntity> page = new Page<>();
//        Date now = new Date();
//        String dateStr = DateUtil.dateStr(now);
//
//        LambdaQueryWrapper<MetricsDeviceStateDailyEntity> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.eq(MetricsDeviceStateDailyEntity::getRecordDate, dateStr);
//        List<MetricsDeviceStateDailyEntity> list = metricsDeviceStateDailyService.list(queryWrapper);
//        List<Integer> deviceIds = list.stream().map(MetricsDeviceStateDailyEntity::getDeviceId).collect(Collectors.toList());
//
//        Map<Integer, String> deviceMap = this.listByIds(deviceIds).stream()
//                .collect(Collectors.toMap(DeviceEntity::getDeviceId, DeviceEntity::getDeviceName));
//        for (MetricsDeviceStateDailyEntity entity : list) {
//            entity.setDeviceName(deviceMap.get(entity.getDeviceId()));
//        }
//        Date dayOutputBeginTime = dictService.getDayOutputBeginTime(now);
//        List<Integer> allDeviceId = this.list().stream().map(DeviceEntity::getDeviceId).collect(Collectors.toList());
//        allDeviceId.removeAll(deviceIds);
//        List<DeviceEntity> deviceEntities = this.listByIds(allDeviceId);
//        Long time = DateUtil.getTime(dayOutputBeginTime, now);
//        Integer stopMin = MathUtil.divideDouble(time, 60000, 0).intValue();
//        for (DeviceEntity deviceEntity : deviceEntities) {
//            MetricsDeviceStateDailyEntity build = MetricsDeviceStateDailyEntity.builder()
//                    .running(0)
//                    .deviceName(deviceEntity.getDeviceName())
//                    .pause(0)
//                    .deviceId(deviceEntity.getDeviceId())
//                    .stop(stopMin)
//                    .recordDate(now)
//                    .time(now)
//                    .build();
//            list.add(build);
//        }
//        if (dto.getCurrentPage() == null || dto.getPageSize() == null) {
//            dto.setCurrentPage(1);
//            dto.setPageSize(Integer.MAX_VALUE);
//        }
//        page.setCurrent(dto.getCurrentPage()).setSize(dto.getPageSize());
//        List<MetricsDeviceStateDailyEntity> entityList = PageUtil.startPage(list, dto.getCurrentPage(), dto.getPageSize());
//        page.setRecords(entityList).setTotal(list.size());
//        return page;
    }

    @Override
    public DeviceStateOverviewDTO getDeviceStatusOverview() {
        LambdaQueryWrapper<DeviceEntity> stopQW = new LambdaQueryWrapper<>();
        stopQW.in(DeviceEntity::getState, DevicesStateEnum.STOP.getCode(), DevicesStateEnum.FAULT.getCode());
        LambdaQueryWrapper<DeviceEntity> pauseQW = new LambdaQueryWrapper<>();
        pauseQW.eq(DeviceEntity::getState, DevicesStateEnum.SUSPEND.getCode());
        LambdaQueryWrapper<DeviceEntity> runQW = new LambdaQueryWrapper<>();
        runQW.eq(DeviceEntity::getState, DevicesStateEnum.RUNNING.getCode());
        return DeviceStateOverviewDTO.builder()
                .total((int) this.count())
                .stopCount((int) this.count(stopQW))
                .pauseCount((int) this.count(pauseQW))
                .runCount((int) this.count(runQW))
                .build();
    }

    /**
     * 导入校验
     *
     * @param deviceImportExcelDTOList
     * @return
     */
    private List<DeviceImportExcelDTO> importCheck(List<DeviceImportExcelDTO> deviceImportExcelDTOList) {
        List<DeviceImportExcelDTO> deviceImportExcelDTOs = deviceImportExcelDTOList.stream()
                .filter(i -> {
                    boolean b = true;
                    StringBuilder builder = new StringBuilder();
                    // 导入数据校验
                    if (StringUtils.isBlank(i.getDeviceName())) {
                        b = false;
                        builder.append("设备名称不能为空;");
                    }
                    if (StringUtils.isBlank(i.getDeviceCode())) {
                        b = false;
                        builder.append("设备编码不能为空;");
                    }
                    if (StringUtils.isBlank(i.getModelName())) {
                        b = false;
                        builder.append("设备类型不能为空;");
                    } else {
                        LambdaQueryWrapper<ModelEntity> modelWrapper = new LambdaQueryWrapper<>();
                        modelWrapper.eq(ModelEntity::getName, i.getModelName());
                        if (modelMapper.selectCount(modelWrapper) == 0) {
                            b = false;
                            builder.append("未找到改设备类型:").append(i.getModelName());
                        }
                    }
                    if (StringUtils.isBlank(i.getTypeName())) {
                        b = false;
                        builder.append("设备分类不能为空;");
                    } else {
                        List<String> types = Arrays.stream(DeviceTypeEnum.values()).map(DeviceTypeEnum::getType).collect(Collectors.toList());
                        if (!types.contains(i.getTypeName())) {
                            b = false;
                            builder.append("未找到改设备分类:").append(i.getTypeName());
                        }
                    }
                    if (StringUtils.isNotBlank(i.getMagNickname())) {
                        LambdaQueryWrapper<SysUserEntity> queryWrapper = new LambdaQueryWrapper<>();
                        queryWrapper.eq(SysUserEntity::getEnabled, EnabledEnum.ENABLE.getCode());
                        queryWrapper.eq(SysUserEntity::getNickname, i.getMagNickname());
                        if (sysUserService.count(queryWrapper) == 0) {
                            b = false;
                            builder.append("未找到该人员信息或该人员已被禁用:").append(i.getMagNickname());
                        }
                    }

                    if (StringUtils.isNotBlank(i.getFname())) {
                        LambdaQueryWrapper<FacilitiesEntity> queryWrapper = new LambdaQueryWrapper<>();
                        queryWrapper.eq(FacilitiesEntity::getFname, i.getFname());
                        if (facilitiesService.count(queryWrapper) == 0) {
                            b = false;
                            builder.append("未找到该工位信息:").append(i.getFname());
                        }
                    }
                    if (StringUtils.isNotBlank(i.getGname())) {
                        LambdaQueryWrapper<GridEntity> queryWrapper = new LambdaQueryWrapper<>();
                        queryWrapper.eq(GridEntity::getGname, i.getGname());
                        if (gridService.count(queryWrapper) == 0) {
                            b = false;
                            builder.append("未找到该车间信息:").append(i.getGname());
                        }
                    }
                    if (StringUtils.isNotBlank(i.getEuiNames())) {
                        LambdaQueryWrapper<SensorEntity> queryWrapper = new LambdaQueryWrapper<>();
                        queryWrapper.in(SensorEntity::getName, Arrays.asList(i.getEuiNames().split(Constant.SEP)));
                        if (sensorService.count(queryWrapper) == 0) {
                            b = false;
                            builder.append("未找到该采集设备信息:").append(i.getEuiNames());
                        }
                    }
                    i.setReason(builder.toString());
                    return b;
                }).collect(Collectors.toList());
        deviceImportExcelDTOList.removeAll(deviceImportExcelDTOs);
        return deviceImportExcelDTOs;
    }


    /**
     * 绑定采集器后设置缓存
     *
     * @param deviceId
     */
    private void dealAfterBindSensor(Integer deviceId) {
        deviceSensorService.resetDeviceSensorIntoRedis();
        targetModelService.resetCountTargetCache();
        DeviceEntity deviceEntity = deviceMapper.selectById(deviceId);
        deviceEntity.setBindingDate(new Date());
        this.updateById(deviceEntity);
        if (deviceEntity.getFid() != null) {
            FacCalEuiService facCalEuiService = SpringUtil.getBean(FacCalEuiService.class);
            facCalEuiService.isKeyFac(facilitiesService.getById(deviceEntity.getFid()));
        }
    }

    @Override
    public Map<String, String> getDeviceCodeNameMap(List<String> deviceCodes) {
        List<DeviceEntity> devices = CollUtil.isEmpty(deviceCodes) ? Collections.emptyList() : this.lambdaQuery().in(DeviceEntity::getDeviceCode, deviceCodes).list();
        return devices.stream().collect(Collectors.toMap(DeviceEntity::getDeviceCode, DeviceEntity::getDeviceName));
    }

    @Override
    public Map<Integer, DeviceEntity> getIdMap(List<Integer> ids) {
        List<DeviceEntity> devices = CollUtil.isEmpty(ids) ? Collections.emptyList() : this.listByIds(ids);
        return devices.stream().collect(Collectors.toMap(DeviceEntity::getDeviceId, Function.identity()));
    }

    @Override
    public ExcelLogVO importData(MultipartFile file) {
        InputStream inputStream = null;
        try {
            inputStream = file.getInputStream();
//            List<ExcelDeviceDTO> excels = EasyExcelUtil.read(file.getInputStream(), ExcelDeviceDTO.class, 0, 1);
            List<ExcelDeviceDTO> excels = EasyExcelUtil.dynamicColumnRead(file.getInputStream(), ExcelDeviceDTO.class, 0, 1);
            ExcelLogVO vo = ExcelLogVO.init();
            vo.setImportData(excels);
            // 转换成系统已有的 + 自有的校验
            List<DeviceImportDTO> totalImportRecords = excels.stream().map(e -> JacksonUtil.convertObject(e, DeviceImportDTO.class)).collect(Collectors.toList());
            // 数据校验
            dealDeviceImport(totalImportRecords);
            for (int i = 0; i < totalImportRecords.size(); i++) {
                DeviceImportDTO dto = totalImportRecords.get(i);
                if (StringUtils.isBlank(dto.getReason())) {
                    // 导入成的
                    vo.addSuccess();
                    excels.get(i).setImportResult("数据校验通过");
                } else {
                    excels.get(i).setImportResult(dto.getReason());
                    vo.appendErr("第" + (i + 1) + "个记录异常, msg:" + dto.getReason() + ";\n");
                    vo.addFail();
                }
            }
            return vo;
        } catch (IOException e) {
            throw new ResponseException(String.format("excel导入数据错误: %s", e.getMessage()));
        } finally {
            IOUtils.closeQuietly(inputStream);
        }
    }

    @Override
    public void uploadListExportTemplate(MultipartFile file, String username) throws IOException {
        EasyExcelUtil.isValidExcelFile(file);
        MultipartFile modifiedFile = new MockMultipartFile(
                file.getName(),
                file.getOriginalFilename(),
                file.getContentType(),
                EasyExcelUtil.clearAndConvertToBytes(file, "数据源"));
        modelUploadFileService.uploadReferencedFile(modifiedFile, ModelUploadFileEnum.DEVICE_EXPORT.getCode(), username);
    }

    @Override
    public Page<DeviceVO> getList2(DeviceQueryDTO dto) {
        String sql = SqlUtil.getSql(dto);
        Page<DeviceEntity> page = this.baseMapper.getList(sql, dto.getPage());
        showName(page.getRecords());
        return JacksonUtil.convertPage(page, DeviceVO.class);
    }
}
