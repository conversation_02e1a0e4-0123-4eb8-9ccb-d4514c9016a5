package com.yelink.dfs.service.impl.order;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yelink.dfs.common.UserAuthenService;
import com.yelink.dfs.constant.target.TargetConst;
import com.yelink.dfs.entity.device.DeviceEntity;
import com.yelink.dfs.entity.order.RecordWorkOrderResumeConsumptionEntity;
import com.yelink.dfs.entity.order.RecordWorkOrderResumeEntity;
import com.yelink.dfs.entity.order.WorkOrderDeviceRelevanceEntity;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.entity.order.dto.WorkOrderResumeSelectDTO;
import com.yelink.dfs.entity.order.vo.WorkOrderResumeVO;
import com.yelink.dfs.entity.user.SysUserEntity;
import com.yelink.dfs.entity.work.calendar.WorkCalendarEntity;
import com.yelink.dfs.mapper.order.RecordWorkOrderResumeMapper;
import com.yelink.dfs.service.common.DictService;
import com.yelink.dfs.service.device.DeviceService;
import com.yelink.dfs.service.energy.manage.DeviceCarbonConsumptionConstantService;
import com.yelink.dfs.service.energy.manage.DeviceEnergyConsumptionConstantService;
import com.yelink.dfs.service.energy.manage.DeviceGasConsumptionConstantService;
import com.yelink.dfs.service.energy.manage.DeviceWaterConsumptionConstantService;
import com.yelink.dfs.service.order.RecordWorkOrderResumeConsumptionService;
import com.yelink.dfs.service.order.RecordWorkOrderResumeService;
import com.yelink.dfs.service.order.WorkOrderBasicUnitRelationService;
import com.yelink.dfs.service.order.WorkOrderDeviceRelevanceService;
import com.yelink.dfs.service.order.WorkOrderService;
import com.yelink.dfs.service.user.SysUserService;
import com.yelink.dfs.service.work.calendar.WorkCalendarService;
import com.yelink.dfscommon.constant.ModelEnum;
import com.yelink.dfscommon.constant.dfs.order.WorkOrderStateEnum;
import com.yelink.dfscommon.constant.model.WorkCenterTypeEnum;
import com.yelink.dfscommon.utils.DateUtil;
import com.yelink.dfscommon.utils.JacksonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 工单履历
 * <AUTHOR>
 */
@Slf4j
@Service
public class RecordWorkOrderResumeServiceImpl extends ServiceImpl<RecordWorkOrderResumeMapper, RecordWorkOrderResumeEntity> implements RecordWorkOrderResumeService {

    private final List<Integer> STATES = Arrays.asList(WorkOrderStateEnum.INVESTMENT.getCode(), WorkOrderStateEnum.HANG_UP.getCode());

    @Resource
    private RedissonClient redissonClient;
    @Resource
    private WorkOrderService workOrderService;
    @Resource
    private SysUserService userService;
    @Resource
    private WorkCalendarService workCalendarService;
    @Resource
    protected UserAuthenService userAuthenService;
    @Resource
    private RecordWorkOrderResumeConsumptionService workOrderResumeConsumptionService;
    @Resource
    private WorkOrderDeviceRelevanceService workOrderDeviceRelevanceService;
    @Resource
    private DeviceService deviceService;
    @Resource
    private DeviceEnergyConsumptionConstantService energyConsumptionConstantService;
    @Resource
    private DeviceGasConsumptionConstantService gasConsumptionConstantService;
    @Resource
    private DeviceWaterConsumptionConstantService waterConsumptionConstantService;
    @Resource
    private DeviceCarbonConsumptionConstantService carbonConsumptionConstantService;
    @Resource
    private WorkOrderBasicUnitRelationService workOrderBasicUnitRelationService;

    @Override
    public List<RecordWorkOrderResumeEntity> batchQueryLast(List<String> workOrderNumbers) {
        return baseMapper.batchQueryLast(workOrderNumbers);
    }

    @Override
    public void deal(WorkOrderEntity workOrder, RecordWorkOrderResumeEntity last, String username) {
        RLock lock = redissonClient.getLock("WO_RESUME_LOCK_" + workOrder.getWorkOrderNumber());
        try {
            int waitTime = 1;
            int leaseTime = 30;
            if (lock.tryLock(waitTime, leaseTime, TimeUnit.SECONDS)) {
                dealDate(workOrder, last, username);
            }
        } catch (InterruptedException e) {
            log.error("处理工单履历的锁遇到问题. 工单号: {}, msg: {}", workOrder.getWorkOrderNumber(), e.getMessage());
        } finally {
            //解锁
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

    }

    private void dealDate(WorkOrderEntity workOrder, RecordWorkOrderResumeEntity last, String username) {
        Date now = new Date();
        // 开始的时间, 默认以现在为准
        Double finishCount = workOrder.getFinishCount();
        // 会导致分隔的条件
        // 1. 工单状态改变
        boolean stateChange = last != null && !Objects.equals(workOrder.getState(), last.getWorkOrderState());
        // 2. last == null
        // --->
        boolean split = stateChange || last == null;

        if(stateChange && last.getEndTime() == null) {
            last.setEndTime(now);
            // 其他值的处理
            last.setUpdateTime(now);
            last.setRecordTime(now);
            last.setEndFinishCount(finishCount);
            this.updateById(last);
            refreshWorkOrderConsumption(workOrder, last);
        }


        // 若没有旧的 || 分隔 -> 增加一条新的数据
        if(split) {
            RecordWorkOrderResumeEntity newOne = RecordWorkOrderResumeEntity.builder()
                    .recordTime(now)
                    .workOrderNumber(workOrder.getWorkOrderNumber())
                    .workOrderState(workOrder.getState())
                    .startTime(now)
                    .createTime(now)
                    .operator(username)
                    .startFinishCount(finishCount)
                    .endFinishCount(finishCount)
                    .build();
            if(workOrder.getLineId() != null) {
                WorkCalendarEntity calendar = workCalendarService.getCalendarByTime(workOrder.getLineId(), ModelEnum.LINE.getType(), now);
                Optional.ofNullable(calendar).ifPresent(e -> newOne.setCalendarId(e.getCalendarId()));
            }
            this.save(newOne);
            refreshWorkOrderConsumption(workOrder, newOne);
        }
    }

    @Override
    public void deal2(WorkOrderEntity workOrder, String username) {
        if(workOrder == null || workOrder.getWorkOrderNumber() == null) {
            return;
        }
        if(StringUtils.isEmpty(username)) {
            username = userAuthenService.getUsername();
        }
        List<RecordWorkOrderResumeEntity> resumes = batchQueryLast(Collections.singletonList(workOrder.getWorkOrderNumber()));
        RecordWorkOrderResumeEntity resume = CollUtil.isEmpty(resumes)? null : resumes.get(0);
        deal(workOrder, resume, username);
    }

    @Override
    public Page<WorkOrderResumeVO> getPage(WorkOrderResumeSelectDTO dto) {
        LambdaQueryWrapper<RecordWorkOrderResumeEntity> wrapper = Wrappers.lambdaQuery(RecordWorkOrderResumeEntity.class);
        // 查工单
        if(StringUtils.isNotEmpty(dto.getWorkOrderNumber())) {
            List<WorkOrderEntity> workOrders = workOrderService.lambdaQuery().like(WorkOrderEntity::getWorkOrderNumber, dto.getWorkOrderNumber()).list();
            if(CollUtil.isEmpty(workOrders)) {
                return new Page<>();
            }
            List<String> workOrderNumbers = workOrders.stream().map(WorkOrderEntity::getWorkOrderNumber).collect(Collectors.toList());
            wrapper.in(RecordWorkOrderResumeEntity::getWorkOrderNumber, workOrderNumbers);
        }
        if(StringUtils.isNotEmpty(dto.getOperatorName())) {
            List<SysUserEntity> users = userService.listKeyword(dto.getOperatorName());
            List<String> usernames = users.stream().map(SysUserEntity::getUsername).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(usernames)) {
                return new Page<>();
            }
            wrapper.in(RecordWorkOrderResumeEntity::getOperator, usernames);
        }
        wrapper.ge(StringUtils.isNotEmpty(dto.getStartTimeLower()), RecordWorkOrderResumeEntity::getStartTime, dto.getStartTimeLower());
        wrapper.lt(StringUtils.isNotEmpty(dto.getStartTimeUpper()), RecordWorkOrderResumeEntity::getStartTime, dto.getStartTimeUpper());
        wrapper.in(CollUtil.isNotEmpty(dto.getWorkOrderState()), RecordWorkOrderResumeEntity::getWorkOrderState, dto.getWorkOrderState());
        wrapper.ge(StringUtils.isNotEmpty(dto.getEndTimeLower()), RecordWorkOrderResumeEntity::getEndTime, dto.getEndTimeLower());
        wrapper.lt(StringUtils.isNotEmpty(dto.getEndTimeUpper()), RecordWorkOrderResumeEntity::getEndTime, dto.getEndTimeUpper());
        wrapper.orderByDesc(RecordWorkOrderResumeEntity::getId);
        Page<RecordWorkOrderResumeEntity> page = page(dto.sortPage(), wrapper);
        Page<WorkOrderResumeVO> result = JacksonUtil.convertPage(page, WorkOrderResumeVO.class);
        showName(result.getRecords());
        return result;
    }

    private void showName(List<WorkOrderResumeVO> records) {
        List<String> usernames = records.stream().map(WorkOrderResumeVO::getOperator).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<Integer> calendarIds = records.stream().map(WorkOrderResumeVO::getCalendarId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<String, String> userNameNickMap = userService.getUserNameNickMap(usernames);
        Map<Integer, WorkCalendarEntity> calendarIdMap = workCalendarService.calendarIdMap(calendarIds);
        for (WorkOrderResumeVO vo : records) {
            // 人名
            vo.setOperatorName(userNameNickMap.get(vo.getOperator()));
            // 日历
            WorkCalendarEntity calendar = calendarIdMap.get(vo.getCalendarId());
            Optional.ofNullable(calendar).ifPresent(e -> vo.setCalendarName(e.getName()));
        }
    }

    /**
     * 刷新工单能耗数据
     * @param workOrder 工单
     * @param resume 工单履历
     */
    private void refreshWorkOrderConsumption(WorkOrderEntity workOrder, RecordWorkOrderResumeEntity resume) {
        // 查工单关联的设备
        Set<Integer> deviceIdSet = new HashSet<>();
        Integer lineId = workOrder.getLineId();
        // 如果是产线的
        if(WorkCenterTypeEnum.LINE.getCode().equals(workOrder.getWorkCenterType())) {
            List<Integer> lineDeviceIds = deviceService.deviceByLineId(lineId).stream().map(DeviceEntity::getDeviceId).collect(Collectors.toList());
            deviceIdSet.addAll(lineDeviceIds);
            // 基本生产单元
        }else if(WorkCenterTypeEnum.DEVICE.getCode().equals(workOrder.getWorkCenterType())) {
            List<Integer> relateDeviceIds = workOrderBasicUnitRelationService.getDeviceIds(workOrder.getWorkOrderNumber(), null);
            deviceIdSet.addAll(relateDeviceIds);
        }
        // 关联资源
        List<WorkOrderDeviceRelevanceEntity> relevanceDevices = workOrderDeviceRelevanceService.lambdaQuery().eq(WorkOrderDeviceRelevanceEntity::getWorkOrderId, workOrder.getWorkOrderId()).list();
        List<Integer> relateDeviceIds = relevanceDevices.stream().map(WorkOrderDeviceRelevanceEntity::getDeviceId).collect(Collectors.toList());
        deviceIdSet.addAll(relateDeviceIds);
        // 最终的设备信息
        if(CollUtil.isEmpty(deviceIdSet)) {
            return;
        }
        List<DeviceEntity> devices = deviceService.listByIds(deviceIdSet);

        // 查该履历下的所有能耗信息，并按设备分组
        List<RecordWorkOrderResumeConsumptionEntity> allResumeConsumptions = workOrderResumeConsumptionService.lambdaQuery()
                .eq(RecordWorkOrderResumeConsumptionEntity::getResumeId, resume.getId())
                .list();
        Map<Integer, List<RecordWorkOrderResumeConsumptionEntity>> resumeGroupByDevice = allResumeConsumptions.stream().collect(Collectors.groupingBy(RecordWorkOrderResumeConsumptionEntity::getDeviceId));

        // 最终批量操作的结果
        List<RecordWorkOrderResumeConsumptionEntity> results = new ArrayList<>();
        // 每个设备操作
        for (DeviceEntity device : devices) {
            Integer deviceId = device.getDeviceId();
            // 用电/用气/用水/碳排放
            List<String> consumptionTargets = Arrays.asList(TargetConst.DAILY_ENERGY_CONSUMPTION, TargetConst.DAILY_GAS_CONSUMPTION, TargetConst.DAILY_WATER_CONSUMPTION, TargetConst.DAILY_CARBON_CONSUMPTION);

            // 过去之前的
            List<RecordWorkOrderResumeConsumptionEntity> resumeConsumptions = resumeGroupByDevice.getOrDefault(deviceId, Collections.emptyList());
            Map<String, RecordWorkOrderResumeConsumptionEntity> targetNameConsumptionMap = resumeConsumptions.stream().collect(Collectors.toMap(RecordWorkOrderResumeConsumptionEntity::getTargetName, Function.identity()));

            for (String targetName: consumptionTargets) {
                // 获取的是指标当天的[起始/截止] 值 (根据配置找到的是 零点或按首班时间)
                Double targetVal = getTargetVal(deviceId, targetName, resume.getStartTime(), resume.getEndTime());
                if(targetVal == null) {
                    continue;
                }
                // 不存在则新增一个
                RecordWorkOrderResumeConsumptionEntity resumeConsumption = targetNameConsumptionMap.get(targetName);
                if(resumeConsumption == null) {
                    resumeConsumption = RecordWorkOrderResumeConsumptionEntity.builder()
                            .resumeId(resume.getId())
                            .deviceId(deviceId)
                            .targetName(targetName)
                            .startVal(targetVal)
                            .build();
                }
                resumeConsumption.setEndVal(targetVal);
                results.add(resumeConsumption);
            }
        }
        workOrderResumeConsumptionService.saveOrUpdateBatch(results);
    }

    private Double getTargetVal(Integer deviceId, String targetName, Date startTime, Date endTime) {
        switch (targetName) {
            case TargetConst.DAILY_ENERGY_CONSUMPTION:
                return energyConsumptionConstantService.getNewVal(deviceId, startTime, endTime);
            case TargetConst.DAILY_GAS_CONSUMPTION:
                return gasConsumptionConstantService.getNewVal(deviceId, startTime, endTime);
            case TargetConst.DAILY_WATER_CONSUMPTION:
                return waterConsumptionConstantService.getNewVal(deviceId, startTime, endTime);
            case TargetConst.DAILY_CARBON_CONSUMPTION:
                return carbonConsumptionConstantService.getNewVal(deviceId, startTime, endTime);
            default:
                return null;
        }
    }

}
