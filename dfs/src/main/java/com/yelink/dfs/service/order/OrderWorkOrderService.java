package com.yelink.dfs.service.order;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yelink.dfs.entity.order.OrderWorkOrderEntity;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfscommon.entity.ams.ProductOrderEntity;
import com.yelink.dfscommon.entity.ams.SaleOrderEntity;
import com.yelink.dfscommon.entity.ams.dto.SaleOrderVO;

import java.util.List;
import java.util.Map;

/**
 * Service
 *
 * <AUTHOR>
 * @Date 2021/4/6 21:25
 */
public interface OrderWorkOrderService extends IService<OrderWorkOrderEntity> {
    /**
     * 查询这个订单关联的工单
     *
     * @param orderId   订单id
     * @param orderType 订单id对应订单类型
     * @return
     */
    List<OrderWorkOrderEntity> listOrderWorkOrderRelation(Integer orderId, String orderType);

    /**
     * 查询这个订单关联的工单
     *
     * @param orderIds   订单id
     * @param orderType 订单id对应订单类型
     * @return
     */
    List<OrderWorkOrderEntity> listOrderWorkOrderRelations(List<Integer> orderIds, String orderType);

    /**
     * 通过工单查询
     *
     * @param workOrderId
     * @param orderType
     * @return
     */
    List<OrderWorkOrderEntity> getByWorkOrderIdAnOrderType(Integer workOrderId, String orderType);

    /**
     * 通过订单id查询
     *
     * @param orderId
     * @return
     */
    List<OrderWorkOrderEntity> listByOrderId(Integer orderId);

    /**
     * 通过工单id查询
     *
     * @param workOrderId
     * @param orderType
     * @return
     */
    List<OrderWorkOrderEntity> getByWorkOrderId(Integer workOrderId, String orderType);

    /**
     * 关联多条工单
     *
     * @param workOrderIds
     * @param orderId
     */
    void add(Integer orderId, String[] workOrderIds, String type);

    /**
     * 通过工单id获取所有关联生产订单
     *
     * @return
     */
    List<ProductOrderEntity> productOrderListByWorkId(WorkOrderEntity workOrderEntity);

    /**
     * 通过工单id获取所有关联销售订单
     *
     * @return
     */
    List<SaleOrderEntity> saleOrderListByWorkId(WorkOrderEntity workOrderEntity);

    /**
     * 通过订单id获取所有关联的工单
     *
     * @param orderId
     * @param orderType OrderNumTypeEnum
     * @return
     */
    List<WorkOrderEntity> listWorkOrderByOrderId(Integer orderId, String orderType);

    /**
     * 通过订单id和lineId获取关联的工单
     *
     * @param orderNumber
     * @param orderType OrderNumTypeEnum
     * @return
     */
   WorkOrderEntity getWorkOrderByOrderIdAndLineId(String orderNumber, Integer lineId, String orderType,Integer state, String workOrderNumber,Integer fid);

    /**
     * 通过订单id和订单类型获取所有关联的工单
     *
     * @param orderId
     * @param orderType
     * @return
     */
    List<WorkOrderEntity> listWorkOrdersByIdAndType(Integer orderId, String orderType);

    /**
     * 通过订单编号获取所有关联的工单
     *
     * @param orderNumber 订单号
     * @return
     */
    List<WorkOrderEntity> listWorkOrderByOrderNumber(String orderNumber);

    /**
     * 通过工单id列表获取所有关联订单
     *
     * @param workOrderIdList
     * @return
     */
    Map<Integer, SaleOrderVO> listOrderByWorkIdList(List<Integer> workOrderIdList);

    /**
     * 通过工单id列表获取所有关联生产订单
     *
     * @param workOrderIdList
     * @return
     */
    Map<Integer, ProductOrderEntity> listProductOrderByWorkIdList(List<Integer> workOrderIdList);

    /**
     * 通过工单id和类型查询
     *
     * @param workOrderIds
     * @param orderType
     * @return
     */
    List<OrderWorkOrderEntity> getByWorkOrderIdsAndOrderType(List<Integer> workOrderIds, String orderType);

    /**
     * 通过订单号和类型获取工单
     *
     * @param orderNumber
     * @param orderType
     * @return
     */
    List<WorkOrderEntity> listWorkOrderByOrderAndType(String orderNumber, String orderType);


    /**
     * 通过销售订单id查询信息
     *
     * @param orderId
     * @param saleOrder
     * @return
     */
    List<OrderWorkOrderEntity> getByOrderIdsAndOrderType(Integer orderId, String saleOrder);

    /**
     * 获取销售订单关联的生产订单，在查询生产订单关联的工单
     *
     * @param saleOrderNumber
     * @return
     */
    List<WorkOrderEntity> listWorkOrderOfProductOrderBySaleOrder(String saleOrderNumber);

    /**
     * 查询生产工单关联的生产订单
     *
     * @param workOrderId
     * @return
     */

    List<SaleOrderEntity> listSaleOrderByWorkIdOfProductOrder(Integer workOrderId);

    /**
     * 查询生产工单列表
     *
     * @param orderIds
     * @param typeCode
     * @return
     */
    List<WorkOrderEntity> listWorkOrderByOrderIds(List<Integer> orderIds, String typeCode);

    Map<Integer, SaleOrderVO> orderMaterialListByWorkOrders(List<WorkOrderEntity> workOrderEntities);
}
