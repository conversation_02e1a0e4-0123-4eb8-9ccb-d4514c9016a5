package com.yelink.dfs.service.common.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yelink.dfs.common.UserAuthenService;
import com.yelink.dfs.constant.AccountsPayableType;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.DictTypeEnum;
import com.yelink.dfs.constant.RedisKeyPrefix;
import com.yelink.dfs.constant.TermsType;
import com.yelink.dfs.constant.station.PassStandardEnum;
import com.yelink.dfs.constant.target.SpecialTargetEnum;
import com.yelink.dfs.constant.target.TargetConst;
import com.yelink.dfs.entity.common.QueryDTO;
import com.yelink.dfs.entity.model.FacilitiesEntity;
import com.yelink.dfs.entity.product.CraftEntity;
import com.yelink.dfs.entity.product.MaterialEntity;
import com.yelink.dfs.entity.product.MaterialTypeConfigFieldEntity;
import com.yelink.dfs.entity.product.dto.MaterialTypeAddDTO;
import com.yelink.dfs.entity.screen.dto.PassStandardDTO;
import com.yelink.dfs.entity.target.dto.RecordManualCollectionDTO;
import com.yelink.dfs.entity.user.SysUserEntity;
import com.yelink.dfs.mapper.common.DictMapper;
import com.yelink.dfs.mapper.model.FacilitiesMapper;
import com.yelink.dfs.mapper.product.CraftMapper;
import com.yelink.dfs.mapper.product.MaterialMapper;
import com.yelink.dfs.open.v1.common.dto.DictDTO;
import com.yelink.dfs.open.v1.common.dto.DictInsertDTO;
import com.yelink.dfs.open.v1.common.dto.TermsSelectDTO;
import com.yelink.dfs.open.v1.material.dto.MaterialTypeInsert2DTO;
import com.yelink.dfs.open.v1.production.dto.CustomerInsertOrUpdateDTO;
import com.yelink.dfs.open.v2.common.dto.BatchDeleteDTO;
import com.yelink.dfs.open.v2.common.dto.DictFileVO;
import com.yelink.dfs.open.v2.common.dto.DictInsertReturnVO;
import com.yelink.dfs.open.v2.common.dto.DictInsertV2DTO;
import com.yelink.dfs.open.v2.common.dto.DictQueryDTO;
import com.yelink.dfs.open.v2.common.dto.DictUpdateV2DTO;
import com.yelink.dfs.open.v2.common.dto.DictVO;
import com.yelink.dfs.open.v2.material.dto.MaterialTypeBatchDeleteDTO;
import com.yelink.dfs.open.v2.material.dto.MaterialTypeBatchInsertDTO;
import com.yelink.dfs.open.v2.material.dto.MaterialTypeBatchInsertOrUpdateDTO;
import com.yelink.dfs.open.v2.material.dto.MaterialTypeBatchUpdateDTO;
import com.yelink.dfs.open.v2.material.dto.MaterialTypeDetailQueryDTO;
import com.yelink.dfs.open.v2.material.dto.MaterialTypeQueryDTO;
import com.yelink.dfs.open.v2.material.dto.MaterialTypeQuerySqlDTO;
import com.yelink.dfs.open.v2.material.vo.MaterialTypeBatchDeleteResultVO;
import com.yelink.dfs.open.v2.material.vo.MaterialTypeBatchInsertResultVO;
import com.yelink.dfs.open.v2.material.vo.MaterialTypeBatchUpdateResultVO;
import com.yelink.dfs.open.v2.material.vo.MaterialTypeSyncResultVO;
import com.yelink.dfs.open.v2.material.vo.MaterialTypeVO;
import com.yelink.dfs.open.v2.common.constant.BatchOperationErrorType;
import com.yelink.dfs.open.v2.common.util.BatchOperationResultBuilder;
import com.yelink.dfscommon.exception.BatchOperationException;
import com.yelink.dfs.open.v2.unit.dto.UnitQueryDTO;
import com.yelink.dfs.provider.constant.KafkaMessageTypeEnum;
import com.yelink.dfs.service.common.DictFileService;
import com.yelink.dfs.service.common.DictService;
import com.yelink.dfs.service.mongodb.MongodbService;
import com.yelink.dfs.service.product.CraftService;
import com.yelink.dfs.service.product.MaterialAttributeService;
import com.yelink.dfs.service.product.MaterialService;
import com.yelink.dfs.service.product.MaterialTypeConfigAttributeService;
import com.yelink.dfs.service.product.MaterialTypeConfigFieldService;
import com.yelink.dfs.service.user.SysUserService;
import com.yelink.dfs.utils.SpringUtil;
import com.yelink.dfs.utils.SqlUtil;
import com.yelink.dfscommon.cache.RedisCache;
import com.yelink.dfscommon.common.unit.constant.RoundingType;
import com.yelink.dfscommon.common.unit.entity.UnitEntity;
import com.yelink.dfscommon.common.unit.service.UnitService;
import com.yelink.dfscommon.constant.Constants;
import com.yelink.dfscommon.constant.DictConstant;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.dto.MyPage;
import com.yelink.dfscommon.entity.CommonType;
import com.yelink.dfscommon.entity.dfs.DictEntity;
import com.yelink.dfscommon.entity.dfs.DictFileEntity;
import com.yelink.dfscommon.entity.dfs.MaterialAttributeEntity;
import com.yelink.dfscommon.entity.dfs.MaterialTypeConfigAttributeEntity;
import com.yelink.dfscommon.entity.dfs.material.MaterialInspectMethodEntity;
import com.yelink.dfscommon.entity.dfs.material.MaterialInspectTriggerConditionEntity;
import com.yelink.dfscommon.provider.MessagePushToKafkaService;
import com.yelink.dfscommon.utils.DateUtil;
import com.yelink.dfscommon.utils.JacksonUtil;
import com.yelink.dfscommon.utils.WrapperUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 字典表一般储存在缓存中，若有更改则清除缓存
 *
 * <AUTHOR>
 * @Date 2021-02-22 17:23
 */
@Slf4j
@Service
public class DictServiceImpl extends ServiceImpl<DictMapper, DictEntity> implements DictService {

    public static final String BEGIN_TIME_OF_DAY = "beginTimeOfDay";
    private static final String BACKUP_TABLE_STRUCTURE = "BackupTableStructure";
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private DictFileService dictFileService;
    @Resource
    private MaterialMapper materialMapper;
    @Resource
    private DictMapper dictMapper;
    @Resource
    private FacilitiesMapper facilitiesMapper;
    @Resource
    private SysUserService userService;
    @Resource
    private UserAuthenService userAuthenService;
    @Resource
    private MaterialTypeConfigFieldService materialTypeConfigFieldService;
    @Resource
    private MessagePushToKafkaService messagePushToKafkaService;
    @Resource
    @Lazy
    private UnitService unitService;
    @Resource
    private CraftMapper craftMapper;
    @Resource
    private MaterialTypeConfigAttributeService materialTypeConfigAttributeService;
    @Resource
    private MongodbService mongodbService;
    @Resource
    private MaterialAttributeService materialAttributeService;
    @Resource
    private MaterialService materialService;

    @Override
    public List<DictEntity> getList() {
        List<DictEntity> dictEntities = JSON.parseArray((String) redisTemplate.opsForValue().get(RedisKeyPrefix.DICT), DictEntity.class);
        if (CollectionUtils.isEmpty(dictEntities)) {
            LambdaQueryWrapper<DictEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.orderByDesc(DictEntity::getCreateTime);
            dictEntities = dictMapper.selectList(wrapper);
        }
        return dictEntities;
    }

    @Override
    public DictEntity getByTypeAndCode(String type, String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        List<DictEntity> dictEntities = getList();
        for (DictEntity dictEntity : dictEntities) {
            if (type.equals(dictEntity.getType()) && code.equals(dictEntity.getCode())) {
                return dictEntity;
            }
        }
        return null;
    }

    @Override
    public DictEntity getAlarmTypeByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        return getByTypeAndCode(DictTypeEnum.ALARM_TYPE.getType(), code);
    }

    @Override
    public DictEntity getAlarmLevelByCode(String code) {
        return getByTypeAndCode(DictTypeEnum.ALARM_LEVEL.getType(), code);
    }

    @Override
    public DictEntity getAlarmAdviceByCode(String code) {
        return getByTypeAndCode(DictTypeEnum.ALARM_ADVICE.getType(), code);
    }

    @Override
    public Page<DictEntity> termsList(TermsSelectDTO selectDTO) {
        List<String> termList = new ArrayList<>();
        for (TermsType typeNum : TermsType.values()) {
            termList.add(typeNum.getType());
        }
        LambdaQueryWrapper<DictEntity> lambda = new LambdaQueryWrapper<>();
        WrapperUtil.like(lambda, DictEntity::getName, selectDTO.getName());
        if (StringUtils.isNotBlank(selectDTO.getType())) {
            lambda.eq(DictEntity::getType, selectDTO.getType());
        } else {
            lambda.in(DictEntity::getType, termList);
        }
        lambda.orderByDesc(DictEntity::getId);
        Page<DictEntity> page = this.page(selectDTO.sortPage(), lambda);
        List<DictEntity> result = page.getRecords();
        //查出附件信息
        if (CollectionUtils.isNotEmpty(result)) {
            List<Integer> collect = result.stream().map(DictEntity::getId).collect(Collectors.toList());
            List<DictFileEntity> dictFileList = dictFileService.lambdaQuery().in(DictFileEntity::getDictId, collect).list();
            Map<Integer, List<DictFileEntity>> dictFileListMap = dictFileList.stream().collect(Collectors.groupingBy(DictFileEntity::getDictId));
            for (DictEntity entity : result) {
                entity.setFileList(dictFileListMap.get(entity.getId()));
                entity.setValue(AccountsPayableType.getNameByCode(entity.getValue()));
            }
        }
        return page;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addTerms(DictEntity entity) {
        //重复名称判断
        LambdaQueryWrapper<DictEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DictEntity::getName, entity.getName());
        DictEntity dictEntity = getOne(queryWrapper);
        if (dictEntity != null) {
            throw new ResponseException(RespCodeEnum.TERMS_NAME_DUPLICATE);
        }
        entity.setCreateTime(new Date());
        entity.setUpdateTime(entity.getCreateTime());
        save(entity);

        //保存多个附件
        saveDictFileList(entity);
        // 推送通知消息
        messagePushToKafkaService.pushNewMessage(entity, Constants.KAFKA_MAIN_DATA_TOPIC, KafkaMessageTypeEnum.TERM_ADD_MESSAGE);
    }

    private void saveDictFileList(DictEntity entity) {
        Date now = new Date();
        List<DictFileEntity> fileList = entity.getFileList();
        if (CollectionUtils.isNotEmpty(fileList)) {
            for (DictFileEntity dictFileEntity : fileList) {
                dictFileEntity.setDictId(entity.getId());
                dictFileEntity.setCreateTime(now);
                dictFileEntity.setUpdateTime(now);
                dictFileEntity.setCreateBy(entity.getUpdateBy());
                dictFileEntity.setUpdateBy(entity.getUpdateBy());
            }
            dictFileService.saveBatch(fileList);
        }
    }

    @Override
    public void updateTerms(DictEntity entity) {
        UpdateWrapper<DictEntity> uw = new UpdateWrapper<>();
        uw.lambda().set(DictEntity::getType, entity.getType())
                .set(DictEntity::getDes, entity.getDes())
                .set(DictEntity::getName, entity.getName())
                .set(DictEntity::getUrl, entity.getUrl())
                .set(DictEntity::getUpdateTime, new Date())
                .set(DictEntity::getUpdateBy, entity.getUpdateBy())
                .set(DictEntity::getUnit, entity.getUnit())
                .set(DictEntity::getValue, entity.getValue())
                .eq(DictEntity::getId, entity.getId());
        update(uw);

        //先删除附件
        deleteDictFileList(entity.getId());

        //保存多个附件
        saveDictFileList(entity);
        // 推送通知消息
        messagePushToKafkaService.pushNewMessage(entity, Constants.KAFKA_MAIN_DATA_TOPIC, KafkaMessageTypeEnum.TERM_UPDATE_MESSAGE);
    }

    private void deleteDictFileList(Integer id) {
        QueryWrapper<DictFileEntity> qw = new QueryWrapper<>();
        qw.lambda().eq(DictFileEntity::getDictId, id);
        dictFileService.remove(qw);
    }

    @Override
    public List<CommonType> termsType() {
        List<CommonType> list = new ArrayList<>();
        for (TermsType type : TermsType.values()) {
            list.add(
                    CommonType.builder()
                            .type(type.getType())
                            .name(type.getName())
                            .build()
            );
        }
        return list;
    }

    @Override
    public List<DictEntity> termsBatchList(String ids) {
        QueryWrapper<DictEntity> qw = new QueryWrapper<>();
        qw.lambda().orderByDesc(DictEntity::getCreateTime);
        qw.lambda().in(DictEntity::getId, Collections.singletonList(ids));
        return list(qw);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteTerms(Integer id) {
        //删除记录
        removeById(id);
        //删除附属文件记录
        deleteDictFileList(id);
        DictEntity termEntity = this.getById(id);
        // 推送通知消息
        messagePushToKafkaService.pushNewMessage(termEntity, Constants.KAFKA_MAIN_DATA_TOPIC, KafkaMessageTypeEnum.TERM_DELETE_MESSAGE);
    }

    @Override
    public Page<DictEntity> unitList(String name, String type, Integer id, Integer current, Integer size) {
        List<String> list = new ArrayList<>();
        list.add(DictTypeEnum.UNIT.getType());

        QueryWrapper<DictEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.like(StringUtils.isNotBlank(name), "BINARY name", name);
        LambdaQueryWrapper<DictEntity> lambda = queryWrapper.lambda();
        //WrapperUtil.like(lambda, DictEntity::getName, name);
        WrapperUtil.like(lambda, DictEntity::getId, id);
        if (StringUtils.isNotBlank(type)) {
            lambda.eq(DictEntity::getType, type);
        } else {
            lambda.in(DictEntity::getType, list);
        }
        lambda.orderByDesc(DictEntity::getId);
        Page<DictEntity> page;
        if (current != null && size != null) {
            page = page(new Page<>(current, size), lambda);
        } else {
            List<DictEntity> result = list(lambda);
            page = new Page<>(1, result.size(), result.size());
            page.setRecords(result);
        }
        page.getRecords().forEach(e -> e.setUrl(RoundingType.getNameByCode(e.getValue())));
        return page;
    }

    @Override
    public DictEntity addUnits(DictEntity entity) {
        if (StringUtils.isBlank(entity.getUnit())) {
            //精度默认两位
            entity.setUnit("2");
        }
        if (StringUtils.isBlank(entity.getValue())) {
            //默认四舍五入
            entity.setValue(RoundingType.HALF_UP.getCode());
        }
        checkUnit(entity);
        DictEntity dictEntity = getUnit(entity.getName());
        if (dictEntity != null) {
            throw new ResponseException(RespCodeEnum.UNIT_NAME_DUPLICATE);
        }
        entity.setType(Constant.UNIT);
        entity.setCreateTime(new Date());
        entity.setUpdateTime(entity.getCreateTime());
        this.save(entity);
        // 推送消息
        messagePushToKafkaService.pushNewMessage(entity, Constants.KAFKA_MAIN_DATA_TOPIC, KafkaMessageTypeEnum.MATERIAL_UNIT_ADD_MESSAGE);
        return entity;
    }

    private DictEntity getUnit(String unitName) {
        //重复名称判断(区分大小写)
        QueryWrapper<DictEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("BINARY name", unitName);
        queryWrapper.lambda().eq(DictEntity::getType, Constant.UNIT).last("limit 1");
        return getOne(queryWrapper);
    }

    @Override
    public void checkUnit(DictEntity entity) {
        if (entity == null) {
            throw new ResponseException(RespCodeEnum.DFS_UNIT_NOT_EXIST);
        }
        // 精度必须为数字
        if (!StringUtils.isNumeric(entity.getUnit())) {
            throw new ResponseException(RespCodeEnum.DFS_UNIT_IS_NOT_NUMERIC);
        }
        int unitAccuracy = Integer.parseInt(entity.getUnit());
        if (unitAccuracy > 10 || unitAccuracy < 0) {
            throw new ResponseException(RespCodeEnum.DFS_UNIT_ACCURACY_ERROR);
        }
        // 用value 存放舍入类型
        RoundingType roundingType = RoundingType.getByCode(entity.getValue());
        if (roundingType == null) {
            throw new ResponseException(RespCodeEnum.DFS_ROUNDING_TYPE_ERROR);
        }
    }

    @Override
    public Integer accuracyLong(String materialCode) {
        UnitEntity unit = unitService.queryUnitByMaterialCode(materialCode);
        return Optional.ofNullable(unit)
                .map(UnitEntity::getAccuracy)
                .orElse(null);
    }

    @Override
    public Map<String, Integer> accuracyLongs(List<String> materialCodes) {
        if (CollUtil.isEmpty(materialCodes)) {
            return new HashMap<>(0);
        }
        return materialCodes.stream().distinct().filter(Objects::nonNull).collect(HashMap::new, (m, v) -> m.put(v, accuracyLong(v)), HashMap::putAll);
    }

    @Override
    public void updateUnit(DictEntity entity) {
        checkUnit(entity);
        UpdateWrapper<DictEntity> uw = new UpdateWrapper<>();
        uw.lambda().set(DictEntity::getDes, entity.getDes())
                .set(DictEntity::getName, entity.getName())
                .set(DictEntity::getCode, entity.getCode())
                .set(DictEntity::getUnit, entity.getUnit())
                .set(DictEntity::getValue, entity.getValue())
                .set(DictEntity::getUpdateTime, new Date())
                .set(DictEntity::getUpdateBy, entity.getUpdateBy())
                .eq(DictEntity::getId, entity.getId());
        update(uw);
        deleteUnitCache(entity.getName());
        // 推送消息
        messagePushToKafkaService.pushNewMessage(entity, Constants.KAFKA_MAIN_DATA_TOPIC, KafkaMessageTypeEnum.MATERIAL_UNIT_UPDATE_MESSAGE);
    }

    @Override
    public void deleteUnit(Integer id) {
        DictEntity entity = this.getById(id);
        if (entity == null) {
            return;
        }
        deleteUnitCache(entity.getName());
        //删除单位信息
        removeById(id);
        // 推送消息
        messagePushToKafkaService.pushNewMessage(entity, Constants.KAFKA_MAIN_DATA_TOPIC, KafkaMessageTypeEnum.MATERIAL_UNIT_DELETE_MESSAGE);
    }

    private void deleteUnitCache(String name) {
        // 物料的缓存全部删除
        Set<String> keys = redisTemplate.keys(UnitServiceImpl.CACHE_PREFIX_MATERIAL + "*");
        if (CollectionUtils.isNotEmpty(keys)) {
            redisTemplate.delete(keys);
            log.info("Delete unitMCache, key: {}", keys);
        }
        String uCache = UnitServiceImpl.CACHE_PREFIX_UNIT + ":" + name;
        // 单位的缓存删除对应的
        redisTemplate.delete(uCache);
        log.info("Delete unitUCache, key: {}", uCache);
    }

    private void showName(List<DictEntity> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Map<String, String> nickNames = userService.getUserNameNickMap(null);
        for (DictEntity entity : list) {
            entity.setCreateByName(nickNames.get(entity.getCreateBy()));
            entity.setUpdateByName(nickNames.get(entity.getUpdateBy()));
            if (Constant.MATERIAL_TYPE.equals(entity.getType())) {
                if (StringUtils.isBlank(entity.getValue())) {
                    continue;
                }
                // 默认工艺名称
                List<String> defaultCraftIds = Arrays.stream(entity.getValue().split(Constant.SEP)).collect(Collectors.toList());
                String defaultCraftNames = craftMapper.selectBatchIds(defaultCraftIds).stream().map(CraftEntity::getName).collect(Collectors.joining(Constant.SEP));
                entity.setDefaultCraftNames(defaultCraftNames);
            }
        }
    }

    @Override
    public Page<DictEntity> commonTypeList(DictDTO dto) {
        Integer current = dto.getCurrent();
        Integer size = dto.getSize();
        LambdaQueryWrapper<DictEntity> lambda = new LambdaQueryWrapper<>();
        lambda.like(StringUtils.isNotBlank(dto.getName()), DictEntity::getName, dto.getName())
                .like(StringUtils.isNotBlank(dto.getDes()), DictEntity::getDes, dto.getDes())
                .eq(StringUtils.isNotBlank(dto.getFullName()), DictEntity::getName, dto.getFullName())
                .eq(StringUtils.isNotBlank(dto.getType()), DictEntity::getType, dto.getType())
                .eq(dto.getPid() != null, DictEntity::getPid, dto.getPid())
                .eq(StringUtils.isNotBlank(dto.getPCode()), DictEntity::getPCode, dto.getPCode())
                .like(StringUtils.isNotBlank(dto.getCode()), DictEntity::getCode, dto.getCode())
                .in(CollectionUtils.isNotEmpty(dto.getIds()), DictEntity::getId, dto.getIds())
                .eq(Objects.nonNull(dto.getId()), DictEntity::getId, dto.getId())
                .eq(Objects.nonNull(dto.getIsProcessAssembly()), DictEntity::getIsProcessAssembly, dto.getIsProcessAssembly())
                .orderByDesc(DictEntity::getId);
        if (dto.getEnable() != null) {
            lambda.eq(DictEntity::getUnit, dto.getEnable().toString());
        }
        Page<DictEntity> page;
        if (current != null && size != null) {
            page = page(new Page<>(current, size), lambda);
        } else {
            List<DictEntity> result = list(lambda);
            page = new Page<>(1, result.size(), result.size());
            page.setRecords(result);
        }
        List<DictEntity> result = page.getRecords();
        showName(result);
        //查出附件信息
        if (CollectionUtils.isNotEmpty(result)) {
            List<Integer> collect = result.stream().map(DictEntity::getId).collect(Collectors.toList());
            List<DictFileEntity> dictFileList = dictFileService.lambdaQuery().in(DictFileEntity::getDictId, collect).list();
            Map<Integer, List<DictFileEntity>> dictFileListMap = dictFileList.stream().collect(Collectors.groupingBy(DictFileEntity::getDictId));
            for (DictEntity entity : result) {
                entity.setFileList(dictFileListMap.get(entity.getId()));
            }
        }
        return page;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addMaterialType(MaterialTypeAddDTO entity) {
        DictEntity dictEntity = entity.getDictEntity();
        if (StringUtils.isBlank(dictEntity.getCode())) {
            dictEntity.setCode(RandomUtil.randomString(10));
        }
        //重复名称判断
        if (lambdaQuery().eq(DictEntity::getType, Constant.MATERIAL_TYPE).eq(DictEntity::getName, dictEntity.getName()).count() > 0) {
            throw new ResponseException(RespCodeEnum.MATERIAL_TYPE_NAME_DUPLICATE);
        }
        if (lambdaQuery().eq(DictEntity::getType, Constant.MATERIAL_TYPE).eq(DictEntity::getCode, dictEntity.getCode()).count() > 0) {
            throw new ResponseException(RespCodeEnum.MATERIAL_TYPE_CODE_DUPLICATE);
        }
        dictEntity.setType(Constant.MATERIAL_TYPE);
        dictEntity.setUnit(StringUtils.isNotBlank(dictEntity.getValue()) ? Constants.TRUE_STR : Constants.FALSE_STR);
        checkMaterialUnit(dictEntity.getUrl());
        this.save(dictEntity);
        //添加可配置字段
        materialTypeConfigFieldService.addMaterialTypeFieldConfig(dictEntity.getId(), dictEntity.getName(), dictEntity.getUpdateBy(), entity.getMaterialTypeConfigFieldEntityList());
        List<MaterialTypeConfigAttributeEntity> attributeEntities = entity.getMaterialTypeConfigAttributeEntityList();
        // 新增物料类型为工装品时，物料属性配置默认加实际使用寿命
        if (Boolean.TRUE.equals(dictEntity.getIsProcessAssembly())) {
            MaterialAttributeEntity attributeEntity = materialAttributeService.lambdaQuery()
                    .eq(MaterialAttributeEntity::getAttributeCode, SpecialTargetEnum.ACTUAL_SERVICE_LIFE.getEnName()).one();
            if (attributeEntity == null) {
                throw new ResponseException("未找到实际使用寿命的物料属性");
            }
            if (CollectionUtils.isEmpty(attributeEntities)) {
                attributeEntities = new ArrayList<>();
                attributeEntities.add(JacksonUtil.convertObject(attributeEntity, MaterialTypeConfigAttributeEntity.class));
            } else {
                List<String> attributeCodes = attributeEntities.stream().map(MaterialTypeConfigAttributeEntity::getAttributeCode).collect(Collectors.toList());
                if (!attributeCodes.contains(SpecialTargetEnum.ACTUAL_SERVICE_LIFE.getEnName())) {
                    attributeEntities.add(JacksonUtil.convertObject(attributeEntity, MaterialTypeConfigAttributeEntity.class));
                }
            }
        }
        // 保存物料属性配置
        materialTypeConfigAttributeService.saveMaterialTypeAttributeConfig(dictEntity.getId(), dictEntity.getName(), dictEntity.getUpdateBy(), attributeEntities);
        // 推送消息
        messagePushToKafkaService.pushNewMessage(entity, Constants.KAFKA_MAIN_DATA_TOPIC, KafkaMessageTypeEnum.MATERIAL_TYPE_ADD_MESSAGE);
        //增加标识关联指标表
        mongodbService.addIndices("code", Constant.MATERIAL_TYPE_TARGET + dictEntity.getId());
    }

    /**
     * 校验物料类型的默认单位
     *
     * @param unitName 单位名称
     */
    private void checkMaterialUnit(String unitName) {
        if (StringUtils.isEmpty(unitName)) {
            return;
        }
        DictEntity unit = getUnit(unitName);
        if (unit == null) {
            throw new ResponseException("该单位不存在");
        }
    }

    @Override
    public void updateMaterialType(MaterialTypeAddDTO entity) {
        DictEntity dictEntity = entity.getDictEntity();
        // 物料名称不能和其他物料类型重复
        Long count = this.lambdaQuery().ne(DictEntity::getId, dictEntity.getId()).eq(DictEntity::getName, dictEntity.getName()).count();
        if (count > 0) {
            throw new ResponseException(RespCodeEnum.MATERIAL_TYPE_NAME_DUPLICATE);
        }
        checkMaterialUnit(dictEntity.getUrl());
        // 工艺模板如果有值，则将 `是否启用默认工艺` 字段改为启用态
        dictEntity.setUnit(StringUtils.isNotBlank(dictEntity.getValue()) ? Constants.TRUE_STR : Constants.FALSE_STR);
        // 如果名称修改了，需要同步刷新物料表的物料类型名称字段
        DictEntity oldEntity = this.getById(dictEntity.getId());
        if (!oldEntity.getName().equals(dictEntity.getName())) {
            MaterialService materialService = SpringUtil.getBean(MaterialService.class);
            materialService.lambdaUpdate().eq(MaterialEntity::getType, dictEntity.getId())
                    .set(MaterialEntity::getTypeName, dictEntity.getName())
                    .update();
        }
        this.updateById(dictEntity);
        // 推送消息
        messagePushToKafkaService.pushNewMessage(entity, Constants.KAFKA_MAIN_DATA_TOPIC, KafkaMessageTypeEnum.MATERIAL_TYPE_UPDATE_MESSAGE);
        //修改可配置字段信息
        if (CollectionUtils.isNotEmpty(entity.getMaterialTypeConfigFieldEntityList())) {
            for (MaterialTypeConfigFieldEntity materialTypeConfigFieldEntity : entity.getMaterialTypeConfigFieldEntityList()) {
                materialTypeConfigFieldEntity.setUpdateBy(dictEntity.getUpdateBy());
                materialTypeConfigFieldEntity.setUpdateTime(dictEntity.getUpdateTime());
            }
            materialTypeConfigFieldService.updateBatchById(entity.getMaterialTypeConfigFieldEntityList());
        }
        // 保存物料属性配置
        materialTypeConfigAttributeService.saveMaterialTypeAttributeConfig(dictEntity.getId(), dictEntity.getName(), dictEntity.getUpdateBy(), entity.getMaterialTypeConfigAttributeEntityList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeMaterialTypeById(Integer id) {
        // 判断是否关联物料模块的数据，如果是，则不能删除
        LambdaQueryWrapper<MaterialEntity> qw = new LambdaQueryWrapper<>();
        qw.eq(MaterialEntity::getType, id);
        List<MaterialEntity> list = materialMapper.selectList(qw);
        if (!list.isEmpty()) {
            throw new ResponseException(RespCodeEnum.MATERIAL_TYPE_RELATE_DATA);
        }
        // 推送消息
        DictEntity entity = this.getById(id);
        this.removeById(id);
        //删除对应字段默认配置
        LambdaUpdateWrapper<MaterialTypeConfigFieldEntity> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(MaterialTypeConfigFieldEntity::getMaterialTypeId, id);
        materialTypeConfigFieldService.remove(lambdaUpdateWrapper);
        // 删除物料属性配置
        materialTypeConfigAttributeService.lambdaUpdate().eq(MaterialTypeConfigAttributeEntity::getMaterialTypeId, id).remove();
        messagePushToKafkaService.pushNewMessage(entity, Constants.KAFKA_MAIN_DATA_TOPIC, KafkaMessageTypeEnum.MATERIAL_TYPE_DELETE_MESSAGE);
    }

    @Override
    public void addDeviceSort(DictEntity entity) {
        //重复名称判断
        LambdaQueryWrapper<DictEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DictEntity::getType, Constant.DEVICE_SORT).eq(DictEntity::getName, entity.getName());
        if (count(queryWrapper) == 1) {
            throw new ResponseException(RespCodeEnum.DEVICE_SORT_NAME_DUPLICATE);
        }
        entity.setType(Constant.DEVICE_SORT);
        entity.setCode(entity.getCode());
        save(entity);
    }

    @Override
    public void updatDeviceSort(DictEntity entity) {
        UpdateWrapper<DictEntity> uw = new UpdateWrapper<>();
        uw.lambda().set(DictEntity::getDes, entity.getDes())
                .set(DictEntity::getName, entity.getName())
                .eq(DictEntity::getId, entity.getId());
        update(uw);
    }

    @Override
    public void deleteDeviceSortById(Integer id) {
        this.removeById(id);
    }

    @Override
    public List<RecordManualCollectionDTO> getTargetName(Integer modelId) {
        QueryWrapper<DictEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(DictEntity::getDes, modelId)
                .eq(DictEntity::getType, "targetName");
        List<DictEntity> dictEntities = dictMapper.selectList(wrapper);
        ArrayList<RecordManualCollectionDTO> targetNameList = new ArrayList<>();
        dictEntities.forEach(dictEntity -> {
            RecordManualCollectionDTO build = RecordManualCollectionDTO.builder()
                    .ename(dictEntity.getCode())
                    .name(dictEntity.getName())
                    .value(null)
                    .unit(dictEntity.getUnit())
                    .build();
            targetNameList.add(build);
        });
        return targetNameList;
    }

    @Override
    public List<DictEntity> listByType(String type) {
        QueryWrapper<DictEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(DictEntity::getType, type);
        return dictMapper.selectList(wrapper);
    }

    @Override
    public List<DictEntity> listByTypeDes(String type, String fid) {
        //找到工位对应的模块
        FacilitiesEntity facilitiesEntity = facilitiesMapper.selectById(fid);
        //找到模块对应的异常列表
        QueryWrapper<DictEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(DictEntity::getType, type);
        wrapper.lambda().eq(DictEntity::getDes, facilitiesEntity.getModelId());
        return dictMapper.selectList(wrapper);
    }

    @Override
    public String getNameById(Integer id) {
        return dictMapper.selectById(id).getName();
    }

    @Override
    public DictEntity detail(Integer id) {
        DictEntity entity = this.getById(id);
        return entity;
    }


    @Override
    public void setStandard(PassStandardDTO dto) {
        String lineType = dto.getLineType();
        Double dateStandard = dto.getDateStandard() == null ? 0.0 : dto.getDateStandard();
        Double materialStandard = dto.getMaterialStandard() == null ? 0.0 : dto.getMaterialStandard();
        boolean a = (dateStandard < materialStandard) ? (dateStandard < 0) : (materialStandard < 0);
        boolean b = (dateStandard > materialStandard) ? (dateStandard > 100) : (materialStandard > 100);
        if (a || b) {
            throw new ResponseException(RespCodeEnum.STANDARD_OUT_OF_RANGE);
        }
        String dateStandardStr = String.valueOf(dateStandard);
        String materialStandardStr = String.valueOf(materialStandard);
        String passStandardCode = PassStandardEnum.PASS_STANDARD.getCode();
        String dateStandardCode = PassStandardEnum.DATE_STANDARD.getCode();
        String materialStandardCode = PassStandardEnum.MATERIAL_STANDARD.getCode();

        LambdaQueryWrapper<DictEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DictEntity::getType, passStandardCode);
        wrapper.eq(DictEntity::getUnit, lineType);
        List<DictEntity> list = this.list(wrapper);
        if (CollectionUtils.isEmpty(list)) {
            DictEntity date = DictEntity.builder()
                    .type(passStandardCode)
                    .code(dateStandardCode)
                    .name(PassStandardEnum.DATE_STANDARD.getName())
                    .url(dateStandardStr)
                    .unit(lineType)
                    .build();
            DictEntity material = DictEntity.builder()
                    .type(passStandardCode)
                    .code(materialStandardCode)
                    .name(PassStandardEnum.MATERIAL_STANDARD.getName())
                    .url(materialStandardStr)
                    .unit(lineType)
                    .build();
            list.add(date);
            list.add(material);
            this.saveBatch(list);
            return;
        }
        Map<String, DictEntity> collect = list.stream().collect(Collectors.toMap(DictEntity::getCode, o -> o));
        if (collect.containsKey(dateStandardCode)) {
            DictEntity entity = collect.get(dateStandardCode);
            entity.setUrl(dateStandardStr);
            this.updateById(entity);
        }
        if (collect.containsKey(materialStandardCode)) {
            DictEntity entity = collect.get(materialStandardCode);
            entity.setUrl(materialStandardStr);
            this.updateById(entity);
        }
    }

    /**
     * 获取直通率标准
     *
     * @return
     */
    @Override
    public PassStandardDTO getPassStandard(Integer fid) {
        LambdaQueryWrapper<DictEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DictEntity::getType, PassStandardEnum.PASS_STANDARD.getCode());
        wrapper.eq(DictEntity::getUnit, fid);
        List<DictEntity> list = this.list(wrapper);
        PassStandardDTO build = PassStandardDTO.builder()
                .dateStandardName(PassStandardEnum.DATE_STANDARD.getName())
                .materialStandardName(PassStandardEnum.MATERIAL_STANDARD.getName())
                .lineType(fid.toString())
                .build();
        if (CollectionUtils.isEmpty(list)) {
            return build;
        }
        Map<String, String> collect = list.stream().collect(Collectors.toMap(DictEntity::getCode, DictEntity::getUrl));
        if (collect.containsKey(PassStandardEnum.DATE_STANDARD.getCode())) {
            String s = collect.get(PassStandardEnum.DATE_STANDARD.getCode());
            build.setDateStandard(Double.valueOf(s));
        }
        if (collect.containsKey(PassStandardEnum.MATERIAL_STANDARD.getCode())) {
            String s = collect.get(PassStandardEnum.MATERIAL_STANDARD.getCode());
            build.setMaterialStandard(Double.valueOf(s));
        }
        return build;
    }

    @Override
    public DictEntity getCodeByName(String name) {
        LambdaQueryWrapper<DictEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DictEntity::getName, name)
                .orderByDesc(DictEntity::getId)
                .last("limit 1");
        return dictMapper.selectOne(wrapper);
    }

    @Override
    public String getBeginTimeOfDay() {
//        String cache = (String) redisTemplate.opsForValue().get(RedisKeyPrefix.DICT_BEGIN_DAY);
//        if (cache != null) {
//            return cache;
//        }
        LambdaQueryWrapper<DictEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DictEntity::getType, BEGIN_TIME_OF_DAY);
        DictEntity dictEntity = this.getOne(wrapper);
        String code = DateUtil.TIME_OF_ZERO;
        if (dictEntity != null) {
            code = dictEntity.getCode() + ":00";
        }
//        redisTemplate.opsForValue().set(RedisKeyPrefix.DICT_BEGIN_DAY, code, 30, TimeUnit.MINUTES);
        return code;
    }

    @Override
    public DictEntity getBeginTimeEntity() {
        LambdaQueryWrapper<DictEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DictEntity::getType, BEGIN_TIME_OF_DAY);
        DictEntity entity = this.getOne(wrapper);
        if (entity != null) {
            String createBy = entity.getCreateBy();
            String updateBy = entity.getUpdateBy();
            if (StringUtils.isNotBlank(createBy)) {
                String createName = userService.getNicknameByUsername(createBy);
                entity.setCreateByName(createName);
            }
            if (StringUtils.isNotBlank(updateBy)) {
                if (updateBy.equals(createBy)) {
                    entity.setUpdateByName(entity.getCreateByName());
                } else {
                    String updateName = userService.getNicknameByUsername(updateBy);
                    entity.setUpdateByName(updateName);
                }
            }
        }
        return entity;
    }

    @Override
    public DictEntity setBeginTimeOfDay(String time, String userName) {
        LambdaQueryWrapper<DictEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DictEntity::getType, BEGIN_TIME_OF_DAY);
        DictEntity dictEntity = this.getOne(wrapper);

        if (dictEntity == null) {
            dictEntity = DictEntity.builder()
                    .code(time)
                    .name("首班最早时间")
                    .type(BEGIN_TIME_OF_DAY)
                    .createBy(userName)
                    .createTime(new Date())
                    .build();
            this.save(dictEntity);
        } else {
            LambdaUpdateWrapper<DictEntity> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(DictEntity::getId, dictEntity.getId())
                    .set(DictEntity::getCode, time)
                    .set(DictEntity::getUpdateBy, userName)
                    .set(DictEntity::getUpdateTime, new Date());
            this.update(updateWrapper);
        }
        return dictEntity;
    }

    /**
     * 获取记录时间
     * 例如工厂开工时间为8:00
     * 入参：2022-06-17 07:00:00
     * 返回：2022-06-16 00:00:00
     *
     * @param date
     * @return
     */
    @Override
    public Date getRecordDate(Date date) {
        String outputBeginTime = this.getBeginTimeOfDay();
        String compareStr = DateUtil.format(date, DateUtil.DATE_FORMAT);
        Date compare = DateUtil.parse(compareStr + " " + outputBeginTime, DateUtil.DATETIME_FORMAT);
        Date recordDate;
        Date dateTimeZero = DateUtil.formatToDate(date, DateUtil.DATETIME_FORMAT_ZERO);
        //如果上报时间小于当日的时间分隔点，如小于4点，则记录日期归为上一日
        if (date.getTime() < compare.getTime()) {
            recordDate = DateUtil.addDate(dateTimeZero, -1);
        } else {
            recordDate = dateTimeZero;
        }
        return recordDate;
    }

    /**
     * 根据配置文件获取开始计算产量时间
     * <p>
     * 例如工厂开工时间为8:00
     * 入参：2022-06-17 15:24:10
     * 返回：2022-06-17 08:00:00
     *
     * @return
     */
    @Override
    public Date getDayOutputBeginTime(Date base) {
        Date date = DateUtil.getDayBegin(base);
        String outputBeginTime = this.getBeginTimeOfDay();
        if (StringUtils.isNotBlank(outputBeginTime)) {
            String format = DateUtil.format(date, DateUtil.DATE_FORMAT);
            Date dateFormat = DateUtil.parse(format + " " + outputBeginTime, DateUtil.DATETIME_FORMAT);
            if (dateFormat != null) {
                //如果格式化的时间大于当前时间，开始时间取昨天
                if (dateFormat.getTime() > base.getTime()) {
                    dateFormat = DateUtil.addDate(dateFormat, -1);
                }
                return dateFormat;
            }
        }
        return date;
    }

    /**
     * 根据记录时间拼接开始时间
     *
     * @return
     */
    @Override
    public Date getBeginTimeByReportDate(Date reportDate) {
        String outputBeginTime = this.getBeginTimeOfDay();
        if (StringUtils.isNotBlank(outputBeginTime)) {
            return DateUtil.parse(DateUtil.dateStr(reportDate) + " " + outputBeginTime, DateUtil.DATETIME_FORMAT);
        }
        return null;
    }

    @Override
    @RedisCache(prefix = "ALARM_CYCLE")
    public Integer getAlarmCycleByCode(String code, String type) {
        QueryWrapper<DictEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(DictEntity::getCode, code)
                .eq(DictEntity::getType, type)
                .orderByDesc(DictEntity::getId)
                .last("limit 1");
        DictEntity entity = this.getOne(wrapper);
        if (entity != null) {
            return Integer.valueOf(entity.getDes());
        }
        return 0;
    }

    @Override
    public List<DictEntity> getListByType(String typeCode, String name) {
        if (StringUtils.isBlank(typeCode)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<DictEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DictEntity::getType, typeCode);
        if (StringUtils.isNotBlank(name)) {
            wrapper.like(DictEntity::getName, name);
        }
        List<DictEntity> list = this.list(wrapper);
        setNameByList(list);
        return list;
    }

    private void setNameByList(List<DictEntity> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<String> userNames = list.stream().map(DictEntity::getCreateBy).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        userNames.addAll(list.stream().map(DictEntity::getUpdateBy).filter(StringUtils::isNotBlank).collect(Collectors.toList()));
        String[] strings = userNames.toArray(new String[0]);
        List<SysUserEntity> userEntities = userService.selectByUsernames(strings);
        Map<String, String> map = userEntities.stream()
                .peek(u -> u.setUsername(u.getUsername().toLowerCase()))
                .collect(Collectors.toMap(SysUserEntity::getUsername, SysUserEntity::getNickname));
        list.forEach(o -> {
            o.setCreateByName(map.get(o.getCreateBy()));
            o.setUpdateByName(map.get(o.getUpdateBy()));
        });
    }

    @Override
    public List<String> getDistinctTypeNameList(String typeName) {
        LambdaQueryWrapper<DictEntity> qw = new LambdaQueryWrapper<>();
        qw.eq(DictEntity::getType, typeName);
        List<DictEntity> list = this.list(qw);
        return list.stream().map(DictEntity::getName).distinct().collect(Collectors.toList());
    }

    @Override
    public Page<DictEntity> unitOpenList(String name, Integer current, Integer size) {
        LambdaQueryWrapper<DictEntity> lambda = new LambdaQueryWrapper<>();
        String type = DictTypeEnum.UNIT.getType();
        lambda.select(DictEntity::getId, DictEntity::getCode, DictEntity::getName).eq(DictEntity::getType, type)
                .orderByDesc(DictEntity::getId);
        WrapperUtil.like(lambda, DictEntity::getName, name);
        Page<DictEntity> page;
        if (current != null && size != null) {
            page = page(new Page<>(current, size), lambda);
        } else {
            List<DictEntity> result = list(lambda);
            page = new Page<>(1, result.size(), result.size());
            page.setRecords(result);
        }
        return page;
    }

    @Override
    public List<DictEntity> getManualRunningTargetByModelId(Integer modelId) {
        LambdaQueryWrapper<DictEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DictEntity::getDes, modelId).eq(DictEntity::getType, Constant.TARGET_NAME)
                .eq(DictEntity::getCode, TargetConst.DEVICE_RUNNING_DURATION);
        return this.list(wrapper);
    }

    @Override
    public DictEntity getByType(String type) {
        LambdaQueryWrapper<DictEntity> dictWrapper = new LambdaQueryWrapper<>();
        dictWrapper.eq(DictEntity::getType, type);
        return getOne(dictWrapper);
    }

    @Override
    public String getNameByCodeAndType(String code, String type) {
        LambdaQueryWrapper<DictEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DictEntity::getCode, code)
                .eq(DictEntity::getType, type).last("limit 1");
        DictEntity dictEntity = this.getOne(queryWrapper);
        if (dictEntity == null) {
            return "";
        }
        return dictEntity.getName();
    }

    /**
     * 新增供应商类型
     *
     * @return
     */
    @Override
    public void addSupplierType(DictEntity dictEntity, String username) {
        Long countCode = this.lambdaQuery().eq(DictEntity::getCode, dictEntity.getCode()).eq(DictEntity::getType, DictTypeEnum.SUPPLIER_TYPE.getType()).count();
        if (countCode > 0) {
            throw new ResponseException(RespCodeEnum.SUPPLIER_TYPE_MODEL_ADD_REPEAT);
        }
        Long count = this.lambdaQuery().eq(DictEntity::getName, dictEntity.getName()).eq(DictEntity::getType, DictTypeEnum.SUPPLIER_TYPE.getType()).count();
        if (count > 0) {
            throw new ResponseException(RespCodeEnum.SUPPLIER_TYPE_MODEL_ADD_REPEAT);
        }
        dictEntity.setType(DictTypeEnum.SUPPLIER_TYPE.getType());
        this.save(dictEntity);

    }

    /**
     * 获取供应商类型
     *
     * @return
     */
    @Override
    public Page<DictEntity> getSupplierType(Integer current, Integer size, String code, String name) {
        LambdaQueryWrapper<DictEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(DictEntity::getId, DictEntity::getCode, DictEntity::getName);
        WrapperUtil.eq(wrapper, DictEntity::getType, DictTypeEnum.SUPPLIER_TYPE.getType());
        WrapperUtil.like(wrapper, DictEntity::getCode, code);
        WrapperUtil.like(wrapper, DictEntity::getName, name);
        Page<DictEntity> page = new Page<>(current, size);
        return this.page(page, wrapper);
    }


    /**
     * 获取供应商类型不分页
     *
     * @return
     */
    @Override
    public List<DictEntity> getSupplierTypes() {
        LambdaQueryWrapper<DictEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DictEntity::getType, DictTypeEnum.SUPPLIER_TYPE.getType());
        return this.list(queryWrapper);
    }


    @Override
    public DictEntity add(DictEntity entity) {
        //重复名称判断
        entity.setCreateTime(new Date());
        entity.setUpdateTime(entity.getCreateTime());
        save(entity);
        saveDictFileList(entity);
        // 推送消息
        messagePushToKafkaService.pushNewMessage(entity, Constants.KAFKA_MAIN_DATA_TOPIC, KafkaMessageTypeEnum.DICT_ADD_MESSAGE);
        return entity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void edit(DictEntity entity) {
        this.updateById(entity);
        //先删除附件
        deleteDictFileList(entity.getId());
        //保存多个附件
        saveDictFileList(entity);
        // 推送消息
        messagePushToKafkaService.pushNewMessage(entity, Constants.KAFKA_MAIN_DATA_TOPIC, KafkaMessageTypeEnum.DICT_UPDATE_MESSAGE);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Integer id) {
        DictEntity entity = getById(id);
        //删除记录
        removeById(id);
        //删除附属文件记录
        deleteDictFileList(id);
        // 推送消息
        messagePushToKafkaService.pushNewMessage(entity, Constants.KAFKA_MAIN_DATA_TOPIC, KafkaMessageTypeEnum.DICT_UPDATE_MESSAGE);
    }

    @Override
    public List<String> getBackupTableList() {
        List<String> list = new ArrayList<>();
        LambdaQueryWrapper<DictEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DictEntity::getCode, BACKUP_TABLE_STRUCTURE).eq(DictEntity::getType, BACKUP_TABLE_STRUCTURE).last("limit 1");
        DictEntity dictEntity = this.getOne(queryWrapper);
        if (dictEntity != null) {
            String des = dictEntity.getDes();
            list = Arrays.asList(des.split(Constant.SEP));
        }
        return list;
    }

    @Override
    public Map<String, List<MaterialInspectMethodEntity>> getMaterialInspectMethod() {
        List<DictEntity> dictEntities = this.lambdaQuery().eq(DictEntity::getValue, com.yelink.dfscommon.constant.Constant.MATERIAL_INSPECT_METHOD).list();
        Map<String, List<DictEntity>> map = dictEntities.stream().collect(Collectors.groupingBy(DictEntity::getType));
        return map.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().stream().map(o ->
                        MaterialInspectMethodEntity.builder()
                                .inspectMethodCode(o.getCode())
                                .inspectMethodName(o.getName())
                                .inspectTypeName(o.getType())
                                .build()
                ).collect(Collectors.toList())
        ));
    }

    @Override
    public List<CommonType> getCraftProcedureInspectMethod(String inspectTypeName) {
        List<DictEntity> dictEntities = this.lambdaQuery()
                .eq(DictEntity::getValue, com.yelink.dfscommon.constant.Constant.CRAFT_PROCEDURE_INSPECT_METHOD)
                .eq(DictEntity::getType, inspectTypeName)
                .list();
        return dictEntities.stream().map(o ->
                        CommonType.builder().code(o.getCode()).name(o.getName()).build())
                .collect(Collectors.toList());
    }

    @Override
    public void setValue(DictInsertDTO dictInsert) {
        List<DictEntity> list = this.lambdaQuery()
                .eq(DictEntity::getType, DictTypeEnum.KEY_VALUE.getType())
                .eq(DictEntity::getCode, dictInsert.getCode())
                .list();
        String username = userAuthenService.getUsername();
        if (CollectionUtils.isNotEmpty(list)) {
            // 如果存在,则更新
            // 应该只有一个,避免极端情况出现多个
            list.forEach(res -> {
                res.setValue(dictInsert.getValue());
                res.setUpdateTime(new Date());
                res.setUpdateBy(username);
            });
            this.updateBatchById(list);
            return;
        }
        // 不存在,新增
        DictEntity dict = DictEntity.builder()
                .type(DictTypeEnum.KEY_VALUE.getType())
                .code(dictInsert.getCode())
                .value(dictInsert.getValue())
                .createTime(new Date())
                .updateTime(new Date())
                .createBy(username)
                .updateBy(username)
                .build();
        this.save(dict);
    }

    @Override
    public Map<String, List<MaterialInspectTriggerConditionEntity>> getMaterialInspectTriggerCondition() {
        Map<String, List<DictEntity>> map = this.lambdaQuery()
                .eq(DictEntity::getValue, DictConstant.INSPECT_TRIGGER_CONDITION)
                .list().stream().collect(Collectors.groupingBy(DictEntity::getType));
        return map.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().stream().map(o ->
                        MaterialInspectTriggerConditionEntity.builder()
                                .inspectTriggerConditionCode(o.getCode())
                                .inspectTriggerConditionName(o.getName())
                                .inspectTypeName(o.getType())
                                .build()
                ).collect(Collectors.toList())
        ));
    }

    @Override
    public List<CommonType> getCraftProcedureInspectTriggerCondition(String inspectTypeName) {
        List<DictEntity> dictEntities = this.lambdaQuery()
                .eq(DictEntity::getValue, DictConstant.INSPECT_TRIGGER_CONDITION)
                .eq(DictEntity::getType, inspectTypeName)
                .list();
        return dictEntities.stream().map(o ->
                        CommonType.builder().code(o.getCode()).name(o.getName()).build())
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void upsertUnits(DictEntity entity) {
        DictEntity one = getUnit(entity.getName());
        if (one == null) {
            //新增
            addUnits(entity);
            return;
        }
        //更新
        entity.setId(one.getId());
        updateUnit(entity);
    }

    @Override
    public Page<MaterialTypeVO> getMaterialTypeList(MaterialTypeQueryDTO dto) {
        Page<MaterialTypeVO> page = new Page<>(dto.getCurrent(), dto.getSize());
        Map<String, QueryDTO<MaterialTypeQueryDTO.CustomerQueryCriteriaDTO>> dtos = SqlUtil.split(dto, "materialType");
        // 字段过滤
        QueryDTO<MaterialTypeQueryDTO.CustomerQueryCriteriaDTO> materialTypeDTO = dtos.get("materialType");
        // 重新组转成sql
        MaterialTypeQuerySqlDTO querySqlDTO = MaterialTypeQuerySqlDTO.builder()
                .materialTypeSql(SqlUtil.getSelectSql(materialTypeDTO, false))
                .orderBySql(
                        Stream.of(SqlUtil.getSortSql(materialTypeDTO, false))
                                .filter(StringUtils::isNotBlank).collect(Collectors.joining(Constant.SEP))
                )
                .build();
//        String sql = SqlUtil.getSql(dto);
        Page<DictEntity> dictPage = this.baseMapper.getMaterialTypeList(querySqlDTO, dto.getPage());
        if (CollectionUtils.isEmpty(dictPage.getRecords())) {
            return page;
        }
        CraftService craftService = SpringUtil.getBean(CraftService.class);
        List<MaterialTypeVO> materialTypeVOS = dictPage.getRecords().stream().map(o -> getMaterialTypeVO(o, craftService)
        ).collect(Collectors.toList());
        page.setRecords(materialTypeVOS);
        page.setTotal(dictPage.getTotal());
        return page;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDeleteTerms(BatchDeleteDTO batchDeleteDTO) {
        List<String> nameList = Optional.ofNullable(batchDeleteDTO.getNameList())
                .orElse(Collections.emptyList()) // 如果为 null，返回空 List
                .stream()
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(nameList)) {
            return;
        }
        List<DictEntity> dicts = this.lambdaQuery()
                .in(DictEntity::getName, nameList)
                .list();
        dicts.forEach(dict -> {
            deleteTerms(dict.getId());
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<DictInsertReturnVO> batchAddTerms(List<DictInsertV2DTO> insertDTOS) {
        // 转换
        List<DictEntity> dictEntities = new ArrayList<>();
        insertDTOS.forEach(insertDTO -> {
            DictEntity dictEntity = JacksonUtil.convertObject(insertDTO, DictEntity.class);
            List<DictFileEntity> dictFileEntities = JacksonUtil.convertArray(insertDTO.getFileList(), DictFileEntity.class);
            dictEntity.setFileList(dictFileEntities);
            dictEntities.add(dictEntity);
        });
        dictEntities.forEach(entity -> {
            entity.setCreateTime(Objects.isNull(entity.getCreateTime()) ? new Date() : entity.getCreateTime());
            addTerms(entity);
        });
        List<DictInsertReturnVO> returnVOS = new ArrayList<>();
        dictEntities.forEach(entity -> {
            Integer id = entity.getId();
            DictInsertReturnVO vo = new DictInsertReturnVO();
            vo.setName(entity.getName());
            returnVOS.add(vo);
            List<Integer> fileIds = dictFileService.lambdaQuery()
                    .in(DictFileEntity::getDictId, id)
                    .list()
                    .stream()
                    .map(DictFileEntity::getId)
                    .collect(Collectors.toList());
            vo.setDictFileIds(fileIds);
        });
        return returnVOS;
    }

    @Override
    public MyPage<DictVO> queryList(DictQueryDTO dictQueryDTO) {
        String sql = SqlUtil.getSql(dictQueryDTO);
        Page<DictVO> page = this.baseMapper.getList(sql, dictQueryDTO.getPage());
        MyPage<DictVO> myPage = new MyPage<>(page.getCurrent(), page.getSize(), page.getTotal());
        if (dictQueryDTO.getQueryAddition().contains("dictFile")) {
            page.getRecords().forEach(p -> {
                List<DictFileEntity> list = dictFileService.lambdaQuery()
                        .in(DictFileEntity::getDictId, p.getId())
                        .list();
                // 转换
                List<DictFileVO> vos = new ArrayList<>();
                for (DictFileEntity dictFileEntity : list) {
                    DictFileVO dictFileVO = JacksonUtil.convertObject(dictFileEntity, DictFileVO.class);
                    vos.add(dictFileVO);
                }
                p.setFileList(vos);
            });
        }
        myPage.setRecords(page.getRecords());
        return myPage;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateTerms(List<DictUpdateV2DTO> dictUpdateDTOS) {
        List<String> nameList = dictUpdateDTOS.stream().map(DictUpdateV2DTO::getName).distinct().collect(Collectors.toList());
        if (nameList.size() != dictUpdateDTOS.size()) {
            throw new ResponseException(RespCodeEnum.TERMS_NAME_DUPLICATE);
        }
        // 校验条款数据是否存在库中
        List<DictEntity> list = this.lambdaQuery()
                .in(DictEntity::getName, nameList)
                .list();
        if (CollectionUtils.isEmpty(list)) {
            throw new ResponseException(RespCodeEnum.TERMS_NOT_EXIST);
        }
        Map<String, Integer> stringIntMap = list.stream().collect(Collectors.toMap(DictEntity::getName, DictEntity::getId));
        // 转换
        List<DictEntity> dictEntityList = new ArrayList<>();
        dictUpdateDTOS.forEach(updateDTO -> {
            DictEntity dictEntity = JacksonUtil.convertObject(updateDTO, DictEntity.class);
            List<DictFileEntity> dictFileEntities = JacksonUtil.convertArray(updateDTO.getFileList(), DictFileEntity.class);
            dictEntity.setFileList(dictFileEntities);
            dictEntity.setId(stringIntMap.get(updateDTO.getName()));
            dictEntityList.add(dictEntity);
        });
        dictEntityList.forEach(this::updateTerms);
    }

    @Override
    public Page<DictEntity> unitOpenList2(UnitQueryDTO dto) {
        dto.setFilterSql(SqlUtil.getSelectSql(dto, false));
        dto.setOrderBySql(SqlUtil.getSortSql(dto, false));
        Page<DictEntity> page = this.baseMapper.unitOpenList2(dto, dto.getPage());
        return page;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MaterialTypeSyncResultVO syncMaterialType(List<MaterialTypeInsert2DTO> dtos) {
        // 使用工具类构建结果
        BatchOperationResultBuilder<MaterialTypeSyncResultVO.MaterialTypeSyncFailureVO> resultBuilder =
                BatchOperationResultBuilder.create();

        List<String> insertedCodes = new ArrayList<>();
        List<String> updatedCodes = new ArrayList<>();

        // 批量查询已存在的物料类型
        List<String> codes = dtos.stream().map(MaterialTypeInsert2DTO::getCode).collect(Collectors.toList());
        Map<String, DictEntity> existingMap = this.lambdaQuery()
                .eq(DictEntity::getType, DictTypeEnum.MATERIAL_TYPE.getType())
                .in(DictEntity::getCode, codes)
                .list().stream()
                .collect(Collectors.toMap(DictEntity::getCode, entity -> entity));

        for (MaterialTypeInsert2DTO dto : dtos) {
            try {
                DictEntity existing = existingMap.get(dto.getCode());
                if (existing == null) {
                    materialService.upsertMaterialType2(dto);
                    insertedCodes.add(dto.getCode());
                } else {
                    materialService.upsertMaterialType2(dto);
                    updatedCodes.add(dto.getCode());
                }
                resultBuilder.addSuccess(dto.getCode());
            } catch (Exception e) {
                log.error("同步物料类型失败，编码：{}，错误：{}", dto.getCode(), e.getMessage(), e);
                resultBuilder.addFailure(MaterialTypeSyncResultVO.MaterialTypeSyncFailureVO.builder()
                        .materialTypeCode(dto.getCode())
                        .failureReason(e.getMessage())
                        .errorType(BatchOperationErrorType.BUSINESS_ERROR)
                        .build());
            }
        }
        MaterialTypeSyncResultVO resultVO = JacksonUtil.convertObject(resultBuilder, MaterialTypeSyncResultVO.class);
        resultVO.setInsertedCodes(insertedCodes);
        resultVO.setUpdatedCodes(updatedCodes);
        return resultVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MaterialTypeBatchInsertResultVO batchAddMaterialTypeV2(MaterialTypeBatchInsertDTO dto) {
        List<MaterialTypeBatchInsertOrUpdateDTO> materialTypes = dto.getMaterialTypes();
        // 使用工具类构建结果
        BatchOperationResultBuilder<MaterialTypeBatchInsertResultVO.MaterialTypeBatchInsertFailureVO> resultBuilder =
                BatchOperationResultBuilder.create();

        CraftService craftService = SpringUtil.getBean(CraftService.class);

        // 批量查询已存在的物料类型编码和名称
        List<String> codes = materialTypes.stream().map(MaterialTypeBatchInsertOrUpdateDTO::getCode).collect(Collectors.toList());
        List<String> names = materialTypes.stream().map(MaterialTypeBatchInsertOrUpdateDTO::getName).collect(Collectors.toList());

        Set<String> existingCodes = this.lambdaQuery()
                .eq(DictEntity::getType, DictTypeEnum.MATERIAL_TYPE.getType())
                .in(DictEntity::getCode, codes)
                .list().stream()
                .map(DictEntity::getCode).collect(Collectors.toSet());

        Set<String> existingNames = this.lambdaQuery()
                .eq(DictEntity::getType, DictTypeEnum.MATERIAL_TYPE.getType())
                .in(DictEntity::getName, names)
                .list().stream()
                .map(DictEntity::getName).collect(Collectors.toSet());

        // 第一阶段：验证所有数据，收集失败项
        List<DictEntity> validEntities = new ArrayList<>();
        for (MaterialTypeBatchInsertOrUpdateDTO materialTypeDTO : materialTypes) {
            try {
                // 检查编码是否已存在
                if (existingCodes.contains(materialTypeDTO.getCode())) {
                    resultBuilder.addFailure(MaterialTypeBatchInsertResultVO.MaterialTypeBatchInsertFailureVO.builder()
                            .materialTypeCode(materialTypeDTO.getCode())
                            .failureReason("物料类型编码已存在")
                            .errorType(BatchOperationErrorType.DATA_ALREADY_EXISTS)
                            .build());
                    continue;
                }
                // 检查名称是否已存在
                if (existingNames.contains(materialTypeDTO.getName())) {
                    resultBuilder.addFailure(MaterialTypeBatchInsertResultVO.MaterialTypeBatchInsertFailureVO.builder()
                            .materialTypeCode(materialTypeDTO.getCode())
                            .failureReason("物料类型名称已存在")
                            .errorType(BatchOperationErrorType.DATA_ALREADY_EXISTS)
                            .build());
                    continue;
                }

                List<Integer> craftIds = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(materialTypeDTO.getBindDefaultCraftCodes())) {
                    craftIds = craftService.lambdaQuery()
                            .eq(CraftEntity::getIsTemplate, true)
                            .in(CraftEntity::getCraftCode, materialTypeDTO.getBindDefaultCraftCodes())
                            .list().stream().map(CraftEntity::getCraftId).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(craftIds)) {
                        resultBuilder.addFailure(MaterialTypeBatchInsertResultVO.MaterialTypeBatchInsertFailureVO.builder()
                                .materialTypeCode(materialTypeDTO.getCode())
                                .failureReason("关联的工艺模板不存在")
                                .errorType(BatchOperationErrorType.DATA_NOT_FOUND)
                                .build());
                        continue;
                    }
                }

                validEntities.add(DictEntity.builder()
                        .code(materialTypeDTO.getCode())
                        .name(materialTypeDTO.getName())
                        .des(materialTypeDTO.getDes())
                        .url(materialTypeDTO.getUnitName())
                        .type(DictTypeEnum.MATERIAL_TYPE.getType())
                        .qualityLevel(materialTypeDTO.getQualityLevel())
                        .isProcessAssembly(materialTypeDTO.getIsProcessAssembly())
                        .unit(CollectionUtils.isNotEmpty(materialTypeDTO.getBindDefaultCraftCodes()) ? Constants.TRUE_STR : Constants.FALSE_STR)
                        .value(craftIds.stream().map(String::valueOf).collect(Collectors.joining(Constants.SEP)))
                        .createTime(new Date())
                        .build());
                resultBuilder.addSuccess(materialTypeDTO.getCode());
            } catch (Exception e) {
                log.error("批量新增物料类型验证失败，编码：{}，错误：{}", materialTypeDTO.getCode(), e.getMessage(), e);
                resultBuilder.addFailure(MaterialTypeBatchInsertResultVO.MaterialTypeBatchInsertFailureVO.builder()
                        .materialTypeCode(materialTypeDTO.getCode())
                        .failureReason(e.getMessage())
                        .errorType(BatchOperationErrorType.SYSTEM_ERROR)
                        .build());
            }
        }

        // 如果有失败项，抛出异常触发事务回滚
        if (resultBuilder.getFailedCount() > 0) {
            MaterialTypeBatchInsertResultVO result = JacksonUtil.convertObject(resultBuilder, MaterialTypeBatchInsertResultVO.class);
            throw new BatchOperationException("批量新增物料类型操作失败，存在" + resultBuilder.getFailedCount() + "个失败项", result);
        }

        // 第二阶段：执行所有操作
        try {
            this.saveBatch(validEntities);
        } catch (Exception e) {
            log.error("批量新增物料类型执行失败，错误：{}", e.getMessage(), e);
            throw new RuntimeException("批量新增物料类型执行失败，错误：" + e.getMessage(), e);
        }

        return JacksonUtil.convertObject(resultBuilder, MaterialTypeBatchInsertResultVO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MaterialTypeBatchUpdateResultVO batchUpdateMaterialTypeV2(MaterialTypeBatchUpdateDTO dto) {
        List<MaterialTypeBatchInsertOrUpdateDTO> materialTypes = dto.getMaterialTypes();
        // 使用工具类构建结果
        BatchOperationResultBuilder<MaterialTypeBatchUpdateResultVO.MaterialTypeBatchUpdateFailureVO> resultBuilder =
                BatchOperationResultBuilder.create();
        CraftService craftService = SpringUtil.getBean(CraftService.class);

        // 批量查询已存在的物料类型
        List<String> codes = materialTypes.stream().map(MaterialTypeBatchInsertOrUpdateDTO::getCode).collect(Collectors.toList());
        Map<String, DictEntity> existingMap = this.lambdaQuery()
                .eq(DictEntity::getType, DictTypeEnum.MATERIAL_TYPE.getType())
                .in(DictEntity::getCode, codes)
                .list().stream()
                .collect(Collectors.toMap(DictEntity::getCode, entity -> entity));

        // 批量查询所有可能重复的名称
        List<String> names = materialTypes.stream()
                .map(MaterialTypeBatchInsertOrUpdateDTO::getName)
                .filter(StringUtils::isNotBlank).collect(Collectors.toList());
        Map<String, DictEntity> nameMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(names)) {
            nameMap = this.lambdaQuery()
                    .eq(DictEntity::getType, DictTypeEnum.MATERIAL_TYPE.getType())
                    .in(DictEntity::getName, names)
                    .list().stream()
                    .collect(Collectors.toMap(DictEntity::getName, entity -> entity));
        }

        // 第一阶段：验证所有数据，收集失败项
        List<DictEntity> validEntities = new ArrayList<>();
        for (MaterialTypeBatchInsertOrUpdateDTO materialTypeDTO : materialTypes) {
            try {
                // 检查物料类型是否存在
                DictEntity existing = existingMap.get(materialTypeDTO.getCode());
                if (existing == null) {
                    resultBuilder.addFailure(MaterialTypeBatchUpdateResultVO.MaterialTypeBatchUpdateFailureVO.builder()
                            .materialTypeCode(materialTypeDTO.getCode())
                            .failureReason("物料类型不存在")
                            .errorType(BatchOperationErrorType.DATA_NOT_FOUND)
                            .build());
                    continue;
                }
                // 如果要更新名称，检查名称是否与其他记录重复
                if (StringUtils.isNotBlank(materialTypeDTO.getName()) && !materialTypeDTO.getName().equals(existing.getName())) {
                    DictEntity nameEntity = nameMap.get(materialTypeDTO.getName());
                    if (nameEntity != null && !nameEntity.getId().equals(existing.getId())) {
                        resultBuilder.addFailure(MaterialTypeBatchUpdateResultVO.MaterialTypeBatchUpdateFailureVO.builder()
                                .materialTypeCode(materialTypeDTO.getCode())
                                .failureReason("物料类型名称已存在")
                                .errorType(BatchOperationErrorType.DATA_ALREADY_EXISTS)
                                .build());
                        continue;
                    }
                }

                List<Integer> craftIds = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(materialTypeDTO.getBindDefaultCraftCodes())) {
                    craftIds = craftService.lambdaQuery()
                            .eq(CraftEntity::getIsTemplate, true)
                            .in(CraftEntity::getCraftCode, materialTypeDTO.getBindDefaultCraftCodes())
                            .list().stream().map(CraftEntity::getCraftId).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(craftIds)) {
                        resultBuilder.addFailure(MaterialTypeBatchUpdateResultVO.MaterialTypeBatchUpdateFailureVO.builder()
                                .materialTypeCode(materialTypeDTO.getCode())
                                .failureReason("关联的工艺模板不存在")
                                .errorType(BatchOperationErrorType.DATA_NOT_FOUND)
                                .build());
                        continue;
                    }
                }

                // 构建更新实体
                validEntities.add(DictEntity.builder()
                        .id(existing.getId())
                        .code(materialTypeDTO.getCode())
                        .name(materialTypeDTO.getName())
                        .des(materialTypeDTO.getDes())
                        .url(materialTypeDTO.getUnitName())
                        .type(DictTypeEnum.MATERIAL_TYPE.getType())
                        .qualityLevel(materialTypeDTO.getQualityLevel())
                        .isProcessAssembly(materialTypeDTO.getIsProcessAssembly())
                        .unit(CollectionUtils.isNotEmpty(materialTypeDTO.getBindDefaultCraftCodes()) ? Constants.TRUE_STR : Constants.FALSE_STR)
                        .value(craftIds.stream().map(String::valueOf).collect(Collectors.joining(Constants.SEP)))
                        .updateTime(new Date())
                        .build());
                resultBuilder.addSuccess(materialTypeDTO.getCode());
            } catch (Exception e) {
                log.error("批量更新物料类型验证失败，编码：{}，错误：{}", materialTypeDTO.getCode(), e.getMessage(), e);
                resultBuilder.addFailure(MaterialTypeBatchUpdateResultVO.MaterialTypeBatchUpdateFailureVO.builder()
                        .materialTypeCode(materialTypeDTO.getCode())
                        .failureReason(e.getMessage())
                        .errorType(BatchOperationErrorType.SYSTEM_ERROR)
                        .build());
            }
        }

        // 如果有失败项，抛出异常触发事务回滚
        if (resultBuilder.getFailedCount() > 0) {
            MaterialTypeBatchUpdateResultVO result = JacksonUtil.convertObject(resultBuilder, MaterialTypeBatchUpdateResultVO.class);
            throw new BatchOperationException("批量更新物料类型操作失败，存在" + resultBuilder.getFailedCount() + "个失败项", result);
        }

        // 第二阶段：执行所有操作
        try {
            this.updateBatchById(validEntities);
        } catch (Exception e) {
            log.error("批量更新物料类型执行失败，错误：{}", e.getMessage(), e);
            throw new RuntimeException("批量更新物料类型执行失败，错误：" + e.getMessage(), e);
        }

        return JacksonUtil.convertObject(resultBuilder, MaterialTypeBatchUpdateResultVO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MaterialTypeBatchDeleteResultVO batchDeleteMaterialTypeV2(MaterialTypeBatchDeleteDTO deleteDTO) {
        // 使用工具类构建结果
        BatchOperationResultBuilder<MaterialTypeBatchDeleteResultVO.MaterialTypeBatchDeleteFailureVO> resultBuilder =
                BatchOperationResultBuilder.create();

        // 批量查询物料类型
        Map<String, DictEntity> materialTypeMap = this.lambdaQuery()
                .eq(DictEntity::getType, DictTypeEnum.MATERIAL_TYPE.getType())
                .in(DictEntity::getCode, deleteDTO.getCode())
                .list().stream()
                .collect(Collectors.toMap(DictEntity::getCode, entity -> entity));

        // 批量查询物料关联情况
        List<Integer> materialTypeIds = materialTypeMap.values().stream()
                .map(DictEntity::getId).collect(Collectors.toList());
        Map<Integer, Long> materialCountMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(materialTypeIds)) {
            List<MaterialEntity> materials = materialService.lambdaQuery()
                    .select(MaterialEntity::getType)
                    .in(MaterialEntity::getType, materialTypeIds)
                    .list();
            materialCountMap = materials.stream()
                    .collect(Collectors.groupingBy(MaterialEntity::getType, Collectors.counting()));
        }

        // 第一阶段：验证所有数据，收集失败项和有效的删除目标
        List<DictEntity> validEntities = new ArrayList<>();
        for (String code : deleteDTO.getCode()) {
            try {
                // 检查物料类型是否存在
                DictEntity materialTypeEntity = materialTypeMap.get(code);
                if (materialTypeEntity == null) {
                    resultBuilder.addFailure(MaterialTypeBatchDeleteResultVO.MaterialTypeBatchDeleteFailureVO.builder()
                            .materialTypeCode(code)
                            .failureReason("物料类型不存在")
                            .errorType(BatchOperationErrorType.DATA_NOT_FOUND)
                            .build());
                    continue;
                }

                // 检查是否有物料关联该物料类型
                Long materialCount = materialCountMap.getOrDefault(materialTypeEntity.getId(), 0L);
                if (materialCount > 0) {
                    resultBuilder.addFailure(MaterialTypeBatchDeleteResultVO.MaterialTypeBatchDeleteFailureVO.builder()
                            .materialTypeCode(code)
                            .failureReason("存在关联该物料类型的物料，无法删除")
                            .errorType(BatchOperationErrorType.INVALID_STATE)
                            .build());
                    continue;
                }

                validEntities.add(materialTypeEntity);
                resultBuilder.addSuccess(code);
            } catch (Exception e) {
                log.error("批量删除物料类型验证失败，编码：{}，错误：{}", code, e.getMessage(), e);
                resultBuilder.addFailure(MaterialTypeBatchDeleteResultVO.MaterialTypeBatchDeleteFailureVO.builder()
                        .materialTypeCode(code)
                        .failureReason(e.getMessage())
                        .errorType(BatchOperationErrorType.SYSTEM_ERROR)
                        .build());
            }
        }

        // 如果有失败项，抛出异常触发事务回滚
        if (resultBuilder.getFailedCount() > 0) {
            MaterialTypeBatchDeleteResultVO result = JacksonUtil.convertObject(resultBuilder, MaterialTypeBatchDeleteResultVO.class);
            throw new BatchOperationException("批量删除物料类型操作失败，存在" + resultBuilder.getFailedCount() + "个失败项", result);
        }

        // 第二阶段：执行所有删除操作
        for (DictEntity entity : validEntities) {
            try {
                removeMaterialTypeById(entity.getId());
            } catch (Exception e) {
                log.error("批量删除物料类型执行失败，编码：{}，错误：{}", entity.getCode(), e.getMessage(), e);
                // 执行阶段出现异常，直接抛出触发回滚
                throw new RuntimeException("批量删除物料类型执行失败，编码：" + entity.getCode() + "，错误：" + e.getMessage(), e);
            }
        }

        return JacksonUtil.convertObject(resultBuilder, MaterialTypeBatchDeleteResultVO.class);
    }

    @Override
    public MaterialTypeVO getMaterialTypeDetail(MaterialTypeDetailQueryDTO dto) {
        DictEntity materialTypeEntity = null;
        // 优先使用编码查询
        if (StringUtils.isNotBlank(dto.getMaterialTypeCode())) {
            materialTypeEntity = this.lambdaQuery()
                    .eq(DictEntity::getCode, dto.getMaterialTypeCode())
                    .eq(DictEntity::getType, DictTypeEnum.MATERIAL_TYPE.getType())
                    .one();
        } else if (dto.getMaterialTypeId() != null) {
            // 使用ID查询
            materialTypeEntity = this.lambdaQuery()
                    .eq(DictEntity::getId, dto.getMaterialTypeId())
                    .eq(DictEntity::getType, DictTypeEnum.MATERIAL_TYPE.getType())
                    .one();
        }
        if (materialTypeEntity == null) {
            return null;
        }
        CraftService craftService = SpringUtil.getBean(CraftService.class);
        return getMaterialTypeVO(materialTypeEntity, craftService);
    }

    /**
     * 组装对象
     */
    private MaterialTypeVO getMaterialTypeVO(DictEntity materialTypeEntity, CraftService craftService) {
        List<MaterialTypeVO.DefaultCraft> defaultCrafts = new ArrayList<>();
        if (StringUtils.isNotBlank(materialTypeEntity.getValue())) {
            List<Integer> craftIds = Arrays.stream(materialTypeEntity.getValue().split(Constants.SEP)).map(Integer::valueOf).collect(Collectors.toList());
            defaultCrafts = craftService.listByIds(craftIds).stream().map(craft ->
                            MaterialTypeVO.DefaultCraft.builder().craftId(craft.getCraftId()).craftCode(craft.getCraftCode()).build())
                    .collect(Collectors.toList());
        }
        return MaterialTypeVO.builder()
                .id(materialTypeEntity.getId())
                .code(materialTypeEntity.getCode())
                .name(materialTypeEntity.getName())
                .des(materialTypeEntity.getDes())
                .unitName(materialTypeEntity.getUrl())
                .isProcessAssembly(materialTypeEntity.getIsProcessAssembly())
                .qualityLevel(materialTypeEntity.getQualityLevel())
                .bindDefaultCrafts(defaultCrafts)
                .build();
    }
}
