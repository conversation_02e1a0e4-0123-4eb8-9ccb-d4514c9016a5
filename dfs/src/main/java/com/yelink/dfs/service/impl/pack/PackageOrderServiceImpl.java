package com.yelink.dfs.service.impl.pack;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.RedisKeyPrefix;
import com.yelink.dfs.entity.code.ProductFlowCodeEntity;
import com.yelink.dfs.entity.code.dto.PackageCodePrintDTO;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.entity.pack.PackageBoxEntity;
import com.yelink.dfs.entity.pack.PackageOrderEntity;
import com.yelink.dfs.entity.pack.PackageOrderFacEntity;
import com.yelink.dfs.entity.pack.PackageSchemeEntity;
import com.yelink.dfs.entity.pack.dto.BatchUpdatePackageOrderDTO;
import com.yelink.dfs.entity.pack.dto.PackageOrderDTO;
import com.yelink.dfs.entity.product.MaterialEntity;
import com.yelink.dfs.entity.product.dto.MaterialSkuEntitySelectDTO;
import com.yelink.dfs.mapper.pack.PackageBoxMapper;
import com.yelink.dfs.mapper.pack.PackageOrderMapper;
import com.yelink.dfs.open.v1.locationMachine.dto.PackageOrderDetailDTO;
import com.yelink.dfs.provider.constant.KafkaMessageTypeEnum;
import com.yelink.dfs.service.approve.config.ApproveConfigService;
import com.yelink.dfs.service.barcode.LabelService;
import com.yelink.dfs.service.code.ProductFlowCodeService;
import com.yelink.dfs.service.common.CommonService;
import com.yelink.dfs.service.model.WorkCenterService;
import com.yelink.dfs.service.order.IsolationService;
import com.yelink.dfs.service.order.RuleSeqService;
import com.yelink.dfs.service.order.WorkOrderService;
import com.yelink.dfs.service.pack.PackageOrderFacService;
import com.yelink.dfs.service.pack.PackageOrderService;
import com.yelink.dfs.service.pack.PackageSchemeService;
import com.yelink.dfs.service.pack.RecordPackageOrderStateService;
import com.yelink.dfs.service.product.MaterialService;
import com.yelink.dfs.service.rule.NumberRuleService;
import com.yelink.dfs.service.user.SysUserService;
import com.yelink.dfs.utils.SpringUtil;
import com.yelink.dfscommon.constant.Constants;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.constant.approve.config.ApprovalStatusEnum;
import com.yelink.dfscommon.constant.approve.config.ApproveModuleEnum;
import com.yelink.dfscommon.constant.dfs.barcode.LabelInfoEnum;
import com.yelink.dfscommon.constant.dfs.code.CodeRelationTypeEnum;
import com.yelink.dfscommon.constant.dfs.pack.PackageOrderRelateTypeEnum;
import com.yelink.dfscommon.constant.dfs.pack.PackageOrderStateEnum;
import com.yelink.dfscommon.constant.dfs.rule.NumberCodeDTO;
import com.yelink.dfscommon.dto.ApproveBatchDTO;
import com.yelink.dfscommon.dto.ImportProgressDTO;
import com.yelink.dfscommon.dto.MaterialCodeAndSkuIdSelectDTO;
import com.yelink.dfscommon.dto.dfs.MaterialSkuDTO;
import com.yelink.dfscommon.dto.dfs.RuleSeqDTO;
import com.yelink.dfscommon.entity.dfs.WorkCenterEntity;
import com.yelink.dfscommon.entity.dfs.barcode.LabelEntity;
import com.yelink.dfscommon.entity.dfs.barcode.PrintDTO;
import com.yelink.dfscommon.entity.dfs.barcode.PrintOptionDTO;
import com.yelink.dfscommon.pojo.Result;
import com.yelink.dfscommon.provider.MessagePushToKafkaService;
import com.yelink.dfscommon.utils.ColumnUtil;
import com.yelink.dfscommon.utils.JacksonUtil;
import com.yelink.dfscommon.utils.MathUtil;
import com.yelink.dfscommon.utils.PrintDataUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Service
 *
 * <AUTHOR>
 * @Date 2021-05-21 14:14
 */
@Service
public class PackageOrderServiceImpl extends ServiceImpl<PackageOrderMapper, PackageOrderEntity> implements PackageOrderService {
    /**
     * 打印时需要替换的占位符 ${ }
     */
    private static final Pattern PATTERN = Pattern.compile("\\$\\{(.*?)\\}");
    @Resource
    private SysUserService sysUserService;
    @Resource
    private MaterialService materialService;
    @Resource
    private ApproveConfigService approveConfigService;
    @Resource
    private MessagePushToKafkaService messagePushToKafkaService;
    @Resource
    private PackageOrderFacService packageOrderFacService;
    @Resource
    private WorkCenterService workCenterService;
    @Resource
    private RecordPackageOrderStateService packageOrderStateService;
    @Resource
    private PackageSchemeService packageSchemeService;
    @Resource
    private IsolationService isolationService;
    @Resource
    private LabelService labelService;
    @Resource
    private ProductFlowCodeService productFlowCodeService;
    @Resource
    private WorkOrderService workOrderService;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private RuleSeqService ruleSeqService;
    @Resource
    private NumberRuleService numberRuleService;
    @Resource
    private PackageBoxMapper packageBoxMapper;
    @Lazy
    @Resource
    private PackageOrderServiceImpl packageOrderService;

    @Override
    public Page<PackageOrderEntity> getList(PackageOrderDTO dto, String username) {
        Page<PackageOrderEntity> page = new Page<>();
        LambdaQueryWrapper<PackageOrderEntity> wrapper = new LambdaQueryWrapper<>();
        // 条件查询
        conditionQuery(dto, wrapper);

        if (username != null) {
            // 数据隔离，只能查询角色绑定工作中心下的包装工单
            Boolean hasPermission = isolationService.packageDataIsolation(username, wrapper);
            if (!hasPermission) {
                throw new ResponseException(RespCodeEnum.NO_WORK_CENTER_PERMISSION);
            }
        }

        if (dto.getCurrent() != null && dto.getSize() != null) {
            page = this.page(new Page<>(dto.getCurrent(), dto.getSize()), wrapper);
        } else {
            List<PackageOrderEntity> list = this.list(wrapper);
            page.setRecords(list).setCurrent(1).setSize(list.size());
        }
        showName(page.getRecords(), dto);
        return page;
    }

    private void conditionQuery(PackageOrderDTO dto, LambdaQueryWrapper<PackageOrderEntity> wrapper) {
        wrapper
                // 审批人
                .eq(StringUtils.isNotEmpty(dto.getApprover()), PackageOrderEntity::getApprover, dto.getApprover())
                // 工单名称
                .like(StringUtils.isNotEmpty(dto.getPackageOrderName()), PackageOrderEntity::getPackageOrderName, dto.getPackageOrderName())
                // 工单号
                .like(StringUtils.isNotEmpty(dto.getPackageOrderNumber()), PackageOrderEntity::getPackageOrderNumber, dto.getPackageOrderNumber())
                .in(!CollectionUtils.isEmpty(dto.getPackageOrderNumbers()), PackageOrderEntity::getPackageOrderNumber, dto.getPackageOrderNumbers())
                // 关联单据
                .like(StringUtils.isNotEmpty(dto.getRelateNumber()), PackageOrderEntity::getRelateNumber, dto.getRelateNumber())
                // 关联单号(精确查询)
                .eq(StringUtils.isNotEmpty(dto.getFullRelateNumber()), PackageOrderEntity::getRelateNumber, dto.getFullRelateNumber())
                // 包装方案编码
                .like(StringUtils.isNotEmpty(dto.getPackageSchemeCode()), PackageOrderEntity::getPackageSchemeCode, dto.getPackageSchemeCode())
                // 备注
                .like(StringUtils.isNotEmpty(dto.getRemark()), PackageOrderEntity::getRemark, dto.getRemark())
                //创建时间
                .between(StringUtils.isNoneBlank(dto.getStartTime(), dto.getEndTime()), PackageOrderEntity::getCreateTime, dto.getStartTime(), dto.getEndTime())
                //修改时间
                .between(StringUtils.isNoneBlank(dto.getUpdateStartTime(), dto.getUpdateEndTime()), PackageOrderEntity::getUpdateTime, dto.getUpdateStartTime(), dto.getUpdateEndTime())
                // 计划开始时间
                .between(StringUtils.isNoneBlank(dto.getPlanStartTime(), dto.getPlanEndTime()), PackageOrderEntity::getStartDate, dto.getPlanStartTime(), dto.getPlanEndTime())
                // 实际开始时间
                .between(StringUtils.isNoneBlank(dto.getActualStartTime(), dto.getActualEndTime()), PackageOrderEntity::getActualStartDate, dto.getActualStartTime(), dto.getActualEndTime())
                // 计划完成时间
                .between(StringUtils.isNoneBlank(dto.getPlanCompleteStartTime(), dto.getPlanCompleteEndTime()), PackageOrderEntity::getEndDate, dto.getPlanCompleteStartTime(), dto.getPlanCompleteEndTime())
                // 实际完成时间
                .between(StringUtils.isNoneBlank(dto.getActualCompleteStartTime(), dto.getActualCompleteEndTime()), PackageOrderEntity::getActualEndDate, dto.getActualCompleteStartTime(), dto.getActualCompleteEndTime())
                // 工单id
                .in(!CollectionUtils.isEmpty(dto.getIds()), PackageOrderEntity::getId, dto.getIds())
                // 工单拓展字段
                .like(StringUtils.isNotEmpty(dto.getPackageOrderExtendFieldOne()), PackageOrderEntity::getPackageOrderExtendFieldOne, dto.getPackageOrderExtendFieldOne())
                .like(StringUtils.isNotEmpty(dto.getPackageOrderExtendFieldTwo()), PackageOrderEntity::getPackageOrderExtendFieldTwo, dto.getPackageOrderExtendFieldTwo())
                .like(StringUtils.isNotEmpty(dto.getPackageOrderExtendFieldThree()), PackageOrderEntity::getPackageOrderExtendFieldThree, dto.getPackageOrderExtendFieldThree())
                .like(StringUtils.isNotEmpty(dto.getPackageOrderExtendFieldFour()), PackageOrderEntity::getPackageOrderExtendFieldFour, dto.getPackageOrderExtendFieldFour())
                .like(StringUtils.isNotEmpty(dto.getPackageOrderExtendFieldFive()), PackageOrderEntity::getPackageOrderExtendFieldFive, dto.getPackageOrderExtendFieldFive())
                .like(StringUtils.isNotEmpty(dto.getPackageOrderExtendFieldSix()), PackageOrderEntity::getPackageOrderExtendFieldSix, dto.getPackageOrderExtendFieldSix())
                .like(StringUtils.isNotEmpty(dto.getPackageOrderExtendFieldSeven()), PackageOrderEntity::getPackageOrderExtendFieldSeven, dto.getPackageOrderExtendFieldSeven())
                .like(StringUtils.isNotEmpty(dto.getPackageOrderExtendFieldEight()), PackageOrderEntity::getPackageOrderExtendFieldEight, dto.getPackageOrderExtendFieldEight())
                .like(StringUtils.isNotEmpty(dto.getPackageOrderExtendFieldNine()), PackageOrderEntity::getPackageOrderExtendFieldNine, dto.getPackageOrderExtendFieldNine())
                .like(StringUtils.isNotEmpty(dto.getPackageOrderExtendFieldTen()), PackageOrderEntity::getPackageOrderExtendFieldTen, dto.getPackageOrderExtendFieldTen())
                .like(StringUtils.isNotEmpty(dto.getPackageOrderMaterialExtendFieldOne()), PackageOrderEntity::getPackageOrderMaterialExtendFieldOne, dto.getPackageOrderMaterialExtendFieldOne())
                .like(StringUtils.isNotEmpty(dto.getPackageOrderMaterialExtendFieldTwo()), PackageOrderEntity::getPackageOrderMaterialExtendFieldTwo, dto.getPackageOrderMaterialExtendFieldTwo())
                .like(StringUtils.isNotEmpty(dto.getPackageOrderMaterialExtendFieldThree()), PackageOrderEntity::getPackageOrderMaterialExtendFieldThree, dto.getPackageOrderMaterialExtendFieldThree())
                .like(StringUtils.isNotEmpty(dto.getPackageOrderMaterialExtendFieldFour()), PackageOrderEntity::getPackageOrderMaterialExtendFieldFour, dto.getPackageOrderMaterialExtendFieldFour())
                .like(StringUtils.isNotEmpty(dto.getPackageOrderMaterialExtendFieldFive()), PackageOrderEntity::getPackageOrderMaterialExtendFieldFive, dto.getPackageOrderMaterialExtendFieldFive())
                .like(StringUtils.isNotEmpty(dto.getPackageOrderMaterialExtendFieldSix()), PackageOrderEntity::getPackageOrderMaterialExtendFieldSix, dto.getPackageOrderMaterialExtendFieldSix())
                .like(StringUtils.isNotEmpty(dto.getPackageOrderMaterialExtendFieldSeven()), PackageOrderEntity::getPackageOrderMaterialExtendFieldSeven, dto.getPackageOrderMaterialExtendFieldSeven())
                .like(StringUtils.isNotEmpty(dto.getPackageOrderMaterialExtendFieldEight()), PackageOrderEntity::getPackageOrderMaterialExtendFieldEight, dto.getPackageOrderMaterialExtendFieldEight())
                .like(StringUtils.isNotEmpty(dto.getPackageOrderMaterialExtendFieldNine()), PackageOrderEntity::getPackageOrderMaterialExtendFieldNine, dto.getPackageOrderMaterialExtendFieldNine())
                .like(StringUtils.isNotEmpty(dto.getPackageOrderMaterialExtendFieldTen()), PackageOrderEntity::getPackageOrderMaterialExtendFieldTen, dto.getPackageOrderMaterialExtendFieldTen())
        ;

        // 生产基本单元ID
        if (StringUtils.isNotBlank(dto.getProductionBasicUnitIds())) {
            List<Integer> splits = Arrays.stream(dto.getProductionBasicUnitIds().split(Constants.SEP)).map(Integer::valueOf).collect(Collectors.toList());
            wrapper.in(PackageOrderEntity::getProductionBasicUnitId, splits);
        }
        // 工作中心id
        if (StringUtils.isNotBlank(dto.getWorkCenterIds())) {
            List<Integer> splits = Arrays.stream(dto.getWorkCenterIds().split(Constants.SEP)).map(Integer::valueOf).collect(Collectors.toList());
            wrapper.in(PackageOrderEntity::getWorkCenterId, splits);
        }
        // 关联类型
        if (StringUtils.isNotEmpty(dto.getRelateTypes())) {
            List<String> relateTypes = Arrays.stream(dto.getRelateTypes().split(Constants.SEP)).collect(Collectors.toList());
            wrapper.in(PackageOrderEntity::getRelateType, relateTypes);
        }
        // 基本生产单元类型
        if (StringUtils.isNotBlank(dto.getWorkCenterType())) {
            List<String> workCenterTypes = Stream.of(dto.getWorkCenterType().split(Constants.SEP)).collect(Collectors.toList());
            wrapper.in(PackageOrderEntity::getWorkCenterType, workCenterTypes);
        }
        // 审批状态
        if (StringUtils.isNotBlank(dto.getApprovalStatus())) {
            List<Integer> approvalStatus = Arrays.stream(dto.getApprovalStatus().split(Constants.SEP)).map(Integer::parseInt).collect(Collectors.toList());
            wrapper.in(PackageOrderEntity::getApprovalStatus, approvalStatus);
        }
        // 多个状态过滤
        if (StringUtils.isNotBlank(dto.getStates())) {
            List<Integer> stateList = Arrays.stream(dto.getStates().split(Constants.SEP)).map(Integer::parseInt).collect(Collectors.toList());
            wrapper.in(PackageOrderEntity::getState, stateList);
        }

        //使用物料条件查询
        MaterialSkuEntitySelectDTO skuSelectDTO = MaterialSkuEntitySelectDTO.builder().materialFields(dto.getMaterialFields())
                .materialSkus(dto.getMaterialSkus()).build();
        MaterialSkuDTO skuDTO = materialService.codesAndSkuIdsByMaterialFields(skuSelectDTO);
        if (skuDTO.getUseConditions()) {
            List<String> materialCodes = skuDTO.getMaterialCodes();
            List<Integer> skuIds = skuDTO.getSkuIds();
            if (CollectionUtils.isEmpty(materialCodes) && CollectionUtils.isEmpty(skuIds)) {
                wrapper.isNull(PackageOrderEntity::getPackageOrderNumber);
            } else {
                wrapper.in(!CollectionUtils.isEmpty(materialCodes), PackageOrderEntity::getMaterialCode, materialCodes);
                wrapper.in(!CollectionUtils.isEmpty(skuIds), PackageOrderEntity::getSkuId, skuIds);
            }
        }
        // 工位ID过滤
        if (StringUtils.isNotEmpty(dto.getFids())) {
            List<Integer> fids = Arrays.stream(dto.getFids().split(Constants.SEP)).map(Integer::parseInt).collect(Collectors.toList());

            List<PackageOrderFacEntity> orderFacs = packageOrderFacService.lambdaQuery().in(PackageOrderFacEntity::getFid, fids).list();
            if (CollectionUtils.isEmpty(orderFacs)) {
                wrapper.isNull(PackageOrderEntity::getId);
            } else {
                List<Integer> ids = orderFacs.stream().map(PackageOrderFacEntity::getPackageOrderId).collect(Collectors.toList());
                wrapper.in(PackageOrderEntity::getId, ids);
            }
        }

        wrapper.orderByDesc(PackageOrderEntity::getCreateTime).orderByDesc(PackageOrderEntity::getId);
    }

    @Override
    public void showName(List<PackageOrderEntity> records, PackageOrderDetailDTO dto) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }
        // 为提升查询效率，如果仅查询简单信息，直接返回
        if (Boolean.TRUE.equals(dto.getIsShowSimpleInfo())) {
            return;
        }
        Set<String> usernameSet = records.stream().map(PackageOrderEntity::getCreateBy).filter(Objects::nonNull).collect(Collectors.toSet());
        usernameSet.addAll(records.stream().map(PackageOrderEntity::getUpdateBy).filter(Objects::nonNull).collect(Collectors.toSet()));
        usernameSet.addAll(records.stream().map(PackageOrderEntity::getApprover).filter(Objects::nonNull).collect(Collectors.toSet()));
        usernameSet.addAll(records.stream().map(PackageOrderEntity::getActualApprover).filter(Objects::nonNull).collect(Collectors.toSet()));
        Map<String, String> userNameNickMap = sysUserService.getUserNameNickMap(new ArrayList<>(usernameSet));
        Map<String, String> signatureUrlMap = sysUserService.getSignatureUrlMap(new ArrayList<>(usernameSet));
        // 获取物料相关字段
        List<MaterialCodeAndSkuIdSelectDTO> codeAndSkuIds = records.stream()
                .map(res -> MaterialCodeAndSkuIdSelectDTO.builder().materialCode(res.getMaterialCode()).skuId(res.getSkuId()).build())
                .collect(Collectors.toList());
        List<MaterialEntity> materials = materialService.listSimpleMaterialByCodesAndSkuIds(codeAndSkuIds);
        Map<String, MaterialEntity> keyMaterialMap = materials.stream().collect(Collectors.toMap(material -> ColumnUtil.getMaterialSku(material.getCode(), material.getSkuEntity()), v -> v));
        // 包装方案
        List<String> packageSchemeCodes = records.stream().map(PackageOrderEntity::getPackageSchemeCode).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        Map<String, PackageSchemeEntity> packageSchemeMap = packageSchemeService.getDetailByCodes(packageSchemeCodes)
                .stream().collect(Collectors.toMap(PackageSchemeEntity::getSchemeCode, v -> v));
        // 工位信息
        List<Integer> ids = records.stream().map(PackageOrderEntity::getId).collect(Collectors.toList());
        Map<Integer, List<PackageOrderFacEntity>> facMap = packageOrderFacService.getList(ids)
                .stream().collect(Collectors.groupingBy(PackageOrderFacEntity::getPackageOrderId));
        for (PackageOrderEntity entity : records) {
            MaterialEntity materialEntity = keyMaterialMap.get(ColumnUtil.getMaterialSku(entity.getMaterialCode(), entity.getSkuId()));
            entity.setMaterialFields(materialEntity);
            // 展示状态名称
            entity.setStateName(PackageOrderStateEnum.getNameByCode(entity.getState()));
            // 关联类型名称
            entity.setRelateTypeName(PackageOrderRelateTypeEnum.getNameByCode(entity.getRelateType()));
            // 展示人名
            entity.setCreateByName(userNameNickMap.getOrDefault(entity.getCreateBy(), entity.getCreateBy()));
            entity.setUpdateByName(userNameNickMap.getOrDefault(entity.getUpdateBy(), entity.getUpdateBy()));
            entity.setApproverName(userNameNickMap.getOrDefault(entity.getApprover(), entity.getApprover()));
            entity.setActualApproverName(userNameNickMap.getOrDefault(entity.getActualApprover(), entity.getActualApprover()));
            entity.setActualApproverSignatureUrl(signatureUrlMap.get(entity.getActualApprover()));
            // 展示审核状态名称
            entity.setApprovalStatusName(ApprovalStatusEnum.getNameByCode(entity.getApprovalStatus()));
            // 包装方案
            entity.setPackageSchemeEntity(packageSchemeMap.get(entity.getPackageSchemeCode()));
            // 工位信息
            List<PackageOrderFacEntity> orderFacEntities = facMap.getOrDefault(entity.getId(), Collections.emptyList());
            entity.setOrderFacEntities(orderFacEntities);
            entity.setFids(orderFacEntities.stream().map(PackageOrderFacEntity::getFid).map(String::valueOf).collect(Collectors.joining(Constants.SEP)));
            entity.setFnames(orderFacEntities.stream().map(PackageOrderFacEntity::getFname).collect(Collectors.joining(Constants.SEP)));
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public PackageOrderEntity addEntity(PackageOrderEntity entity) {
        // 如果传入的编码为空，则由编码规则生成
        if (StringUtils.isBlank(entity.getPackageOrderNumber())) {
            if (Objects.isNull(entity.getNumberRuleId())) {
                throw new ResponseException(RespCodeEnum.NEED_NUMBER_RULE);
            }
            NumberCodeDTO seqById = numberRuleService.getSeqById(
                    RuleSeqDTO.builder()
                            .id(entity.getNumberRuleId())
                            .build()
            );
            entity.setPackageOrderNumber(seqById.getCode());
        }
        //工单编号判断去重
        notRepeat(entity.getPackageOrderNumber());
        if (StringUtils.isBlank(entity.getPackageOrderName())) {
            entity.setPackageOrderName(entity.getPackageOrderNumber());
        }
        entity.setId(null);
        entity.setState(PackageOrderStateEnum.CREATED.getCode());
        entity.setFinishCount(0.0);
        entity.setProgress(0.0);
        Date date = new Date();
        entity.setCreateTime(date);
        entity.setUpdateTime(date);
        // 如果需要审批，则缺省为待审核状态
        if (approveConfigService.getConfigByCode(ApproveModuleEnum.PACKAGE_ORDER.getCode())) {
            entity.setApprovalStatus(ApprovalStatusEnum.TO_BE_APPROVAL.getCode());
        }
        // 设置隔离ID
        setWorkCenterAndIsolationId(entity);
        this.save(entity);
        // 如调用到编码规则，则相对应的所有序列号加1
        if (Objects.nonNull(entity.getNumberRuleId())) {
            ruleSeqService.updateSeqEntity(null, entity.getNumberRuleId());
        }
        // 保存工单工位关联信息
        packageOrderFacService.add(entity);
        // 插入状态
        packageOrderStateService.save(entity);
        // 推送消息
        messagePushToKafkaService.pushNewMessage(entity, Constants.KAFKA_VALUE_CHAIN_TOPIC, KafkaMessageTypeEnum.PACKAGE_ORDER_ADD_MESSAGE);
        return entity;
    }

    /**
     * 判断编号是否重复
     */
    private void notRepeat(String packageOrderNumber) {
        //查询编号是否存在
        Long count = this.lambdaQuery().eq(PackageOrderEntity::getPackageOrderNumber, packageOrderNumber).count();
        if (count > 0) {
            throw new ResponseException(RespCodeEnum.WORK_ORDER_CODE_DUPLICATE.getMsgDes());
        }
    }

    /**
     * 设置生产基本单元id和隔离ID和隔离ID，方便后续过滤查询
     *
     * @param entity
     */
    private void setWorkCenterAndIsolationId(PackageOrderEntity entity) {
        Integer workCenterId = entity.getWorkCenterId();
        if (workCenterId == null) {
            throw new ResponseException("工作中心不能为空");
        }
        WorkCenterEntity workCenterEntity = workCenterService.getById(workCenterId);
        entity.setWorkCenterName(workCenterEntity.getName());
        entity.setWorkCenterType(workCenterEntity.getType());
        // 工作中心ID+“-”+生产基本单元id
        String isolationId = workCenterId.toString();
        if (entity.getProductionBasicUnitId() != null) {
            isolationId = isolationId + Constants.CROSSBAR + entity.getProductionBasicUnitId();
        }
        entity.setIsolationId(isolationId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public PackageOrderEntity updateEntity(PackageOrderEntity entity) {
        if (StringUtils.isBlank(entity.getPackageOrderName())) {
            entity.setPackageOrderName(entity.getPackageOrderNumber());
        }
        // 不能使用getById()，有缓存，会取到新对象entity，导致新旧对象一样
        PackageOrderEntity old = this.baseMapper.selectByIdNoCache(entity.getId());
        if (old == null) {
            throw new ResponseException(RespCodeEnum.WORK_ORDER_ID_SELECT_FAIL.fmtDes(entity.getId()));
        }
        //如果是驳回状态,修改数据后变更为待审核状态。此处只要是创建状态,状态改为待审批可覆盖这个需求
        if (entity.getState().equals(PackageOrderStateEnum.CREATED.getCode())
                && approveConfigService.getConfigByCode(ApproveModuleEnum.PACKAGE_ORDER.getCode())
                && ApprovalStatusEnum.REJECTED.getCode() == old.getApprovalStatus()) {
            entity.setApprovalStatus(ApprovalStatusEnum.TO_BE_APPROVAL.getCode());
        }
        // 实际开始时间只能刷新一次
        Date actualStartDate = entity.getState().equals(PackageOrderStateEnum.PACKAGING.getCode()) && Objects.isNull(old.getActualStartDate()) ? new Date() : old.getActualStartDate();
        entity.setActualStartDate(actualStartDate);
        entity.setUpdateTime(new Date());
        if (entity.getSkuId() == null) {
            entity.setSkuId(Constants.SKU_ID_DEFAULT_VAL);
        }
        // 设置隔离ID
        setWorkCenterAndIsolationId(entity);
        // 为了防止进度计算有问题，如果计划数量为0，默认赋值为1，此操作仅作为进度计算使用
        double planQuantity = entity.getPlanQuantity() == 0 ? 1 : entity.getPlanQuantity();
        double progress = new BigDecimal(entity.getFinishCount() / planQuantity).setScale(4, BigDecimal.ROUND_HALF_UP).doubleValue();
        entity.setProgress(progress);
        this.updateById(entity);
        // 保存工单工位关联信息
        packageOrderFacService.add(entity);
        // 推送消息
        messagePushToKafkaService.pushNewMessage(entity, Constants.KAFKA_VALUE_CHAIN_TOPIC, KafkaMessageTypeEnum.PACKAGE_ORDER_UPDATE_MESSAGE);
        // 状态变更逻辑
        stateChange(entity, old);
        return entity;
    }

    /**
     * 状态变更逻辑
     *
     * @param newEntity
     * @param oldEntity
     */
    private void stateChange(PackageOrderEntity newEntity, PackageOrderEntity oldEntity) {
        if (oldEntity.getState().equals(newEntity.getState())) {
            return;
        }
        Date actualEndDate = newEntity.getActualEndDate();
        Date date = new Date();
        if (PackageOrderStateEnum.FINISHED.getCode().equals(newEntity.getState())) {
            actualEndDate = date;
        }
        this.lambdaUpdate()
                .eq(PackageOrderEntity::getId, newEntity.getId())
                .set(PackageOrderEntity::getStateChangeTime, date)
                .set(PackageOrderEntity::getActualEndDate, actualEndDate)
                .update();
        // 插入状态
        packageOrderStateService.save(newEntity);
        // 如果状态发生变更，需要发布状态变更消息
        messagePushToKafkaService.pushNewMessage(newEntity, Constants.KAFKA_VALUE_CHAIN_STATE_TOPIC, KafkaMessageTypeEnum.PACKAGE_ORDER_STATUS_CHANGE_MESSAGE);
    }

    @Override
    public PackageOrderEntity getDetailById(Integer id) {
        PackageOrderEntity entity = this.getById(id);
        if (entity == null) {
            throw new ResponseException(RespCodeEnum.WORK_ORDER_ID_SELECT_FAIL.fmtDes(id));
        }
        showName(Stream.of(entity).collect(Collectors.toList()), new PackageOrderDetailDTO());
        return entity;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public PackageOrderEntity deleteById(Integer id) {
        PackageOrderEntity entity = this.getById(id);
        if (!PackageOrderStateEnum.CREATED.getCode().equals(entity.getState())) {
            throw new ResponseException(RespCodeEnum.NOT_ALLOW_TO_REMOVE);
        }
        this.removeById(id);
        // 删除关联工位
        packageOrderFacService.lambdaUpdate().eq(PackageOrderFacEntity::getPackageOrderId, id).remove();
        // 推送删除生产订单的消息
        messagePushToKafkaService.pushNewMessage(entity, Constants.KAFKA_VALUE_CHAIN_TOPIC, KafkaMessageTypeEnum.PACKAGE_ORDER_DELETE_MESSAGE);
        return entity;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public PackageOrderEntity addReleased(PackageOrderEntity entity) {
        // 判断是否需要审批
        if (approveConfigService.getConfigByCode(ApproveModuleEnum.PACKAGE_ORDER.getCode())) {
            throw new ResponseException(RespCodeEnum.ORDER_NEED_TO_APPROVE);
        }
        packageOrderService.addEntity(entity);

        entity.setState(PackageOrderStateEnum.RELEASED.getCode());
        packageOrderService.updateEntity(entity);
        return entity;
    }

    @Override
    public void examineOrApprove(Integer approvalStatus, String approvalSuggestion, Integer id, String username) {
        PackageOrderEntity orderEntity = this.getById(id);

        Integer preApprovalStatus = orderEntity.getApprovalStatus();
        if (Objects.isNull(preApprovalStatus)) {
            preApprovalStatus = orderEntity.getState().equals(PackageOrderStateEnum.CREATED.getCode()) ?
                    ApprovalStatusEnum.TO_BE_APPROVAL.getCode() : ApprovalStatusEnum.REJECTED.getCode();
        }
        //待审批 -》生效
        boolean toApproved = approvalStatus == ApprovalStatusEnum.APPROVED.getCode()
                && preApprovalStatus == ApprovalStatusEnum.TO_BE_APPROVAL.getCode();
        //待审批 -》驳回
        boolean toReject = approvalStatus == ApprovalStatusEnum.REJECTED.getCode()
                && preApprovalStatus == ApprovalStatusEnum.TO_BE_APPROVAL.getCode();
        if (toApproved || toReject) {
            Date now = new Date();
            PackageOrderEntity updateOrder = PackageOrderEntity.builder()
                    .id(id)
                    .approvalStatus(approvalStatus)
                    .approvalSuggestion(approvalSuggestion)
                    .actualApprover(username)
                    .approvalTime(now)
                    .updateBy(username)
                    .updateTime(now)
                    .build();
            if (toApproved) {
                updateOrder.setState(PackageOrderStateEnum.RELEASED.getCode());
            }
            this.updateById(updateOrder);
            PackageOrderEntity detail = getDetailById(id);
            messagePushToKafkaService.pushNewMessage(detail, Constants.KAFKA_VALUE_CHAIN_TOPIC, KafkaMessageTypeEnum.PACKAGE_ORDER_UPDATE_MESSAGE);
            if (!orderEntity.getState().equals(updateOrder.getState())) {
                messagePushToKafkaService.pushNewMessage(detail, Constants.KAFKA_VALUE_CHAIN_STATE_TOPIC, KafkaMessageTypeEnum.PACKAGE_ORDER_STATUS_CHANGE_MESSAGE);
            }
        }
    }

    @Override
    public void approveBatch(ApproveBatchDTO dto) {
        List<PackageOrderEntity> entities = this.listByIds(dto.getIds());
        List<String> approvers = entities.stream().map(PackageOrderEntity::getApprover).distinct().collect(Collectors.toList());
        approvers.remove(dto.getActualApprover());
        if (CollectionUtils.isNotEmpty(approvers)) {
            throw new ResponseException(RespCodeEnum.CURRENT_USER_CAN_ONLY_APPROVED_HIS_OWN);
        }
        List<PackageOrderEntity> list = entities.stream().filter(o -> PackageOrderStateEnum.CREATED.getCode().equals(o.getState()))
                .peek(o -> {
                    if (o.getApprovalStatus() == null) {
                        o.setApprovalStatus(ApprovalStatusEnum.TO_BE_APPROVAL.getCode());
                    }
                })
                .filter(o -> o.getApprovalStatus().equals(ApprovalStatusEnum.TO_BE_APPROVAL.getCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Integer approvalStatus = dto.getApprovalStatus();
        Date date = new Date();
        for (PackageOrderEntity entity : list) {
            Integer preApprovalStatus = entity.getApprovalStatus();
            entity.setApprovalStatus(approvalStatus);
            entity.setApprovalSuggestion(dto.getApprovalSuggestion());
            entity.setActualApprover(dto.getActualApprover());
            entity.setApprovalTime(date);

            PackageOrderEntity detail = getDetailById(entity.getId());
            // 待审批 --> 已审批
            if (preApprovalStatus == ApprovalStatusEnum.TO_BE_APPROVAL.getCode() && approvalStatus == ApprovalStatusEnum.APPROVED.getCode()) {
                entity.setState(PackageOrderStateEnum.RELEASED.getCode());
                messagePushToKafkaService.pushNewMessage(detail, Constants.KAFKA_VALUE_CHAIN_STATE_TOPIC, KafkaMessageTypeEnum.PACKAGE_ORDER_STATUS_CHANGE_MESSAGE);
            }
            messagePushToKafkaService.pushNewMessage(detail, Constants.KAFKA_VALUE_CHAIN_TOPIC, KafkaMessageTypeEnum.PACKAGE_ORDER_UPDATE_MESSAGE);
        }
        packageOrderService.updateBatchById(list);
    }


    /**
     * 包装码打印（包装工单模式）
     *
     * @param packageCodePrintDTO
     * @return
     */
    @Override
    public PrintDTO printPackageCode(PackageCodePrintDTO packageCodePrintDTO) {
        String code = packageCodePrintDTO.getCode();
        Integer ruleId = packageCodePrintDTO.getRuleId();
        String packageOrderNumber = packageCodePrintDTO.getPackageOrderNumber();
        PackageOrderEntity packageOrderEntity = this.lambdaQuery()
                .eq(PackageOrderEntity::getPackageOrderNumber, packageOrderNumber)
                .last("limit 1").one();

        if (Objects.isNull(packageOrderEntity)) {
            throw new ResponseException("包装工单不存在");
        }
        //拿第一个被包装物料作为物料信息
        MaterialEntity materialEntity = materialService.getSimpleMaterialByCode(packageOrderEntity.getMaterialCode());

        // 获取打印模板, 替换打印模板里{ }的数据
        LabelEntity labelEntity = labelService.getById(ruleId);
        if (Objects.isNull(labelEntity)) {
            throw new ResponseException(RespCodeEnum.NOT_FIND_BAR_CODE_RULE);
        }
        PrintDTO printDTO = JSON.parseObject(labelEntity.getContent(), PrintDTO.class);
        List<PrintDTO.ParamDto.Content> printElements = printDTO.getPanels().get(0).getPrintElements();

        for (PrintDTO.ParamDto.Content content : printElements) {
            PrintOptionDTO options = content.getOptions();
            String data = options.getTitle();
            if (StringUtils.isBlank(data)) {
                continue;
            }
            // 替换占位符, 替换值为查询对象的某个属性值
            Matcher matcher = PATTERN.matcher(data);
            while (matcher.find()) {
                String placeholder = matcher.group();
                String placeholderValue = replacePrintData(placeholder, content.getOptions().getSliceDigits(), code, packageOrderEntity, materialEntity);
                data = data.replace(placeholder, placeholderValue);
            }
            content.getOptions().setTitle(data);
            // 字体大小如果为空,则设置默认值
            if (content.getOptions().getFontSize() == null) {
                content.getOptions().setFontSize(9.0);
            }
        }
        //填写打印时间
        LambdaUpdateWrapper<PackageBoxEntity> boxWrapper = new LambdaUpdateWrapper<>();
        boxWrapper.eq(PackageBoxEntity::getBoxNumber, code).set(PackageBoxEntity::getPrintBoxTime, new Date());
        packageBoxMapper.update(null, boxWrapper);
        return labelService.getPrintDtoByOpenApi(printDTO);
    }


    private String replacePrintData(String placeholder, String sliceDigits, String code, PackageOrderEntity order, MaterialEntity materialEntity) {
        String placeholderValue = "";
        if (placeholder.contains(LabelInfoEnum.PACKAGE_ORDER_NUMBER.getCode())) {
            placeholderValue = PrintDataUtils.sliceDigits(order.getPackageOrderNumber(), sliceDigits);
        } else if (placeholder.contains(LabelInfoEnum.PACKAGE_ORDER_NAME.getCode())) {
            placeholderValue = PrintDataUtils.sliceDigits(order.getPackageOrderName(), sliceDigits);
        } else if (placeholder.contains(LabelInfoEnum.PACKAGE_ORDER_EXTEND_FIELD_ONE.getCode())) {
            placeholderValue = PrintDataUtils.sliceDigits(order.getPackageOrderExtendFieldOne(), sliceDigits);
        } else if (placeholder.contains(LabelInfoEnum.PACKAGE_ORDER_EXTEND_FIELD_ONE.getCode())) {
            placeholderValue = PrintDataUtils.sliceDigits(order.getPackageOrderExtendFieldOne(), sliceDigits);
        } else if (placeholder.contains(LabelInfoEnum.PACKAGE_ORDER_EXTEND_FIELD_TWO.getCode())) {
            placeholderValue = PrintDataUtils.sliceDigits(order.getPackageOrderExtendFieldTwo(), sliceDigits);
        } else if (placeholder.contains(LabelInfoEnum.PACKAGE_ORDER_EXTEND_FIELD_THREE.getCode())) {
            placeholderValue = PrintDataUtils.sliceDigits(order.getPackageOrderExtendFieldThree(), sliceDigits);
        } else if (placeholder.contains(LabelInfoEnum.PACKAGE_ORDER_EXTEND_FIELD_FOUR.getCode())) {
            placeholderValue = PrintDataUtils.sliceDigits(order.getPackageOrderExtendFieldFour(), sliceDigits);
        } else if (placeholder.contains(LabelInfoEnum.PACKAGE_ORDER_EXTEND_FIELD_FIVE.getCode())) {
            placeholderValue = PrintDataUtils.sliceDigits(order.getPackageOrderExtendFieldFive(), sliceDigits);
        } else if (placeholder.contains(LabelInfoEnum.PACKAGE_ORDER_EXTEND_FIELD_SIX.getCode())) {
            placeholderValue = PrintDataUtils.sliceDigits(order.getPackageOrderExtendFieldSix(), sliceDigits);
        } else if (placeholder.contains(LabelInfoEnum.PACKAGE_ORDER_EXTEND_FIELD_SEVEN.getCode())) {
            placeholderValue = PrintDataUtils.sliceDigits(order.getPackageOrderExtendFieldSeven(), sliceDigits);
        } else if (placeholder.contains(LabelInfoEnum.PACKAGE_ORDER_EXTEND_FIELD_EIGHT.getCode())) {
            placeholderValue = PrintDataUtils.sliceDigits(order.getPackageOrderExtendFieldEight(), sliceDigits);
        } else if (placeholder.contains(LabelInfoEnum.PACKAGE_ORDER_EXTEND_FIELD_NINE.getCode())) {
            placeholderValue = PrintDataUtils.sliceDigits(order.getPackageOrderExtendFieldNine(), sliceDigits);
        } else if (placeholder.contains(LabelInfoEnum.PACKAGE_ORDER_EXTEND_FIELD_TEN.getCode())) {
            placeholderValue = PrintDataUtils.sliceDigits(order.getPackageOrderExtendFieldTen(), sliceDigits);
        } else if (placeholder.contains(LabelInfoEnum.PACKAGE_CODE.getCode())) {
            placeholderValue = PrintDataUtils.sliceDigits(code, sliceDigits);
        } else if (placeholder.contains(LabelInfoEnum.MATERIAL_CODE.getCode())) {
            placeholderValue = PrintDataUtils.sliceDigits(materialEntity.getCode(), sliceDigits);
        } else if (placeholder.contains(LabelInfoEnum.MATERIAL_NAME.getCode())) {
            placeholderValue = PrintDataUtils.sliceDigits(materialEntity.getName(), sliceDigits);
        } else if (placeholder.contains(LabelInfoEnum.MATERIAL_STANDARD.getCode())) {
            placeholderValue = PrintDataUtils.sliceDigits(materialEntity.getStandard(), sliceDigits);
        } else if (placeholder.contains(LabelInfoEnum.CUSTOM_FIELD_ONE.getCode())) {
            placeholderValue = PrintDataUtils.sliceDigits(String.valueOf(materialEntity.getCustomFieldOne()), sliceDigits);
        } else if (placeholder.contains(LabelInfoEnum.CUSTOM_FIELD_TWO.getCode())) {
            placeholderValue = PrintDataUtils.sliceDigits(String.valueOf(materialEntity.getCustomFieldTwo()), sliceDigits);
        } else if (placeholder.contains(LabelInfoEnum.CUSTOM_FIELD_THREE.getCode())) {
            placeholderValue = PrintDataUtils.sliceDigits(String.valueOf(materialEntity.getCustomFieldThree()), sliceDigits);
        } else if (placeholder.contains(LabelInfoEnum.CUSTOM_FIELD_FOUR.getCode())) {
            placeholderValue = PrintDataUtils.sliceDigits(String.valueOf(materialEntity.getCustomFieldFour()), sliceDigits);
        } else if (placeholder.contains(LabelInfoEnum.CUSTOM_FIELD_FIVE.getCode())) {
            placeholderValue = PrintDataUtils.sliceDigits(String.valueOf(materialEntity.getCustomFieldFive()), sliceDigits);
        } else if (placeholder.contains(LabelInfoEnum.CUSTOM_FIELD_SIX.getCode())) {
            placeholderValue = PrintDataUtils.sliceDigits(String.valueOf(materialEntity.getCustomFieldSix()), sliceDigits);
        } else if (placeholder.contains(LabelInfoEnum.CUSTOM_FIELD_SEVEN.getCode())) {
            placeholderValue = PrintDataUtils.sliceDigits(String.valueOf(materialEntity.getCustomFieldSeven()), sliceDigits);
        } else if (placeholder.contains(LabelInfoEnum.CUSTOM_FIELD_EIGHT.getCode())) {
            placeholderValue = PrintDataUtils.sliceDigits(String.valueOf(materialEntity.getCustomFieldEight()), sliceDigits);
        } else if (placeholder.contains(LabelInfoEnum.CUSTOM_FIELD_NINE.getCode())) {
            placeholderValue = PrintDataUtils.sliceDigits(String.valueOf(materialEntity.getCustomFieldNine()), sliceDigits);
        } else if (placeholder.contains(LabelInfoEnum.CUSTOM_FIELD_TEN.getCode())) {
            placeholderValue = PrintDataUtils.sliceDigits(String.valueOf(materialEntity.getCustomFieldTen()), sliceDigits);
        }
        return placeholderValue;
    }

    /**
     * 获取工单简单记录
     *
     * @param packageOrderNumber
     * @return
     */
    @Override
    public PackageOrderEntity getSimpleEntity(String packageOrderNumber) {
        return this.lambdaQuery().eq(PackageOrderEntity::getPackageOrderNumber, packageOrderNumber).one();
    }

    @Override
    public List<PackageOrderEntity> getOrderList(PackageOrderDTO dto) {
        LambdaQueryWrapper<PackageOrderEntity> wrapper = new LambdaQueryWrapper<>();
        // 条件查询
        conditionQuery(dto, wrapper);
        List<PackageOrderEntity> list = this.list(wrapper);
        showName(list, dto);
        return list;
    }

    @Override
    public List<PackageOrderEntity> getPackageOrderByCode(String code) {
        ProductFlowCodeEntity codeEntity = productFlowCodeService.getByProductFlowCode(code);
        if (codeEntity == null) {
            throw new ResponseException("流水码" + code + "无法解析");
        }
        String relationNumber = codeEntity.getRelationNumber();
        // 包装工单
        if (CodeRelationTypeEnum.PACKAGE_ORDER.getCode().equals(codeEntity.getRelationType())) {
            PackageOrderEntity packageOrderEntity = this.getSimpleEntity(relationNumber);
            if (packageOrderEntity == null) {
                throw new ResponseException("条码对应包装工单不存在");
            }
            return Stream.of(packageOrderEntity).collect(Collectors.toList());
        } else if (CodeRelationTypeEnum.PRODUCT_ORDER.getCode().equals(codeEntity.getRelationType())) {
            // 订单流水码
            PackageOrderDTO dto = PackageOrderDTO.builder().fullRelateNumber(relationNumber)
                    .relateTypes(PackageOrderRelateTypeEnum.PRODUCT_ORDER.getCode())
                    .states(PackageOrderStateEnum.PACKAGING.getCode().toString())
                    .isShowSimpleInfo(true)
                    .build();
            List<PackageOrderEntity> list = this.getOrderList(dto);
            if (CollectionUtils.isEmpty(list)) {
                throw new ResponseException("条码对应包装工单不存在");
            }
            return list;
        } else {
            WorkOrderEntity workOrderEntity = workOrderService.getSimpleWorkOrderByNumber(relationNumber);
            if (workOrderEntity == null) {
                throw new ResponseException("条码对应工单不存在");
            }
            if (StringUtils.isBlank(workOrderEntity.getProductOrderNumber())) {
                throw new ResponseException("条码对应工单未关联生产订单");
            }
            PackageOrderDTO dto = PackageOrderDTO.builder().fullRelateNumber(workOrderEntity.getProductOrderNumber())
                    .relateTypes(PackageOrderRelateTypeEnum.PRODUCT_ORDER.getCode())
                    .states(PackageOrderStateEnum.PACKAGING.getCode().toString())
                    .isShowSimpleInfo(true)
                    .build();
            List<PackageOrderEntity> list = this.getOrderList(dto);
            if (CollectionUtils.isEmpty(list)) {
                throw new ResponseException("条码对应包装工单不存在");
            }
            return list;
        }
    }

    @Override
    public void updatePackageCount(String packageOrderNumber, Double finishCount) {
        PackageOrderEntity entity = this.getSimpleEntity(packageOrderNumber);
        entity.setFinishCount(finishCount);
        // 为了防止进度计算有问题，如果计划数量为0，默认赋值为1，此操作仅作为进度计算使用
        double planQuantity = entity.getPlanQuantity() == 0 ? 1 : entity.getPlanQuantity();
        double progress = new BigDecimal(entity.getFinishCount() / planQuantity).setScale(4, BigDecimal.ROUND_HALF_UP).doubleValue();
        entity.setProgress(progress);
        this.updateById(entity);
    }

    @Async
    @Override
    public void batchUpdate(BatchUpdatePackageOrderDTO dto) {
        // 初始化处理进度
        ImportProgressDTO build = ImportProgressDTO.builder()
                .code(Result.SUCCESS_CODE)
                .progress(Double.valueOf(Constant.ZERO_VALUE))
                .executionDescription("正在处理中，请耐心等候...")
                .executionStatus(false)
                .build();
        redisTemplate.opsForValue().set(RedisKeyPrefix.PACKAGE_ORDER_BATCH_EDIT_TASK, JacksonUtil.toJSONString(build), 1, TimeUnit.HOURS);
        Integer state = dto.getState();
        String username = dto.getUsername();
        List<PackageOrderEntity> list = dto.getList();
        // 基本生产单元判定
        checkWorkCenter(list);
        int rowTotal = list.size();
        Date date = new Date();
        try {
            for (int i = 0; i < list.size(); i++) {
                PackageOrderEntity entity = this.getDetailById(list.get(i).getId());
                if (PackageOrderStateEnum.CANCELED.getCode().equals(entity.getState())) {
                    throw new ResponseException("取消的工单无法编辑");
                }
                // 如果审批状态开启，不能直接从创建 --> 非 (创建 | 取消)
                if (PackageOrderStateEnum.CREATED.getCode().equals(entity.getState()) &&
                        !(PackageOrderStateEnum.CREATED.getCode().equals(state) || PackageOrderStateEnum.CANCELED.getCode().equals(state)) &&
                        approveConfigService.getConfigByCode(ApproveModuleEnum.PACKAGE_ORDER.getCode())) {
                    throw new ResponseException("已开启审批功能，需审批才能变更为该状态");
                }

                // 判断字段合法性
                judgeFieldLegality(state, entity);

                // 更新单据状态
                entity.setState(state);
                entity.setUpdateBy(username);
                entity.setUpdateTime(date);
                entity.setApprover(StringUtils.isNotBlank(entity.getApprover()) ? entity.getApprover() : username);

                // 生产基本单元
                if (Objects.nonNull(dto.getProductionBasicUnitId()) && StringUtils.isNotBlank(dto.getProductionBasicUnitName())) {
                    entity.setProductionBasicUnitId(dto.getProductionBasicUnitId());
                    entity.setProductionBasicUnitName(dto.getProductionBasicUnitName());
                }
                // 计划数量
                if (dto.getPlanQuantity() != null) {
                    if (dto.getPlanQuantity() < 0) {
                        throw new ResponseException("计划数量需不可以小于0");
                    }
                    entity.setPlanQuantity(dto.getPlanQuantity());
                }
                // 计划时间
                if (dto.getStartDate() != null) {
                    Date endDate = dto.getEndDate() != null ? dto.getEndDate() : entity.getEndDate();
                    if (endDate != null && endDate.before(dto.getStartDate())) {
                        throw new ResponseException("包装工单:" + entity.getPackageOrderNumber() + " 结束时间需大于开始时间");
                    }
                    entity.setStartDate(dto.getStartDate());
                }
                if (dto.getEndDate() != null) {
                    Date startDate = dto.getStartDate() != null ? dto.getStartDate() : entity.getStartDate();
                    if (startDate != null && startDate.after(dto.getEndDate())) {
                        throw new ResponseException("包装工单:" + entity.getPackageOrderNumber() + " 开始时间需小于结束时间");
                    }
                    entity.setEndDate(dto.getEndDate());
                }
                // 更新
                packageOrderService.updateEntity(entity);
                // 处理进度
                Double processPercent = MathUtil.divideDouble(i + 1, rowTotal, 2);
                boolean finish = processPercent.compareTo(1.0) == 0;
                build = ImportProgressDTO.builder()
                        .code(Result.SUCCESS_CODE)
                        .progress(processPercent)
                        .executionDescription(finish ? "处理完成!" : "正在处理中，请耐心等候...")
                        .executionStatus(finish)
                        .importUrl(null)
                        .build();
                redisTemplate.opsForValue().set(RedisKeyPrefix.PACKAGE_ORDER_BATCH_EDIT_TASK, JacksonUtil.toJSONString(build), 2, TimeUnit.HOURS);
            }
        } catch (Exception e) {
            CommonService commonService = SpringUtil.getBean(CommonService.class);
            build = commonService.getImportProgressDTO(RedisKeyPrefix.PACKAGE_ORDER_BATCH_EDIT_TASK, e);
            redisTemplate.opsForValue().set(RedisKeyPrefix.PACKAGE_ORDER_BATCH_EDIT_TASK, JacksonUtil.toJSONString(build), 2, TimeUnit.HOURS);
            // 手动触发事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
    }

    private void judgeFieldLegality(Integer state, PackageOrderEntity entity) {
        String packageOrderNumber = entity.getPackageOrderNumber();
        // 创建 --> 生效
        if (state.equals(PackageOrderStateEnum.RELEASED.getCode()) && entity.getState().equals(PackageOrderStateEnum.CREATED.getCode())) {
            // 校验字段是否为空
            if (entity.getStartDate() == null) {
                throw new ResponseException("工单号:" + packageOrderNumber + "计划开始时间为空");
            }
            if (entity.getEndDate() == null) {
                throw new ResponseException("工单号:" + packageOrderNumber + "计划结束时间为空");
            }
            if (entity.getWorkCenterId() == null) {
                throw new ResponseException("工单号:" + packageOrderNumber + "关联工作中心为空");
            }
        }
        // 生效 --> 投产
        if (state.equals(PackageOrderStateEnum.PACKAGING.getCode()) && entity.getState().equals(PackageOrderStateEnum.RELEASED.getCode())) {
            // 实际开始时间为空则赋值当前时间
            if (entity.getActualStartDate() == null) {
                entity.setActualStartDate(new Date());
            }
        }
        // 完成
        if (state.equals(PackageOrderStateEnum.FINISHED.getCode())) {
            // 校验字段是否为空
            if (entity.getFinishCount() == null) {
                throw new ResponseException("工单号:" + packageOrderNumber + "完成数量为空");
            }
            // 实际完成时间为空则赋值当前时间
            if (entity.getActualEndDate() == null) {
                entity.setActualEndDate(new Date());
            }
        }
    }

    private void checkWorkCenter(List<PackageOrderEntity> list) {
        String noWorkCenterOrder = list.stream().filter(e -> e.getWorkCenterId() == null).map(PackageOrderEntity::getPackageOrderNumber).collect(Collectors.joining(Constant.SEP));
        if (StringUtils.isNotEmpty(noWorkCenterOrder)) {
            throw new ResponseException("存在包装工单的工作中心为空:" + noWorkCenterOrder);
        }
        Map<Integer, List<PackageOrderEntity>> workCenterIdGroup = list.stream().collect(Collectors.groupingBy(PackageOrderEntity::getWorkCenterId));
        if (workCenterIdGroup.size() != 1) {
            throw new ResponseException("修改基本生产单元时, 所有包装工单的工作中心需保持一致");
        }
    }

    @Override
    public String getBatchUpdateProgress() {
        return (String) redisTemplate.opsForValue().get(RedisKeyPrefix.PACKAGE_ORDER_BATCH_EDIT_TASK);
    }

}
