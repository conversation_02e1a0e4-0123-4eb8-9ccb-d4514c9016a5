package com.yelink.dfs.service.impl.device;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yelink.dfs.constant.AppendixTypeEnum;
import com.yelink.dfs.constant.DictTypeEnum;
import com.yelink.dfs.constant.ImportTypeEnum;
import com.yelink.dfs.constant.RedisKeyPrefix;
import com.yelink.dfs.entity.common.ImportDataRecordEntity;
import com.yelink.dfs.entity.common.UploadEntity;
import com.yelink.dfs.entity.device.ProcessAssemblyEntity;
import com.yelink.dfs.entity.device.dto.ProcessAssemblyAppendixImportDTO;
import com.yelink.dfs.entity.device.dto.ProcessAssemblyImportDTO;
import com.yelink.dfs.mapper.device.ProcessAssemblyMapper;
import com.yelink.dfs.provider.FastDfsClientService;
import com.yelink.dfs.service.common.AppendixService;
import com.yelink.dfs.service.common.DictService;
import com.yelink.dfs.service.common.ImportDataRecordService;
import com.yelink.dfs.service.common.ImportProgressService;
import com.yelink.dfs.service.device.ProcessAssemblyService;
import com.yelink.dfs.service.user.SysUserService;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.dto.ImportProgressDTO;
import com.yelink.dfscommon.entity.AppendixEntity;
import com.yelink.dfscommon.entity.common.FileModel;
import com.yelink.dfscommon.entity.dfs.DictEntity;
import com.yelink.dfscommon.utils.EasyExcelUtil;
import com.yelink.dfscommon.utils.ExcelUtil;
import com.yelink.dfscommon.utils.JacksonUtil;
import com.yelink.dfscommon.utils.MathUtil;
import com.yelink.dfscommon.utils.WrapperUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Date 2022-09-16 09:52
 */
@Slf4j
@Service
public class ProcessAssemblyServiceImpl extends ServiceImpl<ProcessAssemblyMapper, ProcessAssemblyEntity> implements ProcessAssemblyService {

    @Resource
    private ImportProgressService importProgressService;
    @Resource
    private ImportDataRecordService importDataRecordService;
    @Resource
    private DictService dictService;
    @Resource
    private SysUserService sysUserService;
    @Resource
    private AppendixService appendixService;
    @Resource
    private FastDfsClientService fastDfsClientService;

    /**
     * 新增工艺装配信息
     *
     * @param processAssemblyEntity
     * @return
     */
    @Override
    public void saveProcessAssembly(ProcessAssemblyEntity processAssemblyEntity) {
        LambdaQueryWrapper<ProcessAssemblyEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProcessAssemblyEntity::getCode, processAssemblyEntity.getCode());
        long count = this.count(queryWrapper);
        if (count > 0) {
            throw new ResponseException(RespCodeEnum.PROCESS_ASSEMBLY_ADD_REPEAT);
        }
        processAssemblyEntity.setCreateTime(new Date());
        processAssemblyEntity.setUpdateTime(new Date());
        this.save(processAssemblyEntity);
        // 保存附件表
        appendixService.addAppendix(processAssemblyEntity.getAppendixEntities(), AppendixTypeEnum.PROCESS_ASSEMBLY_APPENDIX,
                processAssemblyEntity.getId().toString(), processAssemblyEntity.getCode(), processAssemblyEntity.getCreateBy());
    }

    /**
     * 分页查询工艺装配信息
     *
     * @param current
     * @param size
     * @param code
     * @param name
     * @return
     */
    @Override
    public Page<ProcessAssemblyEntity> listPage(Integer current, Integer size, String code, String name) {
        LambdaQueryWrapper<ProcessAssemblyEntity> wrapper = new LambdaQueryWrapper<>();
        WrapperUtil.like(wrapper, ProcessAssemblyEntity::getCode, code);
        WrapperUtil.like(wrapper, ProcessAssemblyEntity::getName, name);
        Page<ProcessAssemblyEntity> page = this.page(new Page<>(current, size), wrapper);
        return page;
    }

    @Override
    public ProcessAssemblyEntity detail(Integer id) {
        ProcessAssemblyEntity entity = this.getById(id);
        showName(Stream.of(entity).collect(Collectors.toList()));
        return entity;
    }

    @Override
    public void update(ProcessAssemblyEntity processAssemblyEntity) {
        this.updateById(processAssemblyEntity);
        appendixService.removeByRelateId(processAssemblyEntity.getId().toString(), AppendixTypeEnum.PROCESS_ASSEMBLY_APPENDIX.getCode());
        // 保存附件表
        appendixService.addAppendix(processAssemblyEntity.getAppendixEntities(), AppendixTypeEnum.PROCESS_ASSEMBLY_APPENDIX,
                processAssemblyEntity.getId().toString(), processAssemblyEntity.getCode(), processAssemblyEntity.getUpdateBy());
    }

    /**
     * 展示名称
     * @param list
     */
    private void showName(List<ProcessAssemblyEntity> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Set<String> usernameSet = list.stream().map(ProcessAssemblyEntity::getCreateBy).filter(Objects::nonNull).collect(Collectors.toSet());
        usernameSet.addAll(list.stream().map(ProcessAssemblyEntity::getUpdateBy).filter(Objects::nonNull).collect(Collectors.toSet()));
        Map<String, String> nickNameMap = sysUserService.getUserNameNickMap(new ArrayList<>(usernameSet));
        for (ProcessAssemblyEntity entity : list) {
            entity.setCreateByName(nickNameMap.get(entity.getCreateBy()));
            entity.setUpdateByName(nickNameMap.get(entity.getUpdateBy()));
            //获取附件
            List<AppendixEntity> appendixEntities = appendixService.listByRelateId(entity.getId().toString(), AppendixTypeEnum.PROCESS_ASSEMBLY_APPENDIX.getCode());
            entity.setAppendixEntities(appendixEntities);
        }
    }

    @Async
    @Override
    public void sycImportData(String fileName, InputStream inputStream, String operationUsername, String importProgressKey) {
        final String lockKey = RedisKeyPrefix.PROCESS_ASSEMBLY_IMPORT_LOCK;
        importProgressKey = RedisKeyPrefix.PROCESS_ASSEMBLY_IMPORT_PROGRESS;
        //初始化锁 + 进度
        importProgressService.initLockProgress(lockKey, importProgressKey);
        try {
            //1、读取excel第二个sheet,从第3行开始读的数据
            List<ProcessAssemblyImportDTO> totalImportRecords = EasyExcelUtil.read(inputStream, ProcessAssemblyImportDTO.class, 1, 2);
            //2、校验数据
            List<ProcessAssemblyImportDTO> canImportRecords = verifyFormat(totalImportRecords);

            //3、验证结果上传文件服务器
            String importUrl = importProgressService.verifyResultToExcelUploadTo(totalImportRecords, ProcessAssemblyImportDTO.class, operationUsername);
            //4、保存导入记录
            int countPass = CollectionUtils.isEmpty(totalImportRecords) ? 0 : (int) totalImportRecords.stream().filter(ProcessAssemblyImportDTO::getVerifyPass).count();
            importDataRecordService.save(ImportDataRecordEntity.builder()
                    .importFileName(fileName)
                    .importType(ImportTypeEnum.PROCESS_ASSEMBLY_IMPORT.getType())
                    .importTypeName(ImportTypeEnum.PROCESS_ASSEMBLY_IMPORT.getTypeName())
                    .createBy(operationUsername)
                    .createTime(new Date())
                    .failNumber(totalImportRecords.size() - countPass)
                    .successNumber(countPass)
                    .importTime(new Date())
                    .logUrl(importUrl)
                    .operator(operationUsername).build());
            //5、保存数据
            int successCount = canImportRecords.size(), failCount = totalImportRecords.size() - canImportRecords.size();
            Date nowDate = new Date();
            // 新增
            List<ProcessAssemblyEntity> list = JacksonUtil.convertArray(canImportRecords, ProcessAssemblyEntity.class);
            for (ProcessAssemblyEntity entity : list) {
                entity.setCreateBy(operationUsername);
                entity.setUpdateBy(operationUsername);
                entity.setUpdateTime(nowDate);
            }
            this.saveBatch(list);
            // 完成
            importProgressService.updateProgress(importProgressKey, importUrl, true, totalImportRecords.size(), successCount, failCount);
        } catch (Exception e) {
            importProgressService.importProgressException(importProgressKey, e);
            log.warn("excel导入数据错误", e);
        } finally {
            // 释放锁
            IOUtils.closeQuietly(inputStream);
            importProgressService.releaseLock(lockKey);
        }
    }

    private List<ProcessAssemblyImportDTO> verifyFormat(List<ProcessAssemblyImportDTO> imports) {
        log.info("校验工装导入数据：{}", JSONObject.toJSONString(imports));

        List<ProcessAssemblyImportDTO> canImports = new ArrayList<>();
        boolean canImport;
        StringBuilder importResult;
        List<String> classifications = Arrays.asList("通用分类", "专用分类");
        for (ProcessAssemblyImportDTO importDTO : imports) {
            importResult = new StringBuilder();
            canImport = true;

            // 工装编号
            if (StringUtils.isBlank(importDTO.getCode())) {
                importResult.append("工装编号不能为空；");
                canImport = false;
            } else {
                Long count = this.lambdaQuery().eq(ProcessAssemblyEntity::getCode, importDTO.getCode()).count();
                if (count > 0) {
                    importResult.append("工装编号已存在；");
                    canImport = false;
                }
            }
            // 工装名称
            if (StringUtils.isBlank(importDTO.getName())) {
                importResult.append("工装名称不能为空；");
                canImport = false;
            }
            // 工装分类
            if (StringUtils.isNotBlank(importDTO.getClassification()) && !classifications.contains(importDTO.getClassification())) {
                importResult.append("工装分类填写有误；");
                canImport = false;
            }
            // 工装类型编码
            if (StringUtils.isBlank(importDTO.getTypeCode())) {
                importResult.append("工装类型编码不能为空；");
                canImport = false;
            } else {
                DictEntity dictEntity = dictService.lambdaQuery().eq(DictEntity::getCode, importDTO.getTypeCode())
                        .eq(DictEntity::getType, DictTypeEnum.PROCESS_ASSEMBLY.getType()).one();
                if (dictEntity == null) {
                    importResult.append("工装类型在系统中不存在；");
                    canImport = false;
                } else {
                    if (StringUtils.isNotBlank(importDTO.getType()) && !importDTO.getType().equals(dictEntity.getName())) {
                        importResult.append("工装类型编码和工装类型名称不匹配；");
                        canImport = false;
                    } else {
                        importDTO.setModelId(dictEntity.getId());
                        importDTO.setType(dictEntity.getName());
                    }
                }
            }
            // 工装名称
            if (StringUtils.isBlank(importDTO.getUnit())) {
                importResult.append("工装单位不能为空；");
                canImport = false;
            }

            importDTO.setVerifyPass(canImport);
            if (Boolean.TRUE.equals(importDTO.getVerifyPass())) {
                canImports.add(importDTO);
                importDTO.setImportResult("数据校验通过");
            } else {
                importDTO.setImportResult(importResult.toString());
            }
        }
        return canImports;
    }

    @Override
    public ImportProgressDTO importProgress() {
        return importProgressService.importProgress(RedisKeyPrefix.PROCESS_ASSEMBLY_IMPORT_PROGRESS);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importAppendixData(List<FileModel> excelList, FileModel excelFile, String username) throws IOException {
        final String lockKey = RedisKeyPrefix.PROCESS_ASSEMBLY_APPENDIX_IMPORT_LOCK, importProgressKey = RedisKeyPrefix.PROCESS_ASSEMBLY_APPENDIX_IMPORT_PROGRESS;
        //初始化锁 + 进度
        importProgressService.initLockProgress(lockKey, importProgressKey);

        ImportProgressDTO build;
        Workbook workbook = null;
        MultipartFile file = excelFile.getFile();
        String filename = file.getOriginalFilename();
        InputStream inputStream = file.getInputStream();
        //读取表格中的数据
        int successNumber = 0;
        try {
            workbook = WorkbookFactory.create(inputStream);
            Sheet sheet = workbook.getSheetAt(0);
            int total = sheet.getPhysicalNumberOfRows() - 1;
            StringBuilder messageBuilder = new StringBuilder();
            for (int i = 1; i <= total; i++) {
                // 读取excel表格 并校验数据查询对应的附件信息
                ProcessAssemblyAppendixImportDTO dto = getList(excelList, sheet.getRow(i));
                //校验导入数据、写入校验结果
                boolean canImport = verifyFileData(excelList, dto, i, messageBuilder);
                //6、保存导入数据
                if (canImport) {
                    successNumber++;
                    saveList(dto, username);
                }
                //保存日志文件
                String url = null;
                if (i >= total) {
                    url = importDataRecordService.writeLogData(messageBuilder.toString(), username, filename,
                            ImportTypeEnum.PROCESS_ASSEMBLY_APPENDIX_IMPORT.getType(), successNumber, total - successNumber);
                }
                Double processPercent = MathUtil.divideDouble(i, total, 2);
                build = ImportProgressDTO.builder()
                        .progress(processPercent)
                        .executionDescription(processPercent.compareTo(1.0) == 0 ? String.format("导入完成：成功%s条；失败%s条", successNumber, total - successNumber) : String.format("正在处理中，已完成%s条；", i))
                        .executionStatus(processPercent.compareTo(1.0) == 0)
                        .importUrl(url)
                        .build();
                importProgressService.updateProgress(importProgressKey, build);
            }
        } catch (Exception e) {
            log.error("excel导入数据错误", e);
            importProgressService.importProgressException(importProgressKey, e);
        } finally {
            // 释放锁
            IOUtils.closeQuietly(workbook);
            IOUtils.closeQuietly(inputStream);
            importProgressService.releaseLock(lockKey);
        }
    }

    /**
     * 读取上传的excel表格 并校验数据查询对应的附件信息
     *
     * @param unzip
     * @param row
     * @return
     */
    private ProcessAssemblyAppendixImportDTO getList(List<FileModel> unzip, Row row) {
        if (row != null) {
            String code = ExcelUtil.getCellStringValue(row.getCell(0));
            String fileName = ExcelUtil.getCellStringValue(row.getCell(1));
            //根据附件名称获取附件
            List<FileModel> filterFile = unzip.stream().filter(fileModel -> fileModel.getFileName().equals(fileName)).collect(Collectors.toList());
            MultipartFile file = null;
            if (!CollectionUtils.isEmpty(filterFile)) {
                file = filterFile.get(0).getFile();
            }
            return ProcessAssemblyAppendixImportDTO.builder()
                    .code(code)
                    .fileName(fileName)
                    .file(file)
                    .build();
        }
        return null;
    }

    /**
     * 校验附件信息
     *
     * @param excelList
     * @param dto
     * @param index
     * @param stringBuilder
     * @return
     */
    private boolean verifyFileData(List<FileModel> excelList, ProcessAssemblyAppendixImportDTO dto, int index, StringBuilder stringBuilder) {
        boolean canImport = true;
        stringBuilder.append(String.format("导入第%s条目标数据;\n\t", index)).append("校验结果：");
        if (dto == null) {
            stringBuilder.append("\n");
            stringBuilder.append("导入结果：").append("NOK；").append("\n");
            return false;
        }
        if (StringUtils.isBlank(dto.getCode())) {
            stringBuilder.append("工装编码字段为空；");
            canImport = false;
        }
        Long count = this.lambdaQuery().eq(ProcessAssemblyEntity::getCode, dto.getCode()).count();
        if (count == 0) {
            stringBuilder.append("工装编码在系统中未找到；");
            canImport = false;
        }
        if (StringUtils.isBlank(dto.getFileName()) || CollectionUtils.isEmpty(excelList.stream().filter(fileModel -> fileModel.getFileName().equals(dto.getFileName())).collect(Collectors.toList()))) {
            stringBuilder.append("工装文件不合法；");
            canImport = false;
        }
        if (dto.getFile() == null) {
            stringBuilder.append("该工装未找到文件；");
            canImport = false;
        }
        stringBuilder.append("\n");
        stringBuilder.append("导入结果：").append(canImport ? "OK；" : "NOK；").append("\n");
        return canImport;
    }

    /**
     * 用户签名保存（增量保存）
     *
     * @param dto
     * @param username
     */
    private void saveList(ProcessAssemblyAppendixImportDTO dto, String username) {
        String code = dto.getCode();
        ProcessAssemblyEntity entity = this.lambdaQuery().eq(ProcessAssemblyEntity::getCode, code).one();
        UploadEntity uploadEntity = fastDfsClientService.uploadFile(dto.getFile(), username);
        // 查询附件表是否之前上传过相同的文件
        AppendixEntity appendixEntity = appendixService.get(dto.getFileName(), entity.getId(), AppendixTypeEnum.PROCESS_ASSEMBLY_APPENDIX.getCode());
        if (appendixEntity != null) {
            if (uploadEntity != null) {
                appendixEntity.setFilePath(uploadEntity.getUrl());
                appendixEntity.setUpdateUser(username);
                appendixEntity.setUpdateTime(new Date());
                appendixService.updateById(appendixEntity);
            }
        } else {
            // 获取上传文件信息
            AppendixEntity build = AppendixEntity.builder()
                    .createTime(new Date())
                    .createUser(username)
                    .isUsed(true)
                    .fileName(dto.getFileName())
                    .filePath(uploadEntity.getUrl())
                    .fileSize(uploadEntity.getSize())
                    .relateId(entity.getId().toString())
                    .relateName(code)
                    .type(AppendixTypeEnum.PROCESS_ASSEMBLY_APPENDIX.getCode())
                    .build();
            appendixService.save(build);
        }
    }

}
