package com.yelink.dfs.service.impl.common.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.TypeReference;
import com.asyncexcel.core.exporter.DynamicExcelPropertyHandler;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.entity.common.ApiTransFormVO;
import com.yelink.dfs.entity.common.ApiTransformTriggerDTO;
import com.yelink.dfs.entity.common.CommonType;
import com.yelink.dfs.entity.product.MaterialConfigFieldEntity;
import com.yelink.dfs.entity.product.MaterialTypeConfigFieldEntity;
import com.yelink.dfs.entity.supplier.SupplierEntity;
import com.yelink.dfs.entity.user.SysFieldPermissionEntity;
import com.yelink.dfs.entity.user.SysRoleEntity;
import com.yelink.dfs.entity.user.SysRoleFieldPermissionEntity;
import com.yelink.dfs.mapper.common.config.FormFieldConfigMapper;
import com.yelink.dfs.mapper.common.config.FormFieldRuleConfigMapper;
import com.yelink.dfs.open.v1.common.dto.FormFieldInsertDTO;
import com.yelink.dfs.open.v2.common.config.dto.ExtendFieldRelationUpsertDTO;
import com.yelink.dfs.open.v2.common.config.dto.FormQueryDTO;
import com.yelink.dfs.open.v2.common.config.vo.FormFieldRuleConfigVO;
import com.yelink.dfs.open.v2.order.vo.WorkOrderDetailVO;
import com.yelink.dfs.service.common.DictService;
import com.yelink.dfs.service.common.config.FieldMappingService;
import com.yelink.dfs.service.common.config.FormConfigService;
import com.yelink.dfs.service.common.config.FormFieldConfigService;
import com.yelink.dfs.service.common.config.FormFieldModuleConfigService;
import com.yelink.dfs.service.common.config.FormFieldRuleConfigService;
import com.yelink.dfs.service.common.config.FormOverallAllowFieldConfigService;
import com.yelink.dfs.service.common.config.FormOverallFieldConfigService;
import com.yelink.dfs.service.common.config.FormOverallFieldMappingConfigService;
import com.yelink.dfs.service.common.config.FormOverallFieldRuleConfigService;
import com.yelink.dfs.service.common.config.FormUpDownStreamRelationshipConfigService;
import com.yelink.dfs.service.product.MaterialConfigFieldService;
import com.yelink.dfs.service.product.MaterialTypeConfigFieldService;
import com.yelink.dfs.service.target.ApiTransformService;
import com.yelink.dfs.service.user.SysFieldPermissionService;
import com.yelink.dfs.service.user.SysRoleFieldPermissionService;
import com.yelink.dfs.service.user.SysRoleService;
import com.yelink.dfs.utils.ExtApiUtil;
import com.yelink.dfs.utils.SpringUtil;
import com.yelink.dfs.utils.SqlUtil;
import com.yelink.dfscommon.constant.Constants;
import com.yelink.dfscommon.constant.DictConstant;
import com.yelink.dfscommon.constant.InputTypeEnum;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.dto.common.config.FormFieldOptionDTO;
import com.yelink.dfscommon.dto.common.config.FormFieldRuleSycnDTO;
import com.yelink.dfscommon.dto.common.config.FormFieldUpdateDTO;
import com.yelink.dfscommon.dto.common.config.FormOverallInsertDTO;
import com.yelink.dfscommon.dto.common.config.FormOverallUpdateDTO;
import com.yelink.dfscommon.dto.common.config.FullRouteCodeDTO;
import com.yelink.dfscommon.dto.common.config.OverallFormFieldDetailVO;
import com.yelink.dfscommon.dto.dfs.FormFieldRuleConfigUpdateDTO;
import com.yelink.dfscommon.entity.CommonTableType;
import com.yelink.dfscommon.entity.dfs.ApiTransformEntity;
import com.yelink.dfscommon.entity.dfs.DictEntity;
import com.yelink.dfscommon.entity.dfs.FieldMappingEntity;
import com.yelink.dfscommon.entity.dfs.FormConfigEntity;
import com.yelink.dfscommon.entity.dfs.FormFieldConfigEntity;
import com.yelink.dfscommon.entity.dfs.FormFieldModuleConfigEntity;
import com.yelink.dfscommon.entity.dfs.FormFieldRuleConfigEntity;
import com.yelink.dfscommon.entity.dfs.FormOverallAllowFieldConfigEntity;
import com.yelink.dfscommon.entity.dfs.FormOverallFieldConfigEntity;
import com.yelink.dfscommon.entity.dfs.FormOverallFieldMappingConfigEntity;
import com.yelink.dfscommon.entity.dfs.FormOverallFieldRuleConfigEntity;
import com.yelink.dfscommon.entity.dfs.FormUpDownStreamRelationshipConfigEntity;
import com.yelink.dfscommon.utils.CommonUtils;
import com.yelink.dfscommon.utils.DateUtil;
import com.yelink.dfscommon.utils.JacksonUtil;
import feign.FeignException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2023-04-19 10:48:30
 */
@Slf4j
@Service
public class FormFieldRuleConfigServiceImpl extends ServiceImpl<FormFieldRuleConfigMapper, FormFieldRuleConfigEntity> implements FormFieldRuleConfigService {

    @Resource
    private DictService dictService;
    @Resource
    private FormFieldConfigService formFieldConfigService;
    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private FormConfigService formConfigService;
    @Resource
    private SysRoleFieldPermissionService roleFieldPermissionService;
    @Resource
    private SysFieldPermissionService fieldPermissionService;
    @Resource
    @Lazy
    private SysRoleService roleService;
    @Resource
    private ApiTransformService apiTransformService;
    @Resource
    private FormOverallFieldConfigService overallFieldConfigService;
    @Resource
    private FormOverallFieldRuleConfigService overallFieldRuleConfigService;
    @Resource
    private FormOverallAllowFieldConfigService overallAllowFieldConfigService;
    @Resource
    private FormOverallFieldMappingConfigService overallFieldMappingConfigService;
    @Resource
    private FormUpDownStreamRelationshipConfigService upDownStreamRelationshipConfigService;
    @Resource
    private FieldMappingService fieldMappingService;
    @Resource
    private MaterialConfigFieldService materialConfigFieldService;
    @Resource
    private FormFieldModuleConfigService formFieldModuleConfigService;
    @Resource
    private DynamicExcelPropertyHandler dynamicExcelPropertyHandler;

    private static String SYS_FIELD_CONSTANT = "sysField";
    private static String CUSTOM_FIELD_CONSTANT = "customField";

    @Override
    public List<FormFieldRuleConfigEntity> detailRuleByFullPathCode(FullRouteCodeDTO dto) {
        // 获取该全路径下的表单字段规则配置
        List<FormFieldRuleConfigEntity> ruleConfigEntities = this.lambdaQuery()
                .eq(StringUtils.isNotBlank(dto.getFullPathCode()), FormFieldRuleConfigEntity::getFullPathCode, dto.getFullPathCode())
                .eq(StringUtils.isNotBlank(dto.getFullRouteCode()), FormFieldRuleConfigEntity::getRoute, dto.getFullRouteCode())
                .list();
        // 查询该角色下不允许展示的敏感字段
        List<String> notAllowDisplayFieldCodes = getNotAllowDisplayFieldCodeByRole(dto);

        // 获取表单字段的系统默认取值配置
        DictEntity sysField = dictService.lambdaQuery().eq(DictEntity::getType, DictConstant.FORM_GLOBAL_CONF)
                .eq(DictEntity::getCode, DictConstant.DEFAULT_FIELD)
                .one();
        List<String> nameFullPathCodes = ruleConfigEntities.stream().map(FormFieldRuleConfigEntity::getFieldNameFullPathCode).distinct().collect(Collectors.toList());
        Map<String, String> sysDefaultFieldAllMap = new HashMap<>();
        Map<String, String> customFieldAllMap = new HashMap<>();
        // 获取自定义字段取值、系统字段取值，填充到对应的规则配置中去
        for (String fullPathCode : nameFullPathCodes) {
            Map<String, String> defaultFieldMap = formFieldConfigService.lambdaQuery().eq(FormFieldConfigEntity::getFullPathCode, fullPathCode)
                    .eq(FormFieldConfigEntity::getTypeCode, sysField.getValue())
                    .list().stream().collect(Collectors.toMap(o -> o.getFullPathCode() + Constant.UNDERLINE + o.getFieldCode() + Constant.UNDERLINE + o.getModuleCode(), FormFieldConfigEntity::getFieldName));
            sysDefaultFieldAllMap.putAll(defaultFieldMap);
            Map<String, String> customFieldMap = formFieldConfigService.lambdaQuery().eq(FormFieldConfigEntity::getFullPathCode, fullPathCode)
                    .eq(FormFieldConfigEntity::getTypeCode, DictConstant.CUSTOM_FIELD)
                    .list().stream().collect(Collectors.toMap(o -> o.getFullPathCode() + Constant.UNDERLINE + o.getFieldCode() + Constant.UNDERLINE + o.getModuleCode(), FormFieldConfigEntity::getFieldName));
            customFieldAllMap.putAll(customFieldMap);
        }
        for (FormFieldRuleConfigEntity ruleConfigEntity : ruleConfigEntities) {
            String key = ruleConfigEntity.getFieldNameFullPathCode() + Constant.UNDERLINE + ruleConfigEntity.getFieldCode() + Constant.UNDERLINE + ruleConfigEntity.getModuleCode();
            ruleConfigEntity.setFieldCustomName(customFieldAllMap.get(key));
            ruleConfigEntity.setFieldDefaultName(sysDefaultFieldAllMap.get(key));
            // 过滤禁用的选项值
            if (StringUtils.isNotBlank(ruleConfigEntity.getOptionValuesType()) && ruleConfigEntity.getOptionValuesType().equals(Constants.TABLE)) {
                List<FormFieldOptionDTO.EnumDTO> enums = JSON.parseArray(ruleConfigEntity.getAllOption(), FormFieldOptionDTO.EnumDTO.class);
                enums = enums.stream().filter(o -> !o.getIsBan()).collect(Collectors.toList());
                ruleConfigEntity.setAllOption(JSON.toJSONString(enums));
            }
            // 角色权限控制字段是否展示
            if (notAllowDisplayFieldCodes.contains(ruleConfigEntity.getFieldCode())) {
                ruleConfigEntity.setIsShow(false);
            }
            // 如果是API，则获取API对象
            if (StringUtils.isNotBlank(ruleConfigEntity.getRelatedApiCode())) {
                ApiTransformEntity apiTransformEntity = apiTransformService.lambdaQuery().eq(ApiTransformEntity::getCode, ruleConfigEntity.getRelatedApiCode()).one();
                ruleConfigEntity.setRelatedApiEntity(apiTransformEntity);
            }
        }
        // 分组排序，重新组装成一个List（即不包含分组，只是排序后的元素列表）
        Map<String, List<FormFieldRuleConfigEntity>> groupedAndSorted = ruleConfigEntities.stream()
                .collect(Collectors.groupingBy(
                        FormFieldRuleConfigEntity::getFullPathCode,
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                list -> list.stream()
                                        .sorted(Comparator.comparingInt(FormFieldRuleConfigEntity::getSort))
                                        .collect(Collectors.toList())
                        )
                ));
        List<FormFieldRuleConfigEntity> flattenedAndSortedList = new ArrayList<>();
        for (Map.Entry<String, List<FormFieldRuleConfigEntity>> entry : groupedAndSorted.entrySet()) {
            flattenedAndSortedList.addAll(entry.getValue());
        }
        return flattenedAndSortedList;
    }

    private List<String> getNotAllowDisplayFieldCodeByRole(FullRouteCodeDTO dto) {
        List<SysRoleEntity> roleEntities = roleService.selectByUsername(dto.getUserName());
        if (CollectionUtils.isEmpty(roleEntities)) {
            return new ArrayList<>();
        }
        List<Integer> roleIds = roleEntities.stream().map(SysRoleEntity::getId).collect(Collectors.toList());
        List<String> permissionUniqueCode = roleFieldPermissionService.lambdaQuery().in(SysRoleFieldPermissionEntity::getRoleId, roleIds)
                .list().stream().map(SysRoleFieldPermissionEntity::getPermissionUniqueCode)
                .collect(Collectors.toList());
        return fieldPermissionService.lambdaQuery()
                .notIn(CollectionUtils.isNotEmpty(permissionUniqueCode), SysFieldPermissionEntity::getUniqueCode, permissionUniqueCode)
                .eq(SysFieldPermissionEntity::getRoute, dto.getFullRouteCode())
                .list().stream().map(SysFieldPermissionEntity::getCode)
                .collect(Collectors.toList());
    }

    /**
     * 设置表单拓展字段名称
     */
    @Override
    public Map<String, String> getExtendFieldNameMap(String fullPathCode) {
        return this.getExtendFieldNameMap(fullPathCode, null);
    }

    /**
     * 设置表单拓展字段名称
     */
    @Override
    public Map<String, String> getExtendFieldNameMap(String fullPathCode, String moduleCode) {
        // 获取表单字段规则配置，用于回显拓展字段的中文名
        List<FormFieldRuleConfigEntity> fieldRuleConfigEntities = this.lambdaQuery()
                .eq(FormFieldRuleConfigEntity::getFullPathCode, fullPathCode)
                .isNotNull(FormFieldRuleConfigEntity::getOptionValuesType)
                .eq(StringUtils.isNotBlank(moduleCode), FormFieldRuleConfigEntity::getModuleCode, moduleCode)
                .list();
        Map fieldRuleConfMap = new HashMap<>();
        for (FormFieldRuleConfigEntity ruleConfigEntity : fieldRuleConfigEntities) {
            // 如果为API，则需要调用接口获取键值，如果为table，则直接从allOption中获取键值
            if (ruleConfigEntity.getOptionValuesType().equals(Constants.API)) {
                try {
                    ApiTransFormVO apiDetail = apiTransformService.detail(ruleConfigEntity.getRelatedApiCode());
                    String url = apiDetail.getUrl();
                    // 如果上次已通过接口获取到数值，下次可直接通过缓存拿数据，避免重复调用第三方接口
                    if (redisTemplate.hasKey(url)) {
                        List<CommonTableType> commonTypes = JSONArray.parseArray((String) redisTemplate.opsForValue().get(url), CommonTableType.class);
                        for (CommonTableType commonType : commonTypes) {
                            fieldRuleConfMap.put(ruleConfigEntity.getFieldCode() + commonType.getValue(), String.valueOf(commonType.getLabel()));
                        }
                    } else {
//                        ExtApiConfig extApiConfig = new ExtApiConfig();
//                        extApiConfig.setService("open");
//                        extApiConfig.setHttpMethod(apiDetail.getRequestMethod().name());
//                        extApiConfig.setPath(url);
//
                        // 根据上下文获取token
                        String token = ExtApiUtil.getTokenBasedOnContext();
//                        ExtRequestReq req = ExtRequestReq.builder().extApiConfig(extApiConfig).token(token).build();
//                        JSONObject res = ExtApiUtil.call(req);
                        Map<String, String> extraReqHeader = new HashMap<>();
                        extraReqHeader.put("Authorization", token);
                        Object trigger = apiTransformService.trigger(ApiTransformTriggerDTO.builder()
                                .id(apiDetail.getId())
//                                .extraReqHeader(extraReqHeader)
                                .build());
                        // 如果为空，则为服务不可用
//                        if (res == null) {
//                            throw new ResponseException(RespCodeEnum.SERVICE_NOT_USE);
//                        }
                        List<CommonTableType> commonTypes = JSONArray.parseObject(JacksonUtil.toJSONString(trigger), new TypeReference<List<CommonTableType>>() {
                        });
                        for (CommonTableType commonType : commonTypes) {
                            fieldRuleConfMap.put(ruleConfigEntity.getFieldCode() + commonType.getValue(), String.valueOf(commonType.getLabel()));
                        }
                        // 设置缓存
                        redisTemplate.opsForValue().set(url, JSON.toJSONString(commonTypes), 20, TimeUnit.SECONDS);
                    }
                } catch (FeignException.Unauthorized e) {
                    // 401未授权说明是服务器内部调用，根据上下文无法获取token
                } catch (Exception e) {
                    // 不对外抛异常，后台仅捕获调用失败原因
                    log.error("调用第三方接口失败: {}", e.getMessage());
                }
            } else if (ruleConfigEntity.getOptionValuesType().equals(Constants.TABLE)) {
                List<CommonTableType> tableTypes = JSON.parseArray(ruleConfigEntity.getAllOption(), CommonTableType.class);
                for (CommonTableType tableType : tableTypes) {
                    fieldRuleConfMap.put(ruleConfigEntity.getFieldCode() + tableType.getValue(), tableType.getLabel());
                }
            }
        }
        return fieldRuleConfMap;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void setOptionConf(FormFieldOptionDTO optionDTO) {
        // 根据类型判断是url模式还是枚举模式
        // 如果为url则直接插入数据
        switch (optionDTO.getType()) {
            case Constants.API:
                // 规避用户输入的url存在空格的情况
                this.lambdaUpdate().eq(FormFieldRuleConfigEntity::getId, optionDTO.getId())
                        .set(FormFieldRuleConfigEntity::getInputType, optionDTO.getInputType())
                        .set(FormFieldRuleConfigEntity::getOptionValuesType, optionDTO.getType())
                        .set(FormFieldRuleConfigEntity::getRelatedApiCode, optionDTO.getRelatedApiCode())
                        .set(FormFieldRuleConfigEntity::getAllOption, null)
                        .set(FormFieldRuleConfigEntity::getFormula, null)
                        .update();
                break;
            case Constants.FORMULA:
                // 为公式的时候，输入类型只能是输入框
                this.lambdaUpdate().eq(FormFieldRuleConfigEntity::getId, optionDTO.getId())
                        .set(FormFieldRuleConfigEntity::getOptionValuesType, optionDTO.getType())
                        .set(FormFieldRuleConfigEntity::getFormula, optionDTO.getFormula())
                        .set(FormFieldRuleConfigEntity::getInputType, InputTypeEnum.INPUT.getType())
                        .set(FormFieldRuleConfigEntity::getSubType, optionDTO.getSubType())
                        .set(FormFieldRuleConfigEntity::getRelatedApiCode, null)
                        .set(FormFieldRuleConfigEntity::getAllOption, null)
                        .update();
                break;
            case Constants.TIME_FORMULA:
                this.lambdaUpdate().eq(FormFieldRuleConfigEntity::getId, optionDTO.getId())
                        .set(FormFieldRuleConfigEntity::getOptionValuesType, optionDTO.getType())
                        .set(FormFieldRuleConfigEntity::getFormula, optionDTO.getFormula())
                        // 输入类型只能是时间选择器
                        .set(FormFieldRuleConfigEntity::getInputType, InputTypeEnum.DATE_TIME_PICKER.getType())
                        .set(FormFieldRuleConfigEntity::getRelatedApiCode, null)
                        .set(FormFieldRuleConfigEntity::getAllOption, null)
                        .update();
                break;
            case Constants.TABLE:
                // 表单选项值字段校验
                fieldJudge(optionDTO);
                // 根据显示顺序排列数据
                List<FormFieldOptionDTO.EnumDTO> orderEnums = optionDTO.getEnums().stream()
                        .peek(o -> o.setIsBan(false))
                        .sorted(Comparator.comparing(FormFieldOptionDTO.EnumDTO::getShowOrder))
                        .collect(Collectors.toList());
                Map<String, FormFieldOptionDTO.EnumDTO> map = orderEnums.stream().collect(Collectors.toMap(FormFieldOptionDTO.EnumDTO::getValue, o -> o));
                // 用户删除数据后仅对删除的数据进行禁用处理，不进行硬删除
                FormFieldRuleConfigEntity fieldRuleConfigEntity = this.getById(optionDTO.getId());
                String allOption = fieldRuleConfigEntity.getAllOption();
                if (StringUtils.isNotBlank(allOption)) {
                    List<FormFieldOptionDTO.EnumDTO> historyEnums = JSON.parseArray(allOption, FormFieldOptionDTO.EnumDTO.class);
                    for (FormFieldOptionDTO.EnumDTO historyEnum : historyEnums) {
                        if (!map.containsKey(historyEnum.getValue())) {
                            historyEnum.setIsBan(true);
                            orderEnums.add(historyEnum);
                        }
                    }
                }

                // 如果为枚举，转换为json存入数据库
                String enums = JSON.toJSONString(orderEnums);
                this.lambdaUpdate().eq(FormFieldRuleConfigEntity::getId, optionDTO.getId())
                        .set(FormFieldRuleConfigEntity::getInputType, optionDTO.getInputType())
                        .set(FormFieldRuleConfigEntity::getOptionValuesType, optionDTO.getType())
                        .set(FormFieldRuleConfigEntity::getAllOption, enums)
                        .set(FormFieldRuleConfigEntity::getRelatedApiCode, null)
                        .set(FormFieldRuleConfigEntity::getFormula, null)
                        .update();
                break;
            default:
                // 人工输入
                this.lambdaUpdate().eq(FormFieldRuleConfigEntity::getId, optionDTO.getId())
                        .set(FormFieldRuleConfigEntity::getInputType, optionDTO.getInputType())
                        .set(FormFieldRuleConfigEntity::getOptionValuesType, null)
                        .set(FormFieldRuleConfigEntity::getAllOption, null)
                        .set(FormFieldRuleConfigEntity::getRelatedApiCode, null)
                        .set(FormFieldRuleConfigEntity::getFormula, null)
                        .update();
                break;
        }

    }

    /**
     * 表单选项值字段校验
     */
    @Override
    public void fieldJudge(FormFieldOptionDTO optionDTO) {
        // 编码和名称都不能重复
        boolean valueHasDuplicates = optionDTO.getEnums().stream()
                .collect(Collectors.groupingBy(FormFieldOptionDTO.EnumDTO::getValue, Collectors.counting()))
                .values().stream().anyMatch(duplicatesCount -> duplicatesCount > 1);
        if (valueHasDuplicates) {
            throw new ResponseException(RespCodeEnum.OPTION_VALUE_VALUE_IS_DUPLICATES);
        }
        boolean nameHasDuplicates = optionDTO.getEnums().stream()
                .collect(Collectors.groupingBy(FormFieldOptionDTO.EnumDTO::getLabel, Collectors.counting()))
                .values().stream().anyMatch(duplicatesCount -> duplicatesCount > 1);
        if (nameHasDuplicates) {
            throw new ResponseException(RespCodeEnum.OPTION_VALUE_LABEL_IS_DUPLICATES);
        }
        // 顺序不能为空
        long count = optionDTO.getEnums().stream().filter(o -> Objects.isNull(o.getShowOrder())).count();
        if (count > 0) {
            throw new ResponseException(RespCodeEnum.OPTION_VALUE_ORDER_NOT_NULL);
        }
        // 顺序不能相同
        boolean orderHasDuplicates = optionDTO.getEnums().stream()
                .collect(Collectors.groupingBy(FormFieldOptionDTO.EnumDTO::getShowOrder, Collectors.counting()))
                .values().stream().anyMatch(duplicatesCount -> duplicatesCount > 1);
        if (orderHasDuplicates) {
            throw new ResponseException(RespCodeEnum.OPTION_VALUE_ORDER_IS_DUPLICATES);
        }
    }

    @Override
    public FormFieldOptionDTO getOptionConf(Integer id) {
        FormFieldRuleConfigEntity ruleConfigEntity = this.getById(id);
        List<FormFieldOptionDTO.EnumDTO> enums = null;
        if (StringUtils.isNotBlank(ruleConfigEntity.getOptionValuesType()) && ruleConfigEntity.getOptionValuesType().equals(Constants.TABLE)) {
            enums = JSON.parseArray(ruleConfigEntity.getAllOption(), FormFieldOptionDTO.EnumDTO.class);
            enums = enums.stream().filter(o -> !o.getIsBan()).collect(Collectors.toList());
        }
        return FormFieldOptionDTO.builder()
                .id(id)
                .inputType(ruleConfigEntity.getInputType())
                .type(ruleConfigEntity.getOptionValuesType())
                .enums(enums)
                .relatedApiCode(ruleConfigEntity.getRelatedApiCode())
                .formula(ruleConfigEntity.getFormula())
                .subType(ruleConfigEntity.getSubType())
                .build();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void sycnOptionConf(FormFieldRuleSycnDTO sycnDTO) {
        // 获取根目录下的所有子目录 （同字段或者字段后缀加Name）的对象
        List<FormFieldRuleConfigEntity> ruleConfigEntities = this.lambdaQuery()
                .eq(FormFieldRuleConfigEntity::getRoute, sycnDTO.getRoute())
                .and(!sycnDTO.getFieldCode().contains("Name"), wrapper -> wrapper.eq(FormFieldRuleConfigEntity::getFieldCode, sycnDTO.getFieldCode()).or()
                        .eq(FormFieldRuleConfigEntity::getFieldCode, sycnDTO.getFieldCode() + "Name"))
                .and(sycnDTO.getFieldCode().contains("Name"), wrapper -> wrapper.eq(FormFieldRuleConfigEntity::getFieldCode, sycnDTO.getFieldCode()).or()
                        .eq(FormFieldRuleConfigEntity::getFieldCode, sycnDTO.getFieldCode().replace("Name", "")))
                .list();
        // 设置可选值
        for (FormFieldRuleConfigEntity ruleConfigEntity : ruleConfigEntities) {
            FormFieldOptionDTO optionDTO = sycnDTO.getOptionDTO();
            optionDTO.setId(ruleConfigEntity.getId());
            setOptionConf(optionDTO);
        }
        // 物料扩展字段比较特殊，需要同步到物料类型配置上去
        if (sycnDTO.getRoute().equals(Constants.FROM_MATERIAL_ROUTE)) {
            materialConfigFieldService.sycnMaterialTypeOptionConf(sycnDTO);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateRuleByFullPathCode(List<FormFieldRuleConfigUpdateDTO> allList) {
        if (CollectionUtils.isEmpty(allList)) {
            return;
        }
        // 过滤掉物料扩展字段
//        List<FormFieldRuleConfigUpdateDTO> list = allList.stream().filter(o -> !Constants.MATERIAL_EXTEND_FIELD.equals(o.getModuleCode())).collect(Collectors.toList());
//        if (CollectionUtils.isEmpty(list)) {
//            return;
//        }
        // 表单字段规则更新列表
        List<FormFieldRuleConfigUpdateDTO> updateList = allList.stream()
                .peek(o -> {
                            // 需要清空数据来源的数据，防止前端传入历史数据进来，导致数据错乱
                            o.setIsOverall(null);
                            o.setFormula(null);
                            o.setOptionValues(null);
                            o.setOptionValuesType(null);
                            o.setAllOption(null);
                            o.setRelatedApiCode(null);
                            o.setInputType(null);
                        }
                )
                .filter(o -> o.getId() != null).collect(Collectors.toList());
        // 表单字段规则更新列表
        updateRules(updateList);
        // 表单字段规则新增列表
        List<FormFieldRuleConfigUpdateDTO> addList = allList.stream().filter(o -> o.getId() == null).collect(Collectors.toList());
        addRules(addList);
        // 清空缓存，用于刷新调用方需要获取的表单数据
        dynamicExcelPropertyHandler.clearCache(allList.get(0).getFieldNameFullPathCode());
    }

    /**
     * 更新
     *
     * @param updateList
     */
    private void updateRules(List<FormFieldRuleConfigUpdateDTO> updateList) {
        if (CollectionUtils.isEmpty(updateList)) {
            return;
        }
        String fieldNameFullPathCode = updateList.get(0).getFieldNameFullPathCode();
        // 先集体置空该页的字段名
        formFieldConfigService.lambdaUpdate()
                .eq(FormFieldConfigEntity::getFullPathCode, fieldNameFullPathCode)
                .eq(FormFieldConfigEntity::getTypeCode, CUSTOM_FIELD_CONSTANT)
                .eq(FormFieldConfigEntity::getModuleCode, updateList.get(0).getModuleCode())
                .set(FormFieldConfigEntity::getFieldName, "")
                .update();
        // 清空该页表单的自定义名称
        List<FormFieldRuleConfigEntity> ruleConfigEntities = JacksonUtil.convertArray(updateList, FormFieldRuleConfigEntity.class);
        // 保存规则
        this.updateBatchById(ruleConfigEntities);
        // 将`是否显示`同步到其他表单上
        syncIsShowToSameForm(fieldNameFullPathCode, ruleConfigEntities);

        // 自定义名称同步更新到字段名配置(重命名)上，方便用户操作
        List<FormFieldUpdateDTO.FieldUpdateDTO> updateDTOS = updateList.stream().map(o ->
                FormFieldUpdateDTO.FieldUpdateDTO.builder()
                        .fieldCode(o.getFieldCode())
                        .fieldName(o.getFieldCustomName())
                        .moduleCode(o.getModuleCode())
                        .build()).collect(Collectors.toList());
        formConfigService.syncSameOrderFieldName(fieldNameFullPathCode, updateDTOS);
        // 如果是物料的表单配置更新，则需要更新到各个单据的`物料基础字段`和`物料扩展字段`上
        dealDataForMaterialFormUpdate(ruleConfigEntities);
    }

    /**
     * 如果是物料的表单配置更新，则需要更新到各个单据的`物料基础字段`上
     */
    private void dealDataForMaterialFormUpdate(List<FormFieldRuleConfigEntity> ruleConfigEntities) {
        List<String> materialFullPathCodes = Stream.of("material.list", "material.edit", "material.detail").collect(Collectors.toList());
        String materialFullPathCode = ruleConfigEntities.get(0).getFieldNameFullPathCode();
        if (!materialFullPathCodes.contains(materialFullPathCode)) {
            return;
        }
        // 同步到价值流单据
        for (FormFieldRuleConfigEntity ruleConfigEntity : ruleConfigEntities) {
            // 物料类型typeName、物料分类sortName、物料编制人editorName，后缀需要加name才能更新
            // 重命名
            String fieldName = StringUtils.isNotBlank(ruleConfigEntity.getFieldCustomName()) ? ruleConfigEntity.getFieldCustomName() : ruleConfigEntity.getFieldDefaultName();
            formFieldConfigService.lambdaUpdate()
                    .and(wrapper -> wrapper.eq(FormFieldConfigEntity::getModuleCode, Constants.MATERIAL_BASE_FIELD)
                            .or()
                            .eq(FormFieldConfigEntity::getModuleCode, Constants.MATERIAL_EXTEND_FIELD))
                    .eq(FormFieldConfigEntity::getFieldCode, ruleConfigEntity.getFieldCode())
                    .eq(FormFieldConfigEntity::getTypeCode, CUSTOM_FIELD_CONSTANT)
                    .set(FormFieldConfigEntity::getFieldName, fieldName)
                    .update();
            formFieldConfigService.lambdaUpdate()
                    .eq(FormFieldConfigEntity::getModuleCode, Constants.MATERIAL_BASE_FIELD)
                    .eq(FormFieldConfigEntity::getFieldCode, ruleConfigEntity.getFieldCode() + "Name")
                    .eq(FormFieldConfigEntity::getTypeCode, CUSTOM_FIELD_CONSTANT)
                    .set(FormFieldConfigEntity::getFieldName, ruleConfigEntity.getFieldCustomName())
                    .update();
            // 控制字段显示隐藏
            this.lambdaUpdate()
                    .and(wrapper -> wrapper.eq(FormFieldRuleConfigEntity::getModuleCode, Constants.MATERIAL_BASE_FIELD)
                            .or()
                            .eq(FormFieldRuleConfigEntity::getModuleCode, Constants.MATERIAL_EXTEND_FIELD))
                    .eq(FormFieldRuleConfigEntity::getFieldCode, ruleConfigEntity.getFieldCode())
                    .ne(FormFieldRuleConfigEntity::getRoute, Constants.FROM_MATERIAL_ROUTE)
                    .set(FormFieldRuleConfigEntity::getIsShow, ruleConfigEntity.getIsShow())
                    // 如果物料基础字段不展示，则价值流单据的物料基础字段要置灰
                    .set(FormFieldRuleConfigEntity::getShowGray, !ruleConfigEntity.getIsShow())
                    .update();
            this.lambdaUpdate()
                    .eq(FormFieldRuleConfigEntity::getModuleCode, Constants.MATERIAL_BASE_FIELD)
                    .eq(FormFieldRuleConfigEntity::getFieldCode, ruleConfigEntity.getFieldCode() + "Name")
                    .set(FormFieldRuleConfigEntity::getIsShow, ruleConfigEntity.getIsShow())
                    // 如果物料基础字段不展示，则价值流单据的物料基础字段要置灰
                    .set(FormFieldRuleConfigEntity::getShowGray, !ruleConfigEntity.getIsShow())
                    .update();
        }
        // 同步更新到物料类型配置字段表
        MaterialTypeConfigFieldService materialTypeConfigFieldService = SpringUtil.getBean(MaterialTypeConfigFieldService.class);
        ruleConfigEntities.forEach(entity -> materialTypeConfigFieldService.lambdaUpdate()
                .eq(MaterialTypeConfigFieldEntity::getFieldCode, entity.getFieldCode())
                .set(MaterialTypeConfigFieldEntity::getFieldName, StringUtils.isNotBlank(entity.getFieldCustomName()) ? entity.getFieldCustomName() : entity.getFieldDefaultName())
                .set(MaterialTypeConfigFieldEntity::getRemark, entity.getRemark())
                .set(MaterialTypeConfigFieldEntity::getIsUsing, entity.getIsShow())
                .set(MaterialTypeConfigFieldEntity::getIsChange, entity.getIsEdit())
                .set(MaterialTypeConfigFieldEntity::getIsNotNull, entity.getIsNeed())
                .update());
        // 物料扩展字段的显示、必填、编辑需同步到物料所有状态中，规则保持一致才不会有歧义
        // 注释原因：创建态的物料编码不能编辑是因为被其他状态的字段配置影响到了，这么写会有问题
//        ruleConfigEntities.forEach(entity -> this.lambdaUpdate()
//                .in(FormFieldRuleConfigEntity::getFieldNameFullPathCode, materialFullPathCodes)
//                .eq(FormFieldRuleConfigEntity::getFieldCode, entity.getFieldCode())
//                .set(FormFieldRuleConfigEntity::getIsShow, entity.getIsShow())
//                .set(FormFieldRuleConfigEntity::getIsEdit, entity.getIsEdit())
//                .set(FormFieldRuleConfigEntity::getIsNeed, entity.getIsNeed())
//                .update());
        // 避免其他接口使用到废弃的物料扩展字段全局配置表，需对其进行兼容
        MaterialConfigFieldService configFieldService = SpringUtil.getBean(MaterialConfigFieldService.class);
        ruleConfigEntities.forEach(entity -> configFieldService.lambdaUpdate()
                .eq(MaterialConfigFieldEntity::getFieldCode, entity.getFieldCode())
                .set(MaterialConfigFieldEntity::getFieldName, StringUtils.isNotBlank(entity.getFieldCustomName()) ? entity.getFieldCustomName() : entity.getFieldDefaultName())
                .set(MaterialConfigFieldEntity::getRemark, entity.getRemark())
                .set(MaterialConfigFieldEntity::getIsUsing, entity.getIsShow())
                .set(MaterialConfigFieldEntity::getIsChange, entity.getIsEdit())
                .set(MaterialConfigFieldEntity::getIsNotNull, entity.getIsNeed())
                .update());
    }

    /**
     * 新增
     *
     * @param addList
     */
    private void addRules(List<FormFieldRuleConfigUpdateDTO> addList) {
        if (CollectionUtils.isEmpty(addList)) {
            return;
        }
        // 查询所有上下级相关联的表单fullPathCode，将新增字段同步到其他表单上
        List<String> allFullPathCodes = formConfigService.getAllFullPathCodes(addList.get(0).getFullPathCode());
        List<FormFieldModuleConfigEntity> moduleConfigEntities = formFieldModuleConfigService.lambdaQuery()
                .select(FormFieldModuleConfigEntity::getFullPathCode, FormFieldModuleConfigEntity::getFieldNameFullPathCode)
                .in(FormFieldModuleConfigEntity::getFullPathCode, allFullPathCodes)
                .eq(FormFieldModuleConfigEntity::getModuleCode, addList.get(0).getModuleCode())
                .list();
        Set<String> fieldNameFullPathCodes = moduleConfigEntities.stream()
                .map(FormFieldModuleConfigEntity::getFieldNameFullPathCode).collect(Collectors.toSet());
        List<FormFieldInsertDTO> insertDTOS = new ArrayList<>();
        for (String fieldNameFullPathCode : fieldNameFullPathCodes) {
            for (FormFieldRuleConfigUpdateDTO addDto : addList) {
                // 组装表单字段信息
                insertDTOS.add(FormFieldInsertDTO.builder().fullPathCode(fieldNameFullPathCode)
                        .fieldCode(addDto.getFieldCode()).fieldName(addDto.getFieldDefaultName())
                        .moduleCode(addDto.getModuleCode()).build()
                );
            }
        }
        List<FormFieldRuleConfigEntity> configEntities = new ArrayList<>();
        Map<String, String> map = moduleConfigEntities.stream().collect(Collectors.toMap(FormFieldModuleConfigEntity::getFullPathCode, FormFieldModuleConfigEntity::getFieldNameFullPathCode));
        for (FormFieldModuleConfigEntity moduleConfigEntity : moduleConfigEntities) {
            List<FormFieldRuleConfigEntity> entities = JacksonUtil.convertArray(addList, FormFieldRuleConfigEntity.class);
            entities.forEach(o -> {
                o.setFullPathCode(moduleConfigEntity.getFullPathCode());
                o.setFieldNameFullPathCode(map.get(moduleConfigEntity.getFullPathCode()));
            });
            configEntities.addAll(entities);
        }
        if (CollectionUtils.isNotEmpty(insertDTOS)) {
            // 执行存储过程
            ((FormFieldConfigMapper) formFieldConfigService.getBaseMapper()).addField(insertDTOS);
        }
        // 保存字段规则
        this.saveBatch(configEntities);
        // 自定义名称同步更新到字段名配置(重命名)上，方便用户操作
        List<FormFieldUpdateDTO.FieldUpdateDTO> updateDTOS = addList.stream().map(o ->
                FormFieldUpdateDTO.FieldUpdateDTO.builder()
                        .fieldCode(o.getFieldCode())
                        .fieldName(o.getFieldCustomName())
                        .moduleCode(o.getModuleCode())
                        .build()).collect(Collectors.toList());
        formConfigService.syncSameOrderFieldName(addList.get(0).getFieldNameFullPathCode(), updateDTOS);
    }

    /**
     * 将`是否显示`同步到其他表单上
     */
    private void syncIsShowToSameForm(String fieldNameFullPathCode, List<FormFieldRuleConfigEntity> ruleConfigEntities) {
        // 查询所有上下级相关联的表单fullPathCode
        List<String> fullPathCodes = formConfigService.getAllFullPathCodes(fieldNameFullPathCode);
        for (FormFieldRuleConfigEntity ruleConfigEntity : ruleConfigEntities) {
            FormFieldConfigEntity one = formFieldConfigService.lambdaQuery().in(FormFieldConfigEntity::getFullPathCode, ruleConfigEntity.getFieldNameFullPathCode())
                    .eq(FormFieldConfigEntity::getFieldCode, ruleConfigEntity.getFieldCode())
                    .eq(FormFieldConfigEntity::getTypeCode, SYS_FIELD_CONSTANT)
                    .eq(FormFieldConfigEntity::getModuleCode, ruleConfigEntity.getModuleCode())
                    .one();
            List<String> fieldCodes = formFieldConfigService.lambdaQuery().eq(FormFieldConfigEntity::getFieldName, one.getFieldName())
                    .in(FormFieldConfigEntity::getFullPathCode, fullPathCodes)
                    .eq(FormFieldConfigEntity::getTypeCode, SYS_FIELD_CONSTANT)
                    .eq(FormFieldConfigEntity::getModuleCode, ruleConfigEntity.getModuleCode())
                    .list().stream()
                    .map(FormFieldConfigEntity::getFieldCode)
                    .collect(Collectors.toList());

            this.lambdaUpdate().in(FormFieldRuleConfigEntity::getFullPathCode, fullPathCodes)
                    .in(FormFieldRuleConfigEntity::getFieldCode, fieldCodes)
                    .eq(FormFieldRuleConfigEntity::getModuleCode, ruleConfigEntity.getModuleCode())
                    .set(FormFieldRuleConfigEntity::getIsShow, ruleConfigEntity.getIsShow())
                    .update();
        }
    }

    @Override
    public List<FormOverallFieldConfigEntity> getOverallList() {
        List<FormOverallFieldConfigEntity> list = overallFieldConfigService.list();
        // 查询对应的字段映射配置
        List<Integer> relatedOverallIds = list.stream().map(FormOverallFieldConfigEntity::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(relatedOverallIds)) {
            return list;
        }
        Map<Integer, List<FormOverallFieldMappingConfigEntity>> map = overallFieldMappingConfigService.lambdaQuery()
                .in(FormOverallFieldMappingConfigEntity::getRelateOverallId, relatedOverallIds)
                .list().stream()
                .collect(Collectors.groupingBy(FormOverallFieldMappingConfigEntity::getRelateOverallId));
        for (FormOverallFieldConfigEntity overallFieldConfig : list) {
            List<FormOverallFieldMappingConfigEntity> mappingConfigEntities = map.get(overallFieldConfig.getId());
            overallFieldConfig.setMappingConfigEntities(mappingConfigEntities);
        }
        return list;
    }

    @Override
    public OverallFormFieldDetailVO getOverallDetail(Integer id) {
        FormOverallFieldConfigEntity configEntity = overallFieldConfigService.getById(id);
        // 查询全局字段规则
        List<FormOverallFieldRuleConfigEntity> ruleConfigEntities = overallFieldRuleConfigService.lambdaQuery()
                .eq(FormOverallFieldRuleConfigEntity::getRelateOverallId, configEntity.getId()).list();
        // 查询取值配置
        List<FormFieldOptionDTO.EnumDTO> enums = null;
        if (StringUtils.isNotBlank(configEntity.getOptionValuesType()) && configEntity.getOptionValuesType().equals(Constants.TABLE)) {
            enums = JSON.parseArray(configEntity.getAllOption(), FormFieldOptionDTO.EnumDTO.class);
            enums = enums.stream().filter(o -> !o.getIsBan()).collect(Collectors.toList());
        }
        FormFieldOptionDTO build = FormFieldOptionDTO.builder()
                .id(id)
                .inputType(configEntity.getInputType())
                .type(StringUtils.isNotBlank(configEntity.getOptionValuesType()) ? configEntity.getOptionValuesType() : "input")
                .enums(enums)
                .relatedApiCode(configEntity.getRelatedApiCode())
                .formula(configEntity.getFormula())
                .build();
        return OverallFormFieldDetailVO.builder()
                .id(id)
                .fieldCustomName(configEntity.getFieldCustomName())
                .remark(configEntity.getRemark())
                .sort(configEntity.getSort())
                .mappingConfigEntities(configEntity.getMappingConfigEntities())
                .ruleConfigEntities(ruleConfigEntities)
                .optionDTO(build)
                .build();
    }

    /**
     * 设置全局字段的取值配置
     */
    public void setOverallOptionConf(FormFieldOptionDTO optionDTO) {
        // 根据类型判断是url模式还是枚举模式
        // 如果为url则直接插入数据
        switch (optionDTO.getType()) {
            case Constants.API:
                // 规避用户输入的url存在空格的情况
                overallFieldConfigService.lambdaUpdate().eq(FormOverallFieldConfigEntity::getId, optionDTO.getId())
                        .set(FormOverallFieldConfigEntity::getInputType, optionDTO.getInputType())
                        .set(FormOverallFieldConfigEntity::getOptionValuesType, optionDTO.getType())
                        .set(FormOverallFieldConfigEntity::getRelatedApiCode, optionDTO.getRelatedApiCode())
                        .set(FormOverallFieldConfigEntity::getAllOption, null)
                        .set(FormOverallFieldConfigEntity::getFormula, null)
                        .update();
                break;
            case Constants.FORMULA:
                // 为公式的时候，输入类型只能是输入框
                overallFieldConfigService.lambdaUpdate().eq(FormOverallFieldConfigEntity::getId, optionDTO.getId())
                        .set(FormOverallFieldConfigEntity::getOptionValuesType, optionDTO.getType())
                        .set(FormOverallFieldConfigEntity::getFormula, optionDTO.getFormula())
                        .set(FormOverallFieldConfigEntity::getInputType, InputTypeEnum.INPUT.getType())
                        .set(FormOverallFieldConfigEntity::getRelatedApiCode, null)
                        .set(FormOverallFieldConfigEntity::getAllOption, null)
                        .update();
                break;
//            case Constants.DYNAMIC_FORMULA:
//                // 为动态公式的时候，输入类型只能是动态公式
//                this.lambdaUpdate().eq(FormFieldRuleConfigEntity::getId, optionDTO.getId())
//                        .set(FormFieldRuleConfigEntity::getOptionValuesType, optionDTO.getType())
//                        .set(FormFieldRuleConfigEntity::getFormula, optionDTO.getFormula())
//                        .set(FormFieldRuleConfigEntity::getInputType, InputTypeEnum.DYNAMIC_FORMULA.getType())
//                        .set(FormFieldRuleConfigEntity::getRelatedApiCode, null)
//                        .set(FormFieldRuleConfigEntity::getAllOption, null)
//                        .update();
//                break;
            case Constants.TIME_FORMULA:
                overallFieldConfigService.lambdaUpdate().eq(FormOverallFieldConfigEntity::getId, optionDTO.getId())
                        .set(FormOverallFieldConfigEntity::getOptionValuesType, optionDTO.getType())
                        .set(FormOverallFieldConfigEntity::getFormula, optionDTO.getFormula())
                        // 输入类型只能是时间选择器
                        .set(FormOverallFieldConfigEntity::getInputType, InputTypeEnum.DATE_TIME_PICKER.getType())
                        .set(FormOverallFieldConfigEntity::getRelatedApiCode, null)
                        .set(FormOverallFieldConfigEntity::getAllOption, null)
                        .update();
                break;
            case Constants.TABLE:
                // 表单选项值字段校验
                fieldJudge(optionDTO);
                // 根据显示顺序排列数据
                List<FormFieldOptionDTO.EnumDTO> orderEnums = optionDTO.getEnums().stream()
                        .peek(o -> o.setIsBan(false))
                        .sorted(Comparator.comparing(FormFieldOptionDTO.EnumDTO::getShowOrder))
                        .collect(Collectors.toList());
                Map<String, FormFieldOptionDTO.EnumDTO> map = orderEnums.stream().collect(Collectors.toMap(FormFieldOptionDTO.EnumDTO::getValue, o -> o));
                // 用户删除数据后仅对删除的数据进行禁用处理，不进行硬删除
                FormOverallFieldConfigEntity formOverallConfigEntity = overallFieldConfigService.getById(optionDTO.getId());
                String allOption = formOverallConfigEntity.getAllOption();
                if (StringUtils.isNotBlank(allOption)) {
                    List<FormFieldOptionDTO.EnumDTO> historyEnums = JSON.parseArray(allOption, FormFieldOptionDTO.EnumDTO.class);
                    for (FormFieldOptionDTO.EnumDTO historyEnum : historyEnums) {
                        if (!map.containsKey(historyEnum.getValue())) {
                            historyEnum.setIsBan(true);
                            orderEnums.add(historyEnum);
                        }
                    }
                }

                // 如果为枚举，转换为json存入数据库
                String enums = JSON.toJSONString(orderEnums);
                overallFieldConfigService.lambdaUpdate().eq(FormOverallFieldConfigEntity::getId, optionDTO.getId())
                        .set(FormOverallFieldConfigEntity::getInputType, optionDTO.getInputType())
                        .set(FormOverallFieldConfigEntity::getOptionValuesType, optionDTO.getType())
                        .set(FormOverallFieldConfigEntity::getAllOption, enums)
                        .set(FormOverallFieldConfigEntity::getRelatedApiCode, null)
                        .set(FormOverallFieldConfigEntity::getFormula, null)
                        .update();
                break;
            default:
                // 人工输入
                overallFieldConfigService.lambdaUpdate().eq(FormOverallFieldConfigEntity::getId, optionDTO.getId())
                        .set(FormOverallFieldConfigEntity::getInputType, optionDTO.getInputType())
                        .set(FormOverallFieldConfigEntity::getOptionValuesType, null)
                        .set(FormOverallFieldConfigEntity::getAllOption, null)
                        .set(FormOverallFieldConfigEntity::getRelatedApiCode, null)
                        .set(FormOverallFieldConfigEntity::getFormula, null)
                        .update();
                break;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addOverallForm(FormOverallInsertDTO insertDTO) {
        // 新增全局配置
        FormOverallFieldConfigEntity overallFieldConfEntity = JacksonUtil.convertObject(insertDTO, FormOverallFieldConfigEntity.class);
        overallFieldConfigService.save(overallFieldConfEntity);
        // 设置全局规则配置
        insertDTO.getRuleConfigEntities().forEach(ruleConfigEntity -> ruleConfigEntity.setRelateOverallId(overallFieldConfEntity.getId()));
        overallFieldRuleConfigService.saveBatch(insertDTO.getRuleConfigEntities());
        // 设置全局字段的取值配置
        overallFieldConfEntity.getOptionDTO().setId(overallFieldConfEntity.getId());
        setOverallOptionConf(overallFieldConfEntity.getOptionDTO());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOverallForm(FormOverallUpdateDTO updateDTO) {
        // 更新全局配置
        FormOverallFieldConfigEntity overallFieldConfEntity = JacksonUtil.convertObject(updateDTO, FormOverallFieldConfigEntity.class);
        overallFieldConfigService.updateById(overallFieldConfEntity);
        // 设置全局规则配置
        overallFieldRuleConfigService.updateBatchById(updateDTO.getRuleConfigEntities());
        // 设置全局字段的取值配置
        overallFieldConfEntity.getOptionDTO().setId(overallFieldConfEntity.getId());
        setOverallOptionConf(overallFieldConfEntity.getOptionDTO());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteOverallForm(Integer id) {
        // 删除前需要重置配置过的数据
        FormOverallFieldConfigEntity configEntity = overallFieldConfigService.getById(id);
        List<FormOverallFieldMappingConfigEntity> deleteAllList = new ArrayList<>();
        Map<String, String> historyRouteMaps = new HashMap<>();
        Map<Integer, List<FormOverallFieldMappingConfigEntity>> historyFieldMappingMap = overallFieldMappingConfigService
                .lambdaQuery().eq(FormOverallFieldMappingConfigEntity::getRelateOverallId, id)
                .list().stream()
                .collect(Collectors.groupingBy(FormOverallFieldMappingConfigEntity::getRelateOverallId));
        getDeleteData(configEntity, new ArrayList<>(), historyFieldMappingMap, deleteAllList, historyRouteMaps);

        // 获取全局配置对应的字段规则集合，用于后续找到对应的字段规则并进行处理
        Map<String, String> fullPathCodeMap = new HashMap<>();
        List<FormOverallFieldMappingConfigEntity> fieldMappingConfigEntities = overallFieldMappingConfigService.lambdaQuery()
                .eq(FormOverallFieldMappingConfigEntity::getRelateOverallId, id)
                .list();
        for (FormOverallFieldMappingConfigEntity fieldMappingConfigEntity : fieldMappingConfigEntities) {
            FormFieldRuleConfigEntity one = this.lambdaQuery().eq(FormFieldRuleConfigEntity::getRoute, fieldMappingConfigEntity.getRoute())
                    .eq(FormFieldRuleConfigEntity::getFieldCode, fieldMappingConfigEntity.getFieldCode())
                    .last("limit 1")
                    .one();
            String key = fieldMappingConfigEntity.getRoute() + fieldMappingConfigEntity.getFieldCode();
            fullPathCodeMap.put(key, one.getFieldNameFullPathCode());
        }
        fullPathCodeMap.putAll(historyRouteMaps);
        clearFormRuleAndName(deleteAllList, fullPathCodeMap);
        // 删除全局配置
        overallFieldConfigService.removeById(id);
        // 删除全局规则配置
        overallFieldRuleConfigService.remove(
                Wrappers.lambdaQuery(FormOverallFieldRuleConfigEntity.class)
                        .eq(FormOverallFieldRuleConfigEntity::getRelateOverallId, id));
        // 删除字段映射关系表
        overallFieldMappingConfigService.remove(
                Wrappers.lambdaQuery(FormOverallFieldMappingConfigEntity.class)
                        .eq(FormOverallFieldMappingConfigEntity::getRelateOverallId, id));
    }


    @Override
    public List<CommonType> getOverallOrderTypeList() {
        List<CommonType> types = new ArrayList<>();
        // 根据上下游配置获取可配置的单据类型
        for (FormUpDownStreamRelationshipConfigEntity configEntity : upDownStreamRelationshipConfigService.list()) {
            types.add(CommonType.builder().code(configEntity.getUpstreamOrderType()).name(configEntity.getUpstreamOrderName()).build());
            types.add(CommonType.builder().code(configEntity.getDownstreamOrderType()).name(configEntity.getDownstreamOrderName()).build());
        }
//        // 过滤已经绑定的下推单据类型
//        List<String> bindOrderTypes = overallFieldMappingConfigService.lambdaQuery().eq(FormOverallFieldMappingConfigEntity::getRelateOverallId, overallId)
//                .list().stream()
//                .map(FormOverallFieldMappingConfigEntity::getOrderType)
//                .collect(Collectors.toList());
        return types.stream()
                .filter(CommonUtils.distinctPredicate(CommonType::getCode))
//                .filter(o -> !bindOrderTypes.contains(String.valueOf(o.getCode())))
                .collect(Collectors.toList());
    }

    @Override
    public List<FormOverallAllowFieldConfigEntity> getOverallOrderMappingPushList(String orderType) {
        return overallAllowFieldConfigService.lambdaQuery().eq(FormOverallAllowFieldConfigEntity::getOrderType, orderType).orderByAsc(FormOverallAllowFieldConfigEntity::getId).list();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void applyToForm(List<FormOverallFieldConfigEntity> overallFieldConfigEntities, Boolean isClearHistoryConf) {
        // 保存下推映射字段
        for (FormOverallFieldConfigEntity overallFieldConfigEntity : overallFieldConfigEntities) {
            if (CollectionUtils.isEmpty(overallFieldConfigEntity.getMappingConfigEntities())) {
                throw new ResponseException(RespCodeEnum.NEED_SELECT_RELATED_FIELD.fmtDes(overallFieldConfigEntity.getFieldCustomName()));
            }
        }
        List<FormOverallFieldMappingConfigEntity> list = new ArrayList<>();
        List<FormOverallFieldMappingConfigEntity> deleteAllList = new ArrayList<>();
        Map<String, String> historyRouteMaps = new HashMap<>();
        Map<Integer, List<FormOverallFieldMappingConfigEntity>> historyFieldMappingMap = overallFieldMappingConfigService.list().stream().collect(Collectors.groupingBy(FormOverallFieldMappingConfigEntity::getRelateOverallId));
        for (FormOverallFieldConfigEntity overallFieldConfigEntity : overallFieldConfigEntities) {
            List<FormOverallFieldMappingConfigEntity> mappingConfigEntities = overallFieldConfigEntity.getMappingConfigEntities();
            for (FormOverallFieldMappingConfigEntity mappingConfigEntity : mappingConfigEntities) {
                if (StringUtils.isBlank(mappingConfigEntity.getFieldCode()) || StringUtils.isBlank(mappingConfigEntity.getOrderType())) {
                    throw new ResponseException(RespCodeEnum.MAPPING_RELATION_ERROR_EMPTY_DATA);
                }
                mappingConfigEntity.setRelateOverallId(overallFieldConfigEntity.getId());
            }
            ;
            list.addAll(mappingConfigEntities);
            // 查询删除的原数据，目的是为了清空对应的单据字段规则和命名
            // 在id还存在的情况下，修改字段，也应该把之前的清除
            if (isClearHistoryConf) {
                getDeleteData(overallFieldConfigEntity, mappingConfigEntities, historyFieldMappingMap, deleteAllList, historyRouteMaps);
            }
        }
        // 下推映射关系表先清空后批量新增
        overallFieldMappingConfigService.remove(null);
        overallFieldMappingConfigService.saveBatch(list);

        // 获取全局配置对应的字段规则集合，用于后续找到对应的字段规则并进行处理
        Map<String, String> fullPathCodeMap = new HashMap<>();
        List<FormOverallFieldMappingConfigEntity> fieldMappingConfigEntities = overallFieldConfigEntities.stream()
                .flatMap(o -> o.getMappingConfigEntities().stream())
                .collect(Collectors.toList());
        for (FormOverallFieldMappingConfigEntity fieldMappingConfigEntity : fieldMappingConfigEntities) {
            FormFieldRuleConfigEntity one = this.lambdaQuery().eq(FormFieldRuleConfigEntity::getRoute, fieldMappingConfigEntity.getRoute())
                    .eq(FormFieldRuleConfigEntity::getFieldCode, fieldMappingConfigEntity.getFieldCode())
                    .last("limit 1")
                    .one();
            String key = fieldMappingConfigEntity.getRoute() + fieldMappingConfigEntity.getFieldCode();
            fullPathCodeMap.put(key, one.getFieldNameFullPathCode());
        }
        fullPathCodeMap.putAll(historyRouteMaps);

        // 应用到表单以及下推场景
        List<FieldMappingEntity> fieldMappingAllList = new ArrayList<>();
        // 清除已经删除的表单的字段规则和命名
        if (isClearHistoryConf) {
            clearFormRuleAndName(deleteAllList, fullPathCodeMap);
        }
        // 同步对应表单的字段规则、命名
        syncFormRuleAndName(overallFieldConfigEntities, fullPathCodeMap, fieldMappingAllList);
        // 同步下推映射关系
        // 由于通过上下级关系查询映射数据，会存在重复数据，这里去重后，再将下推映射关系同步到下推映射表中
        fieldMappingService.remove(null);
        fieldMappingAllList = new ArrayList<>(fieldMappingAllList.stream().collect(
                Collectors.toMap(o -> o.getUpstreamService() + o.getUpstreamOrderType() + o.getUpstreamFieldCode() + o.getDownstreamService() + o.getDownstreamOrderType() + o.getDownstreamFieldCode(),
                        Function.identity(),
                        (existing, replacement) -> existing
                )).values());
        fieldMappingService.saveBatch(fieldMappingAllList);
    }

    @Override
    public List<String> getTimeFormatTypeEnum() {
        return Stream.of(DateUtil.YEAR_FORMAT,
                        DateUtil.ONLY_MONTH_FORMAT,
                        DateUtil.MONTH_FORMAT_SHORT,
                        DateUtil.MONTH_FORMAT_SHORT_ONE,
                        DateUtil.MONTH_FORMAT,
                        DateUtil.DAY_FORMAT_SHORT_ONE,
                        DateUtil.DATE_FORMAT,
                        DateUtil.DATE_FORMAT_SLASH,
                        DateUtil.DATE_FORMAT_SHORT,
                        DateUtil.HOUR_FORMAT,
                        DateUtil.DATETIME_FORMAT,
                        DateUtil.DATETIME_FORMAT_MIN_WITHOUT_SECOND
                )
                .collect(Collectors.toList());
    }

    @Override
    public Page<FormFieldRuleConfigVO> getList(FormQueryDTO dto) {
        String sql = SqlUtil.getSql(dto);
        Page<FormFieldRuleConfigEntity> formPage = this.baseMapper.getList(sql, dto.getPage());
        if (CollectionUtils.isEmpty(formPage.getRecords())) {
            return new Page<>();
        }
        List<FormFieldRuleConfigEntity> collect = new ArrayList<>();
        // 组转数据
        Map<String, List<FormFieldRuleConfigEntity>> map = formPage.getRecords().stream().collect(Collectors.groupingBy(FormFieldRuleConfigEntity::getRoute));
        for (Map.Entry<String, List<FormFieldRuleConfigEntity>> entry : map.entrySet()) {
            String route = entry.getKey();
            List<FormFieldRuleConfigEntity> list = entry.getValue();
            Map<Integer, FormFieldRuleConfigEntity> showMap = list.stream().collect(Collectors.toMap(FormFieldRuleConfigEntity::getId, o -> o));
            // 查询路由下所有字段的数据
            List<FormFieldRuleConfigEntity> ruleConfigEntities = detailRuleByFullPathCode(FullRouteCodeDTO.builder().fullRouteCode(route).build());
            // 过滤数据.从ruleConfigEntities中找到和list匹配的数据，匹配依据为id
            collect = ruleConfigEntities.stream().filter(o -> showMap.containsKey(o.getId())).collect(Collectors.toList());
        }
        List<FormFieldRuleConfigVO> vos = JacksonUtil.convertArray(collect, FormFieldRuleConfigVO.class);
        Page<FormFieldRuleConfigVO> page = new Page<>(dto.getCurrent(), dto.getSize());
        page.setRecords(vos);
        page.setTotal(formPage.getTotal());
        return page;
    }

    /**
     * 查询删除的原数据，目的是为了清空对应的单据字段规则和命名
     * 在id还存在的情况下，修改字段，也应该把之前的清除
     *
     * @param overallFieldConfigEntity 全局表单配置
     * @param newMappingConfigEntities 新的下推映射关系
     * @param overallFieldConfigEntity 历史下推映射关系
     * @param deleteAllList            根据新旧对比，整理需要删除的映射关系
     * @param historyRouteMaps         根据新旧对比，整理需要删除的映射路由
     */
    private void getDeleteData(FormOverallFieldConfigEntity overallFieldConfigEntity, List<FormOverallFieldMappingConfigEntity> newMappingConfigEntities, Map<Integer, List<FormOverallFieldMappingConfigEntity>> historyFieldMappingMap, List<FormOverallFieldMappingConfigEntity> deleteAllList, Map<String, String> historyRouteMaps) {
        List<Integer> ids = newMappingConfigEntities.stream().map(FormOverallFieldMappingConfigEntity::getId).collect(Collectors.toList());
        List<String> fieldCodes = newMappingConfigEntities.stream().map(FormOverallFieldMappingConfigEntity::getFieldCode).collect(Collectors.toList());
        List<FormOverallFieldMappingConfigEntity> historyFieldMappingList = historyFieldMappingMap.get(overallFieldConfigEntity.getId());
        if (CollectionUtils.isNotEmpty(historyFieldMappingList)) {
            List<FormOverallFieldMappingConfigEntity> deleteList = historyFieldMappingList
                    .stream()
                    .filter(pre -> !ids.contains(pre.getId()) || !fieldCodes.contains(pre.getFieldCode())
                    )
                    .collect(Collectors.toList());
            deleteAllList.addAll(deleteList);
            for (FormOverallFieldMappingConfigEntity deleteFieldMappingConfig : deleteList) {
                FormFieldRuleConfigEntity one = this.lambdaQuery().eq(FormFieldRuleConfigEntity::getRoute, deleteFieldMappingConfig.getRoute())
                        .eq(FormFieldRuleConfigEntity::getFieldCode, deleteFieldMappingConfig.getFieldCode())
                        .last("limit 1")
                        .one();
                String key = deleteFieldMappingConfig.getRoute() + deleteFieldMappingConfig.getFieldCode();
                historyRouteMaps.put(key, one.getFieldNameFullPathCode());
            }
        }
    }

    /**
     * 清除对应表单的字段规则和命名
     */
    private void clearFormRuleAndName(List<FormOverallFieldMappingConfigEntity> deleteAllList, Map<String, String> fullPathCodeMap) {
        for (FormOverallFieldMappingConfigEntity mappingConfigEntity : deleteAllList) {
            String fullPathCode = fullPathCodeMap.get(mappingConfigEntity.getRoute() + mappingConfigEntity.getFieldCode());
            // 将全局配置-自定义名称同步到对应单据的表单中
            List<FormFieldConfigEntity> list = formFieldConfigService.lambdaQuery().eq(FormFieldConfigEntity::getFullPathCode, fullPathCode)
                    .eq(FormFieldConfigEntity::getFieldName, mappingConfigEntity.getFieldName())
                    .eq(FormFieldConfigEntity::getTypeCode, SYS_FIELD_CONSTANT)
                    .list();
            List<FormFieldUpdateDTO.FieldUpdateDTO> updateDTOS = new ArrayList<>();
            for (FormFieldConfigEntity one : list) {
                updateDTOS.add(FormFieldUpdateDTO.FieldUpdateDTO.builder()
                        .fieldCode(one.getFieldCode())
                        .fieldName("")
                        .moduleCode(one.getModuleCode())
                        .build());
            }
            formConfigService.syncSameOrderFieldName(fullPathCode, updateDTOS);
            clearFormRule(mappingConfigEntity.getFieldName(), fullPathCode, null);
        }
    }

    /**
     * 同步对应表单的字段规则、命名
     */
    private void syncFormRuleAndName(List<FormOverallFieldConfigEntity> overallFieldConfigEntities, Map<String, String> fullPathCodeMap, List<FieldMappingEntity> fieldMappingAllList) {
        for (FormOverallFieldConfigEntity entity : overallFieldConfigEntities) {
            // 查询全局字段规则
            List<FormOverallFieldRuleConfigEntity> ruleConfigEntities = overallFieldRuleConfigService.lambdaQuery()
                    .eq(FormOverallFieldRuleConfigEntity::getRelateOverallId, entity.getId()).list();
            Map<String, List<FormOverallFieldRuleConfigEntity>> ruleMap = ruleConfigEntities.stream().collect(Collectors.groupingBy(FormOverallFieldRuleConfigEntity::getType));
            // 查询需要同步的字段
            List<FormOverallFieldMappingConfigEntity> mappingConfigEntities = entity.getMappingConfigEntities();
            for (FormOverallFieldMappingConfigEntity mappingConfigEntity : mappingConfigEntities) {
                String fullPathCode = fullPathCodeMap.get(mappingConfigEntity.getRoute() + mappingConfigEntity.getFieldCode());
                // 将全局配置-自定义名称同步到对应单据的表单中
                List<FormFieldConfigEntity> list = formFieldConfigService.lambdaQuery().eq(FormFieldConfigEntity::getFullPathCode, fullPathCode)
                        .eq(FormFieldConfigEntity::getFieldName, mappingConfigEntity.getFieldName())
                        .eq(FormFieldConfigEntity::getTypeCode, SYS_FIELD_CONSTANT)
                        .list();
                List<FormFieldUpdateDTO.FieldUpdateDTO> updateDTOS = new ArrayList<>();
                for (FormFieldConfigEntity one : list) {
                    updateDTOS.add(FormFieldUpdateDTO.FieldUpdateDTO.builder()
                            .fieldCode(one.getFieldCode())
                            .fieldName(entity.getFieldCustomName())
                            .moduleCode(one.getModuleCode())
                            .build());
                }
                formConfigService.syncSameOrderFieldName(fullPathCode, updateDTOS);
                // 将全局配置-字段规则同步到对应单据的表单里
                syncFormRule(entity, mappingConfigEntity.getFieldName(), fullPathCode, ruleMap);
            }

            Map<String, FormOverallFieldMappingConfigEntity> fieldMappingConfigMap = mappingConfigEntities.stream().collect(Collectors.toMap(FormOverallFieldMappingConfigEntity::getOrderType, o -> o));
            for (FormOverallFieldMappingConfigEntity mappingConfigEntity : mappingConfigEntities) {
                // 查询配置字段的映射关系是否符合映射标准，判断是否存在关联的上下游关系，如果存在则生成一条下推映射数据，如果都不存在则报错
                // 作为上游单据，判断保存的数据是否存在下游单据，存在则生成一条映射数据
                List<FieldMappingEntity> fieldMappingList = new ArrayList<>();
                generateFieldMappingDataFromUpstream(mappingConfigEntity, fieldMappingConfigMap, fieldMappingList);
                // 作为下游单据，判断保存的数据是否存在上游单据，存在则生成一条映射数据
                generateFieldMappingDataFromDownstream(mappingConfigEntity, fieldMappingConfigMap, fieldMappingList);
                // 都不存在，则报错
                if (CollectionUtils.isEmpty(fieldMappingList)) {
                    throw new ResponseException(RespCodeEnum.MAPPING_RELATION_ERROR.fmtDes(entity.getFieldCustomName(), mappingConfigEntity.getFieldName()));
                }
                fieldMappingAllList.addAll(fieldMappingList);
            }
        }
    }

    /**
     * 作为下游单据，判断保存的数据是否存在上游单据，存在则生成一条映射数据
     */
    private void generateFieldMappingDataFromDownstream(FormOverallFieldMappingConfigEntity mappingConfigEntity, Map<String, FormOverallFieldMappingConfigEntity> fieldMappingConfigMap, List<FieldMappingEntity> fieldMappingList) {
        String downStreamOrderType = mappingConfigEntity.getOrderType();
        List<FormUpDownStreamRelationshipConfigEntity> downStreamRelationshipConfigEntities = upDownStreamRelationshipConfigService.lambdaQuery()
                .eq(FormUpDownStreamRelationshipConfigEntity::getDownstreamOrderType, downStreamOrderType)
                .list();
        if (CollectionUtils.isEmpty(downStreamRelationshipConfigEntities)) {
            return;
        }
        Map<String, FormUpDownStreamRelationshipConfigEntity> relationshipMap = downStreamRelationshipConfigEntities.stream().collect(Collectors.toMap(FormUpDownStreamRelationshipConfigEntity::getUpstreamOrderType, o -> o));
        for (Map.Entry<String, FormUpDownStreamRelationshipConfigEntity> relationshipEntry : relationshipMap.entrySet()) {
            String upStreamOrderCode = relationshipEntry.getKey();
            FormUpDownStreamRelationshipConfigEntity relationshipEntity = relationshipEntry.getValue();
            if (fieldMappingConfigMap.containsKey(upStreamOrderCode)) {
                FormOverallFieldMappingConfigEntity fieldMappingConfig = fieldMappingConfigMap.get(upStreamOrderCode);
                fieldMappingList.add(FieldMappingEntity.builder()
                        .upstreamService(relationshipEntity.getUpstreamService())
                        .upstreamOrderType(relationshipEntity.getUpstreamOrderType())
                        .upstreamFieldCode(fieldMappingConfig.getFieldCode())
                        .downstreamService(relationshipEntity.getDownstreamService())
                        .downstreamOrderType(relationshipEntity.getDownstreamOrderType())
                        .downstreamFieldCode(mappingConfigEntity.getFieldCode())
                        .build());
            }
        }
    }

    /**
     * 作为上游单据，判断保存的数据是否存在下游单据，存在则生成一条映射数据
     */
    private void generateFieldMappingDataFromUpstream(FormOverallFieldMappingConfigEntity mappingConfigEntity, Map<String, FormOverallFieldMappingConfigEntity> fieldMappingConfigMap, List<FieldMappingEntity> fieldMappingList) {
        String upStreamOrderType = mappingConfigEntity.getOrderType();
        List<FormUpDownStreamRelationshipConfigEntity> upStreamRelationshipConfigEntities = upDownStreamRelationshipConfigService.lambdaQuery()
                .eq(FormUpDownStreamRelationshipConfigEntity::getUpstreamOrderType, upStreamOrderType)
                .list();
        if (CollectionUtils.isEmpty(upStreamRelationshipConfigEntities)) {
            return;
        }
        Map<String, FormUpDownStreamRelationshipConfigEntity> relationshipMap = upStreamRelationshipConfigEntities.stream().collect(Collectors.toMap(FormUpDownStreamRelationshipConfigEntity::getDownstreamOrderType, o -> o));
        for (Map.Entry<String, FormUpDownStreamRelationshipConfigEntity> relationshipEntry : relationshipMap.entrySet()) {
            String downStreamOrderCode = relationshipEntry.getKey();
            FormUpDownStreamRelationshipConfigEntity relationshipEntity = relationshipEntry.getValue();
            if (fieldMappingConfigMap.containsKey(downStreamOrderCode)) {
                FormOverallFieldMappingConfigEntity fieldMappingConfig = fieldMappingConfigMap.get(downStreamOrderCode);
                fieldMappingList.add(FieldMappingEntity.builder()
                        .upstreamService(relationshipEntity.getUpstreamService())
                        .upstreamOrderType(relationshipEntity.getUpstreamOrderType())
                        .upstreamFieldCode(mappingConfigEntity.getFieldCode())
                        .downstreamService(relationshipEntity.getDownstreamService())
                        .downstreamOrderType(relationshipEntity.getDownstreamOrderType())
                        .downstreamFieldCode(fieldMappingConfig.getFieldCode())
                        .build());
            }
        }
    }

    /**
     * 清空全局配置-字段规则配置到对应的表单里
     */
    private void clearFormRule(String fieldName, String fullPathCode, String moduleCode) {
        // 查询所有上下级相关联的表单fullPathCode
        List<String> fullPathCodes = formConfigService.getAllFullPathCodes(fullPathCode);
        List<String> otherSameFieldCodes = formFieldConfigService.lambdaQuery()
                .in(FormFieldConfigEntity::getFullPathCode, fullPathCodes)
                .eq(FormFieldConfigEntity::getFieldName, fieldName)
                .list().stream()
                .map(FormFieldConfigEntity::getFieldCode)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(otherSameFieldCodes) || CollectionUtils.isEmpty(fullPathCodes)) {
            return;
        }
        this.lambdaUpdate().in(FormFieldRuleConfigEntity::getFullPathCode, fullPathCodes)
                .in(FormFieldRuleConfigEntity::getFieldCode, otherSameFieldCodes)
                .eq(StringUtils.isNotBlank(moduleCode), FormFieldRuleConfigEntity::getModuleCode, moduleCode)
                .set(FormFieldRuleConfigEntity::getIsShow, false)
                .set(FormFieldRuleConfigEntity::getIsEdit, false)
                .set(FormFieldRuleConfigEntity::getIsNeed, false)
                .set(FormFieldRuleConfigEntity::getInputType, null)
                .set(FormFieldRuleConfigEntity::getOptionValuesType, null)
                .set(FormFieldRuleConfigEntity::getAllOption, null)
                .set(FormFieldRuleConfigEntity::getRelatedApiCode, null)
                .set(FormFieldRuleConfigEntity::getIsOverall, false)
                .update();
    }

    /**
     * 将全局配置-字段规则同步到对应的表单里
     */
    private void syncFormRule(FormOverallFieldConfigEntity entity, String fieldName, String fullPathCode, Map<String, List<FormOverallFieldRuleConfigEntity>> ruleMap) {
        // 查询所有上下级相关联的表单配置
        List<FormConfigEntity> configList = formConfigService.getAllFormConfigList(fullPathCode);
        // 找到对应重命名表单对应的字段编码
        List<String> fullPathCodes = configList.stream().map(FormConfigEntity::getFullPathCode).collect(Collectors.toList());
        List<String> otherSameFieldCodes = formFieldConfigService.lambdaQuery()
                .in(FormFieldConfigEntity::getFullPathCode, fullPathCodes)
                .eq(FormFieldConfigEntity::getFieldName, fieldName)
                .list().stream()
                .map(FormFieldConfigEntity::getFieldCode)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(otherSameFieldCodes)) {
            return;
        }
        for (FormConfigEntity configEntity : configList) {
            // 中间页菜单不同步，只同步存在实际字段的菜单
            if (StringUtils.isBlank(configEntity.getModule())) {
                continue;
            }
            List<FormOverallFieldRuleConfigEntity> fieldRuleConfigEntities = ruleMap.get(configEntity.getModule());
            FormOverallFieldRuleConfigEntity fieldRuleConfigEntity = fieldRuleConfigEntities.get(0);
            this.lambdaUpdate().eq(FormFieldRuleConfigEntity::getFullPathCode, configEntity.getFullPathCode())
                    .in(FormFieldRuleConfigEntity::getFieldCode, otherSameFieldCodes)
                    .set(FormFieldRuleConfigEntity::getIsShow, fieldRuleConfigEntity.getIsShow())
                    .set(FormFieldRuleConfigEntity::getIsEdit, fieldRuleConfigEntity.getIsEdit())
                    .set(FormFieldRuleConfigEntity::getIsNeed, fieldRuleConfigEntity.getIsNeed())
                    .set(FormFieldRuleConfigEntity::getInputType, entity.getInputType())
                    .set(FormFieldRuleConfigEntity::getOptionValuesType, entity.getOptionValuesType())
                    .set(FormFieldRuleConfigEntity::getAllOption, entity.getAllOption())
                    .set(FormFieldRuleConfigEntity::getRelatedApiCode, entity.getRelatedApiCode())
                    .set(FormFieldRuleConfigEntity::getFormula, entity.getFormula())
                    .set(FormFieldRuleConfigEntity::getIsOverall, true)
                    .update();
        }
    }


    private void getSubConfigList(String fullPathCode, List<FormConfigEntity> configList) {
        List<FormConfigEntity> subList = new ArrayList<>(formConfigService.lambdaQuery()
                .eq(FormConfigEntity::getParentFullPathCode, fullPathCode)
                .list());
        configList.addAll(subList);
        for (FormConfigEntity s : subList) {
            getSubConfigList(s.getFullPathCode(), configList);
        }
    }

    @Override
    public void refreshAllMaterialExtendField() {
        List<FormFieldRuleConfigEntity> ruleConfigs = detailRuleByFullPathCode(FullRouteCodeDTO.builder().fullPathCode("material.editByCreate").build());
        dealDataForMaterialFormUpdate(
                ruleConfigs.stream().filter(e -> Constants.MATERIAL_EXTEND_FIELD.equals(e.getModuleCode())).collect(Collectors.toList())
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void upsertExtendRelationConf(ExtendFieldRelationUpsertDTO dto) {
        if (dto == null) {
            throw new ResponseException("请求参数不能为空");
        }

        // 处理全局表单允许配置字段
        if (CollectionUtils.isNotEmpty(dto.getAllowFieldConfigs())) {
            upsertAllowFieldConfigs(dto.getAllowFieldConfigs());
        }

        // 处理表单上下游关系配置
        if (CollectionUtils.isNotEmpty(dto.getUpDownStreamRelationConfigs())) {
            upsertUpDownStreamRelationConfigs(dto.getUpDownStreamRelationConfigs());
        }
    }

    /**
     * 处理全局表单允许配置字段的upsert操作
     */
    private void upsertAllowFieldConfigs(List<ExtendFieldRelationUpsertDTO.AllowFieldConfigDTO> allowFieldConfigs) {
        for (ExtendFieldRelationUpsertDTO.AllowFieldConfigDTO configDTO : allowFieldConfigs) {
            // 查询是否已存在相同的配置
            FormOverallAllowFieldConfigEntity existingConfig = overallAllowFieldConfigService.lambdaQuery()
                    .eq(FormOverallAllowFieldConfigEntity::getOrderType, configDTO.getOrderType())
                    .eq(FormOverallAllowFieldConfigEntity::getFieldCode, configDTO.getFieldCode())
                    .one();

            if (existingConfig != null) {
                // 数据相同则不更新，数据不同则更新
                if (!isSameAllowFieldConfig(existingConfig, configDTO)) {
                    existingConfig.setRoute(configDTO.getRoute());
                    existingConfig.setFieldName(configDTO.getFieldName());
                    overallAllowFieldConfigService.updateById(existingConfig);
                }
            } else {
                // 不存在则新增
                FormOverallAllowFieldConfigEntity newConfig = FormOverallAllowFieldConfigEntity.builder()
                        .route(configDTO.getRoute())
                        .orderType(configDTO.getOrderType())
                        .fieldCode(configDTO.getFieldCode())
                        .fieldName(configDTO.getFieldName())
                        .build();
                overallAllowFieldConfigService.save(newConfig);
            }
        }
    }

    /**
     * 处理表单上下游关系配置的upsert操作
     */
    private void upsertUpDownStreamRelationConfigs(List<ExtendFieldRelationUpsertDTO.UpDownStreamRelationConfigDTO> relationConfigs) {
        for (ExtendFieldRelationUpsertDTO.UpDownStreamRelationConfigDTO configDTO : relationConfigs) {
            // 查询是否已存在相同的配置
            FormUpDownStreamRelationshipConfigEntity existingConfig = upDownStreamRelationshipConfigService.lambdaQuery()
                    .eq(FormUpDownStreamRelationshipConfigEntity::getUpstreamService, configDTO.getUpstreamService())
                    .eq(FormUpDownStreamRelationshipConfigEntity::getUpstreamOrderType, configDTO.getUpstreamOrderType())
                    .eq(FormUpDownStreamRelationshipConfigEntity::getDownstreamService, configDTO.getDownstreamService())
                    .eq(FormUpDownStreamRelationshipConfigEntity::getDownstreamOrderType, configDTO.getDownstreamOrderType())
                    .one();

            if (existingConfig != null) {
                // 数据相同则不更新，数据不同则更新
                if (!isSameUpDownStreamConfig(existingConfig, configDTO)) {
                    existingConfig.setUpstreamOrderName(configDTO.getUpstreamOrderName());
                    existingConfig.setDownstreamOrderName(configDTO.getDownstreamOrderName());
                    upDownStreamRelationshipConfigService.updateById(existingConfig);
                }
            } else {
                // 不存在则新增
                FormUpDownStreamRelationshipConfigEntity newConfig = FormUpDownStreamRelationshipConfigEntity.builder()
                        .upstreamService(configDTO.getUpstreamService())
                        .upstreamOrderType(configDTO.getUpstreamOrderType())
                        .upstreamOrderName(configDTO.getUpstreamOrderName())
                        .downstreamService(configDTO.getDownstreamService())
                        .downstreamOrderType(configDTO.getDownstreamOrderType())
                        .downstreamOrderName(configDTO.getDownstreamOrderName())
                        .build();
                upDownStreamRelationshipConfigService.save(newConfig);
            }
        }
    }

    /**
     * 判断全局表单允许配置字段是否相同
     */
    private boolean isSameAllowFieldConfig(FormOverallAllowFieldConfigEntity existing,
                                          ExtendFieldRelationUpsertDTO.AllowFieldConfigDTO dto) {
        return Objects.equals(existing.getRoute(), dto.getRoute()) &&
               Objects.equals(existing.getOrderType(), dto.getOrderType()) &&
               Objects.equals(existing.getFieldCode(), dto.getFieldCode()) &&
               Objects.equals(existing.getFieldName(), dto.getFieldName());
    }

    /**
     * 判断表单上下游关系配置是否相同
     */
    private boolean isSameUpDownStreamConfig(FormUpDownStreamRelationshipConfigEntity existing,
                                           ExtendFieldRelationUpsertDTO.UpDownStreamRelationConfigDTO dto) {
        return Objects.equals(existing.getUpstreamService(), dto.getUpstreamService()) &&
               Objects.equals(existing.getUpstreamOrderType(), dto.getUpstreamOrderType()) &&
               Objects.equals(existing.getUpstreamOrderName(), dto.getUpstreamOrderName()) &&
               Objects.equals(existing.getDownstreamService(), dto.getDownstreamService()) &&
               Objects.equals(existing.getDownstreamOrderType(), dto.getDownstreamOrderType()) &&
               Objects.equals(existing.getDownstreamOrderName(), dto.getDownstreamOrderName());
    }

}

