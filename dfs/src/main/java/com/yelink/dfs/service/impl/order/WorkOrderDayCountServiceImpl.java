package com.yelink.dfs.service.impl.order;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.RedisKeyPrefix;
import com.yelink.dfs.entity.manufacture.ProductionLineEntity;
import com.yelink.dfs.entity.order.RecordWorkOrderDayCountEntity;
import com.yelink.dfs.entity.order.RecordWorkOrderLineDayCountEntity;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.entity.order.dto.RecordDateDTO;
import com.yelink.dfs.entity.order.dto.WorkCountPerDayDTO;
import com.yelink.dfs.entity.order.dto.WorkCountPerDayDTOPage;
import com.yelink.dfs.entity.order.dto.WorkCountPerDayQueryDTO;
import com.yelink.dfs.entity.order.dto.WorkCountPerDayQuerySumDTO;
import com.yelink.dfs.entity.order.dto.WorkOrderLineDayCountDTO;
import com.yelink.dfs.entity.order.vo.WorkCountPerDayVO;
import com.yelink.dfs.entity.product.MaterialEntity;
import com.yelink.dfs.entity.target.record.ReportLineEntity;
import com.yelink.dfs.event.LimitWorkOrderDayCountUpdateEvent;
import com.yelink.dfs.mapper.order.RecordWorkOrderDayCountMapper;
import com.yelink.dfs.mapper.order.WorkOrderMapper;
import com.yelink.dfs.mapper.product.MaterialMapper;
import com.yelink.dfs.mapper.target.record.ReportLineMapper;
import com.yelink.dfs.open.v1.outpt.dto.OutputDTO;
import com.yelink.dfs.open.v1.outpt.dto.OutputReqDTO;
import com.yelink.dfs.service.common.DictService;
import com.yelink.dfs.service.intelligentcreation.production.ProductionOrderProgressService;
import com.yelink.dfs.service.manufacture.ProductionLineService;
import com.yelink.dfs.service.model.WorkCenterService;
import com.yelink.dfs.service.order.RecordWorkOrderDayCountService;
import com.yelink.dfs.service.order.RecordWorkOrderLineDayCountService;
import com.yelink.dfs.service.order.WorkOrderBasicUnitDayCountService;
import com.yelink.dfs.service.order.WorkOrderService;
import com.yelink.dfs.service.target.record.ReportCountService;
import com.yelink.dfs.utils.SpringUtil;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.dto.ScreenOutputDTO;
import com.yelink.dfscommon.utils.DateUtil;
import com.yelink.dfscommon.utils.ExcelUtil;
import com.yelink.dfscommon.utils.JacksonUtil;
import com.yelink.dfscommon.utils.MathUtil;
import com.yelink.dfscommon.utils.Rational;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.DoubleSummaryStatistics;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

/**
 * Service
 *
 * <AUTHOR>
 * @Date 2021-03-11 10:18
 */
@Slf4j
@Service
public class WorkOrderDayCountServiceImpl extends ServiceImpl<RecordWorkOrderDayCountMapper, RecordWorkOrderDayCountEntity> implements RecordWorkOrderDayCountService {


    private static final String DEFAULT_UINT = "个";
    @Autowired
    private RecordWorkOrderDayCountMapper recordWorkOrderDayCountMapper;
    @Autowired
    private WorkOrderMapper workOrderMapper;
    @Autowired
    private MaterialMapper materialMapper;
    @Autowired
    @Lazy
    private ProductionLineService productionLineService;
    @Autowired
    private DictService dictService;
    @Autowired
    private ReportLineMapper reportLineMapper;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private WorkCenterService workCenterService;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private RecordWorkOrderLineDayCountService recordWorkOrderLineDayCountService;
    @Autowired
    private WorkOrderBasicUnitDayCountService workOrderBasicUnitDayCountService;
    @Lazy
    @Resource
    private WorkOrderDayCountServiceImpl recordWorkOrderDayCountService;

    /**
     * 更新工单每日完成量、每日不合格数
     *
     * @param
     */
    @Override
    public void updateWorkOrderDayCount(WorkOrderEntity workOrderEntity) {
        Double finishCount = workOrderEntity.getFinishCount();
        Date recordDate = dictService.getRecordDate(new Date());
        String workOrder = workOrderEntity.getWorkOrderNumber();
        //首先获取今天之前的记录，将总数求和
        QueryWrapper<RecordWorkOrderDayCountEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(RecordWorkOrderDayCountEntity::getWorkOrderNumber, workOrder)
                .lt(RecordWorkOrderDayCountEntity::getTime, recordDate);
        List<RecordWorkOrderDayCountEntity> countEntities = recordWorkOrderDayCountMapper.selectList(wrapper);
        double sum = 0.0;
        double unqualifiedSum = 0.0;
        if (!CollectionUtils.isEmpty(countEntities)) {
            DoubleSummaryStatistics doubleSummaryStatistics = countEntities.stream().mapToDouble(RecordWorkOrderDayCountEntity::getCount).summaryStatistics();
            sum = doubleSummaryStatistics.getSum();
            DoubleSummaryStatistics unqualifiedDoubleSummaryStatistics = countEntities.stream().mapToDouble(RecordWorkOrderDayCountEntity::getUnqualified).summaryStatistics();
            unqualifiedSum = unqualifiedDoubleSummaryStatistics.getSum();
        }
        //用完成数量减去今天之前的完成数，求出今天的数量
        double todayCount = finishCount < sum ? 0 : (finishCount - sum);
        Double unqualified = workOrderEntity.getUnqualified();
        double todayUnqualified = unqualified == null ? 0.0 : unqualified - unqualifiedSum;
        todayUnqualified = todayUnqualified < 0 ? 0 : todayUnqualified;
        //发现负数，则将该工单的每日产量重新计算一遍
        /*if (todayCount < 0 || todayUnqualified < 0) {
            refreshDayCountWhenOccurMinus(workOrderEntity, true, true);
            return;
        }*/
        saveOrUpdateWorkOrderDayCount(workOrderEntity, todayCount, todayUnqualified, recordDate);
    }

    /**
     * 更新工单每日完成量(扫码报工)
     * 有具体时间刷具体时间的，没有刷全部
     *
     * @param
     */
    @Async
    @Override
    public void updateWorkOrderDayCountFlowCode(WorkOrderEntity workOrderEntity, Date date) {
        //分布式锁
        Boolean lockStatus = redisTemplate.opsForValue().setIfAbsent(RedisKeyPrefix.SAVE_OR_UPDATE_WORK_ORDER_DAY_COUNT + workOrderEntity.getWorkOrderNumber() + date, new Date(), 1, TimeUnit.MINUTES);
        if (lockStatus == null || !lockStatus) {
            return;
        }
        try {
            if (date != null) {
                Date recordDate = dictService.getRecordDate(date);
                List<WorkOrderLineDayCountDTO> lineDayCounts = recordWorkOrderDayCountMapper.selectLineReportCount(workOrderEntity.getWorkOrderNumber(), recordDate);
                recordWorkOrderDayCountService.saveOrUpdateWorkOrderDayCount(workOrderEntity, recordDate, lineDayCounts);
            } else {
                List<RecordDateDTO> recordDateDTOList = recordWorkOrderDayCountMapper.selectRecordDateList(workOrderEntity.getWorkOrderNumber());
                if (!CollectionUtils.isEmpty(recordDateDTOList)) {
                    for (RecordDateDTO recordDateDTO : recordDateDTOList) {
                        if(recordDateDTO==null){
                            continue;
                        }
                        Date recordDate = recordDateDTO.getRecordDate();
                        List<WorkOrderLineDayCountDTO> lineDayCounts = recordWorkOrderDayCountMapper.selectLineReportCount(workOrderEntity.getWorkOrderNumber(), recordDate);
                        recordWorkOrderDayCountService.saveOrUpdateWorkOrderDayCount(workOrderEntity, recordDate, lineDayCounts);
                    }
                }
            }
        } finally {
            redisTemplate.delete(RedisKeyPrefix.SAVE_OR_UPDATE_WORK_ORDER_DAY_COUNT + workOrderEntity.getWorkOrderNumber() + date);
        }
    }

    /**
     * 更新工单每日投入量(扫码报工)
     * 有具体时间刷具体时间的，没有刷全部
     *
     * @param
     */
    @Async
    @Override
    public void updateWorkOrderDayInputFlowCode(WorkOrderEntity workOrderEntity, Date date) {
        //分布式锁
        Boolean lockStatus = redisTemplate.opsForValue().setIfAbsent(RedisKeyPrefix.SAVE_OR_UPDATE_WORK_ORDER_DAY_INPUT + workOrderEntity.getWorkOrderNumber() + date, new Date(), 1, TimeUnit.MINUTES);
        if (lockStatus == null || !lockStatus) {
            return;
        }
        try {
            if (date != null) {
                Date recordDate = dictService.getRecordDate(date);
                List<WorkOrderLineDayCountDTO> lineDayCounts = recordWorkOrderDayCountMapper.selectLineInputCount(workOrderEntity.getWorkOrderNumber(), recordDate);
                recordWorkOrderDayCountService.saveOrUpdateWorkOrderDayCount(workOrderEntity, recordDate, lineDayCounts);
            } else {
                List<RecordDateDTO> recordDateDTOList = recordWorkOrderDayCountMapper.selectRecordDateList(workOrderEntity.getWorkOrderNumber());
                if (!CollectionUtils.isEmpty(recordDateDTOList)) {
                    for (RecordDateDTO recordDateDTO : recordDateDTOList) {
                        if(recordDateDTO==null){
                            continue;
                        }
                        Date recordDate = recordDateDTO.getRecordDate();
                        List<WorkOrderLineDayCountDTO> lineDayCounts = recordWorkOrderDayCountMapper.selectLineInputCount(workOrderEntity.getWorkOrderNumber(), recordDate);
                        recordWorkOrderDayCountService.saveOrUpdateWorkOrderDayCount(workOrderEntity, recordDate, lineDayCounts);
                    }
                }
            }
        } finally {
            redisTemplate.delete(RedisKeyPrefix.SAVE_OR_UPDATE_WORK_ORDER_DAY_COUNT + workOrderEntity.getWorkOrderNumber() + date);
        }
    }

    /**
     * 更新工单每日不良量(扫码报工)
     * 有具体时间刷具体时间的，没有刷全部
     *
     * @param
     */
    @Async
    @Override
    public void updateWorkOrderDayUnqualifiedFlowCode(WorkOrderEntity workOrderEntity, Date date) {
        //分布式锁
        Boolean lockStatus = redisTemplate.opsForValue().setIfAbsent(RedisKeyPrefix.SAVE_OR_UPDATE_WORK_ORDER_DAY_UNQUALIFIED + workOrderEntity.getWorkOrderNumber() + date, new Date(), 1, TimeUnit.MINUTES);
        if (lockStatus == null || !lockStatus) {
            return;
        }
        try {
            if (date != null) {
                Date recordDate = dictService.getRecordDate(date);
                List<WorkOrderLineDayCountDTO> lineDayCounts = recordWorkOrderDayCountMapper.selectLineUnqualifiedCount(workOrderEntity.getWorkOrderNumber(), recordDate);
                recordWorkOrderDayCountService.saveOrUpdateWorkOrderDayCount(workOrderEntity, recordDate, lineDayCounts);
            } else {
                List<RecordDateDTO> recordDateDTOList = recordWorkOrderDayCountMapper.selectRecordDateList(workOrderEntity.getWorkOrderNumber());
                if (!CollectionUtils.isEmpty(recordDateDTOList)) {
                    for (RecordDateDTO recordDateDTO : recordDateDTOList) {
                        if(recordDateDTO==null){
                            continue;
                        }
                        Date recordDate = recordDateDTO.getRecordDate();
                        List<WorkOrderLineDayCountDTO> lineDayCounts = recordWorkOrderDayCountMapper.selectLineUnqualifiedCount(workOrderEntity.getWorkOrderNumber(), recordDate);
                        recordWorkOrderDayCountService.saveOrUpdateWorkOrderDayCount(workOrderEntity, recordDate, lineDayCounts);
                    }
                }
            }
        } finally {
            redisTemplate.delete(RedisKeyPrefix.SAVE_OR_UPDATE_WORK_ORDER_DAY_COUNT + workOrderEntity.getWorkOrderNumber() + date);
        }
    }

    private void refreshDayCountWhenOccurMinus(WorkOrderEntity workOrderEntity, boolean updateFinishCount, boolean updateUnqualified) {
        if (!workCenterService.isOperationWorkOrder(workOrderEntity.getWorkOrderId())) {
            //生产工单模式
            QueryWrapper<ReportLineEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(ReportLineEntity::getWorkOrder, workOrderEntity.getWorkOrderNumber());
            List<ReportLineEntity> list = reportLineMapper.selectList(queryWrapper);

            ReportCountService reportCountService = SpringUtil.getBean(ReportCountService.class);
            reportCountService.updateAllWorkOrderDayCount(workOrderEntity, list, updateFinishCount, updateUnqualified);
        } else {
            //作业工单模式
            ProductionOrderProgressService productionOrderProgressService = SpringUtil.getBean(ProductionOrderProgressService.class);
            List<RecordWorkOrderDayCountEntity> list = productionOrderProgressService.getOperationOrderReportList(workOrderEntity.getWorkOrderNumber());
            Map<Date, List<RecordWorkOrderDayCountEntity>> collect = list.stream().collect(Collectors.groupingBy(RecordWorkOrderDayCountEntity::getTime));
            //删除该工单号的每日记录，重新插入
            LambdaQueryWrapper<RecordWorkOrderDayCountEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(RecordWorkOrderDayCountEntity::getWorkOrderNumber, workOrderEntity.getWorkOrderNumber());
            remove(wrapper);
            for (Map.Entry<Date, List<RecordWorkOrderDayCountEntity>> dateListEntry : collect.entrySet()) {
                double finishCount = dateListEntry.getValue().stream().filter(o -> o.getCount() != null).mapToDouble(RecordWorkOrderDayCountEntity::getCount).sum();
                double unqualified = dateListEntry.getValue().stream().filter(o -> o.getUnqualified() != null).mapToDouble(RecordWorkOrderDayCountEntity::getUnqualified).sum();
                saveOrUpdateWorkOrderDayCount(workOrderEntity, finishCount, unqualified, dateListEntry.getKey());
            }
        }
    }

    /**
     * 只更新工单每日完成量
     *
     * @param
     */
    @Override
    public void updateWorkOrderDayCountOnly(WorkOrderEntity workOrderEntity) {
        Double finishCount = workOrderEntity.getFinishCount();
        Double unqualified = workOrderEntity.getUnqualified();
        Date recordDate = dictService.getRecordDate(new Date());
        String workOrder = workOrderEntity.getWorkOrderNumber();
        //首先获取今天之前的记录，将总数求和
        QueryWrapper<RecordWorkOrderDayCountEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(RecordWorkOrderDayCountEntity::getWorkOrderNumber, workOrder)
                .lt(RecordWorkOrderDayCountEntity::getTime, recordDate);
        List<RecordWorkOrderDayCountEntity> countEntities = recordWorkOrderDayCountMapper.selectList(wrapper);
        double totalFinishBeforeToday = 0.0;
        double totalUnqualifiedBeforeToday = 0.0;
        if (!CollectionUtils.isEmpty(countEntities)) {
            DoubleSummaryStatistics doubleSummaryStatistics = countEntities.stream().mapToDouble(RecordWorkOrderDayCountEntity::getCount).summaryStatistics();
            totalFinishBeforeToday = doubleSummaryStatistics.getSum();

            DoubleSummaryStatistics unqualifiedDouble = countEntities.stream().mapToDouble(RecordWorkOrderDayCountEntity::getUnqualified).summaryStatistics();
            totalUnqualifiedBeforeToday = unqualifiedDouble.getSum();
        }
        //用完成数量减去今天之前的完成数，求出今天的数量
        double todayCount = finishCount < totalFinishBeforeToday ? 0 : finishCount - totalFinishBeforeToday;
        double todayUnqualified = unqualified < totalUnqualifiedBeforeToday ? 0 : unqualified - totalUnqualifiedBeforeToday;
        //发现负数，则将该工单的每日产量重新计算一遍
        /*if (todayCount < 0) {
            refreshDayCountWhenOccurMinus(workOrderEntity, true, false);
            return;
        }*/
        saveOrUpdateWorkOrderDayCount(workOrderEntity, todayCount, todayUnqualified, recordDate);
    }

    /**
     * 指定日期上报工单每日完成量
     *
     * @param
     */
    @Override
    public void saveOrUpdateWorkOrderDayCount(WorkOrderEntity workOrderEntity, Double count, Double unqualified, Date date) {
        this.saveOrUpdateWorkOrderDayCount(workOrderEntity, count, unqualified, null, date, false);
    }

    /**
     * 修改或新增工单每日投入数
     *
     * @param
     * @param date
     * @param input
     */
    @Override
    public void updateOrSaveInput(WorkOrderEntity workOrderEntity, Date date, Double input) {
        this.saveOrUpdateWorkOrderDayCount(workOrderEntity, null, null, input, date, false);
    }

    /**
     * 更新工单每日投入数
     *
     * @param
     */
    @Override
    @Async
    public void updateWorkOrderDayInput(WorkOrderEntity workOrderEntity) {
        Date recordDate = dictService.getRecordDate(new Date());
        String workOrder = workOrderEntity.getWorkOrderNumber();
        double inputSum = 0.0;
        //首先获取今天之前的记录，将总数求和
        QueryWrapper<RecordWorkOrderDayCountEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(RecordWorkOrderDayCountEntity::getWorkOrderNumber, workOrder)
                .lt(RecordWorkOrderDayCountEntity::getTime, recordDate);
        List<RecordWorkOrderDayCountEntity> countEntities = recordWorkOrderDayCountMapper.selectList(wrapper);

        if (!CollectionUtils.isEmpty(countEntities)) {
            DoubleSummaryStatistics inputDoubleSummaryStatistics = countEntities.stream().mapToDouble(RecordWorkOrderDayCountEntity::getInput).summaryStatistics();
            inputSum = inputDoubleSummaryStatistics.getSum();
        }
        //用完成数量减去今天之前的完成数，求出今天的数量
        Double input = workOrderEntity.getInputTotal();
        double todayInput = input == null ? 0.0 : input - inputSum;

        saveOrUpdateWorkOrderDayInput(workOrderEntity, todayInput, recordDate);
    }


    @Override
    public void saveOrUpdateWorkOrderDayCount(WorkOrderEntity workOrderEntity, Double count, Date recordDate) {
        this.saveOrUpdateWorkOrderDayCount(workOrderEntity, count, null, null, recordDate, false);
    }

    @Override
    public void saveOrUpdateWorkOrderDayInput(WorkOrderEntity workOrderEntity, Double input, Date recordDate) {
        this.saveOrUpdateWorkOrderDayCount(workOrderEntity, null, null, input, recordDate, false);
    }

    @Override
    public void saveOrUpdateWorkOrderDayUnqualified(WorkOrderEntity workOrderEntity, Double unqualified, Date recordDate) {
        this.saveOrUpdateWorkOrderDayCount(workOrderEntity, null, unqualified, null, recordDate, false);
    }

    /**
     * 获取某天所有产量
     *
     * @param
     */
    @Override
    public Double getCountByDate(Date date, Integer lineId) {
        List<RecordWorkOrderDayCountEntity> list = getByDateAndLineId(date, lineId);
        if (CollectionUtils.isEmpty(list)) {
            return 0.0;
        }
        return list.stream().mapToDouble(RecordWorkOrderDayCountEntity::getCount).sum();
    }

    @Override
    public Double getQualifiedRateCountByDate(Date date, Integer lineId) {
        List<RecordWorkOrderDayCountEntity> list = getByDateAndLineId(date, lineId);
        if (CollectionUtils.isEmpty(list)) {
            return 1.0;
        }
        double unqualified = list.stream().mapToDouble(RecordWorkOrderDayCountEntity::getUnqualified).sum();
        double finish = list.stream().mapToDouble(RecordWorkOrderDayCountEntity::getCount).sum();
        Double total = unqualified + finish;
        return total.equals(0.0) ? 1.0 : (finish / (unqualified + finish));
    }

    @Override
    public List<RecordWorkOrderDayCountEntity> getByDateAndLineId(Date date, Integer lineId) {
        return recordWorkOrderLineDayCountService.getByDateAndLineIds(date, Stream.of(lineId).collect(Collectors.toList()));
    }

    @Override
    public ScreenOutputDTO getRecordOutput(Integer lineId, Integer gridId) {
        Date now = new Date();

        Date end = dictService.getRecordDate(now);
        Date start = DateUtil.addDate(end, -7);

        List<ScreenOutputDTO.ScreenOutput> outputs = new ArrayList<>();
        for (int i = 6; i >= 0; i--) {
            Date date = DateUtil.addDate(end, -i);
            outputs.add(
                    ScreenOutputDTO.ScreenOutput.builder().convertedCompleted(0.0).completed(0.0).time(date).build()
            );
        }
        List<Integer> lineIds = null;
        if (Objects.nonNull(gridId)) {
            lineIds = productionLineService.lambdaQuery()
                    .eq(ProductionLineEntity::getGid, gridId)
                    .list()
                    .stream()
                    .map(ProductionLineEntity::getProductionLineId)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(lineIds)) {
                return ScreenOutputDTO.builder().list(outputs).unit(DEFAULT_UINT).build();
            }
        }
        List<RecordWorkOrderLineDayCountEntity> originList = recordWorkOrderLineDayCountService.lambdaQuery()
                .between(RecordWorkOrderLineDayCountEntity::getTime, start, end)
                .eq(Objects.nonNull(lineId), RecordWorkOrderLineDayCountEntity::getLineId, lineId)
                .in(Objects.nonNull(gridId), RecordWorkOrderLineDayCountEntity::getLineId, lineIds)
                .list();

        if (CollectionUtils.isEmpty(originList)) {
            return ScreenOutputDTO.builder().list(outputs).unit(DEFAULT_UINT).build();
        }

        String unit = DEFAULT_UINT;
        List<RecordWorkOrderLineDayCountEntity> convertedList = new ArrayList<>();

        //获取工单后再获取物料对象
        Set<String> workOrderCollect = originList.stream().map(RecordWorkOrderLineDayCountEntity::getWorkOrderNumber).collect(Collectors.toSet());
        if (!CollectionUtils.isEmpty(workOrderCollect)) {
            LambdaQueryWrapper<WorkOrderEntity> workOrderWrapper = new LambdaQueryWrapper<>();
            workOrderWrapper.in(WorkOrderEntity::getWorkOrderNumber, workOrderCollect);
            List<WorkOrderEntity> workOrderEntities = workOrderMapper.selectList(workOrderWrapper);
            Map<String, String> workOrderMap = workOrderEntities.stream().collect(Collectors.groupingBy(WorkOrderEntity::getWorkOrderNumber, Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0).getMaterialCode())));

            Map<String, MaterialEntity> materialMap = new HashMap<>();
            Set<String> materialCodeCollect = workOrderEntities.stream().map(WorkOrderEntity::getMaterialCode).collect(Collectors.toSet());
            if (!CollectionUtils.isEmpty(materialCodeCollect)) {
                LambdaQueryWrapper<MaterialEntity> materialWrapper = new LambdaQueryWrapper<>();
                materialWrapper.in(MaterialEntity::getCode, materialCodeCollect);
                List<MaterialEntity> materialEntities = materialMapper.selectList(materialWrapper);
                materialMap = materialEntities.stream().collect(Collectors.groupingBy(MaterialEntity::getCode, Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0))));
            }

            for (RecordWorkOrderLineDayCountEntity entity : originList) {
                RecordWorkOrderLineDayCountEntity build = RecordWorkOrderLineDayCountEntity.builder().time(entity.getTime()).count(entity.getCount()).build();
                String materialCode = workOrderMap.get(entity.getWorkOrderNumber());
                MaterialEntity materialEntity = materialMap.get(materialCode);
                //有双单位的转换为双单位
                if (materialEntity != null && materialEntity.getIsDoubleUnit()) {
                    Rational rational = new Rational(materialEntity.getUnitNumerator(), materialEntity.getUnitDenominator());
                    Double scaleFactor = rational.doubleValue();
                    build.setCount(MathUtil.round(entity.getCount() * scaleFactor, 2));
                    if (StringUtils.isBlank(unit)) {
                        unit = materialEntity.getUnit();
                    }
                }
                convertedList.add(build);
            }
        }

        Map<Long, DoubleSummaryStatistics> collect = originList.stream().collect(Collectors.groupingBy(o -> o.getTime().getTime(), Collectors.summarizingDouble(RecordWorkOrderLineDayCountEntity::getCount)));
        Map<Long, DoubleSummaryStatistics> convertedCollect = convertedList.stream().collect(Collectors.groupingBy(o -> o.getTime().getTime(), Collectors.summarizingDouble(RecordWorkOrderLineDayCountEntity::getCount)));

        //设置数据
        for (ScreenOutputDTO.ScreenOutput output : outputs) {
            DoubleSummaryStatistics doubleSummary = collect.get(output.getTime().getTime());
            if (doubleSummary != null) {
                output.setCompleted(doubleSummary.getSum());
            }
            DoubleSummaryStatistics convertedDoubleSummary = convertedCollect.get(output.getTime().getTime());
            if (convertedDoubleSummary != null) {
                output.setConvertedCompleted(convertedDoubleSummary.getSum());
            }
        }

        return ScreenOutputDTO.builder().unit(unit).list(outputs).build();
    }

    /**
     * 根据日期获取不合格数（不包含父工单）
     *
     * @param date
     * @param lineId
     * @return
     */
    @Override
    public Double getUnqualifiedCountCountByDate(Date date, Integer lineId) {
        List<RecordWorkOrderDayCountEntity> list = getByDateAndLineId(date, lineId);
        if (CollectionUtils.isEmpty(list)) {
            return 0.0;
        }
        return list.stream().mapToDouble(RecordWorkOrderDayCountEntity::getUnqualified).sum();
    }


    /**
     * 根据日期获取产量
     *
     * @param start
     * @param end
     * @return
     */
    @Override
    public List<RecordWorkOrderDayCountEntity> getDayCountByDays(String start, String end) {
        if (StringUtils.isAnyBlank(start, end)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<RecordWorkOrderDayCountEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.between(RecordWorkOrderDayCountEntity::getTime, start, end);
        return this.list(wrapper);
    }


    /**
     * 根据日期获取产量(过滤父工单)
     *
     * @param start
     * @param end
     * @return
     */
    @Override
    public List<RecordWorkOrderDayCountEntity> getDayCountByGridIdFilterParent(Integer girdId, String start, String end) {
        if (StringUtils.isAnyBlank(start, end)) {
            return new ArrayList<>();
        }
        List<Integer> lineIds = null;
        if (Objects.nonNull(girdId)) {
            lineIds = productionLineService.lambdaQuery()
                    .select(ProductionLineEntity::getProductionLineId)
                    .eq(ProductionLineEntity::getGid, girdId)
                    .list()
                    .stream().map(ProductionLineEntity::getProductionLineId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(lineIds)) {
                return new ArrayList<>();
            }
        }
        List<RecordWorkOrderLineDayCountEntity> list = recordWorkOrderLineDayCountService.lambdaQuery()
                .in(!CollectionUtils.isEmpty(lineIds), RecordWorkOrderLineDayCountEntity::getLineId, lineIds)
                .between(RecordWorkOrderLineDayCountEntity::getTime, start, end)
                .list();
        return JacksonUtil.convertArray(list, RecordWorkOrderDayCountEntity.class);
    }


    @Override
    public List<RecordWorkOrderDayCountEntity> getDayCountListByWorkOrder(String workOrderNumber) {
        LambdaQueryWrapper<RecordWorkOrderDayCountEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RecordWorkOrderDayCountEntity::getWorkOrderNumber, workOrderNumber);
        return this.list(wrapper);
    }

    @Override
    public List<OutputDTO> outputOpenList(OutputReqDTO dto) {
        LambdaQueryWrapper<RecordWorkOrderLineDayCountEntity> wrapper = new LambdaQueryWrapper<>();
        //不传，默认查询当天
        if (dto.getStartDate() != null && dto.getEndDate() != null) {
            wrapper.between(RecordWorkOrderLineDayCountEntity::getTime, dto.getStartDate(), dto.getEndDate());
        } else {
            Date date = dto.getDate();
            if (date == null) {
                date = dictService.getRecordDate(new Date());
            }
            wrapper.eq(RecordWorkOrderLineDayCountEntity::getTime, date);
        }

        if (StringUtils.isNotBlank(dto.getLineCode())) {
            ProductionLineEntity lineEntity = productionLineService.getEntityByLineCode(dto.getLineCode());
            wrapper.eq(RecordWorkOrderLineDayCountEntity::getLineId, lineEntity.getProductionLineId());
        }

        List<RecordWorkOrderLineDayCountEntity> list = recordWorkOrderLineDayCountService.list(wrapper);

        return list.stream().collect(Collectors.groupingBy(RecordWorkOrderLineDayCountEntity::getTime)).values().stream().map(
                o -> {
                    double count = o.stream().filter(p -> p.getCount() != null).mapToDouble(RecordWorkOrderLineDayCountEntity::getCount).sum();
                    double unqualified = o.stream().filter(p -> p.getUnqualified() != null).mapToDouble(RecordWorkOrderLineDayCountEntity::getUnqualified).sum();
                    return OutputDTO.builder().date(o.get(0).getTime()).count(count).unqualified(unqualified).build();
                }
        ).sorted(Comparator.comparing(OutputDTO::getDate)).collect(Collectors.toList());
    }


    /**
     * 拿到工单某日完成数
     *
     * @param workOrderNumber
     * @param date
     * @return
     */
    @Override
    public Double getWorkOrderDayCount(String workOrderNumber, Date date) {
        LambdaQueryWrapper<RecordWorkOrderDayCountEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(RecordWorkOrderDayCountEntity::getWorkOrderNumber, workOrderNumber)
                .eq(date != null,RecordWorkOrderDayCountEntity::getTime, date);
        List<RecordWorkOrderDayCountEntity> list = this.list(lambdaQueryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return 0.0;
        }
        return list.stream().mapToDouble(RecordWorkOrderDayCountEntity::getCount).sum();
    }

    /**
     * 计数器回调
     *
     * @param
     */
    @Override
    public void updateByWorkOrderNumberCallBack(String code) {
        String lockKey = RedisKeyPrefix.WORK_ORDER_DAY_COUNT_CALL_BACK_LOCK + code;
        Boolean lock = redisTemplate.opsForValue().setIfAbsent(lockKey, 0, 1, TimeUnit.MINUTES);
        if (lock == null || !lock) {
            return;
        }
        String[] split = code.split(Constant.SPECIAL_SEP);
        String workOrderNumber = split[0];
        WorkOrderService workOrderService = SpringUtil.getBean(WorkOrderService.class);
        WorkOrderEntity entity = workOrderService.getSimpleWorkOrderByNumber(workOrderNumber);
        if(entity == null) {
            log.warn("计数器回调每日更新方法，找不到工单={}, code={}", workOrderNumber, code);
            return;
        }
        updateWorkOrderDayCountOnly(entity);
        // 兼容旧场景
        if(split.length > 1) {
            String basicUnitIdStr = split[1];
            if(!StringUtils.isNumeric(basicUnitIdStr)) {
                log.warn("计数器回调每日更新方法，匹配到生产基本单元id={}有误, code={}", basicUnitIdStr, code);
            }else {
                workOrderBasicUnitDayCountService.updateByMac(entity, Integer.valueOf(basicUnitIdStr), true);
            }
            if(split.length > 2) {
                String relevanceDeviceId = split[2];
                if(!StringUtils.isNumeric(relevanceDeviceId)) {
                    log.warn("计数器回调每日更新方法，匹配到关联资源单元id={}有误, code={}", relevanceDeviceId, code);
                }else {
                    workOrderBasicUnitDayCountService.updateByMac(entity, Integer.valueOf(relevanceDeviceId), false);
                }
            }else {
                log.info("计数器回调每日更新方法，匹配到关联资源单元id, code={}", code);
            }
        }else {
            log.warn("计数器回调每日更新方法，未匹配到生产基本单元id, code={}", code);
        }
        redisTemplate.delete(lockKey);
    }

    @Override
    public WorkCountPerDayDTOPage getWorkCountPerDay(WorkCountPerDayVO vo) {
        WorkCountPerDayDTOPage result = new WorkCountPerDayDTOPage();
        result.setCurrent(vo.getCurrent());
        result.setSize(vo.getSize());
        // 根据时间差，计算出总条数
        long total = vo.getEndDate().toEpochDay() - vo.getStartDate().toEpochDay() + 1;
        if (total < 0) {
            return result;
        }
        result.setTotal(total);
        // 总页码
        if (total % vo.getSize() == 0) {
            result.setPages(total / vo.getSize());
        } else {
            result.setPages(total / vo.getSize() + 1);
        }
        // sql按照时间区间进行查询即可
        // 开始时间的偏移量
        int startOffset = (vo.getCurrent() - 1) * vo.getSize();
        int remain = (int) (total - startOffset);
        if (remain <= 0) {
            return result;
        }
        LocalDate startQuery = vo.getStartDate().plusDays(startOffset);
        // 截至时间的偏移量（相对于startQuery）
        int endOffset = Math.min(remain, vo.getSize());
        LocalDate endQuery = startQuery.plusDays(endOffset - 1);
        vo.setStartQuery(startQuery);
        vo.setEndQuery(endQuery);
        List<WorkCountPerDayQueryDTO> originList = recordWorkOrderDayCountMapper.getWorkCountPerDay(vo);

        // 按照时间分组
        Map<String, List<WorkCountPerDayQueryDTO>> groupByDate = originList.stream().collect(Collectors.groupingBy(WorkCountPerDayQueryDTO::getDate, LinkedHashMap::new, Collectors.toList()));

        // 遍历：从第startQuery ~ endQuery
        List<WorkCountPerDayDTO> records = IntStream.range(0, endOffset).mapToObj(i -> {
            WorkCountPerDayDTO record = new WorkCountPerDayDTO();
            LocalDate date = startQuery.plusDays(i);
            record.setDate(date);
            List<WorkCountPerDayQueryDTO> list = groupByDate.get(date.toString());
            if (!CollectionUtils.isEmpty(list)) {
                record.setData(list.stream().collect(Collectors.toMap(WorkCountPerDayQueryDTO::getName, WorkCountPerDayQueryDTO::getCount, (k1, k2) -> k1)));
                record.setCount(list.stream().map(WorkCountPerDayQueryDTO::getCount).reduce(Long::sum).orElse(0L));
            }
            return record;
        }).collect(Collectors.toList());
        result.setRecords(records);

        // 查所有的
        List<WorkCountPerDayQuerySumDTO> workCountPerDaySum = recordWorkOrderDayCountMapper.getWorkCountPerDaySum(vo);
        // 动态表头
        result.setHeadNames(
                workCountPerDaySum.stream().map(WorkCountPerDayQuerySumDTO::getName).collect(Collectors.toList())
        );
        // 最终列的统计
        result.setStatistics(
                workCountPerDaySum.stream().collect(Collectors.toMap(WorkCountPerDayQuerySumDTO::getName, WorkCountPerDayQuerySumDTO::getCount))
        );
        result.setTotalSum(
                workCountPerDaySum.stream().map(WorkCountPerDayQuerySumDTO::getCount).reduce(Long::sum).orElse(0L)
        );
        return result;
    }

    @Override
    public void exportWorkCountPerDay(HttpServletResponse response, WorkCountPerDayVO vo) throws IOException {
        // 限制导出最大数100w, 一次导出全部
        vo.setSize(1000000);
        vo.setCurrent(1);
        WorkCountPerDayDTOPage page = getWorkCountPerDay(vo);
        List<String> headNames = page.getHeadNames();
        List<WorkCountPerDayDTO> records = page.getRecords();
        Map<String, Long> statistics = page.getStatistics();

        XSSFWorkbook workbook = null;
        String fileName = "车间每日产品产量汇总报表";
        try {
            workbook = new XSSFWorkbook();
            XSSFSheet sheet = workbook.createSheet();
            // 1. 表头
            XSSFRow rowT0 = sheet.createRow(0);
            // 1.1 日期
            XSSFCell cellT00 = rowT0.createCell(0);
            cellT00.setCellValue("日期");

            // 1.2 其他动态字段
            for (int i = 0; i < headNames.size(); i++) {
                String headName = headNames.get(i);
                XSSFCell cellT0n = rowT0.createCell(i + 1);
                cellT0n.setCellValue(headName);
            }
            // 1.3 合计
            XSSFCell cellT0l = rowT0.createCell(headNames.size() + 1);
            cellT0l.setCellValue("合计");
            // 2 行数据
            for (int i = 0; i < records.size(); i++) {
                WorkCountPerDayDTO record = records.get(i);
                XSSFRow row = sheet.createRow(i + 1);
                XSSFCell cellTn0 = row.createCell(0);
                // 日期
                cellTn0.setCellValue(Optional.ofNullable(record.getDate()).map(Object::toString).orElse(""));
                // 动态字段
                for (int j = 0; j < headNames.size(); j++) {
                    String headName = headNames.get(j);
                    XSSFCell cellTnn = row.createCell(j + 1);
                    cellTnn.setCellValue(Optional.ofNullable(record.getData()).map(e -> e.get(headName)).orElse(0L));
                }
                // 合计
                XSSFCell cellTnl = row.createCell(headNames.size() + 1);
                cellTnl.setCellValue(record.getCount());
            }
            // 最终行：合计列
            XSSFRow rowTl = sheet.createRow(records.size() + 2);
            for (int j = 0; j < headNames.size(); j++) {
                String headName = headNames.get(j);
                XSSFCell cellTln = rowTl.createCell(j + 1);
                cellTln.setCellValue(statistics.get(headName));
            }
            XSSFCell cellTll = rowTl.createCell(headNames.size() + 1);
            cellTll.setCellValue(page.getTotalSum());


            response.setHeader("Content-disposition", "attachment;filename=\"" + fileName + ExcelUtil.XLSX + "\"; filename*=utf-8'zh_cn'" + fileName + ExcelUtil.XLSX);
            workbook.write(response.getOutputStream());
        } catch (Exception e) {
            log.error("", e);
            throw new ResponseException("导出异常");
        } finally {
            if (workbook != null) {
                workbook.close();
            }
        }

    }

    @Override
    public List<RecordWorkOrderDayCountEntity> getWorkOrderDayCountList(String workOrderNumber) {
        return this.lambdaQuery().eq(RecordWorkOrderDayCountEntity::getWorkOrderNumber, workOrderNumber).list();
    }

    /**
     * 工单每日产量通用更新方法
     *
     * @param workOrderEntity
     * @param count
     * @param unqualified
     * @param input
     * @param date
     * @param isMultiLine 是否是多产线报工
     */
    public RecordWorkOrderDayCountEntity saveOrUpdateWorkOrderDayCount(WorkOrderEntity workOrderEntity, Double count, Double unqualified, Double input, Date date, Boolean isMultiLine) {
        // 工单产线每日统计需要完整实体对象
        List<RecordWorkOrderDayCountEntity> list = this.lambdaQuery()
                .eq(RecordWorkOrderDayCountEntity::getWorkOrderNumber, workOrderEntity.getWorkOrderNumber())
                .eq(RecordWorkOrderDayCountEntity::getTime, date).list();
        RecordWorkOrderDayCountEntity entity;
        RecordWorkOrderDayCountEntity old = null;
        if (CollectionUtils.isEmpty(list)) {
            entity = RecordWorkOrderDayCountEntity.builder()
                    .workOrderNumber(workOrderEntity.getWorkOrderNumber()).count(count)
                    .unqualified(unqualified)
                    .input(input)
                    .lineId(workOrderEntity.getLineId())
                    .teamId(workOrderEntity.getTeamId())
                    .deviceId(workOrderEntity.getDeviceId())
                    .materialCode(workOrderEntity.getMaterialCode()).time(date).build();
            save(entity);
        } else {
            if (list.size() > 1) {
                for (int i = 1; i < list.size(); i++) {
                    this.removeById(list.get(i));
                }
            }
            entity = list.get(0);
            old = BeanUtil.copyProperties(entity, RecordWorkOrderDayCountEntity.class);
            entity.setCount(count);
            entity.setUnqualified(unqualified);
            entity.setInput(input);
            updateById(entity);
        }

        // 每日统计变更事件
        applicationContext.publishEvent(new LimitWorkOrderDayCountUpdateEvent(this, old, entity));
        // 保存工单每产线每日数量，单产线
        if (Boolean.FALSE.equals(isMultiLine)) {
            recordWorkOrderLineDayCountService.saveOrUpdateWorkOrderLineDayCount(entity);
        }
        return entity;
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateWorkOrderDayCount(WorkOrderEntity workOrderEntity, Date date, List<WorkOrderLineDayCountDTO> lineDayCounts) {
        Double count = lineDayCounts.stream().map(WorkOrderLineDayCountDTO::getCount).filter(Objects::nonNull).reduce(Double::sum).orElse(null);
        Double input = lineDayCounts.stream().map(WorkOrderLineDayCountDTO::getInput).filter(Objects::nonNull).reduce(Double::sum).orElse(null);
        Double unqualified = lineDayCounts.stream().map(WorkOrderLineDayCountDTO::getUnqualified).filter(Objects::nonNull).reduce(Double::sum).orElse(null);
        RecordWorkOrderDayCountEntity entity = this.saveOrUpdateWorkOrderDayCount(workOrderEntity, count, unqualified, input, date, true);
        // 保存工单每产线每日数量，多产线
        recordWorkOrderLineDayCountService.saveOrUpdateWorkOrderLineDayCount(entity, lineDayCounts);
    }

}



