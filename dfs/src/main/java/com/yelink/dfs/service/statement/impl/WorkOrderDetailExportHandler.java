package com.yelink.dfs.service.statement.impl;

import com.asyncexcel.core.DataParam;
import com.asyncexcel.core.ExcelContext;
import com.asyncexcel.core.ExportPage;
import com.asyncexcel.core.annotation.ExcelHandle;
import com.asyncexcel.core.exporter.DataExportParam;
import com.asyncexcel.core.exporter.ExportContext;
import com.asyncexcel.core.exporter.ExportHandler;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.entity.statement.dto.WorkOrderOverviewDTO;
import com.yelink.dfs.entity.target.metrics.dto.MetricsWorkOrderSelectDTO;
import com.yelink.dfs.service.common.CommonService;
import com.yelink.dfs.service.statement.StatementService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 销售订单导出
 *
 * <AUTHOR>
 */
@Slf4j
@ExcelHandle
@RequiredArgsConstructor
public class WorkOrderDetailExportHandler implements ExportHandler<WorkOrderOverviewDTO> {

    private final StatementService statementService;
    private final CommonService commonService;

    /**
     * 导出数据最大不能超过100万行
     */
    public static final Long EXPORT_MAX_ROWS = 1000000L;


    @Override
    public void init(ExcelContext ctx, DataParam param) {
        ExportContext context = (ExportContext) ctx;

        Integer templateId = (Integer) param.getParameters().get("templateId");
        String templateSheetName = (String) param.getParameters().get("workOrderDetail");
        String cleanSheetNames = (String) param.getParameters().get("cleanSheetNames");
        commonService.initExcelContext(templateId, templateSheetName, cleanSheetNames, context, WorkOrderOverviewDTO.class, null);
    }

    @Override
    public ExportPage<WorkOrderOverviewDTO> exportData(int startPage, int limit, DataExportParam param) {
        MetricsWorkOrderSelectDTO selectDTO = (MetricsWorkOrderSelectDTO) param.getParameters().get(MetricsWorkOrderSelectDTO.class.getName());
        selectDTO.setCurrent(startPage);
        selectDTO.setSize(limit);
        Page<WorkOrderOverviewDTO> page = statementService.statementListWorkOrder(selectDTO);
        List<WorkOrderOverviewDTO> workOrderExports = page.getRecords();
        ExportPage<WorkOrderOverviewDTO> result = new ExportPage<>();
        result.setTotal(Math.min(page.getTotal(), EXPORT_MAX_ROWS));
        result.setCurrent((long) startPage);
        result.setSize((long) limit);
        result.setRecords(workOrderExports);
        return result;
    }


    @Override
    public void beforePerPage(ExportContext ctx, DataExportParam param) {
        log.info("开始查询第{}页", Math.ceil(ctx.getSuccessCount() + ctx.getFailCount()) / ctx.getLimit());
    }

    @Override
    public void afterPerPage(List<WorkOrderOverviewDTO> list, ExportContext ctx, DataExportParam param) {
        log.info("已完成数量：{}", ctx.getSuccessCount());
    }

    @Override
    public void callBack(ExcelContext ctx, DataParam param) {
        ExportContext context = (ExportContext) ctx;
        log.info("导出完成：{}", context.getFailMessage());
    }
}
