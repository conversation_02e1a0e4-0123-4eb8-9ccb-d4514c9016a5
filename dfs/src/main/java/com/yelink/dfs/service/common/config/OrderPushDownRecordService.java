package com.yelink.dfs.service.common.config;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yelink.dfscommon.dto.pushdown.item.PushDownRecordDeleteDTO;
import com.yelink.dfscommon.dto.pushdown.item.PushDownRecordSelectDTO;
import com.yelink.dfscommon.entity.dfs.OrderPushDownRecordEntity;
import com.yelink.dfscommon.pojo.PageResult;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface OrderPushDownRecordService extends IService<OrderPushDownRecordEntity> {

    /**
     * 下推记录列表
     *
     * @param selectDTO 查询条件
     * @return
     */
    Page<OrderPushDownRecordEntity> getRecordList(PushDownRecordSelectDTO selectDTO);

    /**
     * 新增下推记录
     *
     * @return
     */
    void batchAddRecord(List<OrderPushDownRecordEntity> entities);

    List<OrderPushDownRecordEntity> getSourceRecordList(PushDownRecordSelectDTO selectDTO);

    /**
     * 删除下推记录
     *
     * @return
     */
    void deleteRecord(PushDownRecordDeleteDTO deleteDTO);

    /**
     * 更新下推记录
     *
     * @return
     */
    void batchUpdateRecord(List<OrderPushDownRecordEntity> entities);

    void showName(List<OrderPushDownRecordEntity> result);
}

