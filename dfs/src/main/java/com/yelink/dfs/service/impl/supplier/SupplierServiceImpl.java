package com.yelink.dfs.service.impl.supplier;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yelink.dfs.constant.AppendixTypeEnum;
import com.yelink.dfs.constant.DictTypeEnum;
import com.yelink.dfs.constant.ImportTypeEnum;
import com.yelink.dfs.constant.ReportFormConstant;
import com.yelink.dfs.constant.supplier.SupplierCategoryEnum;
import com.yelink.dfs.constant.supplier.SupplierMaterialStateEnum;
import com.yelink.dfs.constant.supplier.SupplierStateEnum;
import com.yelink.dfs.constant.supplier.SupplierTypeEnum;
import com.yelink.dfs.constant.supplier.SupplierMaterialStateEnum;
import com.yelink.dfs.entity.common.ImportDataRecordEntity;
import com.yelink.dfs.entity.order.CounterpartEntity;
import com.yelink.dfs.entity.product.MaterialEntity;
import com.yelink.dfs.entity.supplier.SupplierEntity;
import com.yelink.dfs.entity.supplier.SupplierMaterialEntity;
import com.yelink.dfs.entity.supplier.SupplierRelatedTypeEntity;
import com.yelink.dfs.entity.supplier.dto.SupplierImportDTO;
import com.yelink.dfs.entity.supplier.dto.SupplierMaterialByMaterialDTO;
import com.yelink.dfs.entity.supplier.dto.SupplierMaterialDTO;
import com.yelink.dfs.entity.user.SysUserEntity;
import com.yelink.dfs.mapper.supplier.SupplierMapper;
import com.yelink.dfs.mapper.user.SysUserMapper;
import com.yelink.dfs.open.v1.supplier.dto.MaterialUpdateInsertDTO;
import com.yelink.dfs.open.v1.supplier.dto.SupplierDTO;
import com.yelink.dfs.open.v1.supplier.dto.SupplierDetailDTO;
import com.yelink.dfs.open.v1.supplier.dto.SupplierInsertOrUpdateDTO;
import com.yelink.dfs.open.v1.supplier.dto.SupplierSelectDTO;
import com.yelink.dfs.open.v2.common.constant.BatchOperationErrorType;
import com.yelink.dfs.open.v2.common.util.BatchOperationResultBuilder;
import com.yelink.dfs.open.v2.supplier.dto.SupplierBatchInsertDTO;
import com.yelink.dfs.open.v2.supplier.dto.SupplierBatchUpdateDTO;
import com.yelink.dfs.open.v2.supplier.dto.SupplierDeleteDTO;
import com.yelink.dfs.open.v2.supplier.dto.SupplierDetailQueryDTO;
import com.yelink.dfs.open.v2.supplier.dto.SupplierQueryDTO;
import com.yelink.dfs.open.v2.supplier.dto.SupplierMaterialBatchInsertDTO;
import com.yelink.dfs.open.v2.supplier.dto.SupplierMaterialBatchUpdateDTO;
import com.yelink.dfs.open.v2.supplier.dto.SupplierMaterialBatchDeleteDTO;
import com.yelink.dfs.open.v2.supplier.dto.SupplierMaterialDetailQueryDTO;
import com.yelink.dfs.open.v2.material.vo.MaterialFieldVO;
import com.yelink.dfs.open.v2.supplier.vo.SupplierBatchDeleteResultVO;
import com.yelink.dfs.open.v2.supplier.vo.SupplierBatchInsertResultVO;
import com.yelink.dfs.open.v2.supplier.vo.SupplierBatchUpdateResultVO;
import com.yelink.dfs.open.v2.supplier.vo.SupplierFieldDetailVO;
import com.yelink.dfs.open.v2.supplier.vo.SupplierMaterialVO;
import com.yelink.dfs.open.v2.supplier.vo.SupplierSyncResultVO;
import com.yelink.dfs.open.v2.supplier.vo.SupplierVO;
import com.yelink.dfs.open.v2.supplier.vo.SupplierMaterialDetailVO;
import com.yelink.dfs.open.v2.supplier.vo.SupplierMaterialBatchOperationResultVO;
import com.yelink.dfs.open.v2.supplier.vo.SupplierMaterialSyncResultVO;
import com.yelink.dfs.provider.FastDfsClientService;
import com.yelink.dfs.provider.constant.KafkaMessageTypeEnum;
import com.yelink.dfs.service.approve.config.ApproveConfigService;
import com.yelink.dfs.service.common.AppendixService;
import com.yelink.dfs.service.common.DictService;
import com.yelink.dfs.service.common.ImportDataRecordService;
import com.yelink.dfs.service.order.RuleSeqService;
import com.yelink.dfs.service.product.MaterialService;
import com.yelink.dfs.service.rule.NumberRuleService;
import com.yelink.dfs.service.supplier.SupplierMaterialService;
import com.yelink.dfs.service.supplier.SupplierRelatedTypeService;
import com.yelink.dfs.service.supplier.SupplierService;
import com.yelink.dfs.service.user.SysUserService;
import com.yelink.dfs.utils.SpringUtil;
import com.yelink.dfs.utils.SqlUtil;
import com.yelink.dfscommon.constant.Constants;
import com.yelink.dfscommon.constant.RedisKeyPrefix;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.constant.approve.config.ApprovalStatusEnum;
import com.yelink.dfscommon.constant.approve.config.ApproveModuleEnum;
import com.yelink.dfscommon.constant.dfs.rule.NumberCodeDTO;
import com.yelink.dfscommon.dto.ApproveBatchDTO;
import com.yelink.dfscommon.dto.ImportProgressDTO;
import com.yelink.dfscommon.dto.dfs.RuleSeqDTO;
import com.yelink.dfscommon.entity.AppendixEntity;
import com.yelink.dfscommon.entity.dfs.DictEntity;
import com.yelink.dfscommon.entity.dfs.supplier.SupplierMaterialSelectDTO;
import com.yelink.dfscommon.exception.BatchOperationException;
import com.yelink.dfscommon.provider.MessagePushToKafkaService;
import com.yelink.dfscommon.utils.EasyExcelUtil;
import com.yelink.dfscommon.utils.ExcelTemplateImportUtil;
import com.yelink.dfscommon.utils.FieldUtil;
import com.yelink.dfscommon.utils.JacksonUtil;
import com.yelink.dfscommon.utils.MathUtil;
import com.yelink.dfscommon.utils.WrapperUtil;
import com.yelink.dfscommon.exception.BatchOperationException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.yelink.dfs.constant.Constant.SEP;
import static com.yelink.dfs.constant.Constant.XLSX;

/**
 * Service
 *
 * <AUTHOR>
 * @Date 2021-04-07 11:11
 */
@Service
@Slf4j
public class SupplierServiceImpl extends ServiceImpl<SupplierMapper, SupplierEntity> implements SupplierService {
    @Resource
    private SupplierMapper supplierMapper;
    @Lazy
    @Resource
    private MaterialService materialService;
    @Resource
    private SupplierMaterialService supplierMaterialService;
    @Resource
    private SysUserService sysUserService;
    @Resource
    private ApproveConfigService approveConfigService;
    @Resource
    private MessagePushToKafkaService messagePushToKafkaService;
    @Resource
    protected RedisTemplate<String, Object> redisTemplate;
    @Resource
    private ImportDataRecordService importDataRecordService;
    @Resource
    private SysUserMapper sysUserMapper;
    @Resource
    private FastDfsClientService fastDfsClientService;
    @Resource
    private DictService dictService;
    @Resource
    private SupplierRelatedTypeService supplierRelatedTypeService;
    @Resource
    private AppendixService appendixService;
    @Resource
    private NumberRuleService numberRuleService;
    @Resource
    private RuleSeqService ruleSeqService;

    @Override
    public Page<SupplierEntity> list(String name, String approvalStatus, String contacts, String counterpart, String code, Integer currentPage, Integer size, String type, String state, String category) {
        LambdaQueryWrapper<SupplierEntity> wrapper = new LambdaQueryWrapper<>();
        WrapperUtil.like(wrapper, SupplierEntity::getName, name);
        WrapperUtil.like(wrapper, SupplierEntity::getCode, code);
        WrapperUtil.like(wrapper, SupplierEntity::getContacts, contacts);
        WrapperUtil.eq(wrapper, SupplierEntity::getCounterpart, counterpart);
        if (StringUtils.isNotBlank(state)) {
            List<Integer> states = Arrays.stream(state.split(SEP)).map(Integer::valueOf).collect(Collectors.toList());
            wrapper.in(SupplierEntity::getState, states);
        }
        if (StringUtils.isNotBlank(approvalStatus)) {
            List<Integer> states = Arrays.stream(approvalStatus.split(SEP)).map(Integer::valueOf).collect(Collectors.toList());
            wrapper.in(SupplierEntity::getApprovalStatus, states);
        }
        if (StringUtils.isNotBlank(type)) {
            List<Integer> typeCodes = Arrays.stream(type.split(SEP)).map(Integer::valueOf).collect(Collectors.toList());
            // 获取委外供应商类型的供应商
            List<SupplierRelatedTypeEntity> supplierRelatedTypeEntities = supplierRelatedTypeService.lambdaQuery().in(SupplierRelatedTypeEntity::getTypeCode, typeCodes).list();
            if (CollectionUtils.isEmpty(supplierRelatedTypeEntities)) {
                return new Page<>();
            }
            List<String> supplierCodes = supplierRelatedTypeEntities.stream().map(SupplierRelatedTypeEntity::getSupplierCode).distinct().collect(Collectors.toList());
            wrapper.in(SupplierEntity::getCode, supplierCodes);
        }
        if (StringUtils.isNotBlank(category)) {
            List<String> categories = Arrays.stream(category.split(SEP)).collect(Collectors.toList());
            wrapper.in(SupplierEntity::getCategory, categories);
        }
        wrapper.orderByDesc(SupplierEntity::getCreateTime)
                .orderByDesc(SupplierEntity::getId);
        Page<SupplierEntity> page;
        if (currentPage == null || size == null) {
            List<SupplierEntity> list = supplierMapper.selectList(wrapper);
            if (list.size() > 0) {
                // 获取供应商绑定的物料信息
                list.forEach(this::setMaterialEntities);
            }
            page = new Page<>(1, list.size(), list.size());
            page.setRecords(list);
        } else {
            // 分页查询
            page = this.page(new Page<>(currentPage, size), wrapper);
        }
        // 获取中文名称
        showName(page.getRecords());
        return page;
    }

    @Override
    public SupplierEntity getEntityByCode(String code) {
        LambdaQueryWrapper<SupplierEntity> qw = new LambdaQueryWrapper<>();
        qw.eq(SupplierEntity::getCode, code);
        SupplierEntity supplierEntity = supplierMapper.selectOne(qw);
        if (supplierEntity != null) {
            // 获取供应商的物料列表
            setMaterialEntities(supplierEntity);
            // 获取人名和状态名称
            List<SupplierEntity> list = new ArrayList<>();
            list.add(supplierEntity);
            showName(list);
            //获取附件
            List<AppendixEntity> appendixEntities = appendixService.listByRelateId(supplierEntity.getId().toString(), AppendixTypeEnum.SUPPLIER_APPENDIX.getCode());
            supplierEntity.setAppendixes(appendixEntities);
        }
        return supplierEntity;
    }

    /**
     * 获取中文名称
     */
    private void showName(List<SupplierEntity> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<SysUserEntity> sysUserEntities = sysUserService.selectList();
        Map<String, String> nickNames = sysUserEntities.stream().collect(Collectors.toMap(SysUserEntity::getUsername, SysUserEntity::getNickname));
        Map<String, String> signatureUrlMap = sysUserService.getSignatureUrlMap(null);
        for (SupplierEntity entity : list) {
            entity.setStateName(SupplierStateEnum.getNameByCode(entity.getState()));
            entity.setApprovalStatusName(ApprovalStatusEnum.getNameByCode(entity.getApprovalStatus()));
            // 获取人名
            entity.setCreateByName(nickNames.get(entity.getCreateBy()) == null ? entity.getCreateBy() : nickNames.get(entity.getCreateBy()));
            entity.setApproverName(nickNames.get(entity.getApprover()) == null ? entity.getApprover() : nickNames.get(entity.getApprover()));
            entity.setActualApproverName(nickNames.get(entity.getActualApprover()) == null ? entity.getActualApprover() : nickNames.get(entity.getActualApprover()));
            entity.setActualApproverSignatureUrl(signatureUrlMap.get(entity.getActualApprover()));
            entity.setContactsName(nickNames.get(entity.getContacts()) == null ? entity.getContacts() : nickNames.get(entity.getContacts()));
            entity.setCounterpartName(nickNames.get(entity.getCounterpart()) == null ? entity.getCounterpart() : nickNames.get(entity.getCounterpart()));
        }
    }

    /**
     * 获取供应商的物料列表
     *
     * @param
     * @return
     */
    private void setMaterialEntities(SupplierEntity supplierEntity) {
        List<SupplierMaterialEntity> list = supplierMaterialService.lambdaQuery()
                .eq(SupplierMaterialEntity::getSupplierId, supplierEntity.getId())
                .list();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (SupplierMaterialEntity supplierMaterialEntity : list) {
            // 查询物料相关字段
            supplierMaterialEntity.setMaterialFields(materialService.lambdaQuery().eq(MaterialEntity::getId, supplierMaterialEntity.getMaterialId()).one());
        }
        supplierEntity.setSupplierMaterialList(list);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveEntity(SupplierEntity supplierEntity) {
        // 如果传入的编码为空，则由编码规则生成
        if (StringUtils.isBlank(supplierEntity.getCode())) {
            if (Objects.isNull(supplierEntity.getNumberRuleId())) {
                throw new ResponseException(RespCodeEnum.NEED_NUMBER_RULE);
            }
            NumberCodeDTO seqById = numberRuleService.getSeqById(
                    RuleSeqDTO.builder()
                            .id(supplierEntity.getNumberRuleId())
                            .build()
            );
            supplierEntity.setCode(seqById.getCode());
        }
        // 判断编号是否重复
        notRepeat(supplierEntity);
        //重复名称判断
        Long count = this.lambdaQuery().eq(SupplierEntity::getName, supplierEntity.getName()).count();
        if (count > 0) {
            throw new ResponseException(RespCodeEnum.SUPPLIER_FAIL2SAME_NAME);
        }
        // 设置状态
        supplierEntity.setState(SupplierStateEnum.CREATE.getCode());
        // 如果需要审批，则缺省为待审核状态
        if (approveConfigService.getConfigByCode(ApproveModuleEnum.SUPPLIER_PROFILE.getCode())) {
            supplierEntity.setApprovalStatus(ApprovalStatusEnum.TO_BE_APPROVAL.getCode());
        }
        // 添加供应商信息
        boolean save = this.save(supplierEntity);
        // 如调用到编码规则，则相对应的所有序列号加1
        if (Objects.nonNull(supplierEntity.getNumberRuleId())) {
            ruleSeqService.updateSeqEntity(null, supplierEntity.getNumberRuleId());
        }
        // 调用新增供应商的方法时，推送消息
        messagePushToKafkaService.pushNewMessage(supplierEntity, Constants.KAFKA_MAIN_DATA_TOPIC, KafkaMessageTypeEnum.SUPPLIER_ADD_MESSAGE);
        // 供应商绑定物料
        if (!CollectionUtils.isEmpty(supplierEntity.getMaterialIds())) {
            for (Integer id : supplierEntity.getMaterialIds()) {
                SupplierMaterialEntity entity = SupplierMaterialEntity.builder()
                        .materialId(id)
                        .supplierId(supplierEntity.getId())
                        .build();
                supplierMaterialService.save(entity);
            }
        }
        // 供应商绑定供应商类型
        bindRelatedSupplierType(supplierEntity);
        // 保存附件表
        appendixService.addAppendix(supplierEntity.getAppendixes(), AppendixTypeEnum.SUPPLIER_APPENDIX,
                supplierEntity.getId().toString(), supplierEntity.getCode(), supplierEntity.getCreateBy());
        return save;
    }

    /**
     * 供应商绑定供应商类型
     */
    private void bindRelatedSupplierType(SupplierEntity supplierEntity) {
        if (StringUtils.isNotBlank(supplierEntity.getType())) {
            // 删除原绑定数据
            supplierRelatedTypeService.lambdaUpdate().eq(SupplierRelatedTypeEntity::getSupplierCode, supplierEntity.getCode()).remove();
            // 重新添加数据
            List<String> supplierNames = Arrays.asList(supplierEntity.getType().split(SEP));
            List<SupplierRelatedTypeEntity> relatedTypeEntities = supplierNames.stream().map(o ->
                    SupplierRelatedTypeEntity.builder()
                            .supplierCode(supplierEntity.getCode())
                            .typeCode(SupplierTypeEnum.getCodeByName(o))
                            .typeName(o)
                            .build()).collect(Collectors.toList());
            supplierRelatedTypeService.saveBatch(relatedTypeEntities);
        }
    }

    /**
     * 新增生效供应商
     *
     * @param entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveReleasedEntity(SupplierEntity entity) {
        if (approveConfigService.getConfigByCode(ApproveModuleEnum.SUPPLIER_PROFILE.getCode())) {
            throw new ResponseException(RespCodeEnum.ORDER_NEED_TO_APPROVE);
        }
        SupplierService supplierService = SpringUtil.getBean(SupplierService.class);
        supplierService.saveEntity(entity);
        entity.setUpdateAppendix(false);
        entity.setState(SupplierStateEnum.TAKE_EFFECT.getCode());
        supplierService.updateEntityById(entity);
    }

    @Override
    public void updateEntityById(SupplierEntity supplierEntity) {
        SupplierEntity oldEntity = getById(supplierEntity.getId());
        //如果是驳回状态,修改数据后变更为待审核状态。此处只要是创建状态,状态改为待审批可覆盖这个需求
        if (supplierEntity.getState() == SupplierStateEnum.CREATE.getCode()
                && approveConfigService.getConfigByCode(ApproveModuleEnum.SUPPLIER_PROFILE.getCode())) {
            supplierEntity.setApprovalStatus(ApprovalStatusEnum.TO_BE_APPROVAL.getCode());
        }
        // 修改供应商信息
        this.updateById(supplierEntity);
        // 修改绑定的供应商类型信息
        bindRelatedSupplierType(supplierEntity);
        // 附件
        if(Boolean.TRUE.equals(supplierEntity.getUpdateAppendix())) {
            appendixService.removeByRelateId(supplierEntity.getId().toString(), AppendixTypeEnum.SUPPLIER_APPENDIX.getCode());
            appendixService.addAppendix(supplierEntity.getAppendixes(), AppendixTypeEnum.SUPPLIER_APPENDIX,
                    supplierEntity.getId().toString(), supplierEntity.getCode(), supplierEntity.getUpdateBy());
        }
        // 调用更新供应商的方法时，推送消息
        messagePushToKafkaService.pushNewMessage(supplierEntity, Constants.KAFKA_MAIN_DATA_TOPIC, KafkaMessageTypeEnum.SUPPLIER_UPDATE_MESSAGE);
        // 如果状态发生变更，需要发布状态变更消息
        if (!oldEntity.getState().equals(supplierEntity.getState())) {
            messagePushToKafkaService.pushNewMessage(supplierEntity, Constants.KAFKA_MAIN_DATA_STATE_TOPIC, KafkaMessageTypeEnum.SUPPLIER_STATUS_CHANGE_MESSAGE);
        }
    }

    @Override
    public void examineOrApprove(Integer approvalStatus, String approvalSuggestion, Integer id, String username) {
        if (id == null) {
            return;
        }
        LambdaQueryWrapper<SupplierEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(SupplierEntity::getId, SupplierEntity::getState, SupplierEntity::getApprovalStatus)
                .eq(SupplierEntity::getId, id);
        SupplierEntity entity = this.getOne(wrapper);
        if (entity == null || entity.getState() == null) {
            return;
        }
        Integer preApprovalStatus = entity.getApprovalStatus();
        if (preApprovalStatus == null) {
            preApprovalStatus = entity.getState().equals(SupplierStateEnum.CREATE.getCode()) ?
                    ApprovalStatusEnum.TO_BE_APPROVAL.getCode() : ApprovalStatusEnum.REJECTED.getCode();
        }
        boolean toApproved = approvalStatus == ApprovalStatusEnum.APPROVED.getCode()
                && preApprovalStatus == ApprovalStatusEnum.TO_BE_APPROVAL.getCode();

        boolean toReject = approvalStatus == ApprovalStatusEnum.REJECTED.getCode()
                && preApprovalStatus == ApprovalStatusEnum.TO_BE_APPROVAL.getCode();
        if (toApproved || toReject) {
            LambdaUpdateWrapper<SupplierEntity> uw = new LambdaUpdateWrapper<>();
            uw.eq(SupplierEntity::getId, id)
                    .set(SupplierEntity::getApprovalStatus, approvalStatus)
                    .set(SupplierEntity::getApprovalSuggestion, approvalSuggestion)
                    .set(SupplierEntity::getActualApprover, username)
                    .set(SupplierEntity::getApprovalTime, new Date());
            if (toApproved) {
                uw.set(SupplierEntity::getState, SupplierStateEnum.TAKE_EFFECT.getCode());
            }
            this.update(uw);
        }
    }

    @Override
    public void approveBatch(ApproveBatchDTO dto) {
        List<Integer> ids = dto.getIds();
        Integer approvalStatus = dto.getApprovalStatus();
        if (CollectionUtils.isEmpty(ids) || approvalStatus == null) {
            return;
        }
        List<SupplierEntity> entities = this.listByIds(ids);
        List<String> approvers = entities.stream().map(SupplierEntity::getApprover).distinct().collect(Collectors.toList());
        approvers.remove(dto.getActualApprover());
        if (!CollectionUtils.isEmpty(approvers)) {
            throw new ResponseException(RespCodeEnum.CURRENT_USER_CAN_ONLY_APPROVED_HIS_OWN);
        }
        List<SupplierEntity> list = entities.stream().filter(o -> SupplierStateEnum.CREATE.getCode() == o.getState())
                .peek(o -> {
                    if (o.getApprovalStatus() == null) {
                        o.setApprovalStatus(ApprovalStatusEnum.TO_BE_APPROVAL.getCode());
                    }
                })
                .filter(o -> o.getApprovalStatus().equals(ApprovalStatusEnum.TO_BE_APPROVAL.getCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Date date = new Date();
        for (SupplierEntity entity : list) {
            Integer preApprovalStatus = entity.getApprovalStatus();
            entity.setApprovalStatus(approvalStatus);
            entity.setApprovalSuggestion(dto.getApprovalSuggestion());
            entity.setActualApprover(dto.getActualApprover());
            entity.setApprovalTime(date);

            boolean toApproved = approvalStatus == ApprovalStatusEnum.APPROVED.getCode()
                    && preApprovalStatus == ApprovalStatusEnum.TO_BE_APPROVAL.getCode();
            if (toApproved) {
                entity.setState(SupplierStateEnum.TAKE_EFFECT.getCode());
            }
        }
        SupplierService supplierService = SpringUtil.getBean(SupplierService.class);
        supplierService.updateBatchById(list);
    }

    private void notRepeat(SupplierEntity entity) {
        LambdaQueryWrapper<SupplierEntity> qw = new LambdaQueryWrapper<>();
        qw.eq(SupplierEntity::getCode, entity.getCode());
        if (supplierMapper.selectCount(qw) > 0) {
            throw new ResponseException(RespCodeEnum.SUPPLIER_FAIL2SAME_CODE.getMsgDes());
        }
    }

    /**
     * 获取供应商物料列表
     *
     * @return
     */
    @Override
    public Page<SupplierMaterialDTO> getMaterialList(SupplierMaterialSelectDTO selectDTO) {
        LambdaQueryWrapper<SupplierMaterialEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper
//                .eq(selectDTO.getSupplierIdOfName() != null, SupplierMaterialEntity::getSupplierId, selectDTO.getSupplierIdOfName())
//                .eq(selectDTO.getSupplierIdOfCode() != null, SupplierMaterialEntity::getSupplierId, selectDTO.getSupplierIdOfCode())
                .eq(selectDTO.getMaterialId() != null, SupplierMaterialEntity::getMaterialId, selectDTO.getMaterialId());
        if (StringUtils.isNotBlank(selectDTO.getSupplierIdOfName())) {
            List<Integer> supplierIds = Arrays.stream(selectDTO.getSupplierIdOfName().split(SEP)).map(Integer::valueOf)
                    .collect(Collectors.toList());
            wrapper.in(SupplierMaterialEntity::getSupplierId, supplierIds);
        }
        if (StringUtils.isNotBlank(selectDTO.getSupplierIdOfCode())) {
            List<Integer> supplierIds = Arrays.stream(selectDTO.getSupplierIdOfCode().split(SEP)).map(Integer::valueOf)
                    .collect(Collectors.toList());
            wrapper.in(SupplierMaterialEntity::getSupplierId, supplierIds);
        }

        if (StringUtils.isNotBlank(selectDTO.getStates())) {
            List<String> states = Arrays.asList(selectDTO.getStates().split(SEP));
            wrapper.in(SupplierMaterialEntity::getState, states);
        }
        if (StringUtils.isNotBlank(selectDTO.getMaterialCode())) {
            List<Integer> materialIds = materialService.lambdaQuery()
                    .select(MaterialEntity::getId).like(MaterialEntity::getCode, selectDTO.getMaterialCode())
                    .list().stream()
                    .map(MaterialEntity::getId)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(materialIds)) {
                return new Page<>();
            }
            wrapper.in(SupplierMaterialEntity::getMaterialId, materialIds);
        }
        wrapper.eq(StringUtils.isNotBlank(selectDTO.getSupplierMaterialCode()), SupplierMaterialEntity::getSupplierMaterialCode, selectDTO.getSupplierMaterialCode());
        wrapper.like(StringUtils.isNotBlank(selectDTO.getSupplierMaterialName()), SupplierMaterialEntity::getSupplierMaterialName, selectDTO.getSupplierMaterialName());
        wrapper.orderByAsc(SupplierMaterialEntity::getSupplierCode)
                .orderByAsc(SupplierMaterialEntity::getId);
        Page<SupplierMaterialEntity> page;
        if (selectDTO.getCurrent() == null || selectDTO.getSize() == null) {
            List<SupplierMaterialEntity> list = supplierMaterialService.list(wrapper);
            page = new Page<>();
            page.setRecords(list).setTotal(list.size()).setCurrent(1).setSize(list.size());
        } else {
            page = supplierMaterialService.page(new Page<>(selectDTO.getCurrent(), selectDTO.getSize()), wrapper);
        }
        List<SupplierMaterialEntity> records = page.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        }
        //设置供应商名称、物料信息等
        Set<Integer> supplierIdSet = records.stream().map(SupplierMaterialEntity::getSupplierId).collect(Collectors.toSet());
        Set<Integer> materialIdSet = records.stream().map(SupplierMaterialEntity::getMaterialId).collect(Collectors.toSet());
        List<SupplierEntity> supplierEntities = this.listByIds(supplierIdSet);
        Map<Integer, SupplierEntity> supplierMap = supplierEntities.stream().collect(Collectors.toMap(SupplierEntity::getId, o -> o));
        List<MaterialEntity> materialEntities = materialService.listByIds(materialIdSet);
        Map<Integer, MaterialEntity> materialMap = materialEntities.stream().collect(Collectors.toMap(MaterialEntity::getId, o -> o));
        List<SupplierMaterialDTO> dtos = new ArrayList<>(records.size());
        for (SupplierMaterialEntity entity : records) {
            SupplierMaterialDTO build = SupplierMaterialDTO.builder().id(entity.getId()).build();
            Integer supplierId1 = entity.getSupplierId();
            if (supplierMap.containsKey(supplierId1)) {
                SupplierEntity supplierEntity = supplierMap.get(supplierId1);
                build.setSupplierId(supplierEntity.getId());
                build.setSupplierCode(supplierEntity.getCode());
                build.setSupplierName(supplierEntity.getName());
                build.setType(supplierEntity.getType());
            }
            Integer materialId1 = entity.getMaterialId();
            if (materialMap.containsKey(materialId1)) {
                MaterialEntity materialEntity = materialMap.get(materialId1);
                build.setMaterialId(materialEntity.getId());
                build.setMaterialCode(materialEntity.getCode());
                build.setMaterialFields(materialEntity);
            }
            build.setSupplierMaterialCode(entity.getSupplierMaterialCode());
            build.setSupplierMaterialName(entity.getSupplierMaterialName());
            build.setSupplierMaterialStandard(entity.getSupplierMaterialStandard());
            build.setStateName(SupplierMaterialStateEnum.getNameByCode(entity.getState()));
            build.setState(entity.getState());
            build.setSupplierMaterialExtendFieldOne(entity.getSupplierMaterialExtendFieldOne());
            build.setSupplierMaterialExtendFieldTwo(entity.getSupplierMaterialExtendFieldTwo());
            build.setSupplierMaterialExtendFieldThree(entity.getSupplierMaterialExtendFieldThree());
            build.setSupplierMaterialExtendFieldFour(entity.getSupplierMaterialExtendFieldFour());
            build.setSupplierMaterialExtendFieldFive(entity.getSupplierMaterialExtendFieldFive());
            build.setSupplierMaterialExtendFieldSix(entity.getSupplierMaterialExtendFieldSix());
            build.setSupplierMaterialExtendFieldSeven(entity.getSupplierMaterialExtendFieldSeven());
            build.setSupplierMaterialExtendFieldEight(entity.getSupplierMaterialExtendFieldEight());
            build.setSupplierMaterialExtendFieldNine(entity.getSupplierMaterialExtendFieldNine());
            build.setSupplierMaterialExtendFieldTen(entity.getSupplierMaterialExtendFieldTen());
            dtos.add(build);
        }
        Page<SupplierMaterialDTO> dtoPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        dtoPage.setRecords(dtos);
        return dtoPage;
    }

    @Override
    public List<SupplierMaterialDTO> getMaterials(String materialCode) {
        LambdaQueryWrapper<MaterialEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(MaterialEntity::getId, MaterialEntity::getCode, MaterialEntity::getName);
        if (StringUtils.isNotBlank(materialCode)) {
            wrapper.like(MaterialEntity::getCode, materialCode);
        }
        List<MaterialEntity> list = materialService.list(wrapper);
        ArrayList<SupplierMaterialDTO> dtos = new ArrayList<>(list.size());
        for (MaterialEntity entity : list) {
            dtos.add(SupplierMaterialDTO.builder()
                    .materialId(entity.getId())
                    .materialCode(entity.getCode())
                    .materialName(entity.getName())
                    .build());
        }
        return dtos;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void refreshSupplierMaterialsBySupplier(SupplierEntity entity) {
        Integer supplierId = entity.getId();
        if (supplierId == null) {
            return;
        }
        supplierMaterialService.removeBySupplierId(supplierId);
        List<SupplierMaterialEntity> materialInsertList = entity.getSupplierMaterialList();
        if (CollectionUtils.isEmpty(materialInsertList)) {
            return;
        }
        List<SupplierMaterialEntity> list = new ArrayList<>();
        SupplierEntity supplierEntity = supplierMapper.selectById(supplierId);
        // 物料相同时抛出异常
        long distinctCount = materialInsertList.stream().map(SupplierMaterialEntity::getMaterialId).distinct().count();
        if (distinctCount != materialInsertList.size()) {
            throw new ResponseException(RespCodeEnum.SUPPLIER_MATERIAL_NOT_REPEAT);
        }
        for (SupplierMaterialEntity insertDTO : materialInsertList) {
            SupplierMaterialEntity build = SupplierMaterialEntity.builder()
                    .supplierId(supplierId)
                    .supplierCode(supplierEntity.getCode())
                    .materialId(insertDTO.getMaterialId())
                    .state(insertDTO.getState())
                    .supplierMaterialCode(insertDTO.getSupplierMaterialCode())
                    .supplierMaterialName(insertDTO.getSupplierMaterialName())
                    .supplierMaterialStandard(insertDTO.getSupplierMaterialStandard())
                    .supplierMaterialExtendFieldOne(insertDTO.getSupplierMaterialExtendFieldOne())
                    .supplierMaterialExtendFieldTwo(insertDTO.getSupplierMaterialExtendFieldTwo())
                    .supplierMaterialExtendFieldThree(insertDTO.getSupplierMaterialExtendFieldThree())
                    .supplierMaterialExtendFieldFour(insertDTO.getSupplierMaterialExtendFieldFour())
                    .supplierMaterialExtendFieldFive(insertDTO.getSupplierMaterialExtendFieldFive())
                    .supplierMaterialExtendFieldSix(insertDTO.getSupplierMaterialExtendFieldSix())
                    .supplierMaterialExtendFieldSeven(insertDTO.getSupplierMaterialExtendFieldSeven())
                    .supplierMaterialExtendFieldEight(insertDTO.getSupplierMaterialExtendFieldEight())
                    .supplierMaterialExtendFieldNine(insertDTO.getSupplierMaterialExtendFieldNine())
                    .supplierMaterialExtendFieldTen(insertDTO.getSupplierMaterialExtendFieldTen())
                    .build();
            list.add(build);
        }
        supplierMaterialService.saveBatch(list);
        // 调用添加供应商物料的方法时，推送消息
        messagePushToKafkaService.pushNewMessage(list, Constants.KAFKA_MAIN_DATA_TOPIC, KafkaMessageTypeEnum.SUPPLIER_MATERIAL_BATCH_ADD_MESSAGE);
    }


    @Override
    public ArrayList<SupplierMaterialDTO> getNotExistMaterials(Integer supplierId) {
        List<MaterialEntity> list = materialService.list();

        LambdaQueryWrapper<SupplierMaterialEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SupplierMaterialEntity::getSupplierId, supplierId);
        List<SupplierMaterialEntity> supplierMaterialEntities = supplierMaterialService.list(wrapper);
        if (!CollectionUtils.isEmpty(supplierMaterialEntities)) {
            Map<Integer, MaterialEntity> collect = list.stream().collect(Collectors.toMap(MaterialEntity::getId, o -> o));
            supplierMaterialEntities.forEach(o -> collect.remove(o.getMaterialId()));
            list = new ArrayList<>(collect.values());
        }
        ArrayList<SupplierMaterialDTO> dtos = new ArrayList<>(list.size());
        for (MaterialEntity entity : list) {
            dtos.add(SupplierMaterialDTO.builder()
                    .materialId(entity.getId())
                    .materialCode(entity.getCode())
                    .materialName(entity.getName())
                    .build());
        }
        return dtos;
    }

    @Override
    public SupplierEntity getDetailWithMaterials(Integer supplierId) {
        SupplierEntity supplierEntity = this.getById(supplierId);
        if (supplierEntity == null) {
            return null;
        }
        List<SupplierMaterialEntity> list = supplierMaterialService.lambdaQuery().eq(SupplierMaterialEntity::getSupplierId, supplierId).list();
        for (SupplierMaterialEntity supplierMaterialEntity : list) {
            supplierMaterialEntity.setMaterialFields(materialService.lambdaQuery().eq(MaterialEntity::getId, supplierMaterialEntity.getMaterialId()).one());
        }
        supplierEntity.setSupplierMaterialList(list);
        // 展示名称
        showName(Stream.of(supplierEntity).collect(Collectors.toList()));
        return supplierEntity;
    }

    @Override
    public Page<SupplierEntity> supplierOpenPage(SupplierSelectDTO dto) {
        LambdaQueryWrapper<SupplierEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StringUtils.isNotBlank(dto.getName()), SupplierEntity::getName, dto.getName());
        wrapper.like(StringUtils.isNotBlank(dto.getCode()), SupplierEntity::getCode, dto.getCode());
        wrapper.eq(StringUtils.isNotBlank(dto.getFullCode()), SupplierEntity::getCode, dto.getFullCode());
        wrapper.in(!CollectionUtils.isEmpty(dto.getFullCodes()), SupplierEntity::getCode, dto.getFullCodes());
        wrapper.eq(Objects.nonNull(dto.getState()), SupplierEntity::getState, dto.getState());
        wrapper.like(StringUtils.isNotBlank(dto.getAddr()), SupplierEntity::getAddr, dto.getAddr());
        wrapper.eq(Objects.nonNull(dto.getCategory()), SupplierEntity::getCategory, dto.getCategory());
        wrapper.eq(StringUtils.isNotBlank(dto.getSupplierExtendFieldOne()), SupplierEntity::getSupplierExtendFieldOne, dto.getSupplierExtendFieldOne());
        wrapper.eq(StringUtils.isNotBlank(dto.getSupplierExtendFieldTwo()), SupplierEntity::getSupplierExtendFieldTwo, dto.getSupplierExtendFieldTwo());
        wrapper.eq(StringUtils.isNotBlank(dto.getSupplierExtendFieldThree()), SupplierEntity::getSupplierExtendFieldThree, dto.getSupplierExtendFieldThree());
        wrapper.eq(StringUtils.isNotBlank(dto.getSupplierExtendFieldFour()), SupplierEntity::getSupplierExtendFieldFour, dto.getSupplierExtendFieldFour());
        wrapper.eq(StringUtils.isNotBlank(dto.getSupplierExtendFieldFive()), SupplierEntity::getSupplierExtendFieldFive, dto.getSupplierExtendFieldFive());
        wrapper.eq(StringUtils.isNotBlank(dto.getSupplierExtendFieldSix()), SupplierEntity::getSupplierExtendFieldSix, dto.getSupplierExtendFieldSix());
        wrapper.eq(StringUtils.isNotBlank(dto.getSupplierExtendFieldSeven()), SupplierEntity::getSupplierExtendFieldSeven, dto.getSupplierExtendFieldSeven());
        wrapper.eq(StringUtils.isNotBlank(dto.getSupplierExtendFieldEight()), SupplierEntity::getSupplierExtendFieldEight, dto.getSupplierExtendFieldEight());
        wrapper.eq(StringUtils.isNotBlank(dto.getSupplierExtendFieldNine()), SupplierEntity::getSupplierExtendFieldNine, dto.getSupplierExtendFieldNine());
        wrapper.eq(StringUtils.isNotBlank(dto.getSupplierExtendFieldTen()), SupplierEntity::getSupplierExtendFieldTen, dto.getSupplierExtendFieldTen());
        if (StringUtils.isNotBlank(dto.getType())) {
            String supplierName = dictService.getNameByCodeAndType(dto.getType(), DictTypeEnum.SUPPLIER_TYPE.getType());
            // 获取委外供应商类型的供应商
            List<SupplierRelatedTypeEntity> supplierRelatedTypeEntities = supplierRelatedTypeService.lambdaQuery().eq(SupplierRelatedTypeEntity::getTypeName, supplierName).list();
            if (CollectionUtils.isEmpty(supplierRelatedTypeEntities)) {
                return new Page<>();
            }
            List<String> supplierCodes = supplierRelatedTypeEntities.stream().map(SupplierRelatedTypeEntity::getSupplierCode).distinct().collect(Collectors.toList());
            wrapper.in(SupplierEntity::getCode, supplierCodes);
        }
        WrapperUtil.eq(wrapper, SupplierEntity::getContacts, dto.getContacts());
        if (dto.getUpdateStartTime() != null && dto.getUpdateEndTime() != null) {
            wrapper.between(SupplierEntity::getUpdateTime, dto.getUpdateStartTime(), dto.getUpdateEndTime());
        }
        wrapper.orderByDesc(SupplierEntity::getCreateTime)
                .orderByDesc(SupplierEntity::getId);
        // 分页查询
        Page<SupplierEntity> page = this.page(dto.sortPage(), wrapper);
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return new Page<>();
        }
        // 获取供应商绑定的物料信息
        page.getRecords().forEach(this::setMaterialEntities);
        // 获取中文名称
        showName(page.getRecords());
        return page;
    }

    /**
     * 下载供应商档案模板
     */
    @Override
    public void exportTemplate(HttpServletResponse response) {
        String fileName;
        InputStream inputStream = null;
        try {
            //从项目中获取默认模板
            fileName = ReportFormConstant.DEFAULT_SUPPLIER_TEMPLATE + XLSX;
            ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
            org.springframework.core.io.Resource resource = resolver.getResource("classpath:template" + File.separator + fileName);
            if (!resource.exists()) {
                throw new ResponseException(RespCodeEnum.TEMPLATE_NOT_EXIST);
            }
            inputStream = resource.getInputStream();
            ExcelTemplateImportUtil.responseToClient(response, inputStream, "供应商导入模板" + XLSX);
        } catch (Exception e) {
            log.error("中文转码失败", e);
            throw new ResponseException(RespCodeEnum.FILE_DOWNLOAD_FAILED);
        } finally {
            IOUtils.closeQuietly(inputStream);
        }
    }

    /**
     * 供应商档案导出
     */
    @Override
    public void exportSupplierList(String name, String approvalStatus, String contacts, String counterpartName, String code, Integer currentPage, Integer size, String type, String states, String category, HttpServletResponse response) {
        Page<SupplierEntity> list = this.list(name, approvalStatus, contacts, counterpartName, code, currentPage, size, type, states, category);
        if (list.getTotal() > 0) {
            try {
                EasyExcelUtil.export(response, "供应商档案数据导出", "供应商档案", list.getRecords(), SupplierEntity.class);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
    }

    /**
     * 供应商档案信息导入
     *
     * @return
     */
    @Async
    @Override
    public void uploadCustomerList(MultipartFile multipartFile, String username) {
        String importUrl = null;
        InputStream inputStream = null;
        try {
            inputStream = multipartFile.getInputStream();
            List<SupplierImportDTO> supplierImportDTOS = EasyExcelUtil.read(inputStream, SupplierImportDTO.class, 1, 2);
            //数据校验
            List<SupplierEntity> checkSupplier = checkSupplier(supplierImportDTOS, username);
            //校验结果上传服务器
            importUrl = fastDfsClientService.listToExcelUploadTo(supplierImportDTOS, SupplierImportDTO.class, username);
            //保存数据
            saveData(supplierImportDTOS, checkSupplier, username, importUrl);
        } catch (Exception e) {
            log.error("excel导入数据错误", e);
            Object importObj = redisTemplate.opsForValue().get(RedisKeyPrefix.SUPPLIER_IMPORT_PROGRESS);
            ImportProgressDTO build;
            if (importObj != null) {
                build = JacksonUtil.parseObject((String) importObj, ImportProgressDTO.class);
            } else {
                build = ImportProgressDTO.builder().progress(0.0).build();
            }
            if (e instanceof DataIntegrityViolationException) {
                build.setExecutionDescription("导入数据超出合法长度，请确认");
            }
            build.setExecutionStatus(true);
            redisTemplate.opsForValue().set(RedisKeyPrefix.SUPPLIER_IMPORT_PROGRESS, JacksonUtil.toJSONString(build), 1, TimeUnit.HOURS);
        } finally {
            // 释放锁
            redisTemplate.delete(RedisKeyPrefix.SUPPLIER_IMPORT_LOCK);
            if (StringUtils.isBlank(importUrl)) {
                fastDfsClientService.deleteFile(importUrl);
            }
            IOUtils.closeQuietly(inputStream);
        }
    }

    /**
     * 保存供应商档案数据
     *
     * @param supplierImportDTOS
     * @param checkSupplier
     * @param username
     * @param importUrl
     */
    private void saveData(List<SupplierImportDTO> supplierImportDTOS, List<SupplierEntity> checkSupplier, String username, String importUrl) {
        int rowTotal = checkSupplier.size();
        int importDataSize = supplierImportDTOS.size();
        if (rowTotal > 0) {
            for (int i = 0; i < rowTotal; i++) {
                SupplierEntity supplierEntity = checkSupplier.get(i);
                this.save(supplierEntity);
                // 供应商绑定供应商类型
                bindRelatedSupplierType(supplierEntity);
                //计算处理进度
                Double processPercent = MathUtil.divideDouble(i + 1, rowTotal, 2);
                ImportProgressDTO build = ImportProgressDTO.builder()
                        .progress(processPercent)
                        .executionDescription(processPercent.compareTo(1.0) == 0 ? String.format("导入完成：成功%s条；不可导入%s条", rowTotal, importDataSize - rowTotal) : String.format("正在保存中，已完成%s条；", i))
                        .executionStatus(processPercent.compareTo(1.0) == 0)
                        .importUrl(processPercent.compareTo(1.0) == 0 ? importUrl : null)
                        .build();
                redisTemplate.opsForValue().set(RedisKeyPrefix.SUPPLIER_IMPORT_PROGRESS, JacksonUtil.toJSONString(build), 1, TimeUnit.HOURS);
            }
        } else {
            ImportProgressDTO build = ImportProgressDTO.builder()
                    .progress(1.0)
                    .executionDescription(String.format("导入完成：成功%s条；不可导入%s条", 0, importDataSize))
                    .executionStatus(true)
                    .importUrl(importUrl)
                    .build();
            redisTemplate.opsForValue().set(RedisKeyPrefix.SUPPLIER_IMPORT_PROGRESS, JacksonUtil.toJSONString(build), 1, TimeUnit.HOURS);
        }
        importDataRecordService.save(ImportDataRecordEntity.builder()
                .importFileName(ImportTypeEnum.SUPPLIER_IMPORT.getTypeName())
                .importType(ImportTypeEnum.SUPPLIER_IMPORT.getType())
                .successNumber(rowTotal)
                .failNumber(importDataSize - rowTotal)
                .logUrl(importUrl)
                .importTime(new Date())
                .operator(username)
                .createBy(username)
                .createTime(new Date())
                .build());
    }

    /**
     * 校验供应商上传数据
     *
     * @param supplierImportDTOS
     * @param username
     * @return
     */
    private List<SupplierEntity> checkSupplier(List<SupplierImportDTO> supplierImportDTOS, String username) {
        List<SupplierEntity> supplierEntities = new ArrayList<>();
        //供应商类型校验
        List<DictEntity> supplierTypes = dictService.lambdaQuery().eq(DictEntity::getType, DictTypeEnum.SUPPLIER_TYPE.getType()).list();
        Set<String> supplierTypeNameSet = supplierTypes.stream().map(DictEntity::getName).collect(Collectors.toSet());
        for (SupplierImportDTO entity : supplierImportDTOS) {
            StringBuilder stringBuilder = new StringBuilder();
            if (StringUtils.isBlank(entity.getName())) {
                stringBuilder.append("供应商名称不能为空;");
            } else {
                //重复名称判断
                Long count = this.lambdaQuery().eq(SupplierEntity::getName, entity.getName()).count();
                if (count > 0) {
                    stringBuilder.append(RespCodeEnum.SUPPLIER_FAIL2SAME_NAME.getMsgDes());
                }
                long count1 = supplierImportDTOS.stream().filter(o -> entity.getName().equals(o.getName())).count();
                if (count1 > 1) {
                    stringBuilder.append("导入的供应商名称重复");
                }
            }
            if (StringUtils.isBlank(entity.getCode())) {
                stringBuilder.append("供应商编号不能为空;");
            } else {
                //重复编号判断
                Long countCode = this.lambdaQuery().eq(SupplierEntity::getCode, entity.getCode()).count();
                if (countCode > 0) {
                    stringBuilder.append(RespCodeEnum.SUPPLIER_FAIL2SAME_CODE.getMsgDes());
                }
                long count = supplierImportDTOS.stream().filter(o -> entity.getCode().equals(o.getCode())).count();
                if (count > 1) {
                    stringBuilder.append("导入的供应商编号重复");
                }
            }
            //本厂对接人校验
            if (entity.getCounterpartName() != null) {
                SysUserEntity byUserName = sysUserService.lambdaQuery().eq(SysUserEntity::getNickname, entity.getCounterpartName()).one();
                if (byUserName == null) {
                    stringBuilder.append(RespCodeEnum.COUNTERPART_CODE_FAIL.getMsgDes());
                }
                entity.setCounterpart(Optional.ofNullable(byUserName).map(SysUserEntity::getUsername).orElse(""));
            }
            //供应类别校验
            if (StringUtils.isEmpty(entity.getCategoryName())) {
                stringBuilder.append("供应类别不能为空");
            } else {
                SupplierCategoryEnum supplierCategoryEnum = SupplierCategoryEnum.getByName(entity.getCategoryName());
                if(supplierCategoryEnum == null) {
                    stringBuilder.append("供应类别:").append(entity.getCategoryName()).append("不存在");
                }else {
                    entity.setCategory(supplierCategoryEnum);
                }
            }
            if (StringUtils.isEmpty(entity.getType())) {
                if(CollectionUtils.isEmpty(supplierTypes)) {
                    stringBuilder.append("尚未存在供应商类型, 请先添加");
                }else {
                    // 默认取第一个，合格供应商
                    entity.setType(supplierTypes.get(0).getName());
                }
            } else {
                List<String> typeNames = Arrays.asList(entity.getType().split(SEP));
                if (!supplierTypeNameSet.containsAll(typeNames)) {
                    stringBuilder.append(RespCodeEnum.SUPPLIER_TYPE_IS_NOT_EXIST.getMsgDes());
                }
            }
            //单据开启审批，审批人不能为空
            if (approveConfigService.getConfigByCode(ApproveModuleEnum.SUPPLIER_PROFILE.getCode())) {
                if (entity.getApproverName() == null) {
                    stringBuilder.append(RespCodeEnum.APPROVE_IS_USER_PERSON_IS_NOT_EMPTY.getMsgDes());
                } else {
                    //审批人校验
                    SysUserEntity usersByPath = sysUserMapper.getUsersByPathAndNickName(ApproveModuleEnum.SUPPLIER_PROFILE.getPath(), entity.getApproverName());
                    if (usersByPath == null) {
                        stringBuilder.append(RespCodeEnum.APPROVE_PERSON_IS_NOT_EXIST.getMsgDes());
                    }
                    entity.setApprover(Optional.ofNullable(usersByPath).map(SysUserEntity::getUsername).orElse(""));
                }
            }
            if (SupplierStateEnum.CREATE.getName().equals(entity.getStateName()) || entity.getStateName() == null) {
                entity.setState(SupplierStateEnum.CREATE.getCode());
                entity.setApprovalStatus(ApprovalStatusEnum.TO_BE_APPROVAL.getCode());
            }
            if (SupplierStateEnum.TAKE_EFFECT.getName().equals(entity.getStateName())) {
                entity.setState(SupplierStateEnum.TAKE_EFFECT.getCode());
                entity.setApprovalStatus(ApprovalStatusEnum.APPROVED.getCode());
                entity.setActualApprover(username);
            }
            if (stringBuilder.toString().isEmpty()) {
                stringBuilder.append("导入成功");
                SupplierEntity supplier = new SupplierEntity();
                BeanUtils.copyProperties(entity, supplier);
                supplier.setUpdateBy(username);
                supplier.setCreateBy(username);
                supplier.setUpdateTime(new Date());
                supplier.setCreateTime(Objects.nonNull(entity.getCreateTime()) ? entity.getCreateTime() : new Date());
                supplierEntities.add(supplier);
            }
            entity.setResult(stringBuilder.toString());
        }
        return supplierEntities;
    }

    /**
     * 更新供应商类型
     *
     * @return
     */
    @Override
    public void updateSupplier(DictEntity dictEntity, String username) {
        long count = supplierRelatedTypeService.lambdaQuery().eq(SupplierRelatedTypeEntity::getTypeName, dictEntity.getName()).count();
        if (count > 0) {
            throw new ResponseException(RespCodeEnum.TYPE_IS_OCCUPIED);
        }
        dictEntity.setUpdateBy(username);
        dictService.updateById(dictEntity);
    }

    /**
     * 、
     * 删除供应商类型
     *
     * @return
     */
    @Override
    public void removeSupplierTypeById(Integer id) {
        DictEntity dictEntity = dictService.getById(id);
        long count = supplierRelatedTypeService.lambdaQuery().eq(SupplierRelatedTypeEntity::getTypeName, dictEntity.getName()).count();
        if (count > 0) {
            throw new ResponseException(RespCodeEnum.TYPE_IS_OCCUPIED);
        }
        dictService.removeById(id);
    }

    @Override
    public List<CounterpartEntity> getExistCounterparts() {
        List<SupplierEntity> lists = this.list();
        List<String> distinctLists = lists.stream().distinct().map(SupplierEntity::getCounterpart).collect(Collectors.toList());
        List<SysUserEntity> listByUserNames = sysUserService.getListByUserNames(distinctLists);
        List<CounterpartEntity> counterpartEntities = new ArrayList<>();
        listByUserNames.forEach(list -> {
            CounterpartEntity counterpartEntity = CounterpartEntity.builder()
                    .nickName(list.getNickname())
                    .userName(list.getUsername()).build();
            counterpartEntities.add(counterpartEntity);
        });
        return counterpartEntities;
    }

    @Override
    public SupplierEntity getByName(String supplier) {
        if (StringUtils.isBlank(supplier)) {
            return null;
        }
        LambdaQueryWrapper<SupplierEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SupplierEntity::getName, supplier).last("limit 1");
        return this.getOne(queryWrapper);
    }

    @Override
    public void batchUpdateSupplierMaterials(com.yelink.dfs.entity.supplier.dto.SupplierMaterialBatchUpdateDTO batchUpdateDTO) {
        supplierMaterialService.lambdaUpdate().in(SupplierMaterialEntity::getId, batchUpdateDTO.getSupplierMaterialIds())
                .set(SupplierMaterialEntity::getState, batchUpdateDTO.getState())
                .update();
    }

    @Override
    public SupplierEntity getEntityByCode(SupplierDetailDTO dto) {
        if (StringUtils.isBlank(dto.getCode())) {
            return null;
        }
        SupplierEntity supplierEntity = this.lambdaQuery()
                .eq(StringUtils.isNotBlank(dto.getCode()), SupplierEntity::getCode, dto.getCode())
                .eq(Objects.nonNull(dto.getState()), SupplierEntity::getState, dto.getState())
                .in(!CollectionUtils.isEmpty(dto.getCategoryList()), SupplierEntity::getCategory, dto.getCategoryList())
                .like(Objects.nonNull(dto.getType()), SupplierEntity::getType, dto.getType())
                .one();
        if (supplierEntity != null) {
            // 获取供应商的物料列表
            setMaterialEntities(supplierEntity);
            // 获取人名和状态名称
            List<SupplierEntity> list = new ArrayList<>();
            list.add(supplierEntity);
            showName(list);
        }
        return supplierEntity;
    }

    @Override
    public void refreshSupplierMaterialsByMaterial(SupplierMaterialByMaterialDTO dto) {
        // 删除原数据
        supplierMaterialService.lambdaUpdate().eq(SupplierMaterialEntity::getMaterialId, dto.getMaterialId()).remove();
        if (CollectionUtils.isEmpty(dto.getSupplierMaterials())) {
            return;
        }
        // 供应商物料不能相同
        long distinctCount = dto.getSupplierMaterials().stream().map(SupplierMaterialByMaterialDTO.SupplierMaterialDTO::getSupplierCode).distinct().count();
        if (distinctCount != dto.getSupplierMaterials().size()) {
            throw new ResponseException(RespCodeEnum.SUPPLIER_MATERIAL_NOT_REPEAT);
        }
        // 刷新数据
        List<SupplierMaterialEntity> list = new ArrayList<>();
        for (SupplierMaterialByMaterialDTO.SupplierMaterialDTO supplierMaterial : dto.getSupplierMaterials()) {
            SupplierMaterialEntity build = SupplierMaterialEntity.builder()
                    .supplierId(supplierMaterial.getSupplierId())
                    .supplierCode(supplierMaterial.getSupplierCode())
                    .materialId(dto.getMaterialId())
                    .state(supplierMaterial.getState())
                    .supplierMaterialCode(supplierMaterial.getSupplierMaterialCode())
                    .supplierMaterialName(supplierMaterial.getSupplierMaterialName())
                    .supplierMaterialStandard(supplierMaterial.getSupplierMaterialStandard())
                    .build();
            list.add(build);
        }
        supplierMaterialService.saveBatch(list);
    }

    @Override
    public List<SupplierEntity> getList(SupplierSelectDTO dto) {
        return lambdaQuery()
                .like(StringUtils.isNotEmpty(dto.getType()), SupplierEntity::getType, dto.getType())
                .eq(Objects.nonNull(dto.getState()), SupplierEntity::getState, dto.getState())
                .eq(Objects.nonNull(dto.getCategory()), SupplierEntity::getCategory, dto.getCategory())
                .list();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void upsert(SupplierEntity entity) {
        Date now = new Date();
        entity.setUpdateTime(entity.getUpdateTime() == null ? now : entity.getUpdateTime());
        entity.setCreateTime(entity.getCreateTime() == null ? now : entity.getCreateTime());

        SupplierEntity one = this.lambdaQuery().eq(SupplierEntity::getCode, entity.getCode()).one();
        if (one == null) {
            SupplierService supplierService = SpringUtil.getBean(SupplierService.class);
            if (entity.getState() != null && SupplierStateEnum.CREATE.getCode() == entity.getState()) {
                supplierService.saveEntity(entity);
            } else {
                //新增
                supplierService.saveReleasedEntity(entity);
            }
            return;
        }
        //更新
        FieldUtil.copyValueTo(one, entity);
        updateEntityById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void upsertSupplierMaterials(MaterialUpdateInsertDTO dto) {
        SupplierMaterialEntity supplierMaterialEntity = JacksonUtil.convertObject(dto, SupplierMaterialEntity.class);
        SupplierEntity supplierEntity = this.lambdaQuery().eq(SupplierEntity::getCode, dto.getSupplierCode()).one();
        if (supplierEntity == null) {
            throw new ResponseException(RespCodeEnum.SUPPLIER_NOT_EXIST);
        }
        MaterialEntity materialEntity = materialService.lambdaQuery().eq(MaterialEntity::getCode, dto.getMaterialCode()).one();
        if (materialEntity == null) {
            throw new ResponseException(RespCodeEnum.MATERIAL_NOT_EXISTS);
        }
        supplierMaterialEntity.setSupplierId(supplierEntity.getId());
        supplierMaterialEntity.setMaterialId(materialEntity.getId());

        SupplierMaterialEntity one = supplierMaterialService.lambdaQuery()
                .eq(SupplierMaterialEntity::getSupplierCode, dto.getSupplierCode())
                .eq(SupplierMaterialEntity::getMaterialId, materialEntity.getId()).one();

        if (one == null) {
            supplierMaterialService.save(supplierMaterialEntity);
            // 调用添加供应商物料的方法时，推送消息
            messagePushToKafkaService.pushNewMessage(supplierMaterialEntity, Constants.KAFKA_MAIN_DATA_TOPIC, KafkaMessageTypeEnum.SUPPLIER_MATERIAL_ADD_MESSAGE);
            return;
        }

        FieldUtil.copyValueTo(one, supplierMaterialEntity);
        supplierMaterialService.updateById(supplierMaterialEntity);

    }

    @Override
    public Page<SupplierVO> getList2(SupplierQueryDTO dto) {
        String sql = SqlUtil.getSql(dto);
        Page<SupplierEntity> page = this.baseMapper.getList(sql, dto.getPage());
        showName(page.getRecords());
        return JacksonUtil.convertPage(page, SupplierVO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SupplierMaterialSyncResultVO syncMaterials(List<MaterialUpdateInsertDTO> dtos) {
        // 使用工具类构建结果
        BatchOperationResultBuilder<SupplierMaterialSyncResultVO.SupplierMaterialSyncFailureVO> resultBuilder =
                BatchOperationResultBuilder.create();

        List<String> insertedIdentifiers = new ArrayList<>();
        List<String> updatedIdentifiers = new ArrayList<>();

        // 批量查询已存在的供应商物料
        Set<String> existingIdentifiers = new HashSet<>();
        if (!CollectionUtils.isEmpty(dtos)) {
            List<SupplierMaterialEntity> existingMaterials = supplierMaterialService.lambdaQuery()
                    .in(SupplierMaterialEntity::getSupplierCode,
                        dtos.stream().map(MaterialUpdateInsertDTO::getSupplierCode).distinct().collect(Collectors.toList()))
                    .list();

            if (!CollectionUtils.isEmpty(existingMaterials)) {
                // 批量查询物料信息
                List<Integer> materialIds = existingMaterials.stream()
                        .map(SupplierMaterialEntity::getMaterialId)
                        .distinct()
                        .collect(Collectors.toList());

                Map<Integer, String> materialCodeMap = materialService.lambdaQuery()
                        .select(MaterialEntity::getId, MaterialEntity::getCode)
                        .in(MaterialEntity::getId, materialIds)
                        .list().stream()
                        .collect(Collectors.toMap(MaterialEntity::getId, MaterialEntity::getCode));

                // 构建已存在的标识符
                for (SupplierMaterialEntity entity : existingMaterials) {
                    String materialCode = materialCodeMap.get(entity.getMaterialId());
                    if (materialCode != null) {
                        existingIdentifiers.add(entity.getSupplierCode() + "-" + materialCode);
                    }
                }
            }
        }

        // 处理每个供应商物料
        for (MaterialUpdateInsertDTO dto : dtos) {
            String identifier = dto.getSupplierCode() + "-" + dto.getMaterialCode();
            String operationType = existingIdentifiers.contains(identifier) ? "UPDATE" : "INSERT";

            try {
                // 执行同步操作（新增或更新）
                upsertSupplierMaterials(dto);
                // 记录成功操作
                resultBuilder.addSuccess(identifier);
                if ("INSERT".equals(operationType)) {
                    insertedIdentifiers.add(identifier);
                } else {
                    updatedIdentifiers.add(identifier);
                }
            } catch (Exception e) {
                // 记录失败操作
                SupplierMaterialSyncResultVO.SupplierMaterialSyncFailureVO failureVO =
                    SupplierMaterialSyncResultVO.SupplierMaterialSyncFailureVO.builder()
                        .supplierCode(dto.getSupplierCode())
                        .materialCode(dto.getMaterialCode())
                        .supplierMaterialCode(dto.getSupplierMaterialCode())
                        .supplierMaterialName(dto.getSupplierMaterialName())
                        .failureReason(e.getMessage())
                        .errorCode(e instanceof ResponseException ? ((ResponseException) e).getCode() : BatchOperationErrorType.SYSTEM_ERROR)
                        .build();
                resultBuilder.addFailure(failureVO);
            }
        }

        // 构建结果
        SupplierMaterialSyncResultVO result = JacksonUtil.convertObject(resultBuilder, SupplierMaterialSyncResultVO.class);
        result.setInsertedIdentifiers(insertedIdentifiers);
        result.setUpdatedIdentifiers(updatedIdentifiers);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SupplierSyncResultVO syncSuppliers(List<SupplierInsertOrUpdateDTO> dtos) {
        // 使用工具类构建结果
        BatchOperationResultBuilder<SupplierSyncResultVO.SupplierSyncFailureVO> resultBuilder =
                BatchOperationResultBuilder.create();

        List<String> insertedCodes = new ArrayList<>();
        List<String> updatedCodes = new ArrayList<>();

        for (SupplierInsertOrUpdateDTO dto : dtos) {
            try {
                // 转换为实体
                SupplierEntity entity = JacksonUtil.convertObject(dto, SupplierEntity.class);
                upsert(entity);
                resultBuilder.addSuccess(entity.getCode());
            } catch (Exception e) {
                log.error("同步供应商失败，供应商编码：{}, 错误：{}", dto.getCode(), e.getMessage(), e);
                resultBuilder.addFailure(SupplierSyncResultVO.SupplierSyncFailureVO.builder()
                        .supplierCode(dto.getCode())
                        .failureReason(e.getMessage())
                        .errorType(BatchOperationErrorType.SYSTEM_ERROR)
                        .build());
            }
        }
        // 构建结果
        SupplierSyncResultVO result = JacksonUtil.convertObject(resultBuilder, SupplierSyncResultVO.class);
        result.setInsertedCodes(insertedCodes);
        result.setUpdatedCodes(updatedCodes);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SupplierBatchDeleteResultVO batchDeleteSuppliers(SupplierDeleteDTO dto) {
        if (CollectionUtils.isEmpty(dto.getSupplierCodes())) {
            throw new ResponseException(RespCodeEnum.PARAM_EXCEPTION);
        }
        List<String> supplierCodes = dto.getSupplierCodes();
        // 使用工具类构建结果
        BatchOperationResultBuilder<SupplierBatchDeleteResultVO.SupplierBatchDeleteFailureVO> resultBuilder =
                BatchOperationResultBuilder.create();

        // 批量查询已存在的供应商
        Map<String, SupplierEntity> existingSuppliersMap = this.lambdaQuery()
                .in(SupplierEntity::getCode, supplierCodes)
                .list().stream()
                .collect(Collectors.toMap(SupplierEntity::getCode, entity -> entity));

        for (String supplierCode : supplierCodes) {
            try {
                SupplierEntity existingSupplier = existingSuppliersMap.get(supplierCode);
                if (existingSupplier == null) {
                    resultBuilder.addFailure(SupplierBatchDeleteResultVO.SupplierBatchDeleteFailureVO.builder()
                            .supplierCode(supplierCode)
                            .failureReason("供应商不存在")
                            .errorType(BatchOperationErrorType.DATA_NOT_FOUND)
                            .build());
                    continue;
                }
                // 检查供应商状态是否允许删除（生效态不能删除）
                if (SupplierStateEnum.TAKE_EFFECT.getCode() == existingSupplier.getState()) {
                    resultBuilder.addFailure(SupplierBatchDeleteResultVO.SupplierBatchDeleteFailureVO.builder()
                            .supplierCode(supplierCode)
                            .failureReason("生效态的供应商不能删除")
                            .errorType(BatchOperationErrorType.INVALID_STATE)
                            .build());
                    continue;
                }
                // 删除供应商物料
                supplierMaterialService.lambdaUpdate()
                        .eq(SupplierMaterialEntity::getSupplierId, existingSupplier.getId())
                        .remove();
                // 删除供应商
                this.removeById(existingSupplier.getId());
                resultBuilder.addSuccess(supplierCode);
            } catch (Exception e) {
                log.error("删除供应商失败，供应商编码：{}, 错误：{}", supplierCode, e.getMessage(), e);
                resultBuilder.addFailure(SupplierBatchDeleteResultVO.SupplierBatchDeleteFailureVO.builder()
                        .supplierCode(supplierCode)
                        .failureReason(e.getMessage())
                        .errorType(BatchOperationErrorType.SYSTEM_ERROR)
                        .build());
            }
        }

        // 如果有失败项，抛出异常触发事务回滚
        if (resultBuilder.getFailedCount() > 0) {
            SupplierBatchDeleteResultVO result = JacksonUtil.convertObject(resultBuilder, SupplierBatchDeleteResultVO.class);
            throw new BatchOperationException("批量删除供应商操作失败，存在" + resultBuilder.getFailedCount() + "个失败项", result);
        }

        // 构建结果
        return JacksonUtil.convertObject(resultBuilder, SupplierBatchDeleteResultVO.class);
    }

    @Override
    public Page<SupplierEntity> getSupplierList(SupplierQueryDTO dto) {
        String sql = SqlUtil.getSql(dto);
        Page<SupplierEntity> page = this.baseMapper.getList(sql, dto.getPage());
        showName(page.getRecords());
        return page;
    }

    @Override
    public SupplierFieldDetailVO getSupplierDetail(SupplierDetailQueryDTO dto) {
        SupplierEntity entity = null;
        if (StringUtils.isNotBlank(dto.getSupplierCode())) {
            entity = getEntityByCode(dto.getSupplierCode());
        } else if (dto.getSupplierId() != null) {
            entity = getDetailWithMaterials(dto.getSupplierId());
        }
        if (entity == null) {
            throw new ResponseException(RespCodeEnum.SUPPLIER_NOT_EXIST);
        }
        // 转换为VO对象
        SupplierFieldDetailVO vo = JacksonUtil.convertObject(entity, SupplierFieldDetailVO.class);

        // 转换供应商物料列表为VO对象
        if (!CollectionUtils.isEmpty(entity.getSupplierMaterialList())) {
            List<SupplierMaterialVO> materialVOList = entity.getSupplierMaterialList().stream()
                    .map(this::convertToSupplierMaterialVO)
                    .collect(Collectors.toList());
            vo.setSupplierMaterialList(materialVOList);
        }

        return vo;
    }

    /**
     * 将SupplierMaterialEntity转换为SupplierMaterialVO
     */
    private SupplierMaterialVO convertToSupplierMaterialVO(SupplierMaterialEntity entity) {
        SupplierMaterialVO vo = JacksonUtil.convertObject(entity, SupplierMaterialVO.class);

        // 转换物料字段
        if (entity.getMaterialFields() != null) {
            vo.setMaterialFields(JacksonUtil.convertObject(entity.getMaterialFields(), MaterialFieldVO.class));
        }

        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SupplierBatchInsertResultVO batchInsertSuppliers(SupplierBatchInsertDTO dto) {
        List<SupplierInsertOrUpdateDTO> suppliers = dto.getSuppliers();
        // 使用工具类构建结果
        BatchOperationResultBuilder<SupplierBatchInsertResultVO.SupplierBatchInsertFailureVO> resultBuilder =
                BatchOperationResultBuilder.create();

        // 第一阶段：验证所有数据
        List<SupplierEntity> validEntities = new ArrayList<>();
        Set<String> supplierCodes = suppliers.stream().map(SupplierInsertOrUpdateDTO::getCode).collect(Collectors.toSet());

        // 批量查询已存在的供应商编码
        Map<String, SupplierEntity> existingSuppliersMap = this.lambdaQuery()
                .in(SupplierEntity::getCode, supplierCodes)
                .list().stream()
                .collect(Collectors.toMap(SupplierEntity::getCode, entity -> entity));

        for (SupplierInsertOrUpdateDTO supplierDto : suppliers) {
            try {
                // 检查供应商编码是否已存在
                if (existingSuppliersMap.containsKey(supplierDto.getCode())) {
                    resultBuilder.addFailure(SupplierBatchInsertResultVO.SupplierBatchInsertFailureVO.builder()
                            .supplierCode(supplierDto.getCode())
                            .failureReason("供应商编码已存在")
                            .errorType(BatchOperationErrorType.DATA_ALREADY_EXISTS)
                            .build());
                    continue;
                }

                // 转换为实体并验证
                SupplierEntity entity = JacksonUtil.convertObject(supplierDto, SupplierEntity.class);
                Date now = new Date();
                entity.setCreateTime(entity.getCreateTime() == null ? now : entity.getCreateTime());
                entity.setUpdateTime(entity.getUpdateTime() == null ? now : entity.getUpdateTime());

                validEntities.add(entity);
                resultBuilder.addSuccess(entity.getCode());

            } catch (Exception e) {
                log.error("批量新增供应商验证失败，供应商编码：{}, 错误：{}", supplierDto.getCode(), e.getMessage(), e);
                resultBuilder.addFailure(SupplierBatchInsertResultVO.SupplierBatchInsertFailureVO.builder()
                        .supplierCode(supplierDto.getCode())
                        .failureReason(e.getMessage())
                        .errorType(BatchOperationErrorType.VALIDATION_ERROR)
                        .build());
            }
        }

        // 如果有失败项，抛出异常触发事务回滚
        if (resultBuilder.getFailedCount() > 0) {
            SupplierBatchInsertResultVO result = JacksonUtil.convertObject(resultBuilder, SupplierBatchInsertResultVO.class);
            throw new BatchOperationException("批量新增供应商操作失败，存在" + resultBuilder.getFailedCount() + "个失败项", result);
        }

        // 第二阶段：执行所有操作
        for (SupplierEntity entity : validEntities) {
            try {
                // 新增供应商
                if (entity.getState() != null && SupplierStateEnum.CREATE.getCode() == entity.getState()) {
                    saveEntity(entity);
                } else {
                    saveReleasedEntity(entity);
                }
            } catch (Exception e) {
                log.error("批量新增供应商执行失败，供应商编码：{}, 错误：{}", entity.getCode(), e.getMessage(), e);
                // 执行阶段出现异常，直接抛出触发回滚
                throw new RuntimeException("批量新增供应商执行失败，供应商编码：" + entity.getCode() + "，错误：" + e.getMessage(), e);
            }
        }

        // 构建结果
        return JacksonUtil.convertObject(resultBuilder, SupplierBatchInsertResultVO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SupplierBatchUpdateResultVO batchUpdateSuppliers(SupplierBatchUpdateDTO dto) {
        List<SupplierInsertOrUpdateDTO> suppliers = dto.getSuppliers();
        // 使用工具类构建结果
        BatchOperationResultBuilder<SupplierBatchUpdateResultVO.SupplierBatchUpdateFailureVO> resultBuilder =
                BatchOperationResultBuilder.create();

        // 第一阶段：验证所有数据
        List<SupplierEntity> validEntities = new ArrayList<>();
        Set<String> supplierCodes = suppliers.stream().map(SupplierInsertOrUpdateDTO::getCode).collect(Collectors.toSet());

        // 批量查询已存在的供应商
        Map<String, SupplierEntity> existingSuppliersMap = this.lambdaQuery()
                .in(SupplierEntity::getCode, supplierCodes)
                .list().stream()
                .collect(Collectors.toMap(SupplierEntity::getCode, entity -> entity));

        for (SupplierInsertOrUpdateDTO supplierDto : suppliers) {
            try {
                // 检查供应商是否存在
                SupplierEntity existingEntity = existingSuppliersMap.get(supplierDto.getCode());
                if (existingEntity == null) {
                    resultBuilder.addFailure(SupplierBatchUpdateResultVO.SupplierBatchUpdateFailureVO.builder()
                            .supplierCode(supplierDto.getCode())
                            .failureReason("供应商不存在")
                            .errorType(BatchOperationErrorType.DATA_NOT_FOUND)
                            .build());
                    continue;
                }

                // 转换为实体并验证
                SupplierEntity entity = JacksonUtil.convertObject(supplierDto, SupplierEntity.class);
                Date now = new Date();
                entity.setUpdateTime(entity.getUpdateTime() == null ? now : entity.getUpdateTime());
                entity.setId(existingEntity.getId()); // 设置ID用于更新
                // 复制字段值
                FieldUtil.copyValueTo(existingEntity, entity);
                validEntities.add(entity);
                resultBuilder.addSuccess(entity.getCode());

            } catch (Exception e) {
                log.error("批量更新供应商验证失败，供应商编码：{}, 错误：{}", supplierDto.getCode(), e.getMessage(), e);
                resultBuilder.addFailure(SupplierBatchUpdateResultVO.SupplierBatchUpdateFailureVO.builder()
                        .supplierCode(supplierDto.getCode())
                        .failureReason(e.getMessage())
                        .errorType(BatchOperationErrorType.VALIDATION_ERROR)
                        .build());
            }
        }

        // 如果有失败项，抛出异常触发事务回滚
        if (resultBuilder.getFailedCount() > 0) {
            SupplierBatchUpdateResultVO result = JacksonUtil.convertObject(resultBuilder, SupplierBatchUpdateResultVO.class);
            throw new BatchOperationException("批量更新供应商操作失败，存在" + resultBuilder.getFailedCount() + "个失败项", result);
        }

        // 第二阶段：执行所有操作
        for (SupplierEntity entity : validEntities) {
            try {
                // 更新供应商
                updateEntityById(entity);
            } catch (Exception e) {
                log.error("批量更新供应商执行失败，供应商编码：{}, 错误：{}", entity.getCode(), e.getMessage(), e);
                // 执行阶段出现异常，直接抛出触发回滚
                throw new RuntimeException("批量更新供应商执行失败，供应商编码：" + entity.getCode() + "，错误：" + e.getMessage(), e);
            }
        }

        // 构建结果
        return JacksonUtil.convertObject(resultBuilder, SupplierBatchUpdateResultVO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SupplierMaterialBatchOperationResultVO batchInsertMaterials(SupplierMaterialBatchInsertDTO dto) {
        // 使用工具类构建结果
        BatchOperationResultBuilder<SupplierMaterialBatchOperationResultVO.SupplierMaterialBatchOperationFailureVO> resultBuilder =
                BatchOperationResultBuilder.create();

        // 批量查询供应商信息
        List<String> supplierCodes = dto.getMaterials().stream()
                .map(SupplierMaterialBatchInsertDTO.SupplierMaterialInsertItemDTO::getSupplierCode)
                .distinct()
                .collect(Collectors.toList());
        Map<String, SupplierEntity> supplierMap = this.lambdaQuery()
                .in(SupplierEntity::getCode, supplierCodes)
                .list().stream()
                .collect(Collectors.toMap(SupplierEntity::getCode, entity -> entity));

        // 批量查询物料信息
        List<String> materialCodes = dto.getMaterials().stream()
                .map(SupplierMaterialBatchInsertDTO.SupplierMaterialInsertItemDTO::getMaterialCode)
                .distinct()
                .collect(Collectors.toList());
        Map<String, MaterialEntity> materialMap = materialService.lambdaQuery()
                .in(MaterialEntity::getCode, materialCodes)
                .list().stream()
                .collect(Collectors.toMap(MaterialEntity::getCode, entity -> entity));

        // 批量查询已存在的供应商物料
        List<SupplierMaterialEntity> existingMaterials = supplierMaterialService.lambdaQuery()
                .in(SupplierMaterialEntity::getSupplierCode, supplierCodes)
                .list();
        Set<String> existingIdentifiers = new HashSet<>();
        if (!CollectionUtils.isEmpty(existingMaterials)) {
            // 批量查询物料ID对应的编码
            List<Integer> materialIds = existingMaterials.stream()
                    .map(SupplierMaterialEntity::getMaterialId)
                    .distinct()
                    .collect(Collectors.toList());
            Map<Integer, String> materialIdCodeMap = materialService.lambdaQuery()
                    .select(MaterialEntity::getId, MaterialEntity::getCode)
                    .in(MaterialEntity::getId, materialIds)
                    .list().stream()
                    .collect(Collectors.toMap(MaterialEntity::getId, MaterialEntity::getCode));

            for (SupplierMaterialEntity entity : existingMaterials) {
                String materialCode = materialIdCodeMap.get(entity.getMaterialId());
                if (materialCode != null) {
                    existingIdentifiers.add(entity.getSupplierCode() + "-" + materialCode);
                }
            }
        }

        // 验证阶段：检查所有供应商物料
        for (SupplierMaterialBatchInsertDTO.SupplierMaterialInsertItemDTO item : dto.getMaterials()) {
            String identifier = item.getSupplierCode() + "-" + item.getMaterialCode();

            try {
                // 验证供应商是否存在
                SupplierEntity supplierEntity = supplierMap.get(item.getSupplierCode());
                if (supplierEntity == null) {
                    throw new ResponseException(RespCodeEnum.SUPPLIER_NOT_EXIST);
                }
                // 验证物料是否存在
                MaterialEntity materialEntity = materialMap.get(item.getMaterialCode());
                if (materialEntity == null) {
                    throw new ResponseException(RespCodeEnum.MATERIAL_NOT_EXISTS);
                }
                // 检查供应商物料是否已存在
                if (existingIdentifiers.contains(identifier)) {
                    throw new ResponseException(RespCodeEnum.SUPPLIER_MATERIAL_NOT_REPEAT);
                }
                // 验证通过，记录成功
                resultBuilder.addSuccess(identifier);
            } catch (Exception e) {
                // 记录失败
                SupplierMaterialBatchOperationResultVO.SupplierMaterialBatchOperationFailureVO failureVO =
                    SupplierMaterialBatchOperationResultVO.SupplierMaterialBatchOperationFailureVO.builder()
                        .supplierCode(item.getSupplierCode())
                        .materialCode(item.getMaterialCode())
                        .supplierMaterialCode(item.getSupplierMaterialCode())
                        .supplierMaterialName(item.getSupplierMaterialName())
                        .failureReason(e.getMessage())
                        .errorCode(e instanceof ResponseException ? ((ResponseException) e).getCode() : BatchOperationErrorType.SYSTEM_ERROR)
                        .build();

                resultBuilder.addFailure(failureVO);
            }
        }

        // 如果有失败项，抛出异常触发回滚
        if (resultBuilder.getFailedCount() > 0) {
            throw new BatchOperationException("批量新增供应商物料存在失败项", resultBuilder);
        }

        // 执行阶段：批量新增供应商物料
        List<SupplierMaterialEntity> entitiesToInsert = new ArrayList<>();
        for (SupplierMaterialBatchInsertDTO.SupplierMaterialInsertItemDTO item : dto.getMaterials()) {
            try {
                // 获取供应商和物料信息（使用已查询的Map）
                SupplierEntity supplierEntity = supplierMap.get(item.getSupplierCode());
                MaterialEntity materialEntity = materialMap.get(item.getMaterialCode());

                // 构建供应商物料实体
                SupplierMaterialEntity entity = JacksonUtil.convertObject(item, SupplierMaterialEntity.class);
                entity.setSupplierId(supplierEntity.getId());
                entity.setMaterialId(materialEntity.getId());
                entity.setSupplierCode(item.getSupplierCode());

                entitiesToInsert.add(entity);
            } catch (Exception e) {
                log.error("批量新增供应商物料执行失败，供应商编码：{}, 物料编码：{}, 错误：{}", item.getSupplierCode(), item.getMaterialCode(), e.getMessage(), e);
                // 执行阶段出现异常，直接抛出触发回滚
                throw new RuntimeException("批量新增供应商物料执行失败，供应商编码：" + item.getSupplierCode() + "，物料编码：" + item.getMaterialCode() + "，错误：" + e.getMessage(), e);
            }
        }

        // 批量保存
        supplierMaterialService.saveBatch(entitiesToInsert);

        // 推送消息
        for (SupplierMaterialEntity entity : entitiesToInsert) {
            messagePushToKafkaService.pushNewMessage(entity, Constants.KAFKA_MAIN_DATA_TOPIC, KafkaMessageTypeEnum.SUPPLIER_MATERIAL_ADD_MESSAGE);
        }

        // 构建结果
        return JacksonUtil.convertObject(resultBuilder, SupplierMaterialBatchOperationResultVO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SupplierMaterialBatchOperationResultVO batchUpdateMaterials(SupplierMaterialBatchUpdateDTO dto) {
        // 使用工具类构建结果
        BatchOperationResultBuilder<SupplierMaterialBatchOperationResultVO.SupplierMaterialBatchOperationFailureVO> resultBuilder =
                BatchOperationResultBuilder.create();

        // 批量查询供应商信息
        List<String> supplierCodes = dto.getMaterials().stream()
                .map(SupplierMaterialBatchUpdateDTO.SupplierMaterialUpdateItemDTO::getSupplierCode)
                .distinct()
                .collect(Collectors.toList());
        Map<String, SupplierEntity> supplierMap = this.lambdaQuery()
                .in(SupplierEntity::getCode, supplierCodes)
                .list().stream()
                .collect(Collectors.toMap(SupplierEntity::getCode, entity -> entity));

        // 批量查询物料信息
        List<String> materialCodes = dto.getMaterials().stream()
                .map(SupplierMaterialBatchUpdateDTO.SupplierMaterialUpdateItemDTO::getMaterialCode)
                .distinct()
                .collect(Collectors.toList());
        Map<String, MaterialEntity> materialMap = materialService.lambdaQuery()
                .in(MaterialEntity::getCode, materialCodes)
                .list().stream()
                .collect(Collectors.toMap(MaterialEntity::getCode, entity -> entity));

        // 批量查询已存在的供应商物料
        List<SupplierMaterialEntity> existingMaterials = supplierMaterialService.lambdaQuery()
                .in(SupplierMaterialEntity::getSupplierCode, supplierCodes)
                .list();
        Map<String, SupplierMaterialEntity> existingMaterialMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(existingMaterials)) {
            // 批量查询物料ID对应的编码
            List<Integer> materialIds = existingMaterials.stream()
                    .map(SupplierMaterialEntity::getMaterialId)
                    .distinct()
                    .collect(Collectors.toList());
            Map<Integer, String> materialIdCodeMap = materialService.lambdaQuery()
                    .select(MaterialEntity::getId, MaterialEntity::getCode)
                    .in(MaterialEntity::getId, materialIds)
                    .list().stream()
                    .collect(Collectors.toMap(MaterialEntity::getId, MaterialEntity::getCode));

            for (SupplierMaterialEntity entity : existingMaterials) {
                String materialCode = materialIdCodeMap.get(entity.getMaterialId());
                if (materialCode != null) {
                    String identifier = entity.getSupplierCode() + "-" + materialCode;
                    existingMaterialMap.put(identifier, entity);
                }
            }
        }

        // 验证阶段：检查所有供应商物料是否存在
        List<SupplierMaterialEntity> entitiesToUpdate = new ArrayList<>();
        for (SupplierMaterialBatchUpdateDTO.SupplierMaterialUpdateItemDTO item : dto.getMaterials()) {
            String identifier = item.getSupplierCode() + "-" + item.getMaterialCode();

            try {
                // 验证供应商是否存在
                SupplierEntity supplierEntity = supplierMap.get(item.getSupplierCode());
                if (supplierEntity == null) {
                    throw new ResponseException(RespCodeEnum.SUPPLIER_NOT_EXIST);
                }

                // 验证物料是否存在
                MaterialEntity materialEntity = materialMap.get(item.getMaterialCode());
                if (materialEntity == null) {
                    throw new ResponseException(RespCodeEnum.MATERIAL_NOT_EXISTS);
                }

                // 检查供应商物料是否存在
                SupplierMaterialEntity existingEntity = existingMaterialMap.get(identifier);
                if (existingEntity == null) {
                    throw new ResponseException(RespCodeEnum.MATERIAL_NOT_EXISTS);
                }

                // 验证通过，记录成功
                resultBuilder.addSuccess(identifier);
                entitiesToUpdate.add(existingEntity);
            } catch (Exception e) {
                // 记录失败
                SupplierMaterialBatchOperationResultVO.SupplierMaterialBatchOperationFailureVO failureVO =
                    SupplierMaterialBatchOperationResultVO.SupplierMaterialBatchOperationFailureVO.builder()
                        .supplierCode(item.getSupplierCode())
                        .materialCode(item.getMaterialCode())
                        .supplierMaterialCode(item.getSupplierMaterialCode())
                        .supplierMaterialName(item.getSupplierMaterialName())
                        .failureReason(e.getMessage())
                        .errorCode(e instanceof ResponseException ? ((ResponseException) e).getCode() : BatchOperationErrorType.SYSTEM_ERROR)
                        .build();

                resultBuilder.addFailure(failureVO);
            }
        }

        // 如果有失败项，抛出异常触发回滚
        if (resultBuilder.getFailedCount() > 0) {
            throw new BatchOperationException("批量更新供应商物料存在失败项", resultBuilder);
        }

        // 执行阶段：批量更新供应商物料
        for (int i = 0; i < dto.getMaterials().size(); i++) {
            SupplierMaterialBatchUpdateDTO.SupplierMaterialUpdateItemDTO item = dto.getMaterials().get(i);
            SupplierMaterialEntity entity = entitiesToUpdate.get(i);

            try {
                // 更新字段
                FieldUtil.copyValueTo(entity, JacksonUtil.convertObject(item, SupplierMaterialEntity.class));
                supplierMaterialService.updateById(entity);
            } catch (Exception e) {
                log.error("批量更新供应商物料执行失败，供应商编码：{}, 物料编码：{}, 错误：{}", item.getSupplierCode(), item.getMaterialCode(), e.getMessage(), e);
                // 执行阶段出现异常，直接抛出触发回滚
                throw new RuntimeException("批量更新供应商物料执行失败，供应商编码：" + item.getSupplierCode() + "，物料编码：" + item.getMaterialCode() + "，错误：" + e.getMessage(), e);
            }
        }

        // 构建结果
        return JacksonUtil.convertObject(resultBuilder, SupplierMaterialBatchOperationResultVO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SupplierMaterialBatchOperationResultVO batchDeleteMaterials(SupplierMaterialBatchDeleteDTO dto) {
        // 使用工具类构建结果
        BatchOperationResultBuilder<SupplierMaterialBatchOperationResultVO.SupplierMaterialBatchOperationFailureVO> resultBuilder =
                BatchOperationResultBuilder.create();

        // 批量查询物料信息
        List<String> materialCodes = dto.getMaterials().stream()
                .map(SupplierMaterialBatchDeleteDTO.SupplierMaterialDeleteItemDTO::getMaterialCode)
                .distinct()
                .collect(Collectors.toList());
        Map<String, MaterialEntity> materialMap = materialService.lambdaQuery()
                .in(MaterialEntity::getCode, materialCodes)
                .list().stream()
                .collect(Collectors.toMap(MaterialEntity::getCode, entity -> entity));

        // 批量查询供应商编码
        List<String> supplierCodes = dto.getMaterials().stream()
                .map(SupplierMaterialBatchDeleteDTO.SupplierMaterialDeleteItemDTO::getSupplierCode)
                .distinct()
                .collect(Collectors.toList());

        // 批量查询已存在的供应商物料
        List<SupplierMaterialEntity> existingMaterials = supplierMaterialService.lambdaQuery()
                .in(SupplierMaterialEntity::getSupplierCode, supplierCodes)
                .list();
        Map<String, SupplierMaterialEntity> existingMaterialMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(existingMaterials)) {
            // 批量查询物料ID对应的编码
            List<Integer> materialIds = existingMaterials.stream()
                    .map(SupplierMaterialEntity::getMaterialId)
                    .distinct()
                    .collect(Collectors.toList());
            Map<Integer, String> materialIdCodeMap = materialService.lambdaQuery()
                    .select(MaterialEntity::getId, MaterialEntity::getCode)
                    .in(MaterialEntity::getId, materialIds)
                    .list().stream()
                    .collect(Collectors.toMap(MaterialEntity::getId, MaterialEntity::getCode));

            for (SupplierMaterialEntity entity : existingMaterials) {
                String materialCode = materialIdCodeMap.get(entity.getMaterialId());
                if (materialCode != null) {
                    String identifier = entity.getSupplierCode() + "-" + materialCode;
                    existingMaterialMap.put(identifier, entity);
                }
            }
        }

        // 验证阶段：检查所有供应商物料是否存在
        List<SupplierMaterialEntity> entitiesToDelete = new ArrayList<>();
        for (SupplierMaterialBatchDeleteDTO.SupplierMaterialDeleteItemDTO item : dto.getMaterials()) {
            String identifier = item.getSupplierCode() + "-" + item.getMaterialCode();

            try {
                // 验证物料是否存在
                MaterialEntity materialEntity = materialMap.get(item.getMaterialCode());
                if (materialEntity == null) {
                    throw new ResponseException(RespCodeEnum.MATERIAL_NOT_EXISTS);
                }

                // 检查供应商物料是否存在
                SupplierMaterialEntity existingEntity = existingMaterialMap.get(identifier);
                if (existingEntity == null) {
                    throw new ResponseException(RespCodeEnum.MATERIAL_NOT_EXISTS);
                }

                // 验证通过，记录成功
                resultBuilder.addSuccess(identifier);
                entitiesToDelete.add(existingEntity);
            } catch (Exception e) {
                // 记录失败
                SupplierMaterialBatchOperationResultVO.SupplierMaterialBatchOperationFailureVO failureVO =
                    SupplierMaterialBatchOperationResultVO.SupplierMaterialBatchOperationFailureVO.builder()
                        .supplierCode(item.getSupplierCode())
                        .materialCode(item.getMaterialCode())
                        .failureReason(e.getMessage())
                        .errorCode(e instanceof ResponseException ? ((ResponseException) e).getCode() : BatchOperationErrorType.SYSTEM_ERROR)
                        .build();

                resultBuilder.addFailure(failureVO);
            }
        }

        // 如果有失败项，抛出异常触发回滚
        if (resultBuilder.getFailedCount() > 0) {
            throw new BatchOperationException("批量删除供应商物料存在失败项", resultBuilder);
        }

        // 执行阶段：批量删除供应商物料
        List<Integer> idsToDelete = entitiesToDelete.stream()
                .map(SupplierMaterialEntity::getId)
                .collect(Collectors.toList());

        supplierMaterialService.removeByIds(idsToDelete);

        // 构建结果
        return JacksonUtil.convertObject(resultBuilder, SupplierMaterialBatchOperationResultVO.class);
    }

    @Override
    public SupplierMaterialDetailVO getMaterialDetail(SupplierMaterialDetailQueryDTO dto) {
        // 查询物料信息
        MaterialEntity materialEntity = materialService.lambdaQuery()
                .eq(MaterialEntity::getCode, dto.getMaterialCode()).one();
        if (materialEntity == null) {
            throw new ResponseException(RespCodeEnum.MATERIAL_NOT_EXISTS);
        }

        // 查询供应商物料
        SupplierMaterialEntity entity = supplierMaterialService.lambdaQuery()
                .eq(SupplierMaterialEntity::getSupplierCode, dto.getSupplierCode())
                .eq(SupplierMaterialEntity::getMaterialId, materialEntity.getId())
                .one();
        if (entity == null) {
            throw new ResponseException(RespCodeEnum.MATERIAL_NOT_EXISTS);
        }
        // 查询供应商信息
        SupplierEntity supplierEntity = this.lambdaQuery()
                .eq(SupplierEntity::getCode, dto.getSupplierCode()).one();
        // 构建返回结果
        SupplierMaterialDetailVO result = JacksonUtil.convertObject(entity, SupplierMaterialDetailVO.class);
        result.setMaterialCode(materialEntity.getCode());
        result.setMaterialName(materialEntity.getName());
        result.setMaterialStandard(materialEntity.getStandard());
        if (supplierEntity != null) {
            result.setSupplierName(supplierEntity.getName());
        }
        // 设置状态名称
        if (entity.getState() != null) {
            result.setStateName(SupplierMaterialStateEnum.getNameByCode(entity.getState()));
        }
        return result;
    }
}
