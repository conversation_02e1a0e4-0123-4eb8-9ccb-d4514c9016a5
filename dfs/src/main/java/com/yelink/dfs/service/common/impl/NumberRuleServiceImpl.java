package com.yelink.dfs.service.common.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yelink.dfs.constant.rule.AutoIncrementConfigureTypeEnum;
import com.yelink.dfs.constant.rule.RulePrefixDTO;
import com.yelink.dfs.constant.rule.RulePrefixEnum;
import com.yelink.dfs.constant.rule.RuleSeqExtendDTO;
import com.yelink.dfs.constant.rule.RuleTypeEnum;
import com.yelink.dfs.entity.barcode.dto.RuleTypeDTO;
import com.yelink.dfs.entity.order.RuleSeqEntity;
import com.yelink.dfs.entity.rule.RulePrefixConfigEntity;
import com.yelink.dfs.entity.rule.RuleTypeConfigEntity;
import com.yelink.dfs.mapper.common.NumberRuleMapper;
import com.yelink.dfs.provider.constant.KafkaMessageTypeEnum;
import com.yelink.dfs.service.order.RuleSeqService;
import com.yelink.dfs.service.rule.NumberRuleService;
import com.yelink.dfs.service.rule.RulePrefixConfigService;
import com.yelink.dfs.service.rule.RuleTypeConfigService;
import com.yelink.dfscommon.constant.Constants;
import com.yelink.dfscommon.constant.RedisKeyPrefix;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.constant.dfs.rule.NumberCodeDTO;
import com.yelink.dfscommon.constant.dfs.rule.NumberRuleCodeDTO;
import com.yelink.dfscommon.constant.dfs.rule.NumberRuleTypeEnum;
import com.yelink.dfscommon.constant.dfs.rule.NumberRulesConfigEntity;
import com.yelink.dfs.open.v2.rule.dto.BatchGenerateRuleDetailDTO;
import com.yelink.dfscommon.dto.dfs.BatchRuleSeqDTO;
import com.yelink.dfscommon.dto.dfs.GenerateCodeDTO;
import com.yelink.dfscommon.dto.dfs.RuleDetailDTO;
import com.yelink.dfscommon.dto.dfs.RuleSeqDTO;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.provider.MessagePushToKafkaService;
import com.yelink.dfscommon.utils.DateUtil;
import com.yelink.dfscommon.utils.JacksonUtil;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.yelink.dfs.constant.RedisKeyPrefix.BAR_CODE_KEY;

/**
 * <AUTHOR>
 */
@Service
public class NumberRuleServiceImpl extends ServiceImpl<NumberRuleMapper, NumberRulesConfigEntity> implements NumberRuleService, ApplicationRunner {

    private static final String DATERULE = "formatCurrentDate";

    @Resource
    private RuleSeqService ruleSeqService;
    @Lazy
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private MessagePushToKafkaService messagePushToKafkaService;
    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private RulePrefixConfigService rulePrefixConfigService;
    @Resource
    private RuleTypeConfigService ruleTypeConfigService;

    /**
     * 函数式接口
     */
    private Map<String, Function<RuleSeqExtendDTO, NumberRuleCodeDTO>> ruleMap = new HashMap<>();

    /**
     * 编码规则通用方法处理
     */
    private NumberRuleCodeDTO getCommonMethod(RuleSeqExtendDTO ruleDetailDTO) {
        // 从数据库获取配置
        RulePrefixConfigEntity config = rulePrefixConfigService.getByCode(ruleDetailDTO.getCode());
        if (config == null) {
            return null;
        }

        // 获取单据号/编码
        String number = ruleDetailDTO.getNumberMap().getOrDefault(ruleDetailDTO.getCode(), "");

        return NumberRuleCodeDTO.builder()
                .name(config.getType())
                .code(config.getCode())
                .type(config.getInput())
                .rule("")
                .value(number)
                .build();
    }


    /**
     * 之前调用mapper，现在改成直接在service中调用
     *
     * @return
     */
    @Override
    public List<String> getType() {
        QueryWrapper<NumberRulesConfigEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().select(NumberRulesConfigEntity::getType);
        List<NumberRulesConfigEntity> list = this.list(queryWrapper);
        List<String> types = list.stream().map(NumberRulesConfigEntity::getType).collect(Collectors.toList());
        return types;
    }

    @Override
    public Page<NumberRulesConfigEntity> getAll(Integer current, Integer size, String ruleType) {
        Page<NumberRulesConfigEntity> page = new Page<>();
        LambdaQueryWrapper<NumberRulesConfigEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(NumberRulesConfigEntity::getType, ruleType);
        wrapper.orderByDesc(NumberRulesConfigEntity::getCreateTime)
                .orderByDesc(NumberRulesConfigEntity::getId);
        if (current == null || size == null) {
            List<NumberRulesConfigEntity> list = this.list(wrapper);
            page.setRecords(list).setTotal(list.size()).setCurrent(1).setSize(list.size());
        } else {
            page = this.page(new Page<>(current, size), wrapper);
        }
        for (NumberRulesConfigEntity numberRulesConfigEntity : page.getRecords()) {
            // 设置类型名称
            numberRulesConfigEntity.setTypeName(RuleTypeEnum.getNameByType(numberRulesConfigEntity.getType()));
        }
        return page;
    }

    /**
     * 现在生成规则按照V1.13.1
     * 2.当选择当前日期时，右侧显示"配置信息"字段，可对日期做类型配置，并且自定义展示哪些位的信息。
     * 3.当选择了自动生成序号时，右侧显示"位数信息"字段，输入正整数之后将样例数据展示出来。（1≤n≤8）
     * 4.当选择了固定信息时，右侧显示"输入信息"字段，请输入。
     * 5.当选择了人工输入时，不显示啥。
     * 6.当选择了生产工单时，不显示啥。
     * 7.当选择了物料编码时，不显示啥。
     * <p>
     * 优化：保持之前接口不变，现在有物料、工单类型，参数需要加个类型字段
     *
     * @param dto id --编码规则id
     *            <p>
     *            materialCode --物料编码
     *            <p>
     *            relatedMap --归一方式查询的关联单据对象
     *            key为关联单号类型
     *            value为关联单号（可以是生产工单号、销售订单号、制造单元类型编号、制造单元编号。。。多种单号）
     *            <p>
     *            numberMap --前端传递的编码（可以是生产工单号、销售订单号、制造单元类型编号、制造单元编号。。。多种单号），用于组成编码信息
     *            key为编码规则类型
     *            value为编码号
     *            numberMap 相关枚举 {@link RulePrefixEnum}
     *            relatedMap 相关枚举{@link AutoIncrementConfigureTypeEnum }
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public NumberCodeDTO getSeqById(RuleSeqDTO dto) {
        // 分布式锁，同一时间段只能有一个线程执行
        String key = RedisKeyPrefix.NUMBER_RULE_LOCK_KEY + dto.getId();
        Boolean lockStatus = redisTemplate.opsForValue().setIfAbsent(key, new Date(), 3, TimeUnit.SECONDS);
        /*if (lockStatus == null || !lockStatus) {
            throw new ResponseException("并发占用，稍后再试");
        }*/
        try {
            //拿到生成规则
            NumberRulesConfigEntity numberRule = this.getById(dto.getId());
            Map<String, String> numberMap = dto.getNumberMap() == null ? new HashMap<>() : dto.getNumberMap();
            Map<String, String> relatedMap = dto.getRelatedMap() == null ? new HashMap<>() : dto.getRelatedMap();
            List<NumberRuleCodeDTO> list = new ArrayList<>();

            if (StringUtils.isNotBlank(numberRule.getPrefixDetail())) {
                List<RuleDetailDTO> ruleDetailDTOs = JSONArray.parseArray(numberRule.getPrefixDetail(), RuleDetailDTO.class);
                // 对组成信息进行规则匹配
                for (RuleDetailDTO ruleDetailDTO : ruleDetailDTOs) {
                    RuleSeqExtendDTO ruleSeqExtendDTO = JacksonUtil.convertObject(ruleDetailDTO, RuleSeqExtendDTO.class);
                    ruleSeqExtendDTO.setId(numberRule.getId());
                    ruleSeqExtendDTO.setNumberRule(numberRule);
                    ruleSeqExtendDTO.setNumberMap(numberMap);
                    ruleSeqExtendDTO.setRelatedMap(relatedMap);
                    // 获取对应方法的组成信息
                    Function<RuleSeqExtendDTO, NumberRuleCodeDTO> function = ruleMap.get(ruleSeqExtendDTO.getCode());
                    NumberRuleCodeDTO numberRuleCodeDTO = function.apply(ruleSeqExtendDTO);
                    list.add(numberRuleCodeDTO);
                }
            }
            // 设置编码规则类型名称，方便日志定位
            RuleTypeConfigEntity typeConfigEntity = ruleTypeConfigService.lambdaQuery().eq(RuleTypeConfigEntity::getType, numberRule.getType()).one();
            return NumberCodeDTO.builder().ruleTypeName(typeConfigEntity.getModuleName()).prefixValueList(list).build();
        } finally {
            redisTemplate.delete(key);
        }
    }

    /**
     * 匹配时间规则
     */
    private NumberRuleCodeDTO getCurrentDateType(RuleSeqExtendDTO ruleDetailDTO) {
        String dateStr;
        // 自定义格式
        if (Constants.CUSTOM_FORMAT.equals(ruleDetailDTO.getRule())) {
            dateStr = "";
        } else {
            // 生成日期
            dateStr = DateUtil.format(new Date(), ruleDetailDTO.getRule());
        }
        return NumberRuleCodeDTO.builder().code(RulePrefixEnum.CURRENTDATE_TYPE.getCode()).name(RulePrefixEnum.CURRENTDATE_TYPE.getType()).type(0).rule(ruleDetailDTO.getRule()).value(dateStr).build();
    }

    /**
     * 匹配自增序号
     */
    public NumberRuleCodeDTO getAutoIncrementType(RuleSeqExtendDTO ruleDetailDTO) {
        // 获取关联类型中的关联单据号/编码
        String relationType = ruleDetailDTO.getAutoIncrementConfigureType();
        String relationNumber = ruleDetailDTO.getRelatedMap().getOrDefault(relationType, null);
        // 获取特殊类型(此类型下调用编码规则时不加1)
//        List<String> notAddOneTypes = getRuleTypeDTOsNotAddOne().stream().map(RuleTypeDTO::getType).collect(Collectors.toList());
        NumberRulesConfigEntity rulesConfigEntity = this.getById(ruleDetailDTO.getId());
        RuleTypeConfigEntity ruleTypeConfigEntity = ruleTypeConfigService.lambdaQuery()
                .eq(RuleTypeConfigEntity::getType, rulesConfigEntity.getType())
                .one();

        // 特殊类型：重复查询编码规则时，生成序列号不自增
        // 其他类型：重复查询编码规则时， 生成序列号使用加1的自增方法
        int seq = StringUtils.isNotBlank(ruleTypeConfigEntity.getTypeCode()) ?
                ruleSeqService.getSeq(ruleDetailDTO.getNumberRule(), ruleDetailDTO, relationNumber) :
                ruleSeqService.getSeqAddOne(ruleDetailDTO.getNumberRule().getType(), ruleDetailDTO, relationNumber, relationType, ruleDetailDTO.getNumberRule().getId());
        String value = "";
        if (ruleDetailDTO.getRule() != null) {
            value = String.format("%0" + ruleDetailDTO.getRule() + "d", seq);
        }
        return NumberRuleCodeDTO.builder()
                .name(RulePrefixEnum.AUTO_INCREMENT_TYPE.getType())
                .code(RulePrefixEnum.AUTO_INCREMENT_TYPE.getCode())
                .rule(ruleDetailDTO.getRule())
                .type(0)
                .value(value)
                .seq(seq)
                .autoIncrementConfigureType(ruleDetailDTO.getAutoIncrementConfigureType())
                .uuid(ruleDetailDTO.getUuid())
                // 历史记录为空，默认赋值为1
                .initValue(Objects.isNull(ruleDetailDTO.getInitValue()) ? 1 : ruleDetailDTO.getInitValue())
                .build();
    }

    /**
     * 匹配固定信息
     */
    private NumberRuleCodeDTO getFixType(RuleSeqExtendDTO ruleDetailDTO) {
        return NumberRuleCodeDTO.builder().name(RulePrefixEnum.FIXED_TYPE.getType()).rule("").code(RulePrefixEnum.FIXED_TYPE.getCode()).type(0).value(ruleDetailDTO.getRule()).build();
    }

    /**
     * 匹配手动输入
     */
    private NumberRuleCodeDTO getArtificialType(RuleSeqExtendDTO ruleDetailDTO) {
        return NumberRuleCodeDTO.builder().name(RulePrefixEnum.ARTIFICIAL_TYPE.getType()).code(RulePrefixEnum.ARTIFICIAL_TYPE.getCode()).type(1).rule("").value("").build();
    }

    /**
     * 匹配下单日期
     */
    private NumberRuleCodeDTO getOrderDate(RuleSeqExtendDTO ruleDetailDTO) {
        String dateStr = ruleDetailDTO.getNumberMap().getOrDefault(ruleDetailDTO.getCode(), "");
        // 生成日期
        String dateTime = StringUtils.isBlank(dateStr) ? "" : DateUtil.dateToDateTime(dateStr, ruleDetailDTO.getRule());
        return NumberRuleCodeDTO.builder().name(RulePrefixEnum.ORDER_DATE.getType()).code(RulePrefixEnum.ORDER_DATE.getCode()).type(0).rule("").value(dateTime).build();
    }

    /**
     * 匹配交货日期
     */
    private NumberRuleCodeDTO getRequireGoodsDate(RuleSeqExtendDTO ruleDetailDTO) {
        String dateStr = ruleDetailDTO.getNumberMap().getOrDefault(ruleDetailDTO.getCode(), "");
        // 生成日期
        String dateTime = StringUtils.isBlank(dateStr) ? "" : DateUtil.dateToDateTime(dateStr, ruleDetailDTO.getRule());
        return NumberRuleCodeDTO.builder().name(RulePrefixEnum.REQUIRE_GOODS_DATE.getType()).code(RulePrefixEnum.REQUIRE_GOODS_DATE.getCode()).type(0).rule("").value(dateTime).build();
    }

    /**
     * 按校验次数自增序号，每调用一次就自增1（定制化需求，目前只有西点精工使用到）
     */
    private NumberRuleCodeDTO getCheckTimes(RuleSeqExtendDTO ruleDetailDTO) {
        String value = ruleSeqService.getSeqByCheckTime(ruleDetailDTO, ruleDetailDTO.getNumberRule().getType(), ruleDetailDTO.getAutoIncrementConfigureType());
        return NumberRuleCodeDTO.builder().name(RulePrefixEnum.CHECK_TIMES.getType()).rule("").code(RulePrefixEnum.CHECK_TIMES.getCode()).type(0).value(value).uuid(ruleDetailDTO.getUuid()).build();
    }


    /**
     * 匹配挡位
     */
    private NumberRuleCodeDTO getGearPosition(RuleSeqExtendDTO ruleDetailDTO) {
        return NumberRuleCodeDTO.builder().name(RulePrefixEnum.GEAR_POSITION.getType()).code(RulePrefixEnum.GEAR_POSITION.getCode()).type(1).value(ruleDetailDTO.getValue()).rule("").build();
    }

    /**
     * 组装拼接字符串
     *
     * @param list
     * @param seq
     * @param ruleDetailDTO
     */
    private void addNumberRuleCodeDto(List<NumberRuleCodeDTO> list, Integer seq, RuleDetailDTO ruleDetailDTO) {
        String value = "";
        if (ruleDetailDTO.getRule() != null) {
            value = String.format("%0" + ruleDetailDTO.getRule() + "d", seq);
        }
        list.add(NumberRuleCodeDTO.builder()
                .name(RulePrefixEnum.AUTO_INCREMENT_TYPE.getType())
                .code(RulePrefixEnum.AUTO_INCREMENT_TYPE.getCode())
                .rule(ruleDetailDTO.getRule())
                .type(0)
                .value(value)
                .seq(seq)
                .autoIncrementConfigureType(ruleDetailDTO.getAutoIncrementConfigureType())
                .uuid(ruleDetailDTO.getUuid())
                // 历史记录为空，默认赋值为1
                .initValue(Objects.isNull(ruleDetailDTO.getInitValue()) ? 1 : ruleDetailDTO.getInitValue())
                .build());
    }

    /**
     * 获取编码规则允许调用后不加1的 模块类型
     *
     * @return
     */
    private List<RuleTypeDTO> getRuleTypeDTOsNotAddOne() {
        return Arrays.stream(NumberRuleTypeEnum.values())
                .map(numberRuleTypeEnum -> RuleTypeDTO.builder()
                        .type(numberRuleTypeEnum.getCode())
                        .name(numberRuleTypeEnum.getName())
                        .build()).collect(Collectors.toList());
    }

    @Override
    public List<NumberRulesConfigEntity> getByTypeCode(String typeCode) {
        List<NumberRulesConfigEntity> list = this.lambdaQuery().eq(NumberRulesConfigEntity::getType, typeCode).list();
        for (NumberRulesConfigEntity entity : list) {
            List<RuleDetailDTO> ruleDetailDTOS = JSON.parseArray(entity.getPrefixDetail(), RuleDetailDTO.class);
            entity.setPrefixDetailList(ruleDetailDTOS);
            entity.setPrefixDetailName(ruleDetailDTOS.stream().map(RuleDetailDTO::getName).collect(Collectors.joining(Constants.CROSSBAR)));
        }
        if (typeCode.equals("12") && CollectionUtils.isEmpty(list)) {
            throw new ResponseException(RespCodeEnum.NOT_CONFIG_FLOW_CODE_RULE);
        }
        return list;
    }

    @Override
    public NumberRulesConfigEntity get(Integer id) {
        NumberRulesConfigEntity numberRulesConfigEntity = this.getById(id);
        if (numberRulesConfigEntity == null) {
            return null;
        }
        // 设置类型名称
        numberRulesConfigEntity.setTypeName(RuleTypeEnum.getEnumByType(numberRulesConfigEntity.getType()) != null ? RuleTypeEnum.getEnumByType(numberRulesConfigEntity.getType()).getModuleName() : null);
        List<RuleDetailDTO> ruleDetailDTOS = JSON.parseArray(numberRulesConfigEntity.getPrefixDetail(), RuleDetailDTO.class);
        numberRulesConfigEntity.setPrefixDetailList(ruleDetailDTOS);
        return numberRulesConfigEntity;
    }

    @Override
    public NumberCodeDTO generateRules(Integer numberRulesId, List<RuleDetailDTO> ruleDetailDTOs, Map<String, String> relatedMap) {
        NumberRulesConfigEntity numberRulesConfigEntity = this.getById(numberRulesId);
        return generateRules(numberRulesConfigEntity, ruleDetailDTOs, relatedMap);
    }

    @Override
    public NumberCodeDTO generateRules(NumberRulesConfigEntity numberRulesConfigEntity, List<RuleDetailDTO> ruleDetailDTOs, Map<String, String> relatedMap) {
        List<NumberRuleCodeDTO> list = new ArrayList<>();
        Map<String, Integer> seqMap = new HashMap<>();

        for (RuleDetailDTO ruleDetailDTO : ruleDetailDTOs) {
            String relationNumber;
            RulePrefixEnum typeEnum = RulePrefixEnum.getEnumByCode(ruleDetailDTO.getCode());
            // 如果为空则走默认通用的逻辑
            if (Objects.isNull(typeEnum)) {
                list.add(NumberRuleCodeDTO.builder().code(ruleDetailDTO.getCode()).name(ruleDetailDTO.getName()).rule(ruleDetailDTO.getRule()).value(ruleDetailDTO.getValue()).build());
                continue;
            }
            switch (typeEnum) {
                // 匹配时间规则
                case CURRENTDATE_TYPE:
                    // 自定义格式
                    String dateStr = Constants.CUSTOM_FORMAT.equals(ruleDetailDTO.getRule()) ? ruleDetailDTO.getValue() : DateUtil.format(new Date(), ruleDetailDTO.getRule());
                    list.add(NumberRuleCodeDTO.builder().code(ruleDetailDTO.getCode()).name(ruleDetailDTO.getName()).rule(ruleDetailDTO.getRule()).value(dateStr).build());
                    break;
                // 匹配自增序号
                case AUTO_INCREMENT_TYPE:
                    relationNumber = relatedMap == null ? null : relatedMap.getOrDefault(ruleDetailDTO.getAutoIncrementConfigureType(), null);
                    Integer currentSeq = ruleSeqService.getSeq(numberRulesConfigEntity, ruleDetailDTO, relationNumber);
                    // 使用uuid作为key存储seq值
                    seqMap.put(ruleDetailDTO.getUuid(), currentSeq - 1);
                    addNumberRuleCodeDto(list, currentSeq, ruleDetailDTO);
                    break;
                // 匹配固定信息
                case FIXED_TYPE:
                    list.add(NumberRuleCodeDTO.builder().code(ruleDetailDTO.getCode()).name(ruleDetailDTO.getName()).rule(ruleDetailDTO.getRule()).value(ruleDetailDTO.getValue() == null ? ruleDetailDTO.getExample() : ruleDetailDTO.getValue()).build());
                    break;
                // 生产订单号(仅下推有效)
                case PRODUCT_ORDER_ONLY_PUSH_DOWN:
                    // 生产订单号(仅按工艺路线下推有效)
                    relationNumber = relatedMap == null ? null : relatedMap.getOrDefault(AutoIncrementConfigureTypeEnum.PRODUCT_ORDER_ONLY_PUSH_DOWN.getCode(), null);
                    list.add(NumberRuleCodeDTO.builder().code(ruleDetailDTO.getCode()).name(ruleDetailDTO.getName()).rule(ruleDetailDTO.getRule()).value(relationNumber).build());
                    break;
                // 销售订单号(仅下推有效)
                case SALE_ORDER_ONLY_PUSH_DOWN:
                    // 销售订单号(仅按工艺路线下推、按BOM下推有效)
                    relationNumber = relatedMap == null ? null : relatedMap.getOrDefault(AutoIncrementConfigureTypeEnum.SALE_ORDER_ONLY_PUSH_DOWN.getCode(), null);
                    list.add(NumberRuleCodeDTO.builder().code(ruleDetailDTO.getCode()).name(ruleDetailDTO.getName()).rule(ruleDetailDTO.getRule()).value(relationNumber).build());
                    break;
                default:
                    list.add(NumberRuleCodeDTO.builder().code(ruleDetailDTO.getCode()).name(ruleDetailDTO.getName()).rule(ruleDetailDTO.getRule()).value(ruleDetailDTO.getValue()).build());
            }
        }
        return NumberCodeDTO.builder()
                .prefixValueList(list)
                .seqMap(seqMap)
                .seq(seqMap.isEmpty() ? 1 : seqMap.values().iterator().next() + 1) // 为了向后兼容,保留一个seq值
                .build();
    }

    @Override
    public List<RulePrefixDTO> getRulesEnums(String ruleType) {
        List<RulePrefixDTO> list = ruleType != null ? RulePrefixEnum.getEnumByRuleType(ruleType) : RulePrefixEnum.toList();
        for (RulePrefixDTO value : list) {
            if (DATERULE.equals(value.getImplName())) {
                // 匹配时间进行时间格式化
                List<Map<String, String>> ruleExtendList = value.getRuleExtendList();
                for (Map<String, String> map : ruleExtendList) {
                    switch (map.get("pattern")) {
                        case DateUtil.SHORT_YEAR_FORMAT:
                            map.put("value", DateUtil.format(new Date(), DateUtil.SHORT_YEAR_FORMAT));
                            break;
                        case DateUtil.DAY_FORMAT_SHORT_ONE:
                            map.put("value", DateUtil.format(new Date(), DateUtil.DAY_FORMAT_SHORT_ONE));
                            break;
                        case DateUtil.YEAR_FORMAT:
                            map.put("value", DateUtil.format(new Date(), DateUtil.YEAR_FORMAT));
                            break;
                        case DateUtil.ONLY_MONTH_FORMAT:
                            map.put("value", DateUtil.format(new Date(), DateUtil.ONLY_MONTH_FORMAT));
                            break;
                        case DateUtil.MONTH_FORMAT:
                            map.put("value", DateUtil.format(new Date(), DateUtil.MONTH_FORMAT));
                            break;
                        case DateUtil.DATE_FORMAT:
                            map.put("value", DateUtil.format(new Date(), DateUtil.DATE_FORMAT));
                            break;
                        case DateUtil.MONTH_FORMAT_SHORT:
                            map.put("value", DateUtil.format(new Date(), DateUtil.MONTH_FORMAT_SHORT));
                            break;
                        case DateUtil.MONTH_FORMAT_SHORT_ONE:
                            map.put("value", DateUtil.format(new Date(), DateUtil.MONTH_FORMAT_SHORT_ONE));
                            break;
                        case DateUtil.DATE_FORMAT_SLASH:
                            map.put("value", DateUtil.format(new Date(), DateUtil.DATE_FORMAT_SLASH));
                            break;
                        case DateUtil.DATE_FORMAT_SHORT:
                            map.put("value", DateUtil.format(new Date(), DateUtil.DATE_FORMAT_SHORT));
                            break;
                        case DateUtil.HOUR_FORMAT:
                            map.put("value", DateUtil.format(new Date(), DateUtil.HOUR_FORMAT));
                            break;
                        case DateUtil.DATETIME_FORMAT:
                            map.put("value", DateUtil.format(new Date(), DateUtil.DATETIME_FORMAT));
                            break;
                        case DateUtil.DATETIME_FORMAT_MIN_WITHOUT_SECOND:
                            map.put("value", DateUtil.format(new Date(), DateUtil.DATETIME_FORMAT_MIN_WITHOUT_SECOND));
                            break;
                        default:
                            break;
                    }
                }
            }
        }
        return list;
    }

    @Override
    @Deprecated
    public String getNumberByRuleType(Integer ruleType) {
        // 版本变更，此方法废弃
        String number = null;
        List<NumberRulesConfigEntity> numberRulesConfigEntities = this.getByTypeCode(ruleType.toString());
        if (!CollectionUtils.isEmpty(numberRulesConfigEntities)) {
            // 采用新的编码规则(默认取第一个规则)
            NumberCodeDTO numberCodeDTO = this.getSeqById(RuleSeqDTO.builder().id(numberRulesConfigEntities.get(0).getId()).build());
            if (numberCodeDTO != null) {
                number = numberCodeDTO.getCode();
            }
        }
        return number;
    }

    @Override
    public ResponseData getBarCodeLoke(QueryWrapper<RuleSeqEntity> queryWrapper, Integer num, List<String> numList) {
        RuleSeqEntity entity = ruleSeqService.getOne(queryWrapper);
        RLock barcodeLoke = redissonClient.getLock(BAR_CODE_KEY);
        try {
            boolean b = barcodeLoke.tryLock();
            if (b) {
                Integer seq = 0;
                for (int i = 0; i < num; i++) {
                    seq = entity.getSeq();
                    String format = String.format("%08d", seq);
                    numList.add(format);
                    entity.setSeq(entity.getSeq() + 1);
                    entity.setUpdateTime(new Date());
                    ruleSeqService.updateById(entity);
                }
            }
        } catch (Exception e) {
            log.error("", e);
            return ResponseData.fail();
        } finally {
            barcodeLoke.unlock();
        }
        return ResponseData.success(numList);
    }

    @Override
    public void batchAddOrUpdate(List<NumberRulesConfigEntity> numberRuleList, String username) {
        if (numberRuleList.isEmpty()) {
            throw new ResponseException(RespCodeEnum.NUMBER_CODE_SAVE_AT_LEAST_ONE);
        }

        // 删除数据库中已被用户删除的数据
        List<Integer> existIds = numberRuleList.stream().map(NumberRulesConfigEntity::getId).filter(Objects::nonNull).collect(Collectors.toList());
        List<Integer> deleteIds = this.lambdaQuery().eq(NumberRulesConfigEntity::getType, numberRuleList.get(0).getType())
                .list()
                .stream()
                .map(NumberRulesConfigEntity::getId)
                .filter(id -> !existIds.contains(id))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(deleteIds)) {
            this.removeByIds(deleteIds);
        }
        // 同个编码规则类型下有且只有一个为默认
        Map<String, Long> typeDefaultCountMap = numberRuleList.stream()
                .filter(o -> Objects.nonNull(o.getIsDefault()) && o.getIsDefault())
                .collect(Collectors.groupingBy(NumberRulesConfigEntity::getType, Collectors.counting()));
        for (Map.Entry<String, Long> entry : typeDefaultCountMap.entrySet()) {
            if (entry.getValue() == 0) {
                throw new ResponseException(RespCodeEnum.NEED_EXIST_ONE_DEFAULT_CODE_RULE);
            }
            if (entry.getValue() > 1) {
                throw new ResponseException(RespCodeEnum.DFS_ONLY_ONE_DEFAULT_CODE_RULE_PER_TYP);
            }
        }

        // 批量新增/更新
        Date date = new Date();
        for (NumberRulesConfigEntity configEntity : numberRuleList) {
            configEntity.setCreateTime(Objects.isNull(configEntity.getCreateTime()) ? date : configEntity.getCreateTime());
            configEntity.setUpdateTime(Objects.isNull(configEntity.getUpdateTime()) ? date : configEntity.getUpdateTime());
            configEntity.setCreateBy(username);
            List<RuleDetailDTO> ruleDetailDTOS = JSON.parseArray(configEntity.getPrefixDetail(), RuleDetailDTO.class);
            List<String> codes = ruleDetailDTOS.stream().map(RuleDetailDTO::getCode).collect(Collectors.toList());
            for (RuleDetailDTO ruleDetailDTO : ruleDetailDTOS) {
                if (ruleDetailDTO.getCode().equals(RulePrefixEnum.AUTO_INCREMENT_TYPE.getCode())) {
                    // 按xx归一：除了不归一，其他组成信息中有此字段才可按该字段归一，例如按供应商归一，需要有供应商的组成信息
                    List<String> needOtherRuleCodes = AutoIncrementConfigureTypeEnum.getNeedOtherRuleCodeByCode(ruleDetailDTO.getAutoIncrementConfigureType());
                    for (String needOtherRuleCode : needOtherRuleCodes) {
                        if (!codes.contains(needOtherRuleCode) && !ruleDetailDTO.getAutoIncrementConfigureType().equals(AutoIncrementConfigureTypeEnum.NO_CROSS.getCode())) {
                            // 组成信息中有此字段才可按该字段归一
                            throw new ResponseException(RespCodeEnum.THIS_FIELD_EXISTS_IN_THE_COMPOSITION_INFORMATION_CAN_BE_GROUPED_BY_THIS_FIELD);
                        }
                    }
                    // 为了能找到对应的自增序列，需要对自动生成序号、检验次数设置uuid，已存在的则需要同步刷新到seq表中
                    if (StringUtils.isBlank(ruleDetailDTO.getUuid())) {
                        ruleDetailDTO.setUuid(UUID.randomUUID().toString());
                    } else {
                        ruleSeqService.lambdaUpdate()
                                .eq(RuleSeqEntity::getUuid, ruleDetailDTO.getUuid())
                                .set(RuleSeqEntity::getRelationType, ruleDetailDTO.getAutoIncrementConfigureType())
                                .update();
                    }
                }
                if (StringUtils.isBlank(ruleDetailDTO.getUuid()) && ruleDetailDTO.getCode().equals(RulePrefixEnum.CHECK_TIMES.getCode())) {
                    ruleDetailDTO.setUuid(UUID.randomUUID().toString());
                }
            }

            // 转为JSON数据，存入到数据库中
            configEntity.setPrefixDetail(JSON.toJSONString(ruleDetailDTOS));
        }
        this.saveOrUpdateBatch(numberRuleList);
        // 推送通知
        messagePushToKafkaService.pushNewMessage(numberRuleList, Constants.KAFKA_MAIN_DATA_TOPIC, KafkaMessageTypeEnum.LABEL_ADD_OR_UPDATE);
    }

    @Override
    public List<NumberCodeDTO> getBatchSeqById(BatchRuleSeqDTO dto) {
        List<NumberCodeDTO> list = new ArrayList<>();
        // 记录第一次调用的所有seq值
        Map<String, Integer> seqMap = null;
        for (int i = 0; i < dto.getCount(); i++) {
            NumberCodeDTO seqById = getSeqById(dto);
            list.add(seqById);
            // 记录第一次调用的序列号
            if (i == 0) {
                seqMap = new HashMap<>(seqById.getSeqMap());
            }
            // 获取特殊类型(此类型下调用编码规则时不加1)
            List<RuleTypeDTO> collect = getRuleTypeDTOsNotAddOne();
            List<String> types = collect.stream().map(RuleTypeDTO::getType).collect(Collectors.toList());
            // 特殊类型在遍历时需要手动更新seq值，不然会出现重复的数据
            if (StringUtils.isNotBlank(dto.getRuleType()) && types.contains(dto.getRuleType())) {
                // 编码规则有自动生成序号的，seq才加1
                ruleSeqService.updateSeqEntity(dto.getRelatedMap(), dto.getId(), false);
            }
        }
        // 如果不需要在调用编码规则的过程中自增序号，则调用完毕后，还原序号
        if (!dto.getIsAutoIncrement() && seqMap != null) {
            // 更新所有自增序号
            ruleSeqService.refreshSeq(dto.getRelatedMap(), dto.getId(), seqMap);
        }
        return list;
    }


    /**
     * 编码规则 兼容历史数据
     * 1系列升级到2系列时需要升级：多个相同编号生成规则，编号配置JSON中code=4的数据有几条 对应dfs_rule_seq中的几条数据，对应规则: UUID
     * 现场已没有1系列的，故注释历史兼容数据代码，避免启动dfs时执行无用代码
     *
     * @param args
     * @throws Exception
     */
    @Override
    public void run(ApplicationArguments args) throws Exception {
        // 初始化编码规则选项
        initRuleMap();
//        List<RuleSeqEntity> ruleSeqEntityList = ruleSeqService.lambdaQuery().ne(RuleSeqEntity::getRuleType, Constant.BAR_CODE_NUMBER).list();
//        if (CollectionUtils.isEmpty(ruleSeqEntityList)) {
//            return;
//        }
//        boolean hasNull = ruleSeqEntityList.stream().anyMatch(o -> o.getUuid() == null);
//        // dfs_rule_seq是否有为空的uuid
//        if (!hasNull) {
//            return;
//        }
//        List<NumberRulesConfigEntity> list = list();
//        if (CollectionUtils.isEmpty(list)) {
//            return;
//        }
//        // 按type分组
//        Map<String, List<NumberRulesConfigEntity>> map = list.stream().collect(Collectors.groupingBy(NumberRulesConfigEntity::getType));
//        for (Map.Entry<String, List<NumberRulesConfigEntity>> entry : map.entrySet()) {
//            List<NumberRulesConfigEntity> numberRulesConfigEntities = entry.getValue();
//            String type = entry.getKey();
//            List<RuleDetailDTO> ruleDetailDTOList = new ArrayList<>();
//            for (NumberRulesConfigEntity numberRulesConfigEntity : numberRulesConfigEntities) {
//                String prefixDetail = numberRulesConfigEntity.getPrefixDetail();
//                List<RuleDetailDTO> ruleDetailDTOS = JSONArray.parseArray(prefixDetail, RuleDetailDTO.class);
//                // 拿到编码规则为自动生成序号的数据
//                List<RuleDetailDTO> detailDTOS = ruleDetailDTOS.stream().filter(o -> o.getCode().equals(RulePrefixEnum.AUTO_INCREMENT_TYPE.getCode())).collect(Collectors.toList());
//                for (RuleDetailDTO detailDTO : detailDTOS) {
//                    if (detailDTO.getUuid() == null) {
//                        String uuid = UUID.randomUUID().toString();
//                        detailDTO.setUuid(uuid);
//                        detailDTO.setAutoIncrementConfigureType(numberRulesConfigEntity.getAutoIncrementConfigureType());
//                    }
//                    if (detailDTO.getAutoIncrementConfigureType().equals("bill")) {
//                        detailDTO.setAutoIncrementConfigureType("workOrder");
//                    }
//                }
//                // 刷新 dfs_config_number_rules 自增序号配置
//                numberRulesConfigEntity.setPrefixDetail(JSON.toJSONString(ruleDetailDTOS));
//                this.updateById(numberRulesConfigEntity);
//
//                // 用于刷新 dfs_rule_seq
//                ruleDetailDTOList.addAll(detailDTOS);
//            }
//
//            if (CollectionUtils.isEmpty(ruleDetailDTOList)) {
//                continue;
//            }
//            // 刷新 dfs_rule_seq
//            List<RuleSeqEntity> ruleSeqEntities = ruleSeqService.lambdaQuery()
//                    .eq(RuleSeqEntity::getRuleType, type)
//                    .list();
//            // 如果ruleSeqEntities为空 遍历新增数据
//            if (CollectionUtils.isEmpty(ruleSeqEntities)) {
//                for (RuleDetailDTO ruleDetailDTO : ruleDetailDTOList) {
//                    RuleSeqEntity build = RuleSeqEntity.builder()
//                            .ruleType(type)
//                            .uuid(ruleDetailDTO.getUuid())
//                            .relationType(ruleDetailDTO.getAutoIncrementConfigureType())
//                            .relationNumber(null)
//                            .seq(1)
//                            .createTime(new Date())
//                            .updateTime(new Date())
//                            .build();
//                    ruleSeqService.save(build);
//                }
//                continue;
//            }
//
//            // 1、如果关联单号不为空且UUID为空，设置UUID为编码规则第一条数据的UUID
//            List<RuleSeqEntity> collect = ruleSeqEntities.stream().filter(o -> StringUtils.isNotBlank(o.getRelationNumber()) && StringUtils.isBlank(o.getUuid())).collect(Collectors.toList());
//            if (!CollectionUtils.isEmpty(collect)) {
//                RuleDetailDTO ruleDetailDTO = ruleDetailDTOList.get(0);
//                for (RuleSeqEntity ruleSeqEntity : collect) {
//                    ruleSeqEntity.setUuid(ruleDetailDTO.getUuid());
//                }
//                ruleSeqService.updateBatchById(collect);
//            }
//
//            // 2、关联单号为空 ，分别设置UUID,重复数据 取创建时间最新的seq
//            RuleSeqEntity ruleSeqEntity = ruleSeqEntities.stream().filter(o -> StringUtils.isBlank(o.getRelationNumber())).max(Comparator.comparing(RuleSeqEntity::getCreateTime)).orElse(null);
//            if (ruleSeqEntity == null) {
//                continue;
//            }
//            List<Integer> noUUIDSeqIds = ruleSeqEntities.stream().filter(o -> StringUtils.isBlank(o.getRelationNumber())).map(RuleSeqEntity::getId).collect(Collectors.toList());
//            // 删除历史重复数据
//            ruleSeqService.removeByIds(noUUIDSeqIds);
//            for (RuleDetailDTO ruleDetailDTO : ruleDetailDTOList) {
//                // 组装新增数据
//                RuleSeqEntity build = RuleSeqEntity.builder()
//                        .ruleType(type)
//                        .uuid(ruleDetailDTO.getUuid())
//                        .relationType(ruleDetailDTO.getAutoIncrementConfigureType())
//                        .relationNumber(ruleSeqEntity.getRelationNumber())
//                        .seq(ruleSeqEntity.getSeq())
//                        .createTime(ruleSeqEntity.getCreateTime())
//                        .updateTime(ruleSeqEntity.getUpdateTime())
//                        .build();
//                ruleSeqService.save(build);
//            }
//
//        }
//        List<RuleSeqEntity> seqEntityList = ruleSeqService.lambdaQuery().ne(RuleSeqEntity::getRuleType, Constant.BAR_CODE_NUMBER).isNull(RuleSeqEntity::getUuid).list();
//        if (!CollectionUtils.isEmpty(seqEntityList)) {
//            // 删除没有UUID的脏数据，避免下次重刷
//            List<Integer> ids = seqEntityList.stream().map(RuleSeqEntity::getId).collect(Collectors.toList());
//            ruleSeqService.removeByIds(ids);
//        }
    }

    private void initRuleMap() {
        // 获取所有规则配置
        List<RulePrefixDTO> configs = rulePrefixConfigService.listAll();

        // 特殊处理的方法
        Map<String, Function<RuleSeqExtendDTO, NumberRuleCodeDTO>> specialHandlers = new HashMap<>();
        specialHandlers.put(RulePrefixEnum.CURRENTDATE_TYPE.getCode(), this::getCurrentDateType);
        specialHandlers.put(RulePrefixEnum.AUTO_INCREMENT_TYPE.getCode(), this::getAutoIncrementType);
        specialHandlers.put(RulePrefixEnum.FIXED_TYPE.getCode(), this::getFixType);
        specialHandlers.put(RulePrefixEnum.ARTIFICIAL_TYPE.getCode(), this::getArtificialType);
        specialHandlers.put(RulePrefixEnum.ORDER_DATE.getCode(), this::getOrderDate);
        specialHandlers.put(RulePrefixEnum.REQUIRE_GOODS_DATE.getCode(), this::getRequireGoodsDate);
        specialHandlers.put(RulePrefixEnum.CHECK_TIMES.getCode(), this::getCheckTimes);
        specialHandlers.put(RulePrefixEnum.GEAR_POSITION.getCode(), this::getGearPosition);

        // 初始化ruleMap
        ruleMap = new HashMap<>();

        // 添加特殊处理的方法
        ruleMap.putAll(specialHandlers);

        // 添加通用处理的方法
        for (RulePrefixDTO config : configs) {
            // 如果不是特殊处理的方法,则使用通用处理方法
            if (!specialHandlers.containsKey(config.getCode())) {
                ruleMap.put(config.getCode(), this::getCommonMethod);
            }
        }
    }

    @Override
    public String generateCodeByType(GenerateCodeDTO dto) {
        Integer ruleConfigId = generatorRuleConfigId(dto);
        if (ruleConfigId == null) {
            return null;
        }
        NumberCodeDTO numberCode = getSeqById(RuleSeqDTO.builder()
                .id(ruleConfigId)
                .numberMap(dto.getNumberMap())
                .relatedMap(dto.getRelatedMap())
                .build());
        return numberCode.getCode();
    }

    @Override
    public String generateCodeByTypeAndAdd(GenerateCodeDTO dto) {
        Integer ruleConfigId = generatorRuleConfigId(dto);
        if (ruleConfigId == null) {
            return null;
        }
        NumberCodeDTO numberCode = getSeqById(RuleSeqDTO.builder()
                .id(ruleConfigId)
                .numberMap(dto.getNumberMap())
                .relatedMap(dto.getRelatedMap())
                .build());
        // 自增
        ruleSeqService.updateSeqEntity(null, ruleConfigId);
        return numberCode.getCode();

    }

    private Integer generatorRuleConfigId(GenerateCodeDTO dto) {
        // 取默认的编码规则
        NumberRulesConfigEntity ruleConfig = this.lambdaQuery().eq(NumberRulesConfigEntity::getType, dto.getType())
                .eq(NumberRulesConfigEntity::getIsDefault, true)
                .one();
        if (Objects.isNull(ruleConfig)) {
            return null;
        }
        List<RuleDetailDTO> ruleDetails = JSON.parseArray(ruleConfig.getPrefixDetail(), RuleDetailDTO.class);
        // 必须存在自动生成序号的组成信息
        if (ruleDetails.stream().map(RuleDetailDTO::getCode).noneMatch(code -> RulePrefixEnum.AUTO_INCREMENT_TYPE.getCode().equals(code))) {
            return null;
        }
        return ruleConfig.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public String generateCodeByTypeWithNewTra(GenerateCodeDTO dto) {
        return generateCodeByType(dto);
    }

    @Override
    public List<NumberCodeDTO> batchGenerateRules(BatchGenerateRuleDetailDTO dto) {
        List<NumberCodeDTO> list = new ArrayList<>();
        // 记录第一次调用的所有seq值
        Map<String, Integer> seqMap = null;
        NumberRulesConfigEntity numberRulesConfigEntity = this.getById(dto.getNumberRulesId());
        if (Objects.isNull(numberRulesConfigEntity)) {
            throw new ResponseException(RespCodeEnum.DFS_NUMBER_RULE_NOT_EXIST);
        }
        // 遍历新增编码规则
        for (int i = 0; i < dto.getCount(); i++) {
            NumberCodeDTO seqById = generateRules(numberRulesConfigEntity, dto.getRuleDetailDTOs(), dto.getRelatedMap());
            // 记录第一次调用的序列号
            if (i == 0) {
                seqMap = new HashMap<>(seqById.getSeqMap());
            }
            list.add(seqById);
            // 编码规则有自动生成序号的，seq才加1
            ruleSeqService.updateSeqEntity(dto.getRelatedMap(), numberRulesConfigEntity, false);
        }

        // 如果不需要在调用编码规则的过程中自增序号，则调用完毕后，还原序号
        if (!dto.getIsAutoIncrement() && seqMap != null) {
            // 更新所有自增序号
            ruleSeqService.refreshSeq(dto.getRelatedMap(), dto.getNumberRulesId(), seqMap);
        }
        return list;
    }
}
