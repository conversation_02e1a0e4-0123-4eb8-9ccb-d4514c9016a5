package com.yelink.dfs.service.impl.common.config;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yelink.dfs.mapper.common.config.FormConfigMapper;
import com.yelink.dfs.open.v1.common.dto.FromMenuInsertDTO;
import com.yelink.dfs.open.v1.common.dto.FromMenuUpdateDTO;
import com.yelink.dfs.service.common.DictService;
import com.yelink.dfs.service.common.config.FormConfigService;
import com.yelink.dfs.service.common.config.FormFieldConfigService;
import com.yelink.dfscommon.constant.DictConstant;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.dto.common.config.FormFieldUpdateDTO;
import com.yelink.dfscommon.entity.dfs.DictEntity;
import com.yelink.dfscommon.entity.dfs.FormConfigEntity;
import com.yelink.dfscommon.entity.dfs.FormFieldConfigEntity;
import com.yelink.dfscommon.utils.JacksonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023-04-19 10:48:30
 */
@Slf4j
@Service
public class FormConfigServiceImpl extends ServiceImpl<FormConfigMapper, FormConfigEntity> implements FormConfigService {

    @Autowired
    private FormFieldConfigService formFieldConfigService;
    @Autowired
    private DictService dictService;

    private static String CUSTOM_FIELD_CONSTANT = "customField";
    private static String SYS_FIELD_CONSTANT = "sysField";


    @Override
    public List<FormConfigEntity> getTreeByFullPathCode(String type, Boolean isNameTree) {
        List<FormConfigEntity> list = this.lambdaQuery()
                .eq(FormConfigEntity::getType, type)
                // 为false时，查询该字段为空的所有数据即为命名树
                .isNull(Objects.nonNull(isNameTree) && isNameTree, FormConfigEntity::getIsNameTree)
                .list();
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList();
        }
        // 根据父级配置的全路径编码进行分组
        Map<String, List<FormConfigEntity>> childrenMap = list.stream().collect(Collectors.groupingBy(FormConfigEntity::getParentFullPathCode));
        List<FormConfigEntity> resultList = new ArrayList();
        // 获取系统全局配置, 字段规则不需要展示此信息配置
        if (Objects.nonNull(isNameTree) && isNameTree) {
            FormConfigEntity sysFormGlobalConf = this.lambdaQuery().isNull(FormConfigEntity::getType).one();
            resultList.add(sysFormGlobalConf);
        }
        // 获取最上级节点
        resultList.addAll(childrenMap.get(""));
        for (FormConfigEntity configEntity : resultList) {
            // 设置子节点
            setChildren(configEntity, childrenMap);
        }
        return resultList;
    }

    @Override
    public List<Map<String, String>> getFormFieldDetailList(String fullPathCode, String moduleCode) {
        return formFieldConfigService.getFormFieldList(fullPathCode, moduleCode);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateField(FormFieldUpdateDTO updateDTO) {
        String fullPathCode = updateDTO.getFullPathCode();
        // 先置空所有自定义字段
        formFieldConfigService.lambdaUpdate().eq(FormFieldConfigEntity::getFullPathCode, fullPathCode)
                .eq(FormFieldConfigEntity::getTypeName, CUSTOM_FIELD_CONSTANT)
                .set(FormFieldConfigEntity::getFieldName, "")
                .update();
        // 获取需要重命名的表单字段并进行重命名
        List<FormFieldUpdateDTO.FieldUpdateDTO> updateDTOS = updateDTO.getUpdateFieldList().stream()
                .filter(o -> o.getTypeCode().equals(CUSTOM_FIELD_CONSTANT) && StringUtils.isNotBlank(o.getFieldName()))
                .collect(Collectors.toList());
        for (FormFieldUpdateDTO.FieldUpdateDTO fieldUpdateDTO : updateDTOS) {
            formFieldConfigService.lambdaUpdate().eq(FormFieldConfigEntity::getFullPathCode, fullPathCode)
                    .eq(FormFieldConfigEntity::getFieldCode, fieldUpdateDTO.getFieldCode())
                    .eq(FormFieldConfigEntity::getTypeCode, fieldUpdateDTO.getTypeCode())
                    .eq(FormFieldConfigEntity::getModuleCode, fieldUpdateDTO.getModuleCode())
                    .set(FormFieldConfigEntity::getFieldName, fieldUpdateDTO.getFieldName())
                    .update();
        }
        // 同步更新同单据的同名字段，目的是减少用户操作
        if (Objects.nonNull(updateDTO.getIsSyncSameOrderFieldName()) && updateDTO.getIsSyncSameOrderFieldName()) {
            syncSameOrderFieldName(fullPathCode, updateDTO.getUpdateFieldList());
        }

    }

    /**
     * 同步更新同单据的同名字段，目的是减少用户操作
     */
    @Override
    public void syncSameOrderFieldName(String fullPathCode, List<FormFieldUpdateDTO.FieldUpdateDTO> updateDTOS) {
        if (CollectionUtils.isEmpty(updateDTOS)) {
            return;
        }
        // 查询所有上下级相关联的表单fullPathCode
        List<String> fullPathCodes = this.getAllFullPathCodes(fullPathCode);
        // 重命名
        for (FormFieldUpdateDTO.FieldUpdateDTO fieldUpdateDTO : updateDTOS) {
            String moduleCode = fieldUpdateDTO.getModuleCode();
            // 获取该字段的系统名称，以便同步更新该单据同名的字段
            FormFieldConfigEntity one = formFieldConfigService.lambdaQuery().eq(FormFieldConfigEntity::getFullPathCode, fullPathCode)
                    .eq(FormFieldConfigEntity::getFieldCode, fieldUpdateDTO.getFieldCode())
                    .eq(FormFieldConfigEntity::getTypeCode, SYS_FIELD_CONSTANT)
                    .eq(FormFieldConfigEntity::getModuleCode, moduleCode)
                    .one();

            List<String> otherSameFieldCodes = formFieldConfigService.lambdaQuery()
                    .in(FormFieldConfigEntity::getFullPathCode, fullPathCodes)
                    .eq(FormFieldConfigEntity::getFieldName, one.getFieldName())
                    .eq(FormFieldConfigEntity::getModuleCode, moduleCode)
                    .list().stream()
                    .map(FormFieldConfigEntity::getFieldCode)
                    .distinct()
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(otherSameFieldCodes)) {
                continue;
            }
            formFieldConfigService.lambdaUpdate()
                    .in(FormFieldConfigEntity::getFullPathCode, fullPathCodes)
                    .in(FormFieldConfigEntity::getFieldCode, otherSameFieldCodes)
                    .eq(FormFieldConfigEntity::getTypeCode, CUSTOM_FIELD_CONSTANT)
                    .eq(FormFieldConfigEntity::getModuleCode, moduleCode)
                    .set(FormFieldConfigEntity::getFieldName, fieldUpdateDTO.getFieldName())
                    .update();
        }
    }


    /**
     * 查询所有上下级相关联的表单fullPathCode
     * @param fullPathCode
     * @return
     */
    @Override
    public List<String> getAllFullPathCodes(String fullPathCode) {
        List<String> fullPathCodes = new ArrayList<>();
        FormConfigEntity formConfigEntity = this.lambdaQuery().eq(FormConfigEntity::getFullPathCode, fullPathCode).one();
        // 找到单据最父级对象，将子级所有对象找出进行数据更新
        List<String> parentNodeList = this.lambdaQuery().eq(FormConfigEntity::getParentFullPathCode, "")
                .list().stream().map(FormConfigEntity::getCode).collect(Collectors.toList());
        FormConfigEntity subParentEntity = this.judgeIsExistParent(formConfigEntity, parentNodeList);
        getSubConfigList(subParentEntity.getFullPathCode(), fullPathCodes);
        return fullPathCodes;
    }

    /**
     * 查询所有上下级相关联的表单配置
     * @param fullPathCode
     * @return
     */
    @Override
    public List<FormConfigEntity> getAllFormConfigList(String fullPathCode) {
        List<FormConfigEntity> configList = new ArrayList<>();
        List<String> allFullPathCodes = this.getAllFullPathCodes(fullPathCode);
        if (CollectionUtils.isEmpty(allFullPathCodes)) {
            return configList;
        }
        return this.lambdaQuery().in(FormConfigEntity::getFullPathCode, allFullPathCodes).list();
    }

    private void getSubConfigList(String fullPathCode, List<String> configList) {
        List<String> subList = this.lambdaQuery().select(FormConfigEntity::getFullPathCode)
                .eq(FormConfigEntity::getParentFullPathCode, fullPathCode)
                .list().stream()
                .map(FormConfigEntity::getFullPathCode)
                .collect(Collectors.toList());
        configList.addAll(subList);
        for (String s : subList) {
            getSubConfigList(s, configList);
        }
    }

    /**
     * 找到最父级对象，将子级所有对象找出进行数据更新
     */
    @Override
    public FormConfigEntity judgeIsExistParent(FormConfigEntity formConfigEntity, List<String> parentNodeList) {
        FormConfigEntity parentConfigEntity = this.lambdaQuery()
                .eq(FormConfigEntity::getFullPathCode, formConfigEntity.getParentFullPathCode())
                .one();
        if (Objects.isNull(parentConfigEntity)) {
            return formConfigEntity;
        }
        if (parentNodeList.contains(parentConfigEntity.getParentFullPathCode())) {
            return parentConfigEntity;
        }
        return judgeIsExistParent(parentConfigEntity, parentNodeList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateFieldGlobalConf(List<DictEntity> dictEntities) {
        dictService.updateBatchById(dictEntities);
    }

    @Override
    public List<DictEntity> getFieldGlobalConf() {
        return dictService.lambdaQuery().eq(DictEntity::getType, DictConstant.FORM_GLOBAL_CONF).list();
    }

    @Override
    public List<DictEntity> getFieldList(String type) {
        return dictService.lambdaQuery().eq(DictEntity::getType, DictConstant.FORM_FIELD_TYPE)
                .eq(StringUtils.isNotBlank(type), DictEntity::getValue, type)
                .list();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addMenu(List<FromMenuInsertDTO> insertDTOs) {
        if (CollectionUtils.isEmpty(insertDTOs)) {
            return;
        }
        Date date = new Date();
        List<FormConfigEntity> formConfigEntities = JacksonUtil.convertArray(insertDTOs, FormConfigEntity.class);
        for (FormConfigEntity formConfigEntity : formConfigEntities) {
            formConfigEntity.setCreateTime(date);
            formConfigEntity.setUpdateTime(date);
            formConfigEntity.setCreateBy("admin");
            formConfigEntity.setUpdateBy("admin");
        }
        this.saveBatch(formConfigEntities);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void editMenu(List<FromMenuUpdateDTO> editDTOs) {
        if (CollectionUtils.isEmpty(editDTOs)) {
            return;
        }
        long count = editDTOs.stream().filter(FromMenuUpdateDTO::getIsInner).count();
        if (count > 0) {
            throw new ResponseException(RespCodeEnum.SYSTEM_DATA_NOT_ALLOW_CHANGE);
        }
        Date date = new Date();
        List<FormConfigEntity> formConfigEntities = JacksonUtil.convertArray(editDTOs, FormConfigEntity.class);
        for (FormConfigEntity formConfigEntity : formConfigEntities) {
            formConfigEntity.setUpdateTime(date);
            formConfigEntity.setUpdateBy("admin");
        }
        this.updateBatchById(formConfigEntities);
    }

    /**
     * 设置子节点
     */
    private void setChildren(FormConfigEntity configEntity, Map<String, List<FormConfigEntity>> childrenMap) {
        if (configEntity == null) {
            return;
        }
        List<FormConfigEntity> children = childrenMap.get(configEntity.getFullPathCode());
        configEntity.setChildren(children);
        if (CollectionUtils.isEmpty(children)) {
            return;
        }
        for (FormConfigEntity childrenConfig : children) {
            setChildren(childrenConfig, childrenMap);
        }
    }


}

