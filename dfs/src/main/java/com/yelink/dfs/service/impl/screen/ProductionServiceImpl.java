package com.yelink.dfs.service.impl.screen;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.alarm.AlarmClassifyEnum;
import com.yelink.dfs.constant.alarm.AlarmTypesEnum;
import com.yelink.dfs.constant.target.TargetConst;
import com.yelink.dfs.entity.alarm.AlarmClassifyEntity;
import com.yelink.dfs.entity.alarm.AlarmEntity;
import com.yelink.dfs.entity.barcode.BarCodeEntity;
import com.yelink.dfs.entity.code.ProductFlowCodeRecordEntity;
import com.yelink.dfs.entity.device.DeviceEntity;
import com.yelink.dfs.entity.device.dto.IndicatorEntityDTO;
import com.yelink.dfs.entity.energy.manage.DeviceEnergyConsumptionEntity;
import com.yelink.dfs.entity.energy.manage.DeviceGasConsumptionEntity;
import com.yelink.dfs.entity.manufacture.ProductionLineEntity;
import com.yelink.dfs.entity.model.FacilitiesEntity;
import com.yelink.dfs.entity.model.GridEntity;
import com.yelink.dfs.entity.order.RecordWorkOrderDayCountEntity;
import com.yelink.dfs.entity.order.RecordWorkOrderLineDayCountEntity;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.entity.order.WorkOrderPlanEntity;
import com.yelink.dfs.entity.order.dto.WorkOrderLineDTO;
import com.yelink.dfs.entity.product.MaterialEntity;
import com.yelink.dfs.entity.screen.dto.CapacityDTO;
import com.yelink.dfs.entity.screen.dto.FacilitiesBeatDTO;
import com.yelink.dfs.entity.screen.dto.LineDayDataDTO;
import com.yelink.dfs.entity.screen.dto.ProductionAlarmDTO;
import com.yelink.dfs.entity.screen.dto.ReportGradeDTO;
import com.yelink.dfs.entity.screen.dto.StateOverviewDTO;
import com.yelink.dfs.entity.screen.dto.WorkOrderDTO;
import com.yelink.dfs.entity.screen.dto.WorkOrderListDTO;
import com.yelink.dfs.entity.screen.vo.GridCompletedVO;
import com.yelink.dfs.entity.screen.vo.UnqualifiedVO;
import com.yelink.dfs.entity.target.TargetModelEntity;
import com.yelink.dfs.entity.target.dto.BatchAndTargetValDTO;
import com.yelink.dfs.entity.target.metrics.MetricsWorkOrderDailyEntity;
import com.yelink.dfs.entity.target.record.RecordLineBeatEntity;
import com.yelink.dfs.entity.target.record.RecordLineDayUnionEntity;
import com.yelink.dfs.entity.target.record.RecordWorkOrderStateEntity;
import com.yelink.dfs.entity.target.record.ReportLineEntity;
import com.yelink.dfs.entity.target.record.dto.BeatDTO;
import com.yelink.dfs.entity.target.record.dto.GridBeatDTO;
import com.yelink.dfs.entity.target.record.dto.LineBeatDTO;
import com.yelink.dfs.mapper.order.RecordWorkOrderDayCountMapper;
import com.yelink.dfs.mapper.order.RecordWorkOrderUnqualifiedMapper;
import com.yelink.dfs.mapper.order.WorkOrderPlanMapper;
import com.yelink.dfs.mapper.target.record.RecordLineDayUnionMapper;
import com.yelink.dfs.mapper.target.record.RecordWorkOrderStateMapper;
import com.yelink.dfs.service.alarm.AlarmClassifyService;
import com.yelink.dfs.service.alarm.AlarmService;
import com.yelink.dfs.service.barcode.BarCodeService;
import com.yelink.dfs.service.code.ProductFlowCodeRecordService;
import com.yelink.dfs.service.common.DictService;
import com.yelink.dfs.service.device.DeviceService;
import com.yelink.dfs.service.energy.manage.DeviceCarbonConsumptionService;
import com.yelink.dfs.service.energy.manage.DeviceEnergyConsumptionService;
import com.yelink.dfs.service.energy.manage.DeviceGasConsumptionService;
import com.yelink.dfs.service.impl.common.WorkPropertise;
import com.yelink.dfs.service.manufacture.ProductionLineService;
import com.yelink.dfs.service.model.FacilitiesService;
import com.yelink.dfs.service.model.GridService;
import com.yelink.dfs.service.model.WorkCenterService;
import com.yelink.dfs.service.order.OrderExecuteSeqService;
import com.yelink.dfs.service.order.RecordWorkOrderDayCountService;
import com.yelink.dfs.service.order.RecordWorkOrderLineDayCountService;
import com.yelink.dfs.service.order.WorkOrderProcedureRelationService;
import com.yelink.dfs.service.order.WorkOrderService;
import com.yelink.dfs.service.product.MaterialService;
import com.yelink.dfs.service.screen.ProductionService;
import com.yelink.dfs.service.screen.RecordOutputService;
import com.yelink.dfs.service.target.TargetModelService;
import com.yelink.dfs.service.target.metrics.MetricsWorkOrderDailyService;
import com.yelink.dfs.service.target.record.RecordLineBeatService;
import com.yelink.dfs.service.target.record.RecordWorkOrderStateService;
import com.yelink.dfs.service.target.record.ReportLineService;
import com.yelink.dfscommon.api.assignment.AssignmentInterface;
import com.yelink.dfscommon.api.scr.ScrOutInterface;
import com.yelink.dfscommon.constant.Constants;
import com.yelink.dfscommon.constant.dfs.order.WorkOrderStateEnum;
import com.yelink.dfscommon.dto.YieldRecordVO;
import com.yelink.dfscommon.entity.RecordOutputEntity;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.DateUtil;
import com.yelink.dfscommon.utils.MathUtil;
import com.yelink.dfscommon.utils.NullableDouble;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @description: 生产大屏逻辑
 * @time 2021/7/19 18:58
 */
@Slf4j
@Service
@AllArgsConstructor
public class ProductionServiceImpl implements ProductionService {
    private WorkOrderService workOrderService;
    private WorkOrderPlanMapper workOrderPlanMapper;
    private AlarmClassifyService alarmClassifyService;
    private RecordWorkOrderDayCountService recordWorkOrderDayCountService;
    private RecordWorkOrderDayCountMapper recordWorkOrderDayCountMapper;
    private RecordOutputService recordOutputService;
    private DictService dictService;
    private ProductionLineService lineService;
    private WorkPropertise workPropertise;
    private AlarmService alarmService;
    private RecordWorkOrderStateService recordWorkOrderStateService;
    private FacilitiesService facilitiesService;
    private RecordWorkOrderStateMapper recordWorkOrderStateMapper;
    private RecordLineDayUnionMapper recordLineDayUnionMapper;
    private GridService gridService;
    private RecordLineBeatService beatService;
    private ScrOutInterface scrOutInterface;
    private TargetModelService targetModelService;
    private AssignmentInterface assignmentInterface;
    private RecordWorkOrderUnqualifiedMapper recordWorkOrderUnqualifiedMapper;
    private WorkOrderProcedureRelationService workOrderProcedureRelationService;
    private ProductFlowCodeRecordService productFlowCodeRecordService;
    private OrderExecuteSeqService orderExecuteSeqService;
    private MaterialService materialService;
    private ReportLineService reportLineService;
    private BarCodeService barCodeService;
    private DeviceService deviceService;
    private DeviceEnergyConsumptionService deviceEnergyConsumptionService;
    private DeviceGasConsumptionService deviceGasConsumptionService;
    private WorkCenterService workCenterService;
    private RecordWorkOrderLineDayCountService recordWorkOrderLineDayCountService;
    private DeviceCarbonConsumptionService deviceCarbonConsumptionService;
    private MetricsWorkOrderDailyService metricsWorkOrderDailyService;

    /**
     * Double保留小数位
     */
    private final static Integer SCALE = 3;

    /**
     * 获取生产大屏工单信息
     *
     * @return
     * <AUTHOR>
     */
    @Override
    public StateOverviewDTO workOrderInformation(String lineIds, Date dateIn) {
        //如果第一天时间小于首班最早时间，则把头一天也计算在内
        Date dateNow = Objects.nonNull(dateIn) ? dateIn : new Date();
        //今天的开始和结束时间
        Date startDate = dictService.getDayOutputBeginTime(dateNow);
        //记录时间
        Date date = DateUtil.formatToDate(startDate, DateUtil.DATETIME_FORMAT_ZERO);
        //获取正在生产的工单
        List<WorkOrderEntity> workOrderEntityList = this.getWorkOrderInPlan(lineIds, dateNow);
        //过滤今天之前挂起的工单
        filterHangUpBeforeToday(workOrderEntityList, dateNow);
        // 工单已完成数量、工单总数量
        int orderTotal = workOrderEntityList.size();
        long orderCompleted = workOrderEntityList.stream()
                .filter(res -> WorkOrderStateEnum.FINISHED.getCode().equals(res.getState()))
                .count();

        List<String> lineIdList = new ArrayList<>();
        if (StringUtils.isNotBlank(lineIds)) {
            lineIdList = Arrays.asList(lineIds.split(Constant.SEP));
        }
        //今日计划生产数量
        Integer planQuantityTotal = 0;
        if (StringUtils.isNotBlank(lineIds)) {
            for (String lineId : lineIdList) {
                planQuantityTotal += getPlanQuantityTotal(Integer.valueOf(lineId));
            }
        } else {
            planQuantityTotal = getPlanQuantityTotal(null);
        }

        //今日已生产数量（合格数）、总合格率
        List<RecordWorkOrderLineDayCountEntity> list = recordWorkOrderLineDayCountService.lambdaQuery()
                .eq(RecordWorkOrderLineDayCountEntity::getTime, date)
                .in(!CollectionUtils.isEmpty(lineIdList), RecordWorkOrderLineDayCountEntity::getLineId, lineIdList)
                .list();
        double todayUnqualified = list.stream().mapToDouble(RecordWorkOrderLineDayCountEntity::getUnqualified).sum();
        double todayComplete = list.stream().mapToDouble(RecordWorkOrderLineDayCountEntity::getCount).sum();
        Double total = todayUnqualified + todayComplete;
        Double qualifiedRate = total.equals(0.0) ? 1.0 : MathUtil.divideDouble(todayComplete, total, SCALE);

        //合格率
        CapacityDTO yieldDTO = CapacityDTO.builder()
                .completion((int) todayComplete)
                .gross((int) todayComplete)
                .yieldRate(qualifiedRate)
                .build();

        //工单达成率临时实体
        CapacityDTO orderAchieveDTO = CapacityDTO.builder()
                .gross(orderTotal)
                .completion((int) orderCompleted)
                .yieldRate(orderTotal == 0 ? 1.0 : MathUtil.divideDouble(orderCompleted, orderTotal, SCALE))
                .build();
        //当日达成临时实体
        CapacityDTO dayReachDTO = CapacityDTO.builder()
                .gross(planQuantityTotal)
                .completion((int) todayComplete)
                .yieldRate(planQuantityTotal == 0 ? 1.0 : MathUtil.divideDouble(todayComplete, planQuantityTotal, SCALE))
                .build();

        //状态总览
        return StateOverviewDTO.builder()
                .yieldDTO(yieldDTO)
                .dayReachDTO(dayReachDTO)
                .orderAchieveDTO(orderAchieveDTO)
                .build();
    }

    /**
     * 获取车间生产大屏工单信息
     *
     * @param gridId
     * @param dateStr
     * @return
     */
    @Override
    public StateOverviewDTO gridWorkOrderInformation(Integer gridId, String dateStr) {
        Date dateNow;
        if (StringUtils.isBlank(dateStr)) {
            dateNow = new Date();
        } else {
            dateNow = DateUtil.parse(dateStr, DateUtil.DATETIME_FORMAT);
        }
        //当天的开始和结束时间
        Date startDate = dictService.getDayOutputBeginTime(dateNow);
        //记录时间
        Date date = DateUtil.formatToDate(startDate, DateUtil.DATETIME_FORMAT_ZERO);
        //获取所有产线
        List<Integer> lineIds = getLineIdsByGridId(gridId);
        if (CollectionUtils.isEmpty(lineIds)) {
            return StateOverviewDTO.builder().build();
        }
        //获取正在生产的工单
        List<WorkOrderEntity> workOrderEntityList = this.getWorkOrderInPlanByGrid(lineIds, startDate);
        //过滤今天之前挂起的工单
        filterHangUpBeforeToday(workOrderEntityList, dateNow);
        // 工单总数量
        int orderTotal = workOrderEntityList.size();
        // 工单已完成数量
        int orderCompleted = (int) workOrderEntityList.stream().map(WorkOrderEntity::getState)
                .filter(o -> WorkOrderStateEnum.FINISHED.getCode().equals(o)).count();
        //今日已生产数量（合格数）
        List<RecordWorkOrderLineDayCountEntity> countEntities = recordWorkOrderLineDayCountService.lambdaQuery()
                .select(RecordWorkOrderLineDayCountEntity::getCount, RecordWorkOrderLineDayCountEntity::getUnqualified)
                .in(RecordWorkOrderLineDayCountEntity::getLineId, lineIds)
                .eq(RecordWorkOrderLineDayCountEntity::getTime, date)
                .list();
        double finish = countEntities.stream().filter(o -> o.getCount() != null).mapToDouble(RecordWorkOrderLineDayCountEntity::getCount).sum();
        Integer completeTotal = (int) finish;
        double unqualified = countEntities.stream().filter(o -> o.getUnqualified() != null).mapToDouble(RecordWorkOrderLineDayCountEntity::getUnqualified).sum();
        //总合格率
        Double total = unqualified + finish;
        Double qualifiedRate = total.equals(0.0) ? 1.0 : MathUtil.divideDouble(finish, total, SCALE, RoundingMode.HALF_UP);
        //今日计划生产数量
        Integer planQuantity = 0;
        for (Integer lineId : lineIds) {
            Integer planQuantityTotal = getPlanQuantityTotal(lineId);
            planQuantity += planQuantityTotal;
        }
        //合格率
        CapacityDTO yieldDTO = CapacityDTO.builder()
                .completion(completeTotal)
                .gross(completeTotal)
                .yieldRate(qualifiedRate)
                .build();
        //工单达成率临时实体
        CapacityDTO orderAchieveDTO = CapacityDTO.builder()
                .gross(orderTotal)
                .completion(orderCompleted)
                .yieldRate(orderTotal == 0 ? 1.0 : MathUtil.divideDouble(orderCompleted, orderTotal, SCALE, RoundingMode.HALF_UP))
                .build();
        //当日达成临时实体
        CapacityDTO dayReachDTO = CapacityDTO.builder()
                .gross(planQuantity)
                .completion(completeTotal)
                .yieldRate(planQuantity == 0 ? 1.0 : MathUtil.divideDouble(completeTotal, planQuantity, SCALE, RoundingMode.HALF_UP))
                .build();
        //状态总览
        return StateOverviewDTO.builder()
                .yieldDTO(yieldDTO)
                .dayReachDTO(dayReachDTO)
                .orderAchieveDTO(orderAchieveDTO)
                .build();
    }

    /**
     * 获取车间当天正在生产的工单
     *
     * @param lineIds
     * @param date
     * @return
     */
    private List<WorkOrderEntity> getWorkOrderInPlanByGrid(List<Integer> lineIds, Date date) {
        if (CollectionUtils.isEmpty(lineIds)) {
            return new ArrayList<>();
        }
        //今天结束时间
        Date tomorrowDate = DateUtil.addDate(date, 1);
        //找到所有预计今天生产的绑定了产线的工单
        LambdaQueryWrapper<WorkOrderEntity> workOrderWrapper = new LambdaQueryWrapper<>();
        workOrderWrapper
                .in(WorkOrderEntity::getLineId, lineIds)
                .notIn(WorkOrderEntity::getState, WorkOrderStateEnum.CREATED.getCode(),
                        WorkOrderStateEnum.CLOSED.getCode(), WorkOrderStateEnum.CANCELED.getCode())
                //实际结束时间等于空或者实际结束时间大于今天的
                .and(a -> a.isNull(WorkOrderEntity::getActualEndDate).or(b -> b.gt(WorkOrderEntity::getActualEndDate, date)))
                //计划完成时间在今天的
                .and(v -> v.between(WorkOrderEntity::getEndDate, date, tomorrowDate)
                        //或者实际完成时间在今日的
                        .or(e -> e.between(WorkOrderEntity::getActualEndDate, date, tomorrowDate))
                        //计划时间在今天之前但却没有完成的
                        .or(o -> o.lt(WorkOrderEntity::getEndDate, date).isNull(WorkOrderEntity::getActualEndDate)));
        return workOrderService.list(workOrderWrapper);
    }


    /**
     * 获取工单当天计划产能
     *
     * @param workOrderNumber
     * @param date
     * @return
     */
    @Override
    public Integer getPlanQuantity(String workOrderNumber, Date date) {
        if (date == null) {
            date = new Date();
        }
        //当天计划
        LambdaQueryWrapper<WorkOrderPlanEntity> workOrderPlanWrapper = new LambdaQueryWrapper<>();
        workOrderPlanWrapper.eq(WorkOrderPlanEntity::getWorkOrderNumber, workOrderNumber);
        workOrderPlanWrapper.eq(WorkOrderPlanEntity::getTime, date);
        List<WorkOrderPlanEntity> workOrderPlanEntityList = workOrderPlanMapper.selectList(workOrderPlanWrapper);
        if (CollectionUtils.isEmpty(workOrderPlanEntityList)) {
            //如果找不到计划的，就去找他今天开始时还剩下的计划数量
            LambdaQueryWrapper<RecordWorkOrderDayCountEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(RecordWorkOrderDayCountEntity::getWorkOrderNumber, workOrderNumber);
            wrapper.eq(RecordWorkOrderDayCountEntity::getTime, date);
            List<RecordWorkOrderDayCountEntity> entityList = recordWorkOrderDayCountService.list(wrapper);
            //筛除掉今天的
            Date finalDate = date;
            List<RecordWorkOrderDayCountEntity> list = entityList.stream().filter(o -> o.getTime().compareTo(finalDate) == 0).collect(Collectors.toList());
            double qualified = list.stream().filter(o -> o.getCount() != null).mapToDouble(RecordWorkOrderDayCountEntity::getCount).sum();
            double unqualified = list.stream().filter(o -> o.getUnqualified() != null).mapToDouble(RecordWorkOrderDayCountEntity::getUnqualified).sum();
            //拿到工单计划数量
            WorkOrderEntity workOrderEntity = workOrderService.getSimpleWorkOrderByNumber(workOrderNumber);
            Double result = workOrderEntity.getPlanQuantity() - qualified - unqualified;

            return result.intValue();
        }
        Double value = workOrderPlanEntityList.stream().filter(o -> o.getPlanQuantity() != null).mapToDouble(WorkOrderPlanEntity::getPlanQuantity).sum();
        return value.intValue();
    }


    /**
     * 获取工单当天计划产能
     * 以产线维度获取该产线的当天计划产能
     *
     * @return
     */
    @Override
    public Integer getPlanQuantityTotal(Integer lineId) {
        Date now = new Date();
        Date recordDate = dictService.getRecordDate(now);
        //当天有计划的计划产量
        LambdaQueryWrapper<WorkOrderPlanEntity> workOrderPlanWrapper = new LambdaQueryWrapper<>();
        workOrderPlanWrapper.eq(WorkOrderPlanEntity::getTime, recordDate);
        List<WorkOrderPlanEntity> workOrderPlanEntityList = workOrderPlanMapper.selectList(workOrderPlanWrapper);
        //查询工单，过滤出独立工单和父工单的最后一个子工单（父工单按最后一个子工单计划量计算）
        String workOrderNumbers = workOrderPlanEntityList.stream().map(WorkOrderPlanEntity::getWorkOrderNumber).collect(Collectors.joining(","));
        List<WorkOrderEntity> workOrderEntities = workOrderService.selectByNumbers(workOrderNumbers);
        List<WorkOrderEntity> workOrderList;
        if (lineId != null) {
            workOrderList = workOrderEntities.stream().filter(o -> lineId.equals(o.getLineId())).collect(Collectors.toList());
        } else {
            workOrderList = workOrderEntities;
        }
        //过滤今天之前挂起的工单
        filterHangUpBeforeToday(workOrderList, now);
        List<String> workOrder = workOrderList.stream().map(WorkOrderEntity::getWorkOrderNumber).collect(Collectors.toList());

        double value = 0.0;
        for (WorkOrderPlanEntity workOrderPlanEntity : workOrderPlanEntityList) {
            if (workOrder.contains(workOrderPlanEntity.getWorkOrderNumber())) {
                Double planQuantity = workOrderPlanEntity.getPlanQuantity();
                value += planQuantity == null ? 0 : planQuantity;
                log.debug("工单号：{},数量：{}", workOrderPlanEntity.getWorkOrderNumber(), planQuantity);
            }
        }
        log.debug("今天计划数：" + (int) value);

        //计划开始时间大于今天，且今天有产量的工单，将这类型的工单所有计划产量进行累加
        Date dayOutputBeginTime = dictService.getDayOutputBeginTime(now);
        LambdaQueryWrapper<WorkOrderEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.gt(WorkOrderEntity::getStartDate, DateUtil.addDate(dayOutputBeginTime, 1))
                .isNotNull(WorkOrderEntity::getLineId);
        List<WorkOrderEntity> list = workOrderService.list(wrapper);
        if (lineId != null) {
            list = list.stream().filter(o -> o.getLineId().equals(lineId)).collect(Collectors.toList());
        }

        Double advance = 0.0;
        if (!CollectionUtils.isEmpty(list)) {
            for (WorkOrderEntity entity : list) {
                LambdaQueryWrapper<RecordWorkOrderDayCountEntity> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(RecordWorkOrderDayCountEntity::getWorkOrderNumber, entity.getWorkOrderNumber())
                        .eq(RecordWorkOrderDayCountEntity::getTime, recordDate);
                if (recordWorkOrderDayCountService.count(queryWrapper) > 0) {
                    advance += getQuantity(entity);
                    log.debug("工单号：" + entity.getWorkOrderNumber());
                }
            }
        }
        log.debug("提前计划数：" + advance.intValue());

        Date tomorrowBeginTime = DateUtil.addDate(dayOutputBeginTime, 1);
        //计划完成时间已过，还没结束或者今天结束的工单
        LambdaQueryWrapper<WorkOrderEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.lt(WorkOrderEntity::getEndDate, dayOutputBeginTime)
                .in(WorkOrderEntity::getState, WorkOrderStateEnum.RELEASED.getCode(), WorkOrderStateEnum.INVESTMENT.getCode(), WorkOrderStateEnum.HANG_UP.getCode())
                .isNotNull(WorkOrderEntity::getLineId)
                .and(
                        a -> a.isNull(WorkOrderEntity::getActualEndDate)
                                .or(b -> b.between(WorkOrderEntity::getActualEndDate, dayOutputBeginTime, tomorrowBeginTime))
                )
        ;
        List<WorkOrderEntity> list2 = workOrderService.list(queryWrapper);
        if (lineId != null) {
            list2 = list2.stream().filter(o -> o.getLineId().equals(lineId)).collect(Collectors.toList());
        }
        //过滤今天之前挂起的工单
        filterHangUpBeforeToday(list2, now);
        double delay = 0.0;
        if (!CollectionUtils.isEmpty(list2)) {
            for (WorkOrderEntity entity : list2) {
                //今天没结束，总数-今天之前完成的
                Double aDouble = recordWorkOrderDayCountMapper.totalCountBeforeToday(entity.getWorkOrderNumber(), recordDate);
                Double quantity = getQuantity(entity);
                double add = (quantity - (aDouble == null ? 0 : aDouble));
                delay += add > 0 ? add : 0;
                log.debug("工单号：" + entity.getWorkOrderNumber() + "计划量:" + quantity + ",完成量：" + aDouble);
            }
        }
        log.debug("延迟完成计划数：" + (int) delay);

        return (int) value + advance.intValue() + (int) delay;
    }

    /**
     * 过滤今天之前挂起的工单
     *
     * @param workOrderList
     */
    @Override
    public void filterHangUpBeforeToday(List<WorkOrderEntity> workOrderList, Date dateIn) {
        Date dayOutputBeginTime = dictService.getDayOutputBeginTime(dateIn);

        Iterator<WorkOrderEntity> iterator = workOrderList.iterator();

        while (iterator.hasNext()) {
            WorkOrderEntity entity = iterator.next();
            if (entity.getState().equals(WorkOrderStateEnum.HANG_UP.getCode())) {
                //昨天最后的状态是否为挂起
                LambdaQueryWrapper<RecordWorkOrderStateEntity> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(RecordWorkOrderStateEntity::getWorkOrder, entity.getWorkOrderNumber())
                        .le(RecordWorkOrderStateEntity::getTime, dayOutputBeginTime)
                        .orderByDesc(RecordWorkOrderStateEntity::getTime)
                        .orderByDesc(RecordWorkOrderStateEntity::getId)
                        .last("limit 1");

                RecordWorkOrderStateEntity yesterdayLastOne = recordWorkOrderStateMapper.selectOne(wrapper);
                if (yesterdayLastOne == null || !yesterdayLastOne.getState().equals(WorkOrderStateEnum.HANG_UP.getCode())) {
                    continue;
                }
                //查询今天是否有投产
                LambdaQueryWrapper<RecordWorkOrderStateEntity> qw = new LambdaQueryWrapper<>();
                qw.eq(RecordWorkOrderStateEntity::getWorkOrder, entity.getWorkOrderNumber())
                        .ge(RecordWorkOrderStateEntity::getTime, dayOutputBeginTime)
                        .eq(RecordWorkOrderStateEntity::getState, WorkOrderStateEnum.INVESTMENT.getCode());

                if (recordWorkOrderStateMapper.selectCount(qw) == 0) {
                    //今天没投产，则需要过滤
                    iterator.remove();
                }
            }
        }
    }


    private Double getQuantity(WorkOrderEntity workOrderEntity) {
        Double packageQuantity = workOrderEntity.getPackageQuantity();
        Double planQuantity = workOrderEntity.getPlanQuantity();
        return packageQuantity != null ? packageQuantity : planQuantity;
    }

//
//    /**
//     * 获取正在生产的工单-预计时间有今天或者实际时间有今天的
//     *
//     * @return
//     * <AUTHOR>
//     */
//    @Override
//    public List<WorkOrderEntity> getWorkOrderInWork(List<Integer> lineIds, Date dateIn) {
//        Date dateTemp = new Date();
//        if (dateIn != null) {
//            dateTemp = dateIn;
//        }
//        //今天开始时间
//        Date tomorrowDateTemp = DateUtil.addDate(dateTemp, 1);
//        Date tomorrowDate = dictService.getDayOutputBeginTime(tomorrowDateTemp);
//        Date date = dictService.getDayOutputBeginTime(dateTemp);
//        //找到所有预计今天生产的父工单
//        LambdaQueryWrapper<WorkOrderEntity> workOrderWrapper = new LambdaQueryWrapper<>();
//        workOrderWrapper.in(!CollectionUtils.isEmpty(lineIds), WorkOrderEntity::getLineId, lineIds);
//        workOrderWrapper.notIn(WorkOrderEntity::getState, WorkOrderStateEnum.CREATED.getCode(), WorkOrderStateEnum.CLOSED.getCode(), WorkOrderStateEnum.CANCELED.getCode())
//                //实际结束时间不等于空或者实际结束时间大于今天的
//                .and(
//                        a -> a.isNull(WorkOrderEntity::getActualEndDate)
//                                .or(b -> b.ge(WorkOrderEntity::getActualEndDate, date))
//                )
//                .and(e -> e.lt(WorkOrderEntity::getStartDate, tomorrowDate)
//                        .ge(WorkOrderEntity::getEndDate, date)
//                        .or(o -> o.isNotNull(WorkOrderEntity::getActualStartDate).isNull(WorkOrderEntity::getActualEndDate))
//                        .or(v -> v.isNotNull(WorkOrderEntity::getActualStartDate).between(WorkOrderEntity::getActualEndDate, date, tomorrowDate))
//                        //计划时间在今天之前但却没有完成的
//                        .or(o -> o.lt(WorkOrderEntity::getEndDate, date).isNull(WorkOrderEntity::getActualEndDate))
//                );
//        List<WorkOrderEntity> workOrderEntityList = workOrderService.list(workOrderWrapper);
//        if (CollectionUtils.isEmpty(workOrderEntityList)) {
//            return new ArrayList<>();
//        }
//        // 需求:相同工序放在一起展示
//        if (!CollectionUtils.isEmpty(lineIds) && workCenterService.isOperationByLineId(lineIds.get(0))) {
//            // 获取关联的工序
//            for (WorkOrderEntity workOrderEntity : workOrderEntityList) {
//                List<String> craftProcedureNames = workOrderProcedureRelationService.getCraftProcedureNamesByWorkOrderNumber(workOrderEntity.getWorkOrderNumber());
//                if (!CollectionUtils.isEmpty(craftProcedureNames)) {
//                    workOrderEntity.setProcedureName(craftProcedureNames.toString());
//                }
//            }
//            // 根据工序名称、创建时间排序
//            workOrderEntityList.sort(Comparator.nullsFirst(Comparator.comparing(WorkOrderEntity::getProcedureName, Comparator.nullsFirst(String::compareTo)).thenComparing(WorkOrderEntity::getStartDate)));
//        } else {
//            // 根据产线实例ID、创建时间排序
//            workOrderEntityList.sort(Comparator.nullsFirst(Comparator.comparing(WorkOrderEntity::getLineId, Comparator.nullsFirst(Integer::compareTo)).thenComparing(WorkOrderEntity::getStartDate)));
//        }
//        return workOrderEntityList;
//    }

    /**
     * 获取正在生产的工单-预计时间有今天或者实际时间有今天的
     *
     * @return
     * <AUTHOR>
     */
    @Override
    public Page<WorkOrderEntity> getWorkOrderInWork(List<Integer> lineIds, Date dateIn, Integer current, Integer size) {
        current = current == null ? 1 : current;
        size = size == null ? Integer.MAX_VALUE : size;
        LambdaQueryWrapper<WorkOrderEntity> workOrderWrapper = getQueryWrapper(lineIds, dateIn);
        Page<WorkOrderEntity> page = workOrderService.page(new Page<>(current, size), workOrderWrapper);
//        List<WorkOrderEntity> workOrderEntityList = workOrderService.list(workOrderWrapper);
        List<WorkOrderEntity> workOrderEntities = page.getRecords();
        if (CollectionUtils.isEmpty(workOrderEntities)) {
            return new Page<>();
        }
        // 获取关联的工序名称
        Map<String, String> procedureNameMap = workOrderProcedureRelationService.getProcedureNameMap(workOrderEntities);

        for (WorkOrderEntity workOrderEntity : workOrderEntities) {
//            List<String> craftProcedureNames = workOrderProcedureRelationService.getCraftProcedureNamesByWorkOrderNumber(workOrderEntity.getWorkOrderNumber());
//            if (!CollectionUtils.isEmpty(craftProcedureNames)) {
            workOrderEntity.setProcedureName(procedureNameMap.get(workOrderEntity.getWorkOrderNumber()));
//            }
        }
        return page;
    }

    /**
     * 获取大屏定制化查询条件
     */
    @Override
    public LambdaQueryWrapper<WorkOrderEntity> getQueryWrapper(List<Integer> lineIds, Date dateIn) {
        Date dateTemp = new Date();
        if (dateIn != null) {
            dateTemp = dateIn;
        }
        //今天开始时间
        Date tomorrowDateTemp = DateUtil.addDate(dateTemp, 1);
        Date tomorrowDate = dictService.getDayOutputBeginTime(tomorrowDateTemp);
        Date date = dictService.getDayOutputBeginTime(dateTemp);
        //找到所有预计今天生产的父工单
        LambdaQueryWrapper<WorkOrderEntity> workOrderWrapper = new LambdaQueryWrapper<>();
        workOrderWrapper.in(!CollectionUtils.isEmpty(lineIds), WorkOrderEntity::getLineId, lineIds);
        // 查询状态不为创建、关闭、取消的工单
        workOrderWrapper.notIn(WorkOrderEntity::getState, WorkOrderStateEnum.CREATED.getCode(), WorkOrderStateEnum.CLOSED.getCode(), WorkOrderStateEnum.CANCELED.getCode())
                //实际结束时间等于空或者实际结束时间大于今天的
                .and(
                        a -> a.isNull(WorkOrderEntity::getActualEndDate)
                                .or(b -> b.ge(WorkOrderEntity::getActualEndDate, date))
                )
                .and(e -> e.lt(WorkOrderEntity::getStartDate, tomorrowDate)
                        .ge(WorkOrderEntity::getEndDate, date)
                        .or(o -> o.isNotNull(WorkOrderEntity::getActualStartDate).isNull(WorkOrderEntity::getActualEndDate))
                        .or(v -> v.isNotNull(WorkOrderEntity::getActualStartDate).between(WorkOrderEntity::getActualEndDate, date, tomorrowDate))
                        //计划时间在今天之前但却没有完成的
                        .or(o -> o.lt(WorkOrderEntity::getEndDate, date).isNull(WorkOrderEntity::getActualEndDate))
                );
        // 需求:相同工序放在一起展示
        if (!CollectionUtils.isEmpty(lineIds) && workCenterService.isOperationByLineId(lineIds.get(0))) {
            // 根据工序名称、创建时间排序
            workOrderWrapper.orderByAsc(WorkOrderEntity::getProcedureName)
                    .orderByAsc(WorkOrderEntity::getStartDate)
                    .orderByAsc(WorkOrderEntity::getWorkOrderId);
//            workOrderEntityList.sort(Comparator.nullsFirst(Comparator.comparing(WorkOrderEntity::getProcedureName, Comparator.nullsFirst(String::compareTo)).thenComparing(WorkOrderEntity::getStartDate)));
        } else {
            // 根据产线实例ID、创建时间排序
            workOrderWrapper.orderByAsc(WorkOrderEntity::getLineId)
                    .orderByAsc(WorkOrderEntity::getStartDate)
                    .orderByAsc(WorkOrderEntity::getWorkOrderId);
//            workOrderEntityList.sort(Comparator.nullsFirst(Comparator.comparing(WorkOrderEntity::getLineId, Comparator.nullsFirst(Integer::compareTo)).thenComparing(WorkOrderEntity::getStartDate)));
        }
        return workOrderWrapper;
    }


    /**
     * 获取今天预计完成的工单列表
     *
     * @return
     * <AUTHOR>
     */
    @Override
    public List<WorkOrderEntity> getWorkOrderInPlan(String lineIds, Date dateIn) {
        //当前时间
        Date dateTemp = Objects.nonNull(dateIn) ? dateIn : new Date();
        //今天开始时间
        Date date = dictService.getDayOutputBeginTime(dateTemp);
        Date tomorrowDateTemp = DateUtil.addDate(dateTemp, 1);
        //今天结束时间
        Date tomorrowDate = dictService.getDayOutputBeginTime(tomorrowDateTemp);
        //找到所有预计今天生产的绑定了产线的工单
        LambdaQueryWrapper<WorkOrderEntity> workOrderWrapper = new LambdaQueryWrapper<>();
        //如果传LineId 则是产线大屏需要的数据
        if (StringUtils.isNotBlank(lineIds)) {
            workOrderWrapper.in(WorkOrderEntity::getLineId, Arrays.asList(lineIds.split(Constant.SEP)));
        }
        workOrderWrapper
                .notIn(WorkOrderEntity::getState, WorkOrderStateEnum.CREATED.getCode(), WorkOrderStateEnum.CLOSED.getCode(), WorkOrderStateEnum.CANCELED.getCode())
                //实际结束时间等于空或者实际结束时间大于今天的
                .and(
                        a -> a.isNull(WorkOrderEntity::getActualEndDate)
                                .or(b -> b.gt(WorkOrderEntity::getActualEndDate, date))
                )
                .and(
                        //计划完成时间在今天的
                        v -> v.ge(WorkOrderEntity::getEndDate, date).lt(WorkOrderEntity::getEndDate, tomorrowDate)
                                //或者实际完成时间在今日的
                                .or(e -> e.between(WorkOrderEntity::getActualEndDate, date, tomorrowDate))
                                //计划时间在今天之前但却没有完成的
                                .or(o -> o.lt(WorkOrderEntity::getEndDate, date).isNull(WorkOrderEntity::getActualEndDate))
                );
        List<WorkOrderEntity> workOrderEntityList = workOrderService.list(workOrderWrapper);
        if (CollectionUtils.isEmpty(workOrderEntityList)) {
            return new ArrayList<>();
        }
        // 筛除掉父工单的数据
        List<WorkOrderEntity> newList = new ArrayList<>();
        for (WorkOrderEntity workOrderEntity : workOrderEntityList) {
            if (workOrderEntity.getLineId() != null) {
                newList.add(workOrderEntity);
                continue;
            }
            List<String> procedureNames = workOrderProcedureRelationService.getCraftProcedureNamesByWorkOrderNumber(workOrderEntity.getWorkOrderNumber());
            if (!CollectionUtils.isEmpty(procedureNames)) {
                newList.add(workOrderEntity);
            }
        }
        return newList;
    }


    /**
     * 获取正在生产的工单状态列表
     *
     * @return
     */
    @Override
    public WorkOrderListDTO listWorkOrder(String gridIds, Integer current, Integer size) {
        Date time = new Date();
        //指定车间
        List<Integer> lineIds = null;
        if (StringUtils.isNotBlank(gridIds)) {
            Set<Integer> gridIdSet = Arrays.stream(gridIds.split(Constant.SEP)).map(Integer::valueOf).collect(Collectors.toSet());
            List<ProductionLineEntity> lineEntities = lineService.lambdaQuery().select(ProductionLineEntity::getProductionLineId)
                    .in(ProductionLineEntity::getGid, gridIdSet).list();
            lineIds = lineEntities.stream().map(ProductionLineEntity::getProductionLineId).collect(Collectors.toList());
        }
        //获取正在生产的工单
        Page<WorkOrderEntity> page = this.getWorkOrderInWork(lineIds, time, current, size);
        List<WorkOrderEntity> workOrderEntityList = page.getRecords();
        //过滤今天之前挂起的工单
        filterHangUpBeforeToday(workOrderEntityList, time);
        if (CollectionUtils.isEmpty(workOrderEntityList)) {
            return null;
        }

        // 生产中数量（投产）、已完成数量（完成）、待生产数量（生效、挂起）
        LambdaQueryWrapper<WorkOrderEntity> investWrapper = getQueryWrapper(lineIds, time);
        investWrapper.eq(WorkOrderEntity::getState, WorkOrderStateEnum.INVESTMENT.getCode());
        long inProduction = workOrderService.count(investWrapper);

        LambdaQueryWrapper<WorkOrderEntity> finishWrapper = getQueryWrapper(lineIds, time);
        finishWrapper.eq(WorkOrderEntity::getState, WorkOrderStateEnum.FINISHED.getCode());
        long completed = workOrderService.count(finishWrapper);

        LambdaQueryWrapper<WorkOrderEntity> toBeProcedureWrapper = getQueryWrapper(lineIds, time);
        toBeProcedureWrapper.in(WorkOrderEntity::getState, WorkOrderStateEnum.RELEASED.getCode(), WorkOrderStateEnum.HANG_UP.getCode());
        long toBeProduced = workOrderService.count(toBeProcedureWrapper);

        List<WorkOrderDTO> resultList = new ArrayList<>();
        //拿到所有工单集合，里面包括父工单和子工单集合
        for (WorkOrderEntity entity : workOrderEntityList) {
            WorkOrderDTO workOrderDTO = new WorkOrderDTO();
            entity.setStateName(WorkOrderStateEnum.getNameByCode(entity.getState()));
            entity.setActualEndDate(entity.getActualEndDate() == null ? time : entity.getActualEndDate());
            entity.setPlanQuantity(entity.getPlanQuantity());
            recordWorkOrderStateService.setProduceRange(entity);
//            //统计状态数量
//            if (WorkOrderStateEnum.INVESTMENT.getCode().equals(entity.getState())) {
//                inProduction++;
//            } else if (WorkOrderStateEnum.FINISHED.getCode().equals(entity.getState())) {
//                completed++;
//            } else {
//                toBeProduced++;
//            }
            workOrderDTO.setWorkOrderEntity(convertWoreOrderEntity(entity));
            resultList.add(workOrderDTO);
        }
        return WorkOrderListDTO.builder()
                .workOrderListDTO(resultList)
                .completed(completed)
                .inProduction(inProduction)
                .toBeProduced(toBeProduced)
                .current(page.getCurrent())
                .size(page.getSize())
                .total(page.getTotal())
                .build();
    }

    /**
     * 获取历史(昨天之前)挂起工单数量
     *
     * @param lineId
     * @param date
     * @return
     */
    @Override
    public Integer handUpWorkOrderCount(Integer lineId, Date date) {
        Date time = new Date();
        if (date != null) {
            time = date;
        }
        Date beginTime = dictService.getDayOutputBeginTime(time);
        //获取正在生产的工单
        List<Integer> lineIds = Stream.of(lineId).collect(Collectors.toList());
//        Page<WorkOrderEntity> page = this.getWorkOrderInWork(lineIds, beginTime, null, null);
        //过滤挂起的工单
//        List<WorkOrderEntity> entities = page.getRecords().stream().filter(o -> WorkOrderStateEnum.HANG_UP.getCode().equals(o.getState())).collect(Collectors.toList());
        // 获取挂起的工单
        LambdaQueryWrapper<WorkOrderEntity> workOrderWrapper = getQueryWrapper(lineIds, time);
        workOrderWrapper.eq(WorkOrderEntity::getState, WorkOrderStateEnum.HANG_UP.getCode());
        workOrderWrapper.select(WorkOrderEntity::getWorkOrderNumber);
        List<WorkOrderEntity> entities = workOrderService.list(workOrderWrapper);

        //查询今天是否有投产或挂起
        int count = 0;
        for (WorkOrderEntity entity : entities) {
            LambdaQueryWrapper<RecordWorkOrderStateEntity> qw = new LambdaQueryWrapper<>();
            qw.eq(RecordWorkOrderStateEntity::getWorkOrder, entity.getWorkOrderNumber())
                    .ge(RecordWorkOrderStateEntity::getTime, beginTime)
                    .in(RecordWorkOrderStateEntity::getState, WorkOrderStateEnum.INVESTMENT.getCode(), WorkOrderStateEnum.HANG_UP.getCode());
            if (recordWorkOrderStateMapper.selectCount(qw) == 0) {
                //今天没投产或挂起，则为历史挂起工单
                count++;
            }
        }
        return count;
    }

    @Override
    public Integer gridHandUpOrderCount(Integer gridId, String dateStr) {
        Date dateNow;
        if (StringUtils.isBlank(dateStr)) {
            dateNow = new Date();
        } else {
            dateNow = DateUtil.parse(dateStr, DateUtil.DATETIME_FORMAT);
        }
        Date beginTime = dictService.getDayOutputBeginTime(dateNow);
        //获取所有产线
        List<Integer> lineIds = getLineIdsByGridId(gridId);
        if (CollectionUtils.isEmpty(lineIds)) {
            return 0;
        }
        //获取正在生产的工单
        List<WorkOrderEntity> list = this.getWorkOrderInPlanByGrid(lineIds, beginTime);
        //过滤挂起的工单
        List<WorkOrderEntity> entities = list.stream().filter(o -> WorkOrderStateEnum.HANG_UP.getCode().equals(o.getState())).collect(Collectors.toList());
        //查询今天是否有投产或挂起
        int count = 0;
        for (WorkOrderEntity entity : entities) {
            LambdaQueryWrapper<RecordWorkOrderStateEntity> qw = new LambdaQueryWrapper<>();
            qw.eq(RecordWorkOrderStateEntity::getWorkOrder, entity.getWorkOrderNumber())
                    .ge(RecordWorkOrderStateEntity::getTime, beginTime)
                    .in(RecordWorkOrderStateEntity::getState, WorkOrderStateEnum.INVESTMENT.getCode(), WorkOrderStateEnum.HANG_UP.getCode());
            if (recordWorkOrderStateMapper.selectCount(qw) == 0) {
                //今天没投产或挂起，则为历史挂起工单
                count++;
            }
        }
        return count;
    }


    /**
     * 将WorkOrderEntity转为WorkOrderLineDTO，精简字段
     */
    @Override
    public WorkOrderLineDTO convertWoreOrderEntity(WorkOrderEntity workOrderEntity) {
        MaterialEntity materialEntity = materialService.getSimpleMaterialByCode(workOrderEntity.getMaterialCode());

        WorkOrderLineDTO workOrderLineDTO = WorkOrderLineDTO.builder()
                .actualEndDate(workOrderEntity.getActualEndDate())
                .actualStartDate(workOrderEntity.getActualStartDate())
                .endDate(workOrderEntity.getEndDate())
                .finishCount(workOrderEntity.getFinishCount())
                .lineCode(workOrderEntity.getLineCode())
                .lineId(workOrderEntity.getLineId())
                .lineModelName(workOrderEntity.getWorkCenterName())
                .lineName(workOrderEntity.getLineName())
                .planQuantity(workOrderEntity.getPlanQuantity())
                .progress(workOrderEntity.getProgress())
                .startDate(workOrderEntity.getStartDate())
                .state(workOrderEntity.getState())
                .stateName(workOrderEntity.getStateName())
                .unqualified(workOrderEntity.getUnqualified())
                .workOrderId(workOrderEntity.getWorkOrderId())
                .workOrderName(workOrderEntity.getWorkOrderName())
                .workOrderNumber(workOrderEntity.getWorkOrderNumber())
                .materialCode(workOrderEntity.getMaterialCode())
                .materialName(Objects.isNull(materialEntity) ? null : materialEntity.getName())
                .produceRange(workOrderEntity.getProduceRange())
//                .customerCode(customerCode)
                .procedureName(workOrderEntity.getProcedureName())
                .build();

        //如果有作业工单特性需要拿到作业工单当日计划数量，作业工单当日完成数量
        if (workCenterService.isOperationWorkCenter(workOrderEntity.getWorkCenterId())) {
            ResponseData dayFinishCountResponse = assignmentInterface.getDayFinishCount(workOrderEntity.getWorkOrderNumber());
            Double dayFinishCount = (Double) dayFinishCountResponse.getData();
            ResponseData dayPlanCountResponse = assignmentInterface.getDayPlanCount(workOrderEntity.getWorkOrderNumber());
            Double dayPlanCount = (Double) dayPlanCountResponse.getData();
            workOrderLineDTO.setDayFinishCount(dayFinishCount);
            workOrderLineDTO.setDayPlanCount(dayPlanCount);
        }
        //如果工单名称不为空则显示工单名称
        if (workOrderEntity.getWorkOrderName() != null) {
            workOrderLineDTO.setWorkOrderNumber(workOrderEntity.getWorkOrderName());
        }
        return workOrderLineDTO;
    }

    /**
     * 获取各类告警的数量
     */
    @Override
    public ProductionAlarmDTO getAlarmCount(String lineIds, String alarmTypes) {
        if (StringUtils.isEmpty(alarmTypes)) {
            //内置告警,如果为空,即为预置sql没有执行
            alarmTypes = String.join(Constants.SEP, AlarmClassifyEnum.PRODUCTION_ALARM.getCode(), AlarmClassifyEnum.EQUIPMENT_ALARM.getCode(), AlarmClassifyEnum.SECURITY_ALARM.getCode());
        }
        List<AlarmClassifyEntity> alarmClassifyList = alarmClassifyService.lambdaQuery()
                .in(AlarmClassifyEntity::getAlarmClassifyCode, Arrays.stream(alarmTypes.split(Constants.SEP)).collect(Collectors.toList()))
                .list();
        Map<Integer, String> classifyMap = alarmClassifyList.stream().collect(Collectors.toMap(AlarmClassifyEntity::getAlarmClassifyId, AlarmClassifyEntity::getAlarmClassifyCode));
        if (CollectionUtils.isEmpty(classifyMap)) {
            return ProductionAlarmDTO.builder().build();
        }
        LambdaQueryWrapper<AlarmEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AlarmEntity::getAlarmType, AlarmTypesEnum.FAULT_ALARM.getCode())
                .in(AlarmEntity::getAlarmClassifyId, classifyMap.keySet());
        List<AlarmEntity> list = alarmService.list(wrapper);
        Map<String, Long> map;
        if (StringUtils.isNotBlank(lineIds)) {
            List<Integer> lineIdList = Arrays.stream(lineIds.split(Constants.SEP)).map(Integer::valueOf).collect(Collectors.toList());
            list = list.stream().filter(o -> lineIdList.contains(o.getProductionLineId())).collect(Collectors.toList());
        }
        map = list.stream().collect(Collectors.groupingBy(o -> classifyMap.get(o.getAlarmClassifyId()), Collectors.counting()));
        for (AlarmClassifyEnum anEnum : AlarmClassifyEnum.values()) {
            if (!map.containsKey(anEnum.getCode())) {
                map.put(anEnum.getCode(), 0L);
            }
        }
        alarmService.setNameAndTimeByList(list);
        list.sort(Comparator.comparing(AlarmEntity::getAlarmLevel, Comparator.reverseOrder()).thenComparing(AlarmEntity::getAlarmReportTime));
        return ProductionAlarmDTO.builder().map(map).list(list).build();
    }


    /**
     * 计划达成率
     *
     * @return
     */
    @Override
    public List<RecordOutputEntity> getPlanRate(Date dateIn, Integer lineId) {
        LambdaQueryWrapper<RecordOutputEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.le(RecordOutputEntity::getTime, dictService.getRecordDate(new Date()));
        if (lineId == null) {
            wrapper.isNull(RecordOutputEntity::getLineId);
        } else {
            wrapper.eq(RecordOutputEntity::getLineId, lineId);
        }
        wrapper.orderByDesc(RecordOutputEntity::getTime)
                .orderByDesc(RecordOutputEntity::getId);
        wrapper.last("limit 7");
        List<RecordOutputEntity> list = recordOutputService.list(wrapper);
        Map<Date, RecordOutputEntity> map = list.stream().collect(Collectors.toMap(RecordOutputEntity::getTime, a -> a));

        Date date = new Date();
        List<RecordOutputEntity> resultList = new ArrayList<>(7);
        //取最近七天数据，没有则补全
        for (int i = 0; i < 7; i++) {
            Date dateTemp = DateUtil.formatToDate(DateUtil.addDate(date, -i), DateUtil.DATETIME_FORMAT_ZERO);
            RecordOutputEntity recordOutputEntityTemp = map.get(dateTemp);
            if (recordOutputEntityTemp != null) {
                resultList.add(recordOutputEntityTemp);
            } else {
                resultList.add(RecordOutputEntity.builder().time(dateTemp).build());
            }
        }
        resultList.forEach(o -> o.setReferenceValue(workPropertise.getPlanAchievementRate()));
        if (CollectionUtils.isEmpty(resultList)) {
            return null;
        }
        resultList.sort(Comparator.comparing(RecordOutputEntity::getTime));
        return resultList;
    }

    /**
     * 获取工单达成率
     *
     * @param dateIn
     * @param lineId
     * @return
     */
    @Override
    public List<RecordOutputEntity> getOrderAchieveRate(Date dateIn, Integer lineId) {
        Date today = new Date();
        Date recordDate = dictService.getRecordDate(today);
        LambdaQueryWrapper<RecordOutputEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.le(RecordOutputEntity::getTime, recordDate);
        if (lineId == null) {
            wrapper.isNull(RecordOutputEntity::getLineId);
        } else {
            wrapper.eq(RecordOutputEntity::getLineId, lineId);
        }
        wrapper.orderByDesc(RecordOutputEntity::getTime)
                .orderByDesc(RecordOutputEntity::getId)
                .last("limit 7");
        List<RecordOutputEntity> list = recordOutputService.list(wrapper);
        SimpleDateFormat format = new SimpleDateFormat(DateUtil.DATETIME_FORMAT_ZERO);
        Map<String, RecordOutputEntity> map = list.stream().collect(Collectors.toMap(o -> format.format(o.getTime()), a -> a, (o1, o2) -> o2));
        //取最近七天数据，没有则补全
        List<RecordOutputEntity> resultList = new ArrayList<>();
        Double planAchievementRate = workPropertise.getPlanAchievementRate();
        Date date = DateUtil.addDate(recordDate, -6);
        while (!date.after(recordDate)) {
            String dateStr = format.format(date);
            RecordOutputEntity entity;
            if (map.containsKey(dateStr)) {
                entity = map.get(dateStr);
                Double orderCompleted = entity.getOrderCompleted();
                Double orderPlanQuantity = entity.getOrderPlanQuantity();
                Double achieveRate = orderCompleted != null && orderPlanQuantity != null && orderPlanQuantity != 0
                        ? MathUtil.divideDouble(orderCompleted, orderPlanQuantity, 2) : null;
                achieveRate = achieveRate == null ? null : MathUtil.round(achieveRate, 2);
                entity.setWorkOrderFinishRate(achieveRate);
            } else {
                entity = RecordOutputEntity.builder().time(date).build();
            }
            entity.setReferenceValue(planAchievementRate);
            resultList.add(entity);
            date = DateUtil.addDate(date, 1);
        }
        return resultList;
    }

    /**
     * 查询每个车间下的良率&oee
     *
     * @return
     */
    @Override
    public Map<String, List<YieldRecordVO>> getYieldAndOee(Date dateIn, Integer gridId) {
        Date time = new Date();
        if (dateIn != null) {
            time = dateIn;
        }

        //<key:时间,value:YieldRecordVO对象list>的map
        Map<String, List<YieldRecordVO>> map;
        //<key:车间名称,value:对象的list>
        Map<String, List<YieldRecordVO>> gridYieldMap = new HashMap<>();
        List<Integer> modelIdCollect = getLineOEEModelIds();
        //查询所有车间
        List<GridEntity> gridEntityList = new ArrayList<>();
        if (gridId == null) {
            gridEntityList = gridService.list();
        } else {
            GridEntity gridEntity = gridService.getById(gridId);
            if (Objects.nonNull(gridEntity)) {
                gridEntityList.add(gridEntity);
            }
        }
        for (GridEntity gridEntity : gridEntityList) {
            List<YieldRecordVO> yieldRecordVOsList = new ArrayList<>();
            //根据车间code查询对应的产线
            List<ProductionLineEntity> productionLineEntityList = lineService.getLineList(gridEntity.getGcode());
            //过滤出带有产线oee指标的产线
            List<Integer> productionLineIds = productionLineEntityList.stream()
                    .filter(o -> modelIdCollect.contains(o.getModelId()))
                    .filter(ProductionLineEntity::getIsCalOee)
                    .map(ProductionLineEntity::getProductionLineId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(productionLineIds)) {
                gridYieldMap.put(gridEntity.getGname(), getEmptyRecord(time));
                continue;
            }
            //所有产线的oee和良率
            List<YieldRecordVO> list = new ArrayList<>();
            for (Integer productionLineId : productionLineIds) {
                //一条产线在七天内对应的良率和oee
                List<YieldRecordVO> yieldRecordVOList = getYieldRecord(productionLineId, time, 7);
                //所有产线在7天内对应的良率和oee
                list.addAll(yieldRecordVOList);
            }
            map = list.stream().collect(Collectors.groupingBy(YieldRecordVO::getTime));
            map = map.entrySet().stream()
                    .sorted(Map.Entry.comparingByKey())
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue,
                            (oleValue, newValue) -> oleValue, LinkedHashMap::new));
            for (Map.Entry<String, List<YieldRecordVO>> entry : map.entrySet()) {
                List<YieldRecordVO> yieldRecordVOList = entry.getValue();
                List<YieldRecordVO> collect = yieldRecordVOList.stream().filter(o -> o.getAvgOee() != null).collect(Collectors.toList());
                Double avgOee = null;
                if (!CollectionUtils.isEmpty(collect)) {
                    avgOee = MathUtil.round(collect.stream().mapToDouble(YieldRecordVO::getAvgOee).average().getAsDouble(), SCALE);
                }
                double qualified = yieldRecordVOList.stream().mapToDouble(YieldRecordVO::getQualified).sum();
                Double yield = yieldRecordVOList.stream().mapToDouble(YieldRecordVO::getYield).sum();
                Double directRate = yield == 0 ? null : MathUtil.divideDouble(qualified, yield, SCALE);
                yieldRecordVOsList.add(YieldRecordVO.builder().time(entry.getKey()).yield(yield).avgOee(avgOee).directRate(directRate).build());
            }
            //车间名称 : 该车间下的所有产线的oee和良率(这一天的)
            gridYieldMap.put(gridEntity.getGname(), yieldRecordVOsList);
        }
        return gridYieldMap;
    }

    private List<YieldRecordVO> getEmptyRecord(Date time) {
        Date startDate = DateUtil.addDate(time, -6);
        List<Date> dateList = DateUtil.getBetweenDate(startDate, time);
        return dateList.stream().map(o -> YieldRecordVO.builder().time(DateUtil.dateStr(o)).build()).collect(Collectors.toList());
    }

    private List<Integer> getLineOEEModelIds() {
        LambdaQueryWrapper<TargetModelEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TargetModelEntity::getTargetName, TargetConst.LINE_OEE);
        List<TargetModelEntity> modelEntities = targetModelService.list(wrapper);
        if (CollectionUtils.isEmpty(modelEntities)) {
            return new ArrayList<>();
        }
        return modelEntities.stream().map(TargetModelEntity::getModelId).collect(Collectors.toList());
    }


    /**
     * 近一周产量&产率
     *
     * @param showDay 显示多少天的数据(包括当天)
     * @return 算法：
     * 产量：该产线下工单每天上报的产量（默认需要近一周的数据）
     * 直通率 = 该产线下所有工单的直通数/产量
     */
    @Override
    public List<YieldRecordVO> getYieldRecord(Integer lineId, Date endTimeDate, Integer showDay) {
        // 前(showDay-1)天
        Date startTimeDate = DateUtil.formatToDate(DateUtil.addDate(endTimeDate, -(showDay - 1)), DateUtil.DATETIME_FORMAT_ZERO);

        SimpleDateFormat sdf = new SimpleDateFormat(DateUtil.DATE_FORMAT);
        HashMap<String, Double> dayCountMap = new HashMap<>(), dayInputMap = new HashMap<>(), dayUnqualifiedMap = new HashMap<>(), dayDirectAccessMap = new HashMap<>(), dayFlowCodeReportMap = new HashMap<>();
        //近一周产线下的工单产量(存在每天会有多个工单上报产量 产品经理说求和)
        List<RecordWorkOrderLineDayCountEntity> list = recordWorkOrderLineDayCountService.lambdaQuery()
                .eq(Objects.nonNull(lineId), RecordWorkOrderLineDayCountEntity::getLineId, lineId)
                .ge(RecordWorkOrderLineDayCountEntity::getTime, startTimeDate)
                .list();
        Map<String, List<RecordWorkOrderLineDayCountEntity>> countMap = list.stream().collect(Collectors.groupingBy(o -> sdf.format(o.getTime())));
        // 工单
        List<String> workOrderNumbers = list.stream().map(RecordWorkOrderLineDayCountEntity::getWorkOrderNumber).distinct().collect(Collectors.toList());
        // 指标的每日统计
        List<MetricsWorkOrderDailyEntity> targetDailyList = CollectionUtils.isEmpty(workOrderNumbers)? Collections.emptyList():
                metricsWorkOrderDailyService.lambdaQuery()
                    .in(MetricsWorkOrderDailyEntity::getWorkOrderNumber, workOrderNumbers)
                    .le(MetricsWorkOrderDailyEntity::getRecordDate, endTimeDate)
                    .ge(MetricsWorkOrderDailyEntity::getRecordDate, startTimeDate)
                    .list();;
        Map<String, MetricsWorkOrderDailyEntity> targetDailyMap = targetDailyList.stream().collect(Collectors.toMap(e -> e.getWorkOrderNumber() + DateUtil.dateStr(e.getRecordDate()), Function.identity()));
        for (RecordWorkOrderLineDayCountEntity dayCount : list) {
            MetricsWorkOrderDailyEntity targetDaily = targetDailyMap.get(dayCount.getWorkOrderNumber() + DateUtil.dateStr(dayCount.getTime()));
            if(targetDaily == null) {
                continue;
            }
            dayCount.setDirectAccessQuantity(targetDaily.getDirectAccessQuantity());
            dayCount.setFlowCodeReportQuantity(targetDaily.getFlowCodeReportQuantity());
        }

        for (Map.Entry<String, List<RecordWorkOrderLineDayCountEntity>> entry : countMap.entrySet()) {
            List<RecordWorkOrderLineDayCountEntity> value = entry.getValue();
            //每天产量
            double dayCount = value.stream().filter(recordWorkOrderDayCountEntity -> recordWorkOrderDayCountEntity.getCount() != null).mapToDouble(RecordWorkOrderLineDayCountEntity::getCount).sum();
            double dayInput = value.stream().filter(recordWorkOrderDayCountEntity -> recordWorkOrderDayCountEntity.getInput() != null).mapToDouble(RecordWorkOrderLineDayCountEntity::getInput).sum();
            double dayUnqualified = value.stream().filter(recordWorkOrderDayCountEntity -> recordWorkOrderDayCountEntity.getUnqualified() != null).mapToDouble(RecordWorkOrderLineDayCountEntity::getUnqualified).sum();
            double dayDirectAccess = value.stream().filter(recordWorkOrderDayCountEntity -> recordWorkOrderDayCountEntity.getDirectAccessQuantity() != null).mapToDouble(RecordWorkOrderLineDayCountEntity::getDirectAccessQuantity).sum();
            double dayFlowCodeReport = value.stream().filter(recordWorkOrderDayCountEntity -> recordWorkOrderDayCountEntity.getFlowCodeReportQuantity() != null).mapToDouble(RecordWorkOrderLineDayCountEntity::getFlowCodeReportQuantity).sum();
            dayCountMap.put(entry.getKey(), dayCount);
            dayInputMap.put(entry.getKey(), dayInput);
            dayUnqualifiedMap.put(entry.getKey(), dayUnqualified);
            dayDirectAccessMap.put(entry.getKey(), dayDirectAccess);
            dayFlowCodeReportMap.put(entry.getKey(), dayFlowCodeReport);
        }
        List<YieldRecordVO> yieldRecordList = new ArrayList<>();
        List<RecordLineDayUnionEntity> recordLineDayUnionEntities;
        //近一周的时间列表（包括当天）
        List<Date> dateList = DateUtil.getBetweenDate(startTimeDate, endTimeDate);
        for (Date date : dateList) {
            String dayTime = sdf.format(date);
            //每日产量
            Double dayCount = dayCountMap.getOrDefault(dayTime, 0.0);
            //每日投产数
            Double dayInput = dayInputMap.getOrDefault(dayTime, 0.0);
            // 每日不合格数
            double unqualifiedQuantity = dayUnqualifiedMap.getOrDefault(dayTime, 0.0);
            // 直通率 = 没有维修过的流水码去重 / 流水码去重
            double directAccess = dayDirectAccessMap.getOrDefault(dayTime, 0.0);
            double flowCodeReport = dayFlowCodeReportMap.getOrDefault(dayTime, 0.0);
            double directRate = NullableDouble.of(directAccess).div(flowCodeReport).scale(SCALE).cal(0d);
            //查询oee
            Double avgOee = null;
            if (lineId == null) {
                //查出配了oee指标的产线
                LambdaQueryWrapper<ProductionLineEntity> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(ProductionLineEntity::getIsCalOee, true);
                List<ProductionLineEntity> productionLineEntities = lineService.list(wrapper);

                if (CollectionUtils.isEmpty(productionLineEntities)) {
                    recordLineDayUnionEntities = new ArrayList<>();
                } else {
                    List<Integer> lineIdsCollect = productionLineEntities.stream().map(ProductionLineEntity::getProductionLineId).collect(Collectors.toList());

                    //若lineId为空，查询当前时间下全部的数据，求平均值oee
                    LambdaQueryWrapper<RecordLineDayUnionEntity> wrapper1 = new LambdaQueryWrapper<>();
                    wrapper1.eq(RecordLineDayUnionEntity::getTime, dayTime).in(RecordLineDayUnionEntity::getProductionLineId, lineIdsCollect);
                    recordLineDayUnionEntities = recordLineDayUnionMapper.selectList(wrapper1);
                }

            } else {
                //若lineId不为空，查询当前产线下当前时间对应的数据，求平均值oee
                QueryWrapper<RecordLineDayUnionEntity> wrapper2 = new QueryWrapper<>();
                wrapper2.lambda().in(RecordLineDayUnionEntity::getProductionLineId, lineId)
                        .eq(RecordLineDayUnionEntity::getTime, dayTime);
                recordLineDayUnionEntities = recordLineDayUnionMapper.selectList(wrapper2);
            }
            if (!CollectionUtils.isEmpty(recordLineDayUnionEntities)) {
                //oee保留三位小数
                avgOee = MathUtil.round(recordLineDayUnionEntities.stream().mapToDouble(RecordLineDayUnionEntity::getOee).average().getAsDouble(), SCALE);
            }
            yieldRecordList.add(YieldRecordVO.builder().time(dayTime).directRate(directRate)
                    .qualified(dayCount).input(dayInput).yield(dayCount)
                    .avgOee(avgOee).numOfBad(unqualifiedQuantity)
                    .build());
        }
        yieldRecordList.sort(Comparator.comparing(YieldRecordVO::getTime));
        return yieldRecordList;
    }


    /**
     * 车间节拍
     *
     * @return
     */
    @Override
    public JSONArray getGridBeat(String timeType, Date dateIn, Integer gridId) {
        Date time = new Date();
        if (dateIn != null) {
            time = dateIn;
        }
//        找出七个时间点，再去获取数据
        List<String> timeList = new ArrayList<>();
        if ("fiveMin".equals(timeType)) {
            timeList = getTimeList(5, time);
        }
        if ("tenMin".equals(timeType)) {
            timeList = getTimeList(10, time);
        }
        if ("oneHour".equals(timeType)) {
            timeList = getTimeList(60, time);
        }
        LambdaQueryWrapper<RecordLineBeatEntity> wrapper = new LambdaQueryWrapper<>();
        //指定车间
        List<RecordLineBeatEntity> list;
        List<Integer> lineIds = getLineIdsByGridId(gridId);
        if (Objects.nonNull(gridId) && CollectionUtils.isEmpty(lineIds)) {
            // 前端传车间id,但不存在产线,直接为空
            list = new ArrayList<>();
        } else {
            wrapper.in(!CollectionUtils.isEmpty(lineIds), RecordLineBeatEntity::getLineId, lineIds);
            wrapper.in(RecordLineBeatEntity::getTime, timeList).orderByAsc(RecordLineBeatEntity::getTime);
            list = beatService.list(wrapper);
        }
        HashMap<String, Map<Integer, String>> gridNames;
        HashMap<Integer, List<BeatDTO>> lineBeatMap = new HashMap<>();
        List<BeatDTO> blankBeats = new ArrayList<>();
        if (CollectionUtils.isEmpty(list)) {
            //<车间id,<产线id,产线名称>>
            gridNames = getGridNames(lineIds);
            for (String s : timeList) {
                blankBeats.add(BeatDTO.builder().date(s).build());
            }
            blankBeats.sort(Comparator.comparing(BeatDTO::getDate));
        } else {
            SimpleDateFormat format = new SimpleDateFormat(DateUtil.DATETIME_FORMAT);
            Map<Integer, List<RecordLineBeatEntity>> map = list.stream().collect(Collectors.groupingBy(RecordLineBeatEntity::getLineId));
            for (Map.Entry<Integer, List<RecordLineBeatEntity>> entry : map.entrySet()) {
                List<RecordLineBeatEntity> value = entry.getValue();
                List<BeatDTO> beats = new ArrayList<>();
                for (RecordLineBeatEntity entity : value) {
                    BeatDTO build = BeatDTO.builder().date(format.format(entity.getTime())).build();
                    if ("fiveMin".equals(timeType)) {
                        build.setBeat(entity.getBeat5min());
                    }
                    if ("tenMin".equals(timeType)) {
                        build.setBeat(entity.getBeat10min());
                    }
                    if ("oneHour".equals(timeType)) {
                        build.setBeat(entity.getBeat60min());
                    }
                    beats.add(build);
                }
                lineBeatMap.put(entry.getKey(), beats);
            }
            Set<Integer> lineSet = list.stream().map(RecordLineBeatEntity::getLineId).collect(Collectors.toSet());
            gridNames = getGridNames(lineSet);
        }
        List<GridBeatDTO> gridBeats = new ArrayList<>();
        for (Map.Entry<String, Map<Integer, String>> entry : gridNames.entrySet()) {
            Map<Integer, String> value = entry.getValue();
            List<LineBeatDTO> lineBeats = new ArrayList<>();
            for (Map.Entry<Integer, String> lineEntry : value.entrySet()) {
                Integer lineId = lineEntry.getKey();
                LineBeatDTO build = LineBeatDTO.builder()
                        .lineId(lineId)
                        .lineName(lineEntry.getValue()).build();
                build.setBeats(lineBeatMap.getOrDefault(lineId, blankBeats));
                lineBeats.add(build);
            }
            gridBeats.add(GridBeatDTO.builder().gridName(entry.getKey()).lineBeats(lineBeats).build());
        }
        return JSONArray.parseArray(JSON.toJSONString(gridBeats, SerializerFeature.WriteMapNullValue));
    }

    private List<Integer> getLineIdsByGridId(Integer gridId) {
        if (gridId == null) {
            return new ArrayList<>();
        }
        List<ProductionLineEntity> lineEntities = lineService.lambdaQuery()
                .select(ProductionLineEntity::getProductionLineId)
                .eq(ProductionLineEntity::getGid, gridId).list();
        return lineEntities.stream().map(ProductionLineEntity::getProductionLineId).collect(Collectors.toList());
    }


    private List<String> getTimeList(int interval, Date time) {
        Instant instant = time.toInstant();
        ZoneId zoneId = ZoneId.systemDefault();
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, zoneId);
        LocalDateTime minutes = localDateTime.minusMinutes(localDateTime.getMinute() % interval);
        List<String> list = new ArrayList<>();
        for (int i = 0; i < 7; i++) {
            String format = minutes.format(DateTimeFormatter.ofPattern(DateUtil.DATETIME_FORMAT_MIN));
            minutes = minutes.minusMinutes(interval);
            list.add(format);
        }
        return list;
    }


    private HashMap<String, Map<Integer, String>> getGridNames(Collection<Integer> lineSet) {
        //获取产线名称
        List<ProductionLineEntity> lineEntities;
        if (CollectionUtils.isEmpty(lineSet)) {
            lineEntities = lineService.list();
        } else {
            lineEntities = lineService.listByIds(lineSet);
        }
        Map<Integer, Map<Integer, String>> collect = lineEntities.stream()
                .filter(o -> o.getGid() != null)
                .collect(Collectors
                        .groupingBy(ProductionLineEntity::getGid,
                                Collectors.toMap(ProductionLineEntity::getProductionLineId, ProductionLineEntity::getName)));
        List<GridEntity> gridEntities = gridService.listByIds(collect.keySet());
        HashMap<String, Map<Integer, String>> map = new HashMap<>();
        for (GridEntity entity : gridEntities) {
            Integer gid = entity.getGid();
            if (collect.containsKey(gid)) {
                map.put(entity.getGname(), collect.get(gid));
            }
        }
        return map;
    }


    /**
     * 计算当天产量、良率
     *
     * @param
     * @return
     */
    @Override
    public JSONObject getUnqualifiedAndFinishCountOnDay(String dateIn) {
        ResponseData responseData = scrOutInterface.getUnqualifiedAndFinishCountOnDay(dateIn);
        return JSONObject.parseObject(JSONObject.toJSONString(responseData.getData()));
    }

    /**
     * 获取质量检测Top
     * 产线下对应工位的所有不良信息——》产线下当天所有工单的不良信息
     */
    @Override
    public List<UnqualifiedVO> getProductUnqualified(List<Integer> lineIds, Integer topNum) {
        Date date = dictService.getRecordDate(new Date());
        //通过产线id和时间筛选当天该产线质量检测不良top5
        // 没有指定top数量,默认为top5
        topNum = Objects.isNull(topNum) ? 5 : topNum;
        List<UnqualifiedVO> list = recordWorkOrderUnqualifiedMapper.getUnqualifiedByLineId(lineIds, date, topNum);
        return list;
    }


    /**
     * 获取今日数据
     *
     * @param lineId
     * @return
     */
    @Override
    @Cacheable(cacheNames = "screen_today_data", key = "'lineId=' + #lineId")
    public Map<String, Object> getTodayData(Integer lineId) {
        Date date = dictService.getRecordDate(new Date());
        Map<String, Object> resultMap = new HashMap<>(4);
        //获取今日达成率
        //今日已生产数量（合格数）
        Integer completeTotal = recordWorkOrderDayCountService.getCountByDate(date, lineId).intValue();
        log.info("已生产量:" + completeTotal);
        //今日计划生产数量
        Integer planQuantityTotal = getPlanQuantityTotal(lineId);
        //当日达成临时实体
        CapacityDTO dayReachDTO = CapacityDTO.builder()
                .gross(planQuantityTotal)
                .completion(completeTotal)
                .yieldRate(planQuantityTotal == 0 ? 1.0 : MathUtil.divideDouble(completeTotal, planQuantityTotal, SCALE))
                .build();
        resultMap.put("yieldRate", dayReachDTO);

        //获取生产良率
        double qulifiedRate = MathUtil.round(recordWorkOrderDayCountService.getQualifiedRateCountByDate(date, lineId), SCALE);
        resultMap.put("qulifiedRate", qulifiedRate);
        resultMap.put("unqulifiedRate", 1 - qulifiedRate);
        //获取OEE
        //一条产线在七天内对应的良率和oee
        //若lineId不为空，查询当前产线下当前时间对应的数据，求平均值oee
        QueryWrapper<RecordLineDayUnionEntity> wrapper2 = new QueryWrapper<>();
        wrapper2.lambda().in(RecordLineDayUnionEntity::getProductionLineId, lineId)
                .eq(RecordLineDayUnionEntity::getTime, date);
        List<RecordLineDayUnionEntity> dayUnionEntityList = recordLineDayUnionMapper.selectList(wrapper2);
        double avgOee;
        if (!CollectionUtils.isEmpty(dayUnionEntityList)) {
            //oee保留三位小数
            avgOee = MathUtil.round(dayUnionEntityList.stream().mapToDouble(RecordLineDayUnionEntity::getOee).average().getAsDouble(), SCALE);
        } else {
            avgOee = 0.0;
        }
        resultMap.put("oee", avgOee);

        //小时产出数
        //今日已生产数量（合格数）
        int hours = DateUtil.getBetweenHours(date, new Date()).size();
        resultMap.put("hourOut", MathUtil.divideDouble(completeTotal, hours, 1));
        return resultMap;
    }

    /**
     * 获取工序节拍列表
     *
     * @param lineId
     * @return
     */
    @Override
    @Cacheable(cacheNames = "screen_list_facilities_beat", key = "'lineId=' + #lineId")
    public List<FacilitiesBeatDTO> listFacilitiesBeat(Integer lineId) {
        Date now = new Date();
        Date startDate = DateUtil.addMin(now, -5);
        Date recordDate = dictService.getRecordDate(now);

        List<FacilitiesBeatDTO> resultList = new ArrayList<>();
        List<FacilitiesEntity> facilitiesEntityList = facilitiesService.getByLineId(lineId);
        if (CollectionUtils.isEmpty(facilitiesEntityList)) {
            return resultList;
        }
        //补充节拍和产出数
        for (FacilitiesEntity facilitiesEntity : facilitiesEntityList) {
            //节拍数（最近五分钟，平均扫码一个花废多少秒）
            LambdaQueryWrapper<ProductFlowCodeRecordEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(ProductFlowCodeRecordEntity::getFacId, facilitiesEntity.getFid())
                    .ge(ProductFlowCodeRecordEntity::getReportTime, startDate)
                    .eq(ProductFlowCodeRecordEntity::getIsReport, true);
            long count = productFlowCodeRecordService.count(lambdaQueryWrapper);

            LambdaQueryWrapper<ProductFlowCodeRecordEntity> lambdaQueryWrapper2 = new LambdaQueryWrapper<>();
            lambdaQueryWrapper2.eq(ProductFlowCodeRecordEntity::getFacId, facilitiesEntity.getFid())
                    .ge(ProductFlowCodeRecordEntity::getReportTime, recordDate)
                    .eq(ProductFlowCodeRecordEntity::getIsReport, true);
            Long countTotal = productFlowCodeRecordService.count(lambdaQueryWrapper2);
            String beatString = null;
            if (count != 0) {
                Double beat = MathUtil.divideDouble(300, Double.valueOf(count), 0);
                beatString = beat + "s";
            }
            FacilitiesBeatDTO facilitiesBeatDTO = FacilitiesBeatDTO.builder().fid(facilitiesEntity.getFid()).fname(facilitiesEntity.getFname()).beat(beatString).outTotal(countTotal).build();
            resultList.add(facilitiesBeatDTO);
        }
        return resultList;
    }


    /**
     * 获取正在生产工单
     *
     * @param lineId
     * @return
     */
    @Override
    @Cacheable(cacheNames = "screen_work_order_in_work", key = "'lineId=' + #lineId", unless = "#result == null ")
    public WorkOrderEntity getWorkOrderOneInWork(Integer lineId) {
        /*LambdaQueryWrapper<ReportCountEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ReportCountEntity::getLineId, lineId)
                .orderByDesc(ReportCountEntity::getId)
                .last("limit 1");
        ReportCountEntity reportCountEntity = reportCountService.getOne(lambdaQueryWrapper);
        if (reportCountEntity == null) {
            return new WorkOrderEntity();
        }
        if (ReportType.FINISHED.getType().equals(reportCountEntity.getType()) || ReportType.HANG_UP.getType().equals(reportCountEntity.getType())) {
            return new WorkOrderEntity();
        }*/
        String workOrderNumber = orderExecuteSeqService.getCalOrderByLineId(lineId);
        if (StringUtils.isBlank(workOrderNumber)) {
            return null;
        }

        return workOrderService.getSimpleWorkOrderByNumber(workOrderNumber);
    }

    /**
     * 获取产线当天数据
     *
     * @param lineId
     */
    @Override
    public LineDayDataDTO getLineTodayData(Integer lineId) {
        //获取首班时间
        Date date = dictService.getRecordDate(new Date());
        //今日已生产数量（合格数）
        Double completeTotal = recordWorkOrderDayCountService.getCountByDate(date, lineId);
        //今日计划生产数量
        Integer planQuantityTotal = getPlanQuantityTotal(lineId);
        //今日计划达成率
        Double completionRate = completeTotal != null && planQuantityTotal != null && planQuantityTotal != 0
                ? MathUtil.divideDouble(completeTotal, planQuantityTotal, SCALE) : 1.0;
        //OEE
        Double oee = getLineOeeByDate(lineId, date);
        //获取生产良率
        Double yield = MathUtil.round(recordWorkOrderDayCountService.getQualifiedRateCountByDate(date, lineId), SCALE);
        //不良数
        Double unqualified = recordWorkOrderDayCountService.getUnqualifiedCountCountByDate(date, lineId);
        return LineDayDataDTO.builder()
                .completed(completeTotal == null ? 0 : completeTotal.intValue())
                .planned(planQuantityTotal)
                .completionRate(completionRate)
                .oee(MathUtil.round(oee, 2))
                .yield(yield)
                .unqualified(unqualified)
                .build();
    }

    /**
     * 通过产线id获取OEE
     *
     * @param lineId
     * @param date
     * @return
     */
    @Override
    public Double getLineOeeByDate(Integer lineId, Date date) {
        LambdaQueryWrapper<RecordLineDayUnionEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(lineId != null, RecordLineDayUnionEntity::getProductionLineId, lineId)
                .eq(RecordLineDayUnionEntity::getTime, date).last("limit 1");
        RecordLineDayUnionEntity lineDayUnionEntity = recordLineDayUnionMapper.selectOne(wrapper);
        return lineDayUnionEntity == null ? 0.0 : lineDayUnionEntity.getOee();
    }

    /**
     * 通过产线集合和时间范围获取oee
     *
     * @param lineIds
     * @param start
     * @param end
     * @return
     */
    @Override
    public List<RecordLineDayUnionEntity> getOeeByLinesAndDays(List<Integer> lineIds, String start, String end) {
        if (CollectionUtils.isEmpty(lineIds)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<RecordLineDayUnionEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(RecordLineDayUnionEntity::getProductionLineId, lineIds)
                .between(RecordLineDayUnionEntity::getTime, start, end);
        return recordLineDayUnionMapper.selectList(wrapper);
    }

    /**
     * 通过产线id和时间范围获取oee
     *
     * @param lineId
     * @param start
     * @param end
     * @return
     */
    @Override
    public List<RecordLineDayUnionEntity> getOeeByLineIdAndDays(Integer lineId, String start, String end) {
        if (lineId == null) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<RecordLineDayUnionEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RecordLineDayUnionEntity::getProductionLineId, lineId)
                .between(RecordLineDayUnionEntity::getTime, start, end);
        return recordLineDayUnionMapper.selectList(wrapper);
    }


    /**
     * 获取指定日期之后的OEE记录
     *
     * @param lineIds
     * @param dateAfter
     * @return
     */
    @Override
    public List<RecordLineDayUnionEntity> getLineOeeByIdsAndDate(List<Integer> lineIds, Date dateAfter) {
        if (CollectionUtils.isEmpty(lineIds)) {
            return new ArrayList<>();
        }
        Date recordDate = dictService.getRecordDate(dateAfter);
        LambdaQueryWrapper<RecordLineDayUnionEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(RecordLineDayUnionEntity::getProductionLineId, lineIds)
                .ge(RecordLineDayUnionEntity::getTime, recordDate);
        return recordLineDayUnionMapper.selectList(wrapper);
    }

    /**
     * 获取七天等级产量列表
     *
     * @param lineId
     * @return
     */
    @Override
    @Cacheable(cacheNames = "screen_list_report_grade", key = "'lineId=' + #lineId")
    public List<ReportGradeDTO> listReportGrade(Integer lineId) {
        Date recordDate = dictService.getRecordDate(new Date());
        List<ReportGradeDTO> reportGradeDTOS = new ArrayList<>();
        for (int i = 0; i < 7; i++) {
            List<ReportLineEntity> lineEntityList = reportLineService.listByDate(DateUtil.addDate(recordDate, -i), lineId);
            Map<String, Double> map = new HashMap<>(6);
            for (ReportLineEntity reportLineEntity : lineEntityList) {
                BarCodeEntity barCodeEntity = barCodeService.getBarCodeByCode(reportLineEntity.getBatch());
                if (barCodeEntity != null && StringUtils.isNotBlank(barCodeEntity.getGrade())) {
                    Double value = 0.0;
                    if (map.containsKey(barCodeEntity.getGrade())) {
                        value = map.get(barCodeEntity.getGrade()) + reportLineEntity.getFinishCount();
                    } else {
                        value = reportLineEntity.getFinishCount();
                    }
                    map.put(barCodeEntity.getGrade(), value);
                }
            }
            ReportGradeDTO reportGradeDTO = ReportGradeDTO.builder()
                    .dateString(DateUtil.format(DateUtil.addDate(recordDate, -i), DateUtil.DATETIME_FORMAT))
                    .gradeNumber(map).build();
            reportGradeDTOS.add(reportGradeDTO);
        }
        return reportGradeDTOS;
    }


    /**
     * 能耗总览组件
     *
     * @param gridId
     * @return
     */
    @Override
    @Cacheable(cacheNames = "screen_overview_energy_consumption", key = "'gridId=' + #gridId")
    public Map<String, Double> overviewOfEnergyConsumption(Integer gridId) {
        Map<String, Double> resultMap = new HashMap<>(6);
        Date date = dictService.getRecordDate(new Date());
        String startDateYear = DateUtil.format(date, DateUtil.DATE_FORMAT_YEAR);
        String endDate = DateUtil.format(new Date(), DateUtil.DATETIME_FORMAT);
        String deviceIds = deviceService.lambdaQuery()
                .select(DeviceEntity::getDeviceId)
                .eq(gridId != null, DeviceEntity::getGid, gridId)
                .list()
                .stream()
                .map(o -> String.valueOf(o.getDeviceId()))
                .collect(Collectors.joining(Constant.SEP));
        if (Objects.nonNull(gridId) && StringUtils.isBlank(deviceIds)) {
            // 车间没有关联设备,直接返回
            return new HashMap<>();
        }
        //年用电量
        List<DeviceEnergyConsumptionEntity> yearRecords = deviceEnergyConsumptionService.getList(deviceIds, null, null, startDateYear, endDate, null, null).getRecords();
        resultMap.put("electricityConsumptionYear", yearRecords.stream().mapToDouble(DeviceEnergyConsumptionEntity::getConsumption).sum());
        //年用气体量
        List<DeviceGasConsumptionEntity> yearGasRecords = deviceGasConsumptionService.listByDate(deviceIds, startDateYear, endDate);
        resultMap.put("gasYear", yearGasRecords.stream().mapToDouble(DeviceGasConsumptionEntity::getConsumption).sum());
        // 年碳排放量
        BatchAndTargetValDTO carbonYearRecords = deviceCarbonConsumptionService.carbonDayList(deviceIds, DateUtil.format(date, DateUtil.DATETIME_FORMAT_YEAR), endDate);
        resultMap.put("carbonYear", carbonYearRecords.getTargetList().stream()
                .map(IndicatorEntityDTO::getDataVal)
                .filter(Objects::nonNull)
                .map(Double::valueOf).mapToDouble(Double::doubleValue).sum());

        //月用电量
        String startDateMouth = DateUtil.format(date, DateUtil.DATE_FORMAT_MOUTH);
        List<DeviceEnergyConsumptionEntity> mouthRecords = deviceEnergyConsumptionService.getList(deviceIds, null, null, startDateMouth, endDate, null, null).getRecords();
        resultMap.put("electricityConsumptionMouth", mouthRecords.stream().mapToDouble(DeviceEnergyConsumptionEntity::getConsumption).sum());
        //月用气量
        List<DeviceGasConsumptionEntity> mouthGasRecords = deviceGasConsumptionService.listByDate(deviceIds, startDateMouth, endDate);
        resultMap.put("gasMouth", mouthGasRecords.stream().mapToDouble(DeviceGasConsumptionEntity::getConsumption).sum());
        // 月碳排放量
        BatchAndTargetValDTO carbonMonthRecords = deviceCarbonConsumptionService.carbonDayList(deviceIds, DateUtil.format(date, DateUtil.DATETIME_FORMAT_MOUTH), endDate);
        resultMap.put("carbonMouth", carbonMonthRecords.getTargetList().stream()
                .map(IndicatorEntityDTO::getDataVal)
                .filter(Objects::nonNull)
                .map(Double::valueOf).mapToDouble(Double::doubleValue).sum());

        //日用电量
        String startDateDay = DateUtil.format(date, DateUtil.DATE_FORMAT);
        List<DeviceEnergyConsumptionEntity> dayRecords = deviceEnergyConsumptionService.getList(deviceIds, null, null, startDateDay, endDate, null, null).getRecords();
        resultMap.put("electricityConsumptionDay", dayRecords.stream().mapToDouble(DeviceEnergyConsumptionEntity::getConsumption).sum());
        //日用气量
        List<DeviceGasConsumptionEntity> dayGasRecords = deviceGasConsumptionService.listByDate(deviceIds, startDateDay, endDate);
        resultMap.put("gasDay", dayGasRecords.stream().mapToDouble(DeviceGasConsumptionEntity::getConsumption).sum());
        // 日碳排放量
        String startDateTimeDay = DateUtil.format(date, DateUtil.DATETIME_FORMAT_ZERO);
        BatchAndTargetValDTO carbonDayRecords = deviceCarbonConsumptionService.carbonDayList(deviceIds, startDateTimeDay, endDate);
        resultMap.put("carbonDay", carbonDayRecords.getTargetList().stream()
                .map(IndicatorEntityDTO::getDataVal)
                .filter(Objects::nonNull)
                .map(Double::valueOf).mapToDouble(Double::doubleValue).sum());

        return resultMap;
    }

    @Override
    public List<GridCompletedVO> getDaysOutputByDate(Integer gridId, Date startDate, Date endDate) {
        String startStr = DateUtil.dateTimeStr(startDate);
        String endStr = DateUtil.dateTimeStr(endDate);

        List<RecordWorkOrderDayCountEntity> countByDays = recordWorkOrderDayCountService.getDayCountByGridIdFilterParent(gridId, startStr, endStr);
        List<GridCompletedVO> dtos = new ArrayList<>();
        if (CollectionUtils.isEmpty(countByDays)) {
            return dtos;
        }
        Map<Date, Double> map = countByDays.stream().collect(Collectors.groupingBy(RecordWorkOrderDayCountEntity::getTime,
                Collectors.summingDouble(RecordWorkOrderDayCountEntity::getCount)));
        while (endDate.compareTo(startDate) >= 0) {
            Double completed = map.getOrDefault(startDate, 0.0);
            dtos.add(GridCompletedVO.builder().dateStr(DateUtil.dateTimeStr(startDate)).completed(completed).build());
            startDate = DateUtil.addDate(startDate, 1);
        }
        dtos.sort(Comparator.comparing(GridCompletedVO::getDateStr));
        return dtos;
    }
}
