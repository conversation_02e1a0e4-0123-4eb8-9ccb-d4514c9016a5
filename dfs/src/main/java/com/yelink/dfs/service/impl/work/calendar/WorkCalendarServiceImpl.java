package com.yelink.dfs.service.impl.work.calendar;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import com.yelink.dfs.common.UserAuthenService;
import com.yelink.dfs.constant.RedisKeyPrefix;
import com.yelink.dfs.constant.work.calendar.WeekEnum;
import com.yelink.dfs.entity.model.dto.InstanceDTO;
import com.yelink.dfs.entity.work.calendar.WorkCalendarDownEntity;
import com.yelink.dfs.entity.work.calendar.WorkCalendarEntity;
import com.yelink.dfs.entity.work.calendar.dto.CalendarCellDTO;
import com.yelink.dfs.entity.work.calendar.dto.CalendarTableDTO;
import com.yelink.dfs.entity.work.calendar.dto.DateDTO;
import com.yelink.dfs.entity.work.calendar.dto.TimeRangeDTO;
import com.yelink.dfs.entity.work.calendar.dto.WorkCalendarSyncDTO;
import com.yelink.dfs.entity.work.calendar.dto.WorkDurationDTO;
import com.yelink.dfs.mapper.work.calendar.WorkCalendarMapper;
import com.yelink.dfs.provider.constant.KafkaMessageTypeEnum;
import com.yelink.dfs.service.common.DictService;
import com.yelink.dfs.service.device.DeviceCalendarService;
import com.yelink.dfs.service.model.ModelService;
import com.yelink.dfs.service.work.calendar.WorkCalendarDownService;
import com.yelink.dfs.service.work.calendar.WorkCalendarService;
import com.yelink.dfscommon.constant.Constants;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.constant.model.WorkCenterTypeEnum;
import com.yelink.dfscommon.provider.MessagePushToKafkaService;
import com.yelink.dfscommon.utils.DateUtil;
import com.yelink.dfscommon.utils.MathUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Service
 *
 * <AUTHOR>
 * @Date 2021-08-20 19:43
 */
@Slf4j
@Service
public class WorkCalendarServiceImpl extends ServiceImpl<WorkCalendarMapper, WorkCalendarEntity> implements WorkCalendarService {

    @Autowired
    protected ModelService modelService;
    @Autowired
    protected WorkCalendarDownService downService;
    @Autowired
    protected MessagePushToKafkaService messagePushToKafkaService;
    @Autowired
    protected DeviceCalendarService deviceCalendarService;
    @Autowired
    protected DictService dictService;
    @Autowired
    protected RedisTemplate redisTemplate;
    @Resource
    protected UserAuthenService userAuthenService;
    private static final String REST_DAY = "休息日";
    private static final String WORK_DAY = "工作日";
    private static final String COMMA = ",";
    private static final String SPACE = " ";
    private static final String DASH = "-";

    @Override
    public Page<WorkCalendarEntity> getList(String modelType, Integer instanceId, Integer current, Integer size) {
        LambdaQueryWrapper<WorkCalendarEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WorkCalendarEntity::getModelType, modelType)
                .eq(WorkCalendarEntity::getInstanceId, instanceId);
        Page<WorkCalendarEntity> page = new Page<>();
        if (current == null || size == null) {
            List<WorkCalendarEntity> list = this.list(wrapper);
            page.setRecords(list).setTotal(list.size()).setCurrent(1).setSize(list.size());
        } else {
            page = this.page(new Page<>(current, size), wrapper);
        }
        List<WorkCalendarEntity> records = page.getRecords();
        if (CollectionUtils.isNotEmpty(records)) {
            for (WorkCalendarEntity record : records) {
                //非生产时间
                getDownTimes(record);
                //班次时间
                getWorkCalendarTimes(record);
            }
        }
        return page;
    }


    @Override
    public Page<WorkCalendarEntity> getListByTypeAndCode(String modelType, String instanceCode, Integer current, Integer size) {
        LambdaQueryWrapper<WorkCalendarEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WorkCalendarEntity::getModelType, modelType)
                .eq(WorkCalendarEntity::getInstanceCode, instanceCode);
        Page<WorkCalendarEntity> page = new Page<>();
        if (current != null && size != null) {
            List<WorkCalendarEntity> list = this.list(wrapper);
            page.setRecords(list).setTotal(list.size()).setCurrent(1).setSize(list.size());
        } else {
            List<WorkCalendarEntity> list = this.list(wrapper);
            page.setRecords(list).setTotal(list.size()).setCurrent(1).setSize(list.size());
        }
        List<WorkCalendarEntity> records = page.getRecords();
        if (CollectionUtils.isNotEmpty(records)) {
            for (WorkCalendarEntity record : records) {
                //非生产时间
                getDownTimes(record);
                //班次时间
                getWorkCalendarTimes(record);
            }
        }
        return page;
    }

    @Override
    public WorkCalendarEntity getDetailById(Integer calendarId) {
        WorkCalendarEntity entity = this.getById(calendarId);
        if (entity != null) {
            //非生产时间
            getDownTimes(entity);
            //班次时间
            getWorkCalendarTimes(entity);
        }
        return entity;
    }

    @Override
    public void getWorkCalendarTimes(WorkCalendarEntity entity) {
        String workCalendarTime = entity.getWorkCalendarTime();
        if (StringUtils.isNotBlank(workCalendarTime)) {
            //格式:name HH:mm:ss-HH:mm:ss,name HH:mm:ss-HH:mm:ss
            List<TimeRangeDTO> dtos = new ArrayList<>();
            String[] eachOnes = workCalendarTime.split(COMMA);
            for (String eachOne : eachOnes) {
                String[] times = eachOne.split(DASH);
                dtos.add(TimeRangeDTO.builder().start(times[0]).end(times[1]).build());
            }
            entity.setWorkCalendarTimes(dtos);
        }
    }

    @Override
    public void addCalendar(WorkCalendarEntity entity) {
        if (CollectionUtils.isEmpty(entity.getWorkCalendarTimes())) {
            throw new ResponseException(RespCodeEnum.CALENDAR_DATE_IS_BLANK);
        }
        // 格式化班次时间
        String workCalendarTime = setWorkCalendarTimes(entity.getWorkCalendarTimes());
        entity.setWorkCalendarTime(workCalendarTime);
        // 格式化非生产时间
        String downTime = setDownTimes(entity.getDownTimes());
        entity.setDownTime(downTime);
        this.save(entity);
        // 除班组外，其他需要特殊处理以下逻辑
        // 如果下级有模型且下级未配置该班次，则需要配置相同的班次
        if (!entity.getModelType().equals(WorkCenterTypeEnum.TEAM.getCode())) {
            WorkCalendarSyncDTO calendarSyncDTO = WorkCalendarSyncDTO.builder()
                    .modelType(entity.getModelType())
                    .instanceCode(entity.getInstanceCode())
                    .name(entity.getName())
                    .workCalendarTime(workCalendarTime)
                    .downTime(downTime)
                    .build();
            addOrUpdateSubCalendar(calendarSyncDTO);
        }
        //推送到doc模块
        messagePushToKafkaService.pushNewMessage(entity, Constants.KAFKA_MAIN_DATA_TOPIC, KafkaMessageTypeEnum.WORK_CALENDAR_ADD_MESSAGE);
        redisTemplate.delete(redisTemplate.keys(RedisKeyPrefix.TODAY_WORK_TIME + "*"));
    }

    /**
     * 同步添加下级日历
     * @param calendarSyncDTO 同步对象
     */
    private void addOrUpdateSubCalendar(WorkCalendarSyncDTO calendarSyncDTO) {
        // 获取工作日历层级树
        Map<String, List<InstanceDTO>> map = getWorkCalendarTreeMap();
        // 同步下级的班次日历
        syncSubWorkCalendar(calendarSyncDTO, map);
    }

    /**
     * 同步下级的班次日历
     */
    private void syncDeleteSubWorkCalendar(WorkCalendarSyncDTO calendarSyncDTO, Map<String, List<InstanceDTO>> map) {
        String key = calendarSyncDTO.getModelType() + Constants.UNDER_LINE + calendarSyncDTO.getInstanceCode();
        if (!map.containsKey(key)) {
            return;
        }
        List<InstanceDTO> instanceDTOS = map.get(key);
        for (InstanceDTO instanceDTO : instanceDTOS) {
            // 如果下级存在相同班次，则更新它的工作时间和休息时间
            this.lambdaUpdate()
                    .eq(WorkCalendarEntity::getInstanceId, instanceDTO.getInstanceId())
                    .eq(WorkCalendarEntity::getName, calendarSyncDTO.getName())
                    .remove();
            WorkCalendarSyncDTO subCalendarSyncDTO = WorkCalendarSyncDTO.builder()
                    .modelType(instanceDTO.getModelType())
                    .instanceCode(instanceDTO.getInstanceCode())
                    .name(calendarSyncDTO.getName())
                    .workCalendarTime(calendarSyncDTO.getWorkCalendarTime())
                    .downTime(calendarSyncDTO.getDownTime())
                    .build();
            // 继续同步下级的班次日历
            syncDeleteSubWorkCalendar(subCalendarSyncDTO, map);
        }
    }

    /**
     * 获取工作日历层级树
     */
    private Map<String, List<InstanceDTO>> getWorkCalendarTreeMap() {
        List<InstanceDTO> lineInstanceTree = modelService.getInstanceTree(WorkCenterTypeEnum.LINE.getCode());
        List<InstanceDTO> deviceInstanceTree = modelService.getInstanceTree(WorkCenterTypeEnum.DEVICE.getCode());
        Map<String, List<InstanceDTO>> map = new HashMap<>();
        // 处理制造单元实例树
        for (InstanceDTO instanceDTO : lineInstanceTree) {
            if (CollectionUtils.isEmpty(instanceDTO.getChildren())) {
                continue;
            }
            String key = instanceDTO.getModelType() + Constants.UNDER_LINE + instanceDTO.getInstanceCode();
            // 获取现有的列表
            List<InstanceDTO> existingList = map.get(key);
            if (existingList == null) {
                // 如果key不存在，直接放入新列表
                map.put(key, new ArrayList<>(instanceDTO.getChildren()));
            } else {
                // 如果key存在，只添加不重复的元素
                for (InstanceDTO child : instanceDTO.getChildren()) {
                    if (existingList.stream().noneMatch(e ->
                            Objects.equals(e.getInstanceId(), child.getInstanceId()) &&
                                    Objects.equals(e.getInstanceCode(), child.getInstanceCode()))) {
                        existingList.add(child);
                    }
                }
            }
            getMap(instanceDTO.getChildren(), map);
        }
        // 处理设备实例树
        for (InstanceDTO instanceDTO : deviceInstanceTree) {
            if (CollectionUtils.isEmpty(instanceDTO.getChildren())) {
                continue;
            }
            String key = instanceDTO.getModelType() + Constants.UNDER_LINE + instanceDTO.getInstanceCode();
            List<InstanceDTO> existingList = map.get(key);
            if (existingList == null) {
                map.put(key, new ArrayList<>(instanceDTO.getChildren()));
            } else {
                for (InstanceDTO child : instanceDTO.getChildren()) {
                    if (existingList.stream().noneMatch(e ->
                            Objects.equals(e.getInstanceId(), child.getInstanceId()) &&
                                    Objects.equals(e.getInstanceCode(), child.getInstanceCode()))) {
                        existingList.add(child);
                    }
                }
            }
            getMap(instanceDTO.getChildren(), map);
        }
        return map;
    }

    private static void getMap(List<InstanceDTO> childrens, Map<String, List<InstanceDTO>> map) {
        if (CollectionUtils.isEmpty(childrens)) {
            return;
        }
        for (InstanceDTO instanceDTO : childrens) {
            if (CollectionUtils.isEmpty(instanceDTO.getChildren())) {
                continue;
            }
            String key = instanceDTO.getModelType() + Constants.UNDER_LINE + instanceDTO.getInstanceCode();
            // 获取现有的列表
            List<InstanceDTO> existingList = map.get(key);
            if (existingList == null) {
                // 如果key不存在，直接放入新列表
                map.put(key, new ArrayList<>(instanceDTO.getChildren()));
            } else {
                // 如果key存在，只添加不重复的元素
                for (InstanceDTO child : instanceDTO.getChildren()) {
                    if (existingList.stream().noneMatch(e ->
                            Objects.equals(e.getInstanceId(), child.getInstanceId()) &&
                                    Objects.equals(e.getInstanceCode(), child.getInstanceCode()))) {
                        existingList.add(child);
                    }
                }
            }
            getMap(instanceDTO.getChildren(), map);
        }
    }

    /**
     * 同步下级的班次日历
     */
    private void syncSubWorkCalendar(WorkCalendarSyncDTO calendarSyncDTO, Map<String, List<InstanceDTO>> map) {
        String key = calendarSyncDTO.getModelType() + Constants.UNDER_LINE + calendarSyncDTO.getInstanceCode();
        if (!map.containsKey(key)) {
            return;
        }
        String username = userAuthenService.getUsername();
        Date date = new Date();
        List<InstanceDTO> instanceDTOS = map.get(key);
        for (InstanceDTO instanceDTO : instanceDTOS) {
            // 如果下级存在相同班次，则更新它的工作时间和休息时间
            WorkCalendarEntity workCalendarEntity = this.lambdaQuery()
                    .eq(WorkCalendarEntity::getModelType, instanceDTO.getModelType())
                    .eq(WorkCalendarEntity::getInstanceId, instanceDTO.getInstanceId())
                    .eq(WorkCalendarEntity::getName, calendarSyncDTO.getName())
                    .one();
            if (Objects.nonNull(workCalendarEntity)) {
                this.lambdaUpdate().eq(WorkCalendarEntity::getName, calendarSyncDTO.getName())
                        .eq(WorkCalendarEntity::getInstanceId, instanceDTO.getInstanceId())
                        .set(WorkCalendarEntity::getWorkCalendarTime, calendarSyncDTO.getWorkCalendarTime())
                        .set(WorkCalendarEntity::getDownTime, calendarSyncDTO.getDownTime())
                        .update();
            } else {
                // 不存在则新增班次
                WorkCalendarEntity subEntity = WorkCalendarEntity.builder()
                        .instanceId(instanceDTO.getInstanceId())
                        .instanceCode(instanceDTO.getInstanceCode())
                        .instanceName(instanceDTO.getInstanceName())
                        .modelType(instanceDTO.getModelType())
                        .name(calendarSyncDTO.getName())
                        .workCalendarTime(calendarSyncDTO.getWorkCalendarTime())
                        .downTime(calendarSyncDTO.getDownTime())
                        .children(instanceDTO.getChildren())
                        .createTime(date)
                        .createBy(username)
                        .build();
                save(subEntity);
            }
            WorkCalendarSyncDTO subCalendarSyncDTO = WorkCalendarSyncDTO.builder()
                    .modelType(instanceDTO.getModelType())
                    .instanceCode(instanceDTO.getInstanceCode())
                    .name(calendarSyncDTO.getName())
                    .workCalendarTime(calendarSyncDTO.getWorkCalendarTime())
                    .downTime(calendarSyncDTO.getDownTime())
                    .build();
            // 继续同步下级的班次日历
            syncSubWorkCalendar(subCalendarSyncDTO, map);
        }
    }

    /**
     * 格式化班次时间
     */
    @Override
    public String setWorkCalendarTimes(List<TimeRangeDTO> workCalendarTimes) {
        String str = "";
        //第一层判断：不确定前端传给后台是否为空
        if (CollectionUtils.isNotEmpty(workCalendarTimes)) {
            workCalendarTimes = workCalendarTimes.stream().filter(o -> StringUtils.isNotBlank(o.getStart()) && StringUtils.isNotBlank(o.getEnd())).collect(Collectors.toList());
            //第二层判断：不确定前端传给后台是否仅仅是值为空，即start:""这种
            if (CollectionUtils.isNotEmpty(workCalendarTimes)) {
                //格式:HH:mm:ss-HH:mm:ss,HH:mm:ss-HH:mm:ss
                List<String> strings = new ArrayList<>(workCalendarTimes.size());
                for (TimeRangeDTO workCalendarTime : workCalendarTimes) {
                    strings.add(workCalendarTime.getStart().trim() + DASH + workCalendarTime.getEnd().trim());
                }
                str = String.join(COMMA, strings);
            }
        }
        return str;
    }

    @Override
    public void updateCalendar(WorkCalendarEntity entity) {
        if (CollectionUtils.isEmpty(entity.getWorkCalendarTimes())) {
            throw new ResponseException(RespCodeEnum.CALENDAR_DATE_IS_BLANK);
        }
        // 格式化班次时间
        String workCalendarTime = setWorkCalendarTimes(entity.getWorkCalendarTimes());
        entity.setWorkCalendarTime(workCalendarTime);
        // 格式化非生产时间
        String downTime = setDownTimes(entity.getDownTimes());
        entity.setDownTime(downTime);
        LambdaUpdateWrapper<WorkCalendarEntity> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(WorkCalendarEntity::getCalendarId, entity.getCalendarId())
                .set(WorkCalendarEntity::getName, entity.getName())
                .set(WorkCalendarEntity::getValidityStart, entity.getValidityStart())
                .set(WorkCalendarEntity::getValidityEnd, entity.getValidityEnd())
                .set(WorkCalendarEntity::getDownTime, entity.getDownTime())
                .set(WorkCalendarEntity::getWorkCalendarTime, entity.getWorkCalendarTime())
                .set(WorkCalendarEntity::getDescription, entity.getDescription())
                .set(WorkCalendarEntity::getUpdateBy, entity.getUpdateBy())
                .set(WorkCalendarEntity::getUpdateTime, entity.getUpdateTime());
        this.update(updateWrapper);
        if (!entity.getModelType().equals(WorkCenterTypeEnum.TEAM.getCode())) {
            WorkCalendarSyncDTO calendarSyncDTO = WorkCalendarSyncDTO.builder()
                    .modelType(entity.getModelType())
                    .instanceCode(entity.getInstanceCode())
                    .name(entity.getName())
                    .workCalendarTime(workCalendarTime)
                    .downTime(downTime)
                    .build();
            addOrUpdateSubCalendar(calendarSyncDTO);
        }
        //推送到doc模块
        messagePushToKafkaService.pushNewMessage(entity, Constants.KAFKA_MAIN_DATA_TOPIC, KafkaMessageTypeEnum.WORK_CALENDAR_UPDATE_MESSAGE);
        redisTemplate.delete(redisTemplate.keys(RedisKeyPrefix.TODAY_WORK_TIME + "*"));
    }

    /**
     * 字符转为List
     *
     * @param entity
     */
    private void getDownTimes(WorkCalendarEntity entity) {
        String downTime = entity.getDownTime();
        if (StringUtils.isNotBlank(downTime)) {
            //格式:name HH:mm:ss-HH:mm:ss,name HH:mm:ss-HH:mm:ss
            List<TimeRangeDTO> dtos = new ArrayList<>();
            String[] eachOnes = downTime.split(COMMA);
            for (String eachOne : eachOnes) {
                String[] strings = eachOne.split(SPACE);
                String name = strings[0];
                String[] times = strings[1].split(DASH);
                dtos.add(TimeRangeDTO.builder().name(name).start(times[0]).end(times[1]).build());
            }
            entity.setDownTimes(dtos);
        }
    }

    /**
     * 格式化非生产时间
     */
    @Override
    public String setDownTimes(List<TimeRangeDTO> downTimes) {
        String str = "";
        //第一层判断：不确定前端传给后台是否为空
        if (CollectionUtils.isNotEmpty(downTimes)) {
            downTimes = downTimes.stream().filter(o -> StringUtils.isNotBlank(o.getStart()) && StringUtils.isNotBlank(o.getEnd())).collect(Collectors.toList());
            //第二层判断：不确定前端传给后台是否仅仅是值为空，即start:""这种
            if (CollectionUtils.isNotEmpty(downTimes)) {
                //格式:name HH:mm:ss-HH:mm:ss,name HH:mm:ss-HH:mm:ss
                List<String> strings = new ArrayList<>(downTimes.size());
                for (TimeRangeDTO downTime : downTimes) {
                    strings.add(downTime.getName().trim() + SPACE + downTime.getStart().trim() + DASH + downTime.getEnd().trim());
                }
                str = String.join(COMMA, strings);
            }
        }
        return str;
    }

    /**
     * 日历汇总
     *
     * @param modelType
     * @param instanceId
     * @param yearAndMonth
     * @return
     */
    @Override
    public CalendarTableDTO getCalendarSummary(String modelType, Integer instanceId, String yearAndMonth) {
        List<Date> dates = getDates(yearAndMonth);
        Page<WorkCalendarEntity> page = this.getList(modelType, instanceId, null, null);
        List<WorkCalendarEntity> calendarEntities = page.getRecords();

        Page<WorkCalendarDownEntity> downPage = downService.getList(modelType, instanceId, null, null);
        List<WorkCalendarDownEntity> downEntities = downPage.getRecords();

        List<CalendarCellDTO> dtos = new ArrayList<>();
        List<String> allNames = new ArrayList<>();
        for (Date date : dates) {
            //计算这天的总工时
            List<TimeRangeDTO> workTimes = new ArrayList<>();
            List<TimeRangeDTO> downTimes = new ArrayList<>();
            setWorkTimesAndDownTimes(date, calendarEntities, workTimes, downTimes);
            setDownTimes(date, downEntities, downTimes);
            if (CollectionUtils.isEmpty(workTimes) && CollectionUtils.isEmpty(downTimes)) {
                dtos.add(CalendarCellDTO.builder().date(date).build());
                continue;
            }
            //去重
            List<TimeRangeDTO> works = removeDuplicates(workTimes);
            List<TimeRangeDTO> downDtos = removeDuplicates(downTimes);
            double duration = 0;
            if (CollectionUtils.isNotEmpty(works)) {
                for (TimeRangeDTO dto : works) {
                    double duration1 = getDuration(dto, downDtos);
                    duration += duration1;
                }
            }
            String dateType = duration == 0 ? REST_DAY : WORK_DAY;
            dtos.add(CalendarCellDTO.builder().date(date).workTimes(workTimes).duration(duration).dateType(dateType).build());
            if (duration == 0) {
                allNames.add(REST_DAY);
            } else {
                List<String> collect = workTimes.stream().map(TimeRangeDTO::getName).collect(Collectors.toList());
                allNames.addAll(collect);
            }
        }
        Map<String, Long> countMap = allNames.stream().collect(Collectors.groupingBy(s -> s, Collectors.counting()));
        SimpleDateFormat format = new SimpleDateFormat(DateUtil.MONTH_FORMAT);
        String finalYearAndMonth = StringUtils.isBlank(yearAndMonth) ? format.format(new Date()) : yearAndMonth;
        List<CalendarCellDTO> collect = dtos.stream().filter(o -> o.getDuration() != null)
                .filter(o -> !finalYearAndMonth.equals(format.format(o.getDate()))).collect(Collectors.toList());
        for (CalendarCellDTO dto : collect) {
            if (dto.getDuration() > 0) {
                List<TimeRangeDTO> workTimes = dto.getWorkTimes();
                for (TimeRangeDTO time : workTimes) {
                    String name = time.getName();
                    if (countMap.containsKey(name)) {
                        long aLong = countMap.get(name) - 1;
                        countMap.put(name, aLong < 0 ? 0 : aLong);
                    }
                }
            } else {
                if (countMap.containsKey(REST_DAY)) {
                    long aLong = countMap.get(REST_DAY) - 1;
                    countMap.put(REST_DAY, aLong < 0 ? 0 : aLong);
                }
            }
        }
        List<DateDTO> counts = new ArrayList<>();
        for (Map.Entry<String, Long> entry : countMap.entrySet()) {
            counts.add(DateDTO.builder().name(entry.getKey()).date(entry.getValue().intValue()).build());
        }
        return CalendarTableDTO.builder().cells(dtos).counts(counts).build();
    }

    private List<Date> getDates(String yearAndMonth) {
        List<Date> betweens;
        if (StringUtils.isBlank(yearAndMonth)) {
            betweens = DateUtil.getMonthStartAndEnd(new Date());
        } else {
            SimpleDateFormat format = new SimpleDateFormat(DateUtil.MONTH_FORMAT);
            Date parse = null;
            try {
                parse = format.parse(yearAndMonth);
            } catch (ParseException e) {
                log.error("日期转换错误", e);
            }
            betweens = DateUtil.getMonthStartAndEnd(parse);
        }
        Date start = betweens.get(0);
        Date end = betweens.get(1);
        int weekOfStart = DateUtil.getDayOfWeek(start);
        int weekOfEnd = DateUtil.getDayOfWeek(end);
        while (weekOfStart != WeekEnum.MONDAY.getCode()) {
            start = DateUtil.addDate(start, -1);
            weekOfStart = DateUtil.getDayOfWeek(start);
        }
        while (weekOfEnd != WeekEnum.SUNDAY.getCode()) {
            end = DateUtil.addDate(end, 1);
            weekOfEnd = DateUtil.getDayOfWeek(end);
        }
        return DateUtil.getBetweenDate(start, end);
    }

    /**
     * 去除交集部分
     * 注：此方法只有StartDate、EndDate字段可用
     *
     * @param list
     * @return
     */
    private static List<TimeRangeDTO> removeDuplicates(List<TimeRangeDTO> list) {
        List<TimeRangeDTO> dtos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            list.sort(Comparator.comparing(TimeRangeDTO::getStartDate));
            for (int i = 0; i < list.size() - 1; i++) {
                TimeRangeDTO former = list.get(i);
                TimeRangeDTO latter = list.get(i + 1);
                //集合已为开始时间排过正序，已确定前者开始时间比后者的要早，只需确定结束时间的关系
                if (!former.getEndDate().isAfter(latter.getStartDate())) {
                    //前者的结束时间不晚于后者的开始时间，即无交集
                    //此处将前者加入合格列,此判断语句一定要在第一个，且此处一定要continue，因为这也意味着前者结束时间早于后者
                    dtos.add(former);
                    continue;
                }
                if (!former.getEndDate().isAfter(latter.getEndDate())) {
                    //前者结束时间早于后者，即为部分交集，合并成一个新的区间，并赋值给后者，下一次遍历时作为“前者”
                    //此处改变了对象的属性，原集合元素的属性也会改变，此做法不建议，但此字段暂无他用，暂无影响
                    latter.setStartDate(former.getStartDate());
                    continue;
                }
                if (!former.getEndDate().isBefore(latter.getEndDate())) {
                    //前者结束时间不早于后者，即为前者包含后者，保留前者即可
                    latter.setStartDate(former.getStartDate());
                    latter.setEndDate(former.getEndDate());
                }
            }
            //遍历完后要把集合最后一个元素加入合格列
            dtos.add(list.get(list.size() - 1));
        }
        return dtos;
    }

    private double getDuration(TimeRangeDTO dto, List<TimeRangeDTO> downDtos) {

        LocalDateTime startDate = dto.getStartDate();
        LocalDateTime endDate = dto.getEndDate();
        if (CollectionUtils.isEmpty(downDtos)) {
            Duration between = Duration.between(startDate, endDate);
            return MathUtil.divideDouble(between.getSeconds(), (60 * 60), 2);
        }
        downDtos.sort(Comparator.comparing(TimeRangeDTO::getEndDate));
        LocalDateTime last = downDtos.get(downDtos.size() - 1).getEndDate();
        downDtos.sort(Comparator.comparing(TimeRangeDTO::getStartDate));
        LocalDateTime first = downDtos.get(0).getStartDate();
        //此时集合以开始时间排正序
        if (!startDate.isBefore(last) || !endDate.isAfter(first)) {
            //开始时间不早于非工作的结束时间||结束时间不晚于非工作的开始时间(这样写才能相等时也返回true)
            Duration between = Duration.between(startDate, endDate);
            return MathUtil.divideDouble(between.getSeconds(), (60 * 60), 2);
        }

        List<TimeRangeDTO> copys = new ArrayList<>(downDtos);
        for (TimeRangeDTO copy : downDtos) {
            LocalDateTime downStart = copy.getStartDate();
            LocalDateTime downEnd = copy.getEndDate();
            //没有任何交集
            if (startDate.isAfter(downEnd) || endDate.isBefore(downStart)) {
                copys.remove(copy);
                continue;
            }
            //起点之间有交集
            if (startDate.isAfter(downStart) && startDate.isBefore(downEnd)) {
                startDate = downEnd;
                copys.remove(copy);
                continue;
            }
            //终点之间有交集
            if (endDate.isAfter(downStart) && endDate.isBefore(downEnd)) {
                endDate = downStart;
                copys.remove(copy);
            }
        }
        Duration between = Duration.between(startDate, endDate);
        double duration = MathUtil.divideDouble(between.getSeconds(), (60 * 60), 2);
        //剩下并集
        //copys已经去过重，没有重叠部分
        if (CollectionUtils.isNotEmpty(copys) && duration > 0) {
            long downSeconds = 0;
            for (TimeRangeDTO copy : copys) {
                Duration between1 = Duration.between(copy.getStartDate(), copy.getEndDate());
                downSeconds += between1.getSeconds();
            }
            double downHours = MathUtil.divideDouble(downSeconds, (60 * 60), 2);
            duration = duration - downHours;
        }
        return duration < 0 ? 0 : duration;
    }

    /**
     * 追溯日历，返回当天工作时长
     *
     * @param instanceId
     * @param modelType
     * @param date
     * @return
     */
    @Override
    public Double traceWorkDuration(Integer instanceId, String modelType, Date date) {

        List<InstanceDTO> instanceTree = modelService.getInstanceTree(modelType);
        List<InstanceDTO> list = modelService.traceInstanceTree(instanceTree, instanceId, modelType);
        if (CollectionUtils.isNotEmpty(list)) {
            Date recordDate = dictService.getRecordDate(date);
            for (InstanceDTO instanceDTO : list) {
                String currentModelType = instanceDTO.getModelType();
                Integer currentInstanceId = instanceDTO.getInstanceId();

                Page<WorkCalendarEntity> page = this.getList(currentModelType, currentInstanceId, null, null);
                List<WorkCalendarEntity> calendarEntities = page.getRecords();

                Page<WorkCalendarDownEntity> downPage = downService.getList(currentModelType, currentInstanceId, null, null);
                List<WorkCalendarDownEntity> downEntities = downPage.getRecords();

                List<TimeRangeDTO> workTimes = new ArrayList<>();
                List<TimeRangeDTO> downTimes = new ArrayList<>();
                //工作时间
                setWorkTimesAndDownTimes(recordDate, calendarEntities, workTimes, downTimes);
                //停产计划
                setDownTimes(recordDate, downEntities, downTimes);
                if (CollectionUtils.isEmpty(workTimes) && CollectionUtils.isEmpty(downTimes)) {
                    continue;
                }
                List<TimeRangeDTO> works = removeDuplicates(workTimes);
                List<TimeRangeDTO> downDtos = removeDuplicates(downTimes);
                double duration = 0;
                if (CollectionUtils.isNotEmpty(works)) {
                    for (TimeRangeDTO dto : works) {
                        double duration1 = getDuration(dto, downDtos);
                        duration += duration1;
                    }
                }
                return duration;
            }
        }
        return null;
    }

    /**
     * @param date             记录时间 recordDate, 没有时分秒
     * @param calendarEntities
     * @param workTimes
     * @param downTimes
     */
    private void setWorkTimesAndDownTimes(Date date, List<WorkCalendarEntity> calendarEntities,
                                          List<TimeRangeDTO> workTimes, List<TimeRangeDTO> downTimes) {
        if (CollectionUtils.isEmpty(calendarEntities)) {
            return;
        }
        List<WorkCalendarEntity> workDays = calendarEntities.stream()
                .filter(o -> (o.getValidityStart() == null && o.getValidityEnd() == null)
                        || (o.getValidityEnd() == null && o.getValidityStart() != null && !o.getValidityStart().after(date))
                        || (o.getValidityStart() == null && o.getValidityEnd() != null && !o.getValidityEnd().before(date))
                        || ((o.getValidityStart() != null && o.getValidityEnd() != null)
                        && (!o.getValidityStart().after(date) && !o.getValidityEnd().before(date))))
                .collect(Collectors.toList());

        LocalDate now = LocalDate.parse(DateUtil.dateStr(date), DateTimeFormatter.ofPattern(DateUtil.DATE_FORMAT));

        for (WorkCalendarEntity entity : workDays) {
            // 班次时间
            getWorkCalendarTimes(entity);
            List<TimeRangeDTO> workCalendarTimes = entity.getWorkCalendarTimes();
            for (TimeRangeDTO timeRangeDTO : workCalendarTimes) {
                String start = timeRangeDTO.getStart();
                String end = timeRangeDTO.getEnd();
                LocalDateTime startDate = LocalDateTime.of(now, LocalTime.parse(start));
                LocalDateTime endDate = LocalDateTime.of(now, LocalTime.parse(end));
                endDate = startDate.isAfter(endDate) ? endDate.plusDays(1) : endDate;
                workTimes.add(TimeRangeDTO.builder().name(entity.getName()).start(start).end(end).startDate(startDate).endDate(endDate).build());
            }
            // 休息时间
            List<TimeRangeDTO> rangeDTOList = entity.getDownTimes();
            if (CollectionUtils.isEmpty(rangeDTOList)) {
                continue;
            }
            for (TimeRangeDTO dto : rangeDTOList) {
                LocalDateTime parse = LocalDateTime.of(now, LocalTime.parse(dto.getStart()));
                LocalDateTime parse1 = LocalDateTime.of(now, LocalTime.parse(dto.getEnd()));
                parse1 = parse.isAfter(parse1) ? parse1.plusDays(1) : parse1;
                dto.setStartDate(parse);
                dto.setEndDate(parse1);
                downTimes.add(dto);
            }
        }
    }

    /**
     * @param date             开工时间
     * @param calendarEntities
     * @param workTimes
     * @param downTimes
     */
    private void setWorkTimesAndDownTimes2(Date date, List<WorkCalendarEntity> calendarEntities,
                                           List<TimeRangeDTO> workTimes, List<TimeRangeDTO> downTimes) {
        if (CollectionUtils.isEmpty(calendarEntities)) {
            return;
        }
        List<WorkCalendarEntity> workDays = calendarEntities.stream()
                .filter(o -> (o.getValidityStart() == null && o.getValidityEnd() == null)
                        || (o.getValidityEnd() == null && o.getValidityStart() != null && !o.getValidityStart().after(date))
                        || (o.getValidityStart() == null && o.getValidityEnd() != null && !o.getValidityEnd().before(date))
                        || ((o.getValidityStart() != null && o.getValidityEnd() != null)
                        && (!o.getValidityStart().after(date) && !o.getValidityEnd().before(date))))
                .collect(Collectors.toList());
        // 拿开工时间做对比
        LocalDateTime beginDateTime = DateUtil.parseToLocalDateTime(date, DateUtil.DATETIME_FORMAT);
        LocalDate now = LocalDate.parse(DateUtil.dateStr(date), DateTimeFormatter.ofPattern(DateUtil.DATE_FORMAT));

        for (WorkCalendarEntity entity : workDays) {
            // 班次时间
            getWorkCalendarTimes(entity);
            List<TimeRangeDTO> workCalendarTimes = entity.getWorkCalendarTimes();
            for (TimeRangeDTO timeRangeDTO : workCalendarTimes) {
                String start = timeRangeDTO.getStart();
                String end = timeRangeDTO.getEnd();
                LocalDateTime startDate = LocalDateTime.of(now, LocalTime.parse(start));
                LocalDateTime endDate = LocalDateTime.of(now, LocalTime.parse(end));
                endDate = beginDateTime.isAfter(endDate) ? endDate.plusDays(1) : endDate;
                end = DateUtil.DF.format(endDate);
                workTimes.add(TimeRangeDTO.builder().name(entity.getName()).start(start).end(end).startDate(startDate).endDate(endDate).build());
            }
            List<TimeRangeDTO> rangeDTOList = entity.getDownTimes();
            if (CollectionUtils.isEmpty(rangeDTOList)) {
                continue;
            }
            for (TimeRangeDTO dto : rangeDTOList) {
                String downStart = dto.getStart();
                String downEnd = dto.getEnd();
                LocalDateTime downStartDate = LocalDateTime.of(now, LocalTime.parse(downStart));
                LocalDateTime downEndDate = LocalDateTime.of(now, LocalTime.parse(downEnd));
                downStartDate = beginDateTime.isAfter(downStartDate) ? downStartDate.plusDays(1) : downStartDate;
                downStart = DateUtil.DF.format(downStartDate);
                downEndDate = beginDateTime.isAfter(downEndDate) ? downEndDate.plusDays(1) : downEndDate;
                downEnd = DateUtil.DF.format(downEndDate);
                downTimes.add(TimeRangeDTO.builder().name(dto.getName()).start(downStart).end(downEnd).startDate(downStartDate).endDate(downEndDate).build());
            }
        }
    }

    private void setDownTimes(Date date, List<WorkCalendarDownEntity> downEntities, List<TimeRangeDTO> downTimes) {
        //停产计划
        if (CollectionUtils.isEmpty(downEntities)) {
            return;
        }
        List<WorkCalendarDownEntity> downDays = downEntities.stream()
                .filter(o -> (o.getValidityStart() == null && o.getValidityEnd() == null)
                        || (o.getValidityEnd() == null && o.getValidityStart() != null && !o.getValidityStart().after(date))
                        || (o.getValidityStart() == null && o.getValidityEnd() != null && !o.getValidityEnd().before(date))
                        || ((o.getValidityStart() != null && o.getValidityEnd() != null)
                        && (!o.getValidityStart().after(date) && !o.getValidityEnd().before(date))))
                .collect(Collectors.toList());
        //再从中筛出指定了周几，这天又不是这周几的，删掉
        int dayOfWeek = DateUtil.getDayOfWeek(date);
        List<WorkCalendarDownEntity> noWeeks = downDays.stream()
                .filter(o -> o.getWeek() != null && o.getWeek() != dayOfWeek).collect(Collectors.toList());
        downDays.removeAll(noWeeks);

        // 拿开工时间做对比
        LocalDateTime beginDateTime = DateUtil.parseToLocalDateTime(date, DateUtil.DATETIME_FORMAT);
        LocalDate now = LocalDate.parse(DateUtil.dateStr(date), DateTimeFormatter.ofPattern(DateUtil.DATE_FORMAT));

        for (WorkCalendarDownEntity entity : downDays) {
            String start = entity.getStart();
            String end = entity.getEnd();
            LocalDateTime startDate = LocalDateTime.of(now, LocalTime.parse(start));
            LocalDateTime endDate = LocalDateTime.of(now, LocalTime.parse(end));
            startDate = beginDateTime.isAfter(startDate) ? startDate.plusDays(1) : startDate;
            start = DateUtil.DF.format(startDate);
            endDate = beginDateTime.isAfter(endDate) ? endDate.plusDays(1) : endDate;
            end = DateUtil.DF.format(endDate);
            downTimes.add(TimeRangeDTO.builder().name(entity.getName()).start(start).end(end).startDate(startDate).endDate(endDate).build());
        }
    }

    @Override
    public Double getWorkDurationUpToNow(Integer instanceId, String modelType, Date now) {
        List<InstanceDTO> instanceTree = modelService.getInstanceTree(modelType);
        List<InstanceDTO> list = modelService.traceInstanceTree(instanceTree, instanceId, modelType);
        if (CollectionUtils.isNotEmpty(list)) {
            Date begin = dictService.getDayOutputBeginTime(now);
            for (InstanceDTO instanceDTO : list) {
                String currentModelType = instanceDTO.getModelType();
                Integer currentInstanceId = instanceDTO.getInstanceId();

                List<WorkCalendarEntity> calendarEntities = getWorkCalendarEntities(currentModelType, currentInstanceId, now);

                Page<WorkCalendarDownEntity> downPage = downService.getList(currentModelType, currentInstanceId, null, null);
                List<WorkCalendarDownEntity> downEntities = downPage.getRecords();

                List<TimeRangeDTO> workTimes = new ArrayList<>();
                List<TimeRangeDTO> downTimes = new ArrayList<>();
                //工作时间
                setWorkTimesAndDownTimes(begin, calendarEntities, workTimes, downTimes);
                //停产计划
                setDownTimes(begin, downEntities, downTimes);
                if (CollectionUtils.isEmpty(workTimes) && CollectionUtils.isEmpty(downTimes)) {
                    continue;
                }
                List<TimeRangeDTO> works = removeDuplicates(workTimes);
                //截取到now时间点的工作时间
                works = cutWorkTimesToNow(works, now);
                List<TimeRangeDTO> downDtos = removeDuplicates(downTimes);
                double duration = 0;
                if (CollectionUtils.isNotEmpty(works)) {
                    for (TimeRangeDTO dto : works) {
                        double duration1 = getDuration(dto, downDtos);
                        duration += duration1;
                    }
                }
                return duration;
            }
        }
        return 0.0;
    }

    @Override
    public Map<Integer, Double> getWorkDurationUpInTimeRange(Integer instanceId, String modelType, List<WorkDurationDTO> rangeList) {
        if (CollectionUtils.isEmpty(rangeList)) {
            return Maps.newHashMap();
        }
        Map<Integer, Double> result = rangeList.stream().collect(Collectors.toMap(WorkDurationDTO::getId, e -> 0d, (v1, v2) -> v1));
        // 查关系树
        List<InstanceDTO> instanceTree = modelService.getInstanceTree(modelType);
        // 向上平铺 -> 方便找不到自己的，就向上查
        List<InstanceDTO> list = modelService.traceInstanceTree(instanceTree, instanceId, modelType);
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }
        // 获取 工作日历 与 停产计划
        List<WorkCalendarEntity> calendarEntities = null;
        List<WorkCalendarDownEntity> downEntities = null;
        for (InstanceDTO instanceDTO : list) {
            String currentModelType = instanceDTO.getModelType();
            Integer currentInstanceId = instanceDTO.getInstanceId();
            // 这里now传null, 因此不支持设备的日历
            calendarEntities = getWorkCalendarEntities(currentModelType, currentInstanceId, null);
            downEntities = downService.getList(currentModelType, currentInstanceId, null, null).getRecords();
            if (CollectionUtils.isNotEmpty(calendarEntities)) {
                break;
            }
        }
        if (CollectionUtils.isEmpty(calendarEntities)) {
            return result;
        }
        for (WorkDurationDTO range : rangeList) {
            Date start = range.getStart();
            Date end = range.getEnd();
            if (start.after(end)) {
                result.put(range.getId(), 0d);
                continue;
            }
            // 某一天开工时间: 从起始的第一天开始算
            Date startBegin = dictService.getDayOutputBeginTime(start);
            Date curBegin = startBegin;
            Date endBegin = dictService.getDayOutputBeginTime(end);
            // 遍历时间：
            boolean flag = true;
            // 统计总时间长度时间
            double duration = 0;
            while (flag) {
                // 统计：工作时间
                List<TimeRangeDTO> workTimes = new ArrayList<>();
                // 统计：休息时间
                List<TimeRangeDTO> downTimes = new ArrayList<>();
                // 查工作日历
                setWorkTimesAndDownTimes2(curBegin, calendarEntities, workTimes, downTimes);
                // 查停产计划
                setDownTimes(curBegin, downEntities, downTimes);
                // 去重
                List<TimeRangeDTO> works = removeDuplicates(workTimes);
                List<TimeRangeDTO> downDtos = removeDuplicates(downTimes);
                // 下一天的开工时间
                Date nextBegin = DateUtil.addDate(curBegin, 1);
                // 标记点
                Date now;
                // 下一次开工时间比截至时间少，结束遍历时间
                if (nextBegin.after(end)) {
                    flag = false;
                    // 标记点为当日截至时间
                    // 找当日已完成的的负荷量
                    works = cutWorkTimesToNow(works, end);
                    // 如果开始日期与截至日期的开工时间相同， 需要扣除（标记点为当日开始时间，已完成的负荷量）
                    if (endBegin.compareTo(startBegin) == 0) {
                        // 这里将其作为休息时间处理
                        List<TimeRangeDTO> subList = cutWorkTimesToNow(works, start);
                        downDtos.addAll(subList);
                        downDtos = removeDuplicates(downDtos);
                    }
                } else {
                    // 标记点为较晚的起始时间
                    now = curBegin.after(start) ? curBegin : start;
                    // 找当日剩余的负荷量
                    works = cutWorkTimesAfterNow(works, now);
                }
                if (CollectionUtils.isNotEmpty(works)) {
                    for (TimeRangeDTO dto : works) {
                        duration += getDuration(dto, downDtos);
                    }
                }
                // 时间向前滚
                curBegin = nextBegin;
            }
            result.put(range.getId(), duration);
        }
        return result;
    }

    @Override
    public Double getWorkDurationUpInTimeRange(Integer instanceId, String modelType, Date start, Date end) {
        Integer id = 0;
        WorkDurationDTO dto = WorkDurationDTO.builder().id(id).start(start).end(end).build();
        Map<Integer, Double> resultMap = getWorkDurationUpInTimeRange(instanceId, modelType, Collections.singletonList(dto));
        return resultMap.get(id);
    }

    @Override
    public boolean inWorkTime(Integer instanceId, String modelType, Date date) {

        LocalDateTime localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();

        List<TimeRangeDTO> workTimeRange = getWorkTimeRange(instanceId, modelType, date);
        //1.15.5设备是否在工作时间，不在则不能上告警
        //没配日历默认一直在工作时间
        if (CollectionUtils.isEmpty(workTimeRange)) {
            return true;
        }
        for (TimeRangeDTO timeRange : workTimeRange) {
            if (dateIsBetween(timeRange.getStartDate(), timeRange.getEndDate(), localDate)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public List<TimeRangeDTO> getWorkTimeRange(Integer instanceId, String modelType, Date now) {

//        Date recordDate = dictService.getRecordDate(now);
        //拿到对应模块下的所有设备
        Date recordDate = DateUtil.parse((String) redisTemplate.opsForValue().get(RedisKeyPrefix.RECORD_DATE), DateUtil.DATETIME_FORMAT);
        if (recordDate == null) {
            recordDate = dictService.getRecordDate(now);
            redisTemplate.opsForValue().set(RedisKeyPrefix.RECORD_DATE, DateUtil.format(recordDate, DateUtil.DATETIME_FORMAT), 1, TimeUnit.HOURS);
        }
        String todayWorkTimeKey = RedisKeyPrefix.getTodayWorkTime(DateUtil.dateStr(recordDate), instanceId, modelType);

        if (redisTemplate.hasKey(todayWorkTimeKey)) {
            return JSONArray.parseArray((String) redisTemplate.opsForValue().get(todayWorkTimeKey), TimeRangeDTO.class);
        }

        //缓存没有值，则设置
        List<InstanceDTO> instanceTree = modelService.getInstanceTree(modelType);
        List<InstanceDTO> list = modelService.traceInstanceTree(instanceTree, instanceId, modelType);
        if (CollectionUtils.isEmpty(list)) {
            List arrayList = new ArrayList<>();
            redisTemplate.opsForValue().set(todayWorkTimeKey, JSON.toJSONString(arrayList), 24, TimeUnit.HOURS);
            return arrayList;
        }
        for (InstanceDTO instanceDTO : list) {
            String currentModelType = instanceDTO.getModelType();
            Integer currentInstanceId = instanceDTO.getInstanceId();

            List<WorkCalendarEntity> calendarEntities = getWorkCalendarEntities(currentModelType, currentInstanceId, now);

            Page<WorkCalendarDownEntity> downPage = downService.getList(currentModelType, currentInstanceId, null, null);
            List<WorkCalendarDownEntity> downEntities = downPage.getRecords();

            List<TimeRangeDTO> workTimes = new ArrayList<>();
            List<TimeRangeDTO> downTimes = new ArrayList<>();
            //工作时间
            setWorkTimesAndDownTimes(recordDate, calendarEntities, workTimes, downTimes);
            //停产计划
            setDownTimes(recordDate, downEntities, downTimes);
            if (CollectionUtils.isEmpty(workTimes) && CollectionUtils.isEmpty(downTimes)) {
                continue;
            }
            List<TimeRangeDTO> works = removeDuplicates(workTimes);
            List<TimeRangeDTO> downs = removeDuplicates(downTimes);
            //工作时长去掉休息时长
            workTimesMinusDownTimes(works, downs);
            works.sort(Comparator.comparing(TimeRangeDTO::getStartDate));
            redisTemplate.opsForValue().set(todayWorkTimeKey, JSON.toJSONString(works), 24, TimeUnit.HOURS);
            return works;
        }

        List<TimeRangeDTO> arrayList = new ArrayList<>();
        redisTemplate.opsForValue().set(todayWorkTimeKey, JSON.toJSONString(arrayList), 24, TimeUnit.HOURS);
        return arrayList;
    }


    /**
     * 工作时间剔除休息时间
     *
     * @param works
     * @param downs
     */
    private static void workTimesMinusDownTimes(List<TimeRangeDTO> works, List<TimeRangeDTO> downs) {

        if (CollectionUtils.isEmpty(works) || CollectionUtils.isEmpty(downs)) {
            return;
        }
        Iterator<TimeRangeDTO> workTimeIterator = works.iterator();
        int size = works.size();

        while (workTimeIterator.hasNext()) {

            TimeRangeDTO workTime = workTimeIterator.next();
            LocalDateTime workStartDate = workTime.getStartDate();
            LocalDateTime workEndDate = workTime.getEndDate();

            for (TimeRangeDTO down : downs) {

                LocalDateTime downStartDate = down.getStartDate();
                LocalDateTime downEndDate = down.getEndDate();

                //无任何交集情况
                if (!workStartDate.isBefore(downEndDate) || !workEndDate.isAfter(downStartDate)) {
                    continue;
                }

                //工作时间在前，停产时间在后，中间有交集
                if (
                        dateIsBetween(downStartDate, downEndDate, workEndDate) &&
                                dateIsBetween(workStartDate, workEndDate, downStartDate)
                ) {
                    workEndDate = downStartDate;
                    workTime.setEndDate(downStartDate);
                    continue;
                }

                //停产时间在前，工作时间在后，中间有交集
                if (
                        dateIsBetween(downStartDate, downEndDate, workStartDate) &&
                                dateIsBetween(workStartDate, workEndDate, downEndDate)
                ) {
                    workStartDate = downEndDate;
                    workTime.setStartDate(downEndDate);
                    continue;
                }

                //工作时间完全覆盖停产时间
                if (downStartDate.isAfter(workStartDate) && downEndDate.isBefore(workEndDate)) {
                    workTimeIterator.remove();
                    //复制2个
                    TimeRangeDTO range1 = JSON.parseObject(JSON.toJSONString(workTime), TimeRangeDTO.class);
                    TimeRangeDTO range2 = JSON.parseObject(JSON.toJSONString(workTime), TimeRangeDTO.class);

                    range1.setEndDate(downStartDate);
                    range2.setStartDate(downEndDate);

                    works.add(range1);
                    works.add(range2);
                    break;
                }
                //停产时间完全覆盖工作时间
                if (
                        dateIsBetween(downStartDate, downEndDate, workStartDate) &&
                                dateIsBetween(downStartDate, downEndDate, workEndDate)
                ) {
                    workTimeIterator.remove();
                    break;
                }
            }
            if (works.size() > size) {
                break;
            }
        }
        //遍历过程拆分了时间段
        if (works.size() > size) {
            workTimesMinusDownTimes(works, downs);
        }
    }


    private static boolean dateIsBetween(LocalDateTime start, LocalDateTime end, LocalDateTime target) {
        return !target.isBefore(start) && !target.isAfter(end);
    }


    @Override
    public List<WorkCalendarEntity> getWorkCalendarEntities(String currentModelType, Integer currentInstanceId, Date now) {
        Page<WorkCalendarEntity> page = this.getList(currentModelType, currentInstanceId, null, null);
        return page.getRecords();
    }

    /**
     * 截取到now时间点的工作时间
     *
     * @param works
     * @param now
     * @return
     */
    private ArrayList<TimeRangeDTO> cutWorkTimesToNow(List<TimeRangeDTO> works, Date now) {
        ArrayList<TimeRangeDTO> list = new ArrayList<>();
        SimpleDateFormat format = new SimpleDateFormat(DateUtil.DATETIME_FORMAT);
        String nowStr = format.format(now);
        LocalDateTime nowTime = LocalDateTime.parse(nowStr, DateTimeFormatter.ofPattern(DateUtil.DATETIME_FORMAT));
        for (TimeRangeDTO dto : works) {
            TimeRangeDTO copy = JSONUtil.toBean(JSONUtil.toJsonStr(dto), TimeRangeDTO.class);
            LocalDateTime startDate = copy.getStartDate();
            LocalDateTime endDate = copy.getEndDate();
            if (endDate.compareTo(nowTime) <= 0) {
                list.add(copy);
            }
            if (startDate.compareTo(nowTime) <= 0 && endDate.compareTo(nowTime) > 0) {
                copy.setEndDate(nowTime);
                copy.setEnd(DateUtil.DF.format(nowTime));
                list.add(copy);
            }
        }
        return list;
    }

    /**
     * 截取now后面剩余的工作时间
     *
     * @param works
     * @param now
     * @return
     */
    private List<TimeRangeDTO> cutWorkTimesAfterNow(List<TimeRangeDTO> works, Date now) {
        List<TimeRangeDTO> list = new ArrayList<>();
        LocalDateTime nowTime = DateUtil.parseToLocalDateTime(now, DateUtil.DATETIME_FORMAT);
        for (TimeRangeDTO dto : works) {
            TimeRangeDTO copy = JSONUtil.toBean(JSONUtil.toJsonStr(dto), TimeRangeDTO.class);
            LocalDateTime startDate = copy.getStartDate();
            LocalDateTime endDate = copy.getEndDate();
            if (!startDate.isBefore(nowTime)) {
                list.add(copy);
            }
            if (startDate.isBefore(nowTime) && !endDate.isBefore(nowTime)) {
                copy.setStartDate(nowTime);
                copy.setStart(DateUtil.DF.format(nowTime));
                list.add(copy);
            }
        }
        return list;
    }


    /**
     * 获取有效的工作日历
     *
     * @return
     */
    @Override
    public List<WorkCalendarEntity> getAvailableList() {
        Date date = new Date();
        List<WorkCalendarEntity> list = this.list();
        return list.stream().filter(o -> o.getValidityStart() == null && o.getValidityEnd() == null || o.getValidityEnd() == null && !o.getValidityStart().after(date) || o.getValidityStart() == null && o.getValidityEnd() != null && !o.getValidityEnd().before(date) || o.getValidityStart() != null && o.getValidityEnd() != null && !o.getValidityStart().after(date) && !o.getValidityEnd().before(date))
                .collect(Collectors.toList());
    }

    @Override
    public Boolean remove(Integer calendarId) {
        WorkCalendarEntity entity = this.getById(calendarId);
        boolean b = this.removeById(calendarId);
        // 需要删除下级所有的班次日历
        Map<String, List<InstanceDTO>> map = getWorkCalendarTreeMap();
        WorkCalendarSyncDTO subCalendarSyncDTO = WorkCalendarSyncDTO.builder()
                .modelType(entity.getModelType())
                .instanceCode(entity.getInstanceCode())
                .name(entity.getName())
                .workCalendarTime(entity.getWorkCalendarTime())
                .downTime(entity.getDownTime())
                .build();
        syncDeleteSubWorkCalendar(subCalendarSyncDTO, map);
        if (b) {
            //推送到doc模块
            messagePushToKafkaService.pushOldMessage(entity, Constants.KAFKA_MAIN_DATA_TOPIC, KafkaMessageTypeEnum.WORK_CALENDAR_DELETE_MESSAGE);
            redisTemplate.delete(Objects.requireNonNull(redisTemplate.keys(RedisKeyPrefix.TODAY_WORK_TIME + "*")));
            return true;
        }

        return false;
    }


    @Override
    public Double getPreviousWorkCalendarDuration(Integer instanceId, String modelType, Date now) {
        //先从缓存中获取
        Integer minusNum = (Integer) redisTemplate.opsForValue().get(getCalendarKey(instanceId, modelType));
        if (minusNum != null) {
            if (minusNum == -1) {
                return 0.0;
            } else {
                Date previous = DateUtil.addDate(now, -minusNum);
                Double duration = getWorkDurationUpToNow(instanceId, modelType, previous);
                log.info("产线id：{}取{}的日历计算工时,工时为：{}", instanceId, DateUtil.dateStr(previous), duration);
                return duration;
            }
        }

        //先查询该实例有无日历
        LambdaQueryWrapper<WorkCalendarEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WorkCalendarEntity::getModelType, modelType).eq(WorkCalendarEntity::getInstanceId, instanceId);
        List<WorkCalendarEntity> list = list(wrapper);
        if (CollectionUtils.isEmpty(list)) {
            return 0.0;
        }

        //计算缓存时间，缓存到明天开工时间为止
        Date dayOutputBeginTime = dictService.getDayOutputBeginTime(DateUtil.addDate(now, 1));
        long survivalTime = dayOutputBeginTime.getTime() - now.getTime();

        //往前取8天的日历，都没有则返回0
        Double duration;
        Date previous;
        for (int i = 1; i < 9; i++) {
            previous = DateUtil.addDate(now, -i);
            duration = getWorkDurationUpToNow(instanceId, modelType, previous);
            if (duration != 0) {
                //将记录下来i放入缓存中，节约计算资源
                redisTemplate.opsForValue().set(getCalendarKey(instanceId, modelType), i, survivalTime, TimeUnit.DAYS);
                log.info("产线id：{}取{}的日历计算工时，工时为：{}", instanceId, DateUtil.dateStr(previous), duration);
                return duration;
            }
        }
        //查询不到日历则记为-1，节约计算资源
        redisTemplate.opsForValue().set(getCalendarKey(instanceId, modelType), -1, survivalTime, TimeUnit.DAYS);
        return 0.0;
    }

    private String getCalendarKey(Integer instanceId, String modelType) {
        return RedisKeyPrefix.CALENDAR_EXIST_DATE_MINUS_NUM + modelType + "_" + instanceId;
    }

    /**
     * 在指定时间范围内的工作时长
     *
     * @param instanceId
     * @param modelType
     * @param start
     * @param end
     * @return
     */
    @Override
    public CalendarCellDTO getWorkDurationWithinRange(Integer instanceId, String modelType, Date start, Date end) {
        //工作时间
        List<WorkCalendarEntity> workList = this.lambdaQuery()
                .eq(WorkCalendarEntity::getModelType, modelType)
                .eq(WorkCalendarEntity::getInstanceId, instanceId)
                .list();
        List<WorkCalendarEntity> workEntities = workList.stream().filter(o -> o.getValidityEnd() == null || o.getValidityEnd().after(end))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(workEntities)) {
            return CalendarCellDTO.builder().build();
        }
        //休息时间
        List<WorkCalendarDownEntity> downList = downService.lambdaQuery()
                .eq(WorkCalendarDownEntity::getModelType, modelType)
                .eq(WorkCalendarDownEntity::getInstanceId, instanceId)
                .list();
        List<WorkCalendarDownEntity> downEntities = downList.stream().filter(o -> o.getValidityEnd() == null || o.getValidityEnd().after(end))
                .collect(Collectors.toList());

        SimpleDateFormat dateTimeFormat = new SimpleDateFormat(DateUtil.DATETIME_FORMAT);
        String paramStartStr = dateTimeFormat.format(start);
        String paramEndStr = dateTimeFormat.format(end);
        LocalDateTime startDate = LocalDateTime.parse(paramStartStr, DateUtil.DF);
        LocalDateTime endDate = LocalDateTime.parse(paramEndStr, DateUtil.DF);
        LocalDateTime paramStart = startDate;
        //考虑时间跨天的情况
        double total = 0.0;
        List<TimeRangeDTO> workRanges = new ArrayList<>();
        while (!startDate.isAfter(endDate)) {
            List<TimeRangeDTO> workTimes = new ArrayList<>();
            List<TimeRangeDTO> downTimes = new ArrayList<>();
            //工作时间
            for (WorkCalendarEntity entity : workEntities) {
                // 班次时间
                getWorkCalendarTimes(entity);
                List<TimeRangeDTO> workCalendarTimes = entity.getWorkCalendarTimes();
                for (TimeRangeDTO timeRangeDTO : workCalendarTimes) {
                    String startStr = timeRangeDTO.getStart();
                    String endStr = timeRangeDTO.getEnd();
                    LocalDateTime startTime = LocalDateTime.of(startDate.toLocalDate(), LocalTime.parse(startStr));
                    LocalDateTime endTime = LocalDateTime.of(startDate.toLocalDate(), LocalTime.parse(endStr));
                    //如果入参结束时间比工作开始时间要早，跳过即可
                    if (endDate.isBefore(startTime)) {
                        continue;
                    }
                    //如果入参开始时间更晚，取入参开始时间
                    startTime = paramStart.isAfter(startTime) ? paramStart : startTime;
                    //如果入参结束时间更早，取入参结束时间
                    endTime = endDate.isBefore(endTime) ? endDate : endTime;
                    workTimes.add(TimeRangeDTO.builder().name(entity.getName()).start(startStr).end(endStr).startDate(startTime).endDate(endTime).build());
                }
                //休息时间
                getDownTimes(entity);
                List<TimeRangeDTO> rangeDTOList = entity.getDownTimes();
                if (CollectionUtils.isNotEmpty(rangeDTOList)) {
                    for (TimeRangeDTO dto : rangeDTOList) {
                        String downStart = dto.getStart();
                        String downEnd = dto.getEnd();
                        LocalDateTime downStartDate = LocalDateTime.of(startDate.toLocalDate(), LocalTime.parse(downStart));
                        LocalDateTime downEndDate = LocalDateTime.of(startDate.toLocalDate(), LocalTime.parse(downEnd));
                        downTimes.add(TimeRangeDTO.builder().name(dto.getName()).start(downStart).end(downEnd).startDate(downStartDate).endDate(downEndDate).build());
                    }
                }
            }
            //停产计划
            for (WorkCalendarDownEntity entity : downEntities) {
                //去掉指定周几的情况
                int dayOfWeek = startDate.getDayOfWeek().getValue();
                if (dayOfWeek != entity.getWeek()) {
                    continue;
                }
                String startStr = entity.getStart();
                String endStr = entity.getEnd();
                LocalDateTime downStartDate = LocalDateTime.of(startDate.toLocalDate(), LocalTime.parse(startStr));
                LocalDateTime downEndDate = LocalDateTime.of(startDate.toLocalDate(), LocalTime.parse(endStr));
                downTimes.add(TimeRangeDTO.builder().name(entity.getName()).start(startStr).end(endStr).startDate(downStartDate).endDate(downEndDate).build());
            }
            List<TimeRangeDTO> works = removeDuplicates(workTimes);
            List<TimeRangeDTO> downDtos = removeDuplicates(downTimes);
            double duration = 0;
            if (CollectionUtils.isNotEmpty(works)) {
                for (TimeRangeDTO dto : works) {
                    double duration1 = getDuration(dto, downDtos);
                    duration += duration1;
                    //工作时长去掉休息时长
                    List<TimeRangeDTO> timeRange = getTimeRange(dto, downDtos);
                    // 收集工作时间
                    if (CollectionUtils.isNotEmpty(timeRange)) {
                        workRanges.addAll(timeRange);
                    }
                }
            }
            total += duration;
            startDate = startDate.plusDays(1);
        }
        return CalendarCellDTO.builder().duration(total).workTimes(workRanges).build();
    }

    /**
     * 获取工作时间范围
     *
     * @param dto
     * @param downDtos
     * @return
     */
    private List<TimeRangeDTO> getTimeRange(TimeRangeDTO dto, List<TimeRangeDTO> downDtos) {
        List<TimeRangeDTO> dtos = new ArrayList<>();
        LocalDateTime startDate = dto.getStartDate();
        LocalDateTime endDate = dto.getEndDate();
        if (!endDate.isAfter(startDate)) {
            return dtos;
        }
        if (CollectionUtils.isEmpty(downDtos)) {
            dtos.add(TimeRangeDTO.builder().startDate(startDate).endDate(endDate).build());
            return dtos;
        }
        downDtos.sort(Comparator.comparing(TimeRangeDTO::getEndDate));
        LocalDateTime last = downDtos.get(downDtos.size() - 1).getEndDate();
        downDtos.sort(Comparator.comparing(TimeRangeDTO::getStartDate));
        LocalDateTime first = downDtos.get(0).getStartDate();
        //此时集合以开始时间排正序
        if (!startDate.isBefore(last) || !endDate.isAfter(first)) {
            //开始时间不早于非工作的结束时间||结束时间不晚于非工作的开始时间
            dtos.add(TimeRangeDTO.builder().startDate(startDate).endDate(endDate).build());
            return dtos;
        }
        List<TimeRangeDTO> copys = new ArrayList<>(downDtos);
        for (TimeRangeDTO rangeDTO : downDtos) {
            LocalDateTime downStart = rangeDTO.getStartDate();
            LocalDateTime downEnd = rangeDTO.getEndDate();
            //非工作时间涵盖整个工作时间,直接返回
            if (!startDate.isBefore(downStart) && !endDate.isAfter(downEnd)) {
                return dtos;
            }
            //没有任何交集
            if (!startDate.isBefore(downEnd) || !endDate.isAfter(downStart)) {
                copys.remove(rangeDTO);
                continue;
            }
            //起点之间有交集
            if (!startDate.isBefore(downStart) && startDate.isBefore(downEnd)) {
                startDate = downEnd;
                copys.remove(rangeDTO);
                continue;
            }
            //终点之间有交集
            if (endDate.isAfter(downStart) && !endDate.isAfter(downEnd)) {
                endDate = downStart;
                copys.remove(rangeDTO);
            }
        }
        //剩下并集，工作时间包含休息时间
        Duration between = Duration.between(startDate, endDate);
        double duration = MathUtil.divideDouble(between.getSeconds(), (60 * 60), 2);
        if (!(duration > 0)) {
            return dtos;
        }
        if (CollectionUtils.isNotEmpty(copys) && startDate.isBefore(endDate)) {
            for (TimeRangeDTO copy : copys) {
                LocalDateTime downStart = copy.getStartDate();
                LocalDateTime downEnd = copy.getEndDate();
                //工作时间包含休息时间
                if (startDate.isBefore(downStart) && endDate.isAfter(downEnd)) {
                    dtos.add(TimeRangeDTO.builder().startDate(startDate).endDate(downStart).build());
                    //休息时间已经糅合过，所以下一段休息时间肯定是晚于downEnd的，赋值startDate后继续下一个
                    startDate = downEnd;
                }
            }
        }
        if (startDate.isBefore(endDate)) {
            dtos.add(TimeRangeDTO.builder().startDate(startDate).endDate(endDate).build());
        }
        return dtos;
    }

    /**
     * 获取时间结束节点
     *
     * @param instanceId
     * @param modelType
     * @param startTag
     * @param total
     * @return
     */
    @Override
    public LocalDateTime getEndTimeByDuration(Integer instanceId, String modelType, Date startTag, double total) {
        SimpleDateFormat dateTimeFormat = new SimpleDateFormat(DateUtil.DATETIME_FORMAT);
        String paramStartStr = dateTimeFormat.format(startTag);
        LocalDateTime paramStart = LocalDateTime.parse(paramStartStr, DateUtil.DF);
        //计算时间
        long timeTemp = (long) (total * 60 * 60);
        while (timeTemp > 0) {
            //获取每天的工作时间
            List<TimeRangeDTO> workTimes = getWorkTimeRangeList(instanceId, modelType, startTag);
            if (CollectionUtils.isEmpty(workTimes)) {
                //指定的日期没有工作日历，无法排期
                return null;
            }
            for (TimeRangeDTO workTime : workTimes) {
                LocalDateTime timeStart = workTime.getStartDate();
                LocalDateTime timeEnd = workTime.getEndDate();
                timeStart = paramStart.isAfter(timeStart) ? paramStart : timeStart;
                Duration between = Duration.between(timeStart, timeEnd);
                long seconds = between.getSeconds();
                if (timeTemp < seconds) {
                    return timeStart.plusSeconds(timeTemp);
                }
                timeTemp = timeTemp - seconds;
            }
            startTag = DateUtil.addDate(startTag, 1);
        }
        return null;
    }

    /**
     * 获取工作区间
     *
     * @param instanceId
     * @param modelType
     * @param date
     * @return
     */
    private List<TimeRangeDTO> getWorkTimeRangeList(Integer instanceId, String modelType, Date date) {
        //工作时间
        List<WorkCalendarEntity> workList = this.lambdaQuery()
                .eq(WorkCalendarEntity::getModelType, modelType)
                .eq(WorkCalendarEntity::getInstanceId, instanceId)
                .list();
        List<WorkCalendarEntity> workEntities = workList.stream().filter(o -> o.getValidityEnd() == null || o.getValidityEnd().after(date))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(workEntities)) {
            return new ArrayList<>();
        }
        //休息时间
        List<WorkCalendarDownEntity> downList = downService.lambdaQuery()
                .eq(WorkCalendarDownEntity::getModelType, modelType)
                .eq(WorkCalendarDownEntity::getInstanceId, instanceId)
                .list();
        int dayOfWeek = DateUtil.getDayOfWeek(date);
        List<WorkCalendarDownEntity> downEntities = downList.stream()
                .filter(o -> o.getValidityEnd() == null || o.getValidityEnd().after(date))
                .filter(o -> o.getWeek() == null || o.getWeek() == dayOfWeek)
                .collect(Collectors.toList());

        SimpleDateFormat dateTimeFormat = new SimpleDateFormat(DateUtil.DATETIME_FORMAT);
        String paramStartStr = dateTimeFormat.format(date);
        LocalDateTime paramStart = LocalDateTime.parse(paramStartStr, DateUtil.DF);

        List<TimeRangeDTO> workTimes = new ArrayList<>();
        List<TimeRangeDTO> downTimes = new ArrayList<>();
        //工作时间
        for (WorkCalendarEntity entity : workEntities) {
            // 班次时间
            getWorkCalendarTimes(entity);
            List<TimeRangeDTO> workCalendarTimes = entity.getWorkCalendarTimes();
            for (TimeRangeDTO timeRangeDTO : workCalendarTimes) {
                TimeRangeDTO build = getTimeRangeDTO(paramStart.toLocalDate(), timeRangeDTO.getStart(), timeRangeDTO.getEnd());
                workTimes.add(build);
            }
            //休息时间
            getDownTimes(entity);
            //班次时间
            getWorkCalendarTimes(entity);
            List<TimeRangeDTO> rangeDTOList = entity.getDownTimes();
            if (CollectionUtils.isNotEmpty(rangeDTOList)) {
                for (TimeRangeDTO dto : rangeDTOList) {
                    TimeRangeDTO rangeDTO = getTimeRangeDTO(paramStart.toLocalDate(), dto.getStart(), dto.getEnd());
                    downTimes.add(rangeDTO);
                }
            }
        }
        //停产计划
        for (WorkCalendarDownEntity entity : downEntities) {
            TimeRangeDTO rangeDTO = getTimeRangeDTO(paramStart.toLocalDate(), entity.getStart(), entity.getEnd());
            downTimes.add(rangeDTO);
        }
        List<TimeRangeDTO> works = removeDuplicates(workTimes);
        List<TimeRangeDTO> downDtos = removeDuplicates(downTimes);
        List<TimeRangeDTO> workRanges = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(works)) {
            for (TimeRangeDTO dto : works) {
                //工作时长去掉休息时长
                List<TimeRangeDTO> timeRange = getTimeRange(dto, downDtos);
                // 收集工作时间
                if (CollectionUtils.isNotEmpty(timeRange)) {
                    workRanges.addAll(timeRange);
                }
            }
        }
        return workRanges;
    }

    private TimeRangeDTO getTimeRangeDTO(LocalDate localDate, String startStr, String endStr) {
        LocalDateTime startTime = LocalDateTime.of(localDate, LocalTime.parse(startStr));
        LocalDateTime endTime = LocalDateTime.of(localDate, LocalTime.parse(endStr));
        return TimeRangeDTO.builder().start(startStr).end(endStr).startDate(startTime).endDate(endTime).build();
    }

    /**
     * 获取工作时间节点
     *
     * @param instanceId
     * @param modelType
     * @param timeNode
     * @return
     */
    @Override
    public Date getWorkTimePoint(Integer instanceId, String modelType, Date timeNode) {
        SimpleDateFormat dateTimeFormat = new SimpleDateFormat(DateUtil.DATETIME_FORMAT);
        String paramStartStr = dateTimeFormat.format(timeNode);
        LocalDateTime paramStart = LocalDateTime.parse(paramStartStr, DateUtil.DF);
        LocalDateTime tag = null;
        while (tag == null) {
            List<TimeRangeDTO> workTimes = getWorkTimeRangeList(instanceId, modelType, timeNode);
            if (CollectionUtils.isEmpty(workTimes)) {
                //指定的日期没有工作日历，无法排期
                break;
            }
            //不晚于最早时间
            if (!paramStart.isAfter(workTimes.get(0).getStartDate())) {
                tag = workTimes.get(0).getStartDate();
                break;
            }
            //不早于最晚时间
            if (!paramStart.isBefore(workTimes.get(workTimes.size() - 1).getEndDate())) {
                timeNode = DateUtil.addDate(timeNode, 1);
                continue;
            }
            for (TimeRangeDTO workTime : workTimes) {
                LocalDateTime timeStart = workTime.getStartDate();
                LocalDateTime timeEnd = workTime.getEndDate();
                if (!paramStart.isBefore(timeStart) && !paramStart.isAfter(timeEnd)) {
                    tag = paramStart;
                    break;
                }
            }
            timeNode = DateUtil.addDate(timeNode, 1);
        }
        if (tag != null) {
            String format = DateUtil.DF.format(tag);
            try {
                return dateTimeFormat.parse(format);
            } catch (ParseException e) {
                log.error("日期转换错误", e);
            }
        }
        return null;
    }

    @Override
    public WorkCalendarEntity getCalendarByTime(Integer instanceId, String modelType, Date time) {
        // 查关系树
        List<InstanceDTO> instanceTree = modelService.getInstanceTree(modelType);
        // 向上平铺 -> 方便找不到自己的，就向上查
        List<InstanceDTO> list = modelService.traceInstanceTree(instanceTree, instanceId, modelType);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        // 获取对应的 工作日历
        List<WorkCalendarEntity> calendarEntities = null;
        for (InstanceDTO instanceDTO : list) {
            String currentModelType = instanceDTO.getModelType();
            Integer currentInstanceId = instanceDTO.getInstanceId();
            // 这里now传null, 因此不支持设备的日历
            calendarEntities = getWorkCalendarEntities(currentModelType, currentInstanceId, null);
            if (CollectionUtils.isNotEmpty(calendarEntities)) {
                break;
            }
        }
        //
        if (CollectionUtils.isEmpty(calendarEntities)) {
            return null;
        }
        // 每一个班次都单独校验
        LocalDateTime localTime = DateUtil.parseToLocalDateTime(time, DateUtil.DATETIME_FORMAT);
        for (WorkCalendarEntity calendar : calendarEntities) {
            List<TimeRangeDTO> workTimes = new ArrayList<>();
            List<TimeRangeDTO> downTimes = new ArrayList<>();
            // 这里过滤掉不在生效范围内的, 并转换生产时间
            setWorkTimesAndDownTimes(time, Collections.singletonList(calendar), workTimes, downTimes);
            // 去重
            List<TimeRangeDTO> works = removeDuplicates(workTimes);
            List<TimeRangeDTO> downs = removeDuplicates(downTimes);
            // 判断存在工作时间集合的任意一个区间 且 不在休息区间的任意一个集合里面即可
            if (containTimeRange(localTime, works, downs)) {
                return calendar;
            }
        }
        return null;
    }

    private boolean containTimeRange(LocalDateTime localTime, List<TimeRangeDTO> works, List<TimeRangeDTO> downs) {
        // 前闭后开
        boolean check1 = works.stream().anyMatch(e -> localTime.compareTo(e.getStartDate()) >= 0 && localTime.compareTo(e.getEndDate()) < 0);
        boolean check2 = downs.stream().allMatch(e -> localTime.isBefore(e.getStartDate()) || localTime.isAfter(e.getEndDate()));
        return check1 && check2;
    }

    @Override
    public Map<Integer, WorkCalendarEntity> calendarIdMap(Collection<Integer> ids) {
        List<WorkCalendarEntity> workCalendars = CollectionUtils.isEmpty(ids) ? Collections.emptyList() : this.listByIds(ids);
        return workCalendars.stream().collect(Collectors.toMap(WorkCalendarEntity::getCalendarId, Function.identity()));
    }
}
