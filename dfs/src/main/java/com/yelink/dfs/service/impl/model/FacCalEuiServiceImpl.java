package com.yelink.dfs.service.impl.model;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.model.FacilitiesCountTypeEnum;
import com.yelink.dfs.constant.model.FacilitiesTypeEnum;
import com.yelink.dfs.constant.target.MethodConst;
import com.yelink.dfs.entity.device.DeviceEntity;
import com.yelink.dfs.entity.manufacture.ProductionLineEntity;
import com.yelink.dfs.entity.model.FacSensorEntity;
import com.yelink.dfs.entity.model.FacilitiesEntity;
import com.yelink.dfs.entity.target.TargetModelEntity;
import com.yelink.dfs.mapper.device.DeviceMapper;
import com.yelink.dfs.mapper.model.FacCalEuiMapper;
import com.yelink.dfs.mapper.model.FacilitiesMapper;
import com.yelink.dfs.mapper.target.TargetModelMapper;
import com.yelink.dfs.service.common.CommonService;
import com.yelink.dfs.service.manufacture.ProductionLineService;
import com.yelink.dfs.service.model.FacCalEuiService;
import com.yelink.dfs.utils.SpringUtil;
import com.yelink.dfscommon.entity.dfs.FacCalEuiEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;


/**
 * @Description: 工位与计数设备关系
 * @Author: zengzhengfu
 * @Date: 2022/3/18
 */
@Service
public class FacCalEuiServiceImpl extends ServiceImpl<FacCalEuiMapper, FacCalEuiEntity> implements FacCalEuiService, ApplicationRunner {


    @Autowired
    private CommonService commonService;
    @Autowired
    private FacilitiesMapper facilitiesMapper;
    @Autowired
    private DeviceMapper deviceMapper;
    @Resource
    @Lazy
    private ProductionLineService lineService;
    @Autowired
    private TargetModelMapper targetModelMapper;

    @Async
    @Override
    public void isKeyFac(FacilitiesEntity facilitiesEntity) {
        //更新工位关键采集器关系
        if (FacilitiesCountTypeEnum.reportCode.contains(facilitiesEntity.getIsCheck())) {
            setByType(facilitiesEntity.getFid(), facilitiesEntity.getProductionLineId(), Constant.REPORT_LINE);
        }
        if (facilitiesEntity.getIsInput()) {
            setByType(facilitiesEntity.getFid(), facilitiesEntity.getProductionLineId(), Constant.INPUT_RECORD);
        }
        if (facilitiesEntity.getIsDefective()) {
            setByType(facilitiesEntity.getFid(), facilitiesEntity.getProductionLineId(), Constant.UNQUALIFIED_LINE);
        }

        LambdaQueryWrapper<TargetModelEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TargetModelEntity::getMethodName, MethodConst.FAC_NEW_COUNT)
                .eq(TargetModelEntity::getModelId, facilitiesEntity.getModelId());
        if (targetModelMapper.selectCount(queryWrapper) > 0) {
            //存在工位投入数指标
            setByType(facilitiesEntity.getFid(), facilitiesEntity.getProductionLineId(), Constant.FAC_INPUT_RECORD);
        }
    }

    @Override
    public void setByType(Integer fid, Integer lineId, String type) {
        LambdaQueryWrapper<FacCalEuiEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FacCalEuiEntity::getFid, fid).eq(FacCalEuiEntity::getType, type);
        remove(wrapper);

        FacSensorEntity counter = commonService.getCounterFacSensor(fid, type);
        if (counter == null) {
            return;
        }
        if (lineId == null) {
            lineId = facilitiesMapper.selectById(counter.getFid()).getProductionLineId();
        }

        save(
                FacCalEuiEntity.builder()
                        .fid(fid)
                        .lineId(lineId)
                        .eui(counter.getEui())
                        .type(type)
                        .targetName(counter.getCountTarget())
                        .build()
        );
    }

    @Override
    public void deleteByFid(Integer fid) {
        LambdaQueryWrapper<FacCalEuiEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FacCalEuiEntity::getFid, fid);
        remove(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void refreshByLine(Integer lineId) {
        List<FacilitiesEntity> facs = getFacsByLineId(lineId);
        if (CollectionUtils.isEmpty(facs)) {
            return;
        }
        this.lambdaUpdate().eq(FacCalEuiEntity::getLineId, lineId).eq(FacCalEuiEntity::getType, Constant.REPORT_LINE).remove();
        this.lambdaUpdate().eq(FacCalEuiEntity::getLineId, lineId).eq(FacCalEuiEntity::getType, Constant.UNQUALIFIED_LINE).remove();
        this.lambdaUpdate().eq(FacCalEuiEntity::getLineId, lineId).eq(FacCalEuiEntity::getType, Constant.INPUT_RECORD).remove();
        this.lambdaUpdate().eq(FacCalEuiEntity::getLineId, lineId).eq(FacCalEuiEntity::getType, Constant.FAC_INPUT_RECORD).remove();

        for (FacilitiesEntity fac : facs) {
            isKeyFac(fac);
        }

       /* refreshReportLine(lineId, facs);
        refreshUnqualifiedLine(lineId, facs);
        refreshInput(lineId, facs);
        refreshFacInput(lineId, facs);*/
    }

    private void refreshReportLine(Integer lineId, List<FacilitiesEntity> list) {
        //将产线相关的记录全部删除
        LambdaQueryWrapper<FacCalEuiEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FacCalEuiEntity::getLineId, lineId).eq(FacCalEuiEntity::getType, Constant.REPORT_LINE);
        remove(wrapper);

        for (FacilitiesEntity entity : list) {
            setByType(entity.getFid(), lineId, Constant.REPORT_LINE);
        }
    }

    private void refreshUnqualifiedLine(Integer lineId, List<FacilitiesEntity> list) {
        //将产线相关的记录全部删除
        this.lambdaUpdate().eq(FacCalEuiEntity::getLineId, lineId).eq(FacCalEuiEntity::getType, Constant.UNQUALIFIED_LINE).remove();

        for (FacilitiesEntity entity : list) {
            setByType(entity.getFid(), lineId, Constant.UNQUALIFIED_LINE);
        }
    }

    private void refreshInput(Integer lineId, List<FacilitiesEntity> list) {
        //将产线相关的记录全部删除
        LambdaQueryWrapper<FacCalEuiEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FacCalEuiEntity::getLineId, lineId).eq(FacCalEuiEntity::getType, Constant.INPUT_RECORD);
        remove(wrapper);

        //工单产量
        List<FacilitiesEntity> isCheckCollect = list.stream().
                filter(FacilitiesEntity::getIsInput).collect(Collectors.toList());

        for (FacilitiesEntity entity : isCheckCollect) {
            setByType(entity.getFid(), lineId, Constant.INPUT_RECORD);
        }
    }

    private void refreshFacInput(Integer lineId, List<FacilitiesEntity> entities) {
        //将产线相关的记录全部删除
        LambdaQueryWrapper<FacCalEuiEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FacCalEuiEntity::getLineId, lineId).eq(FacCalEuiEntity::getType, Constant.FAC_INPUT_RECORD);
        remove(wrapper);

        //工位投入数
        LambdaQueryWrapper<TargetModelEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TargetModelEntity::getMethodName, MethodConst.FAC_NEW_COUNT);
        List<TargetModelEntity> list = targetModelMapper.selectList(queryWrapper);
        List<Integer> facInputModelIdCollect = list.stream().map(TargetModelEntity::getModelId).collect(Collectors.toList());

        List<FacilitiesEntity> facInputFacs = entities.stream().
                filter(o -> facInputModelIdCollect.contains(o.getModelId())).collect(Collectors.toList());

        for (FacilitiesEntity entity : facInputFacs) {
            setByType(entity.getFid(), entity.getProductionLineId(), Constant.FAC_INPUT_RECORD);
        }
    }

    private List<FacilitiesEntity> getFacsByLineId(Integer lineId) {
        LambdaQueryWrapper<FacilitiesEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FacilitiesEntity::getProductionLineId, lineId)
                .eq(FacilitiesEntity::getTypeOneCode, FacilitiesTypeEnum.CUBICLE.getCode());
        return facilitiesMapper.selectList(wrapper);
    }

    @Async
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void refreshByGid(Integer gid) {
        LambdaQueryWrapper<ProductionLineEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductionLineEntity::getGid, gid);
        List<ProductionLineEntity> list = lineService.list(wrapper);
        for (ProductionLineEntity entity : list) {
            refreshByLine(entity.getProductionLineId());
        }
    }

    @Override
    public void refreshFacInputByModelId(Integer modelId) {
        LambdaQueryWrapper<FacilitiesEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FacilitiesEntity::getModelId, modelId);
        List<FacilitiesEntity> facilitiesEntities = facilitiesMapper.selectList(wrapper);

        for (FacilitiesEntity entity : facilitiesEntities) {
            setByType(entity.getFid(), entity.getProductionLineId(), Constant.FAC_INPUT_RECORD);
        }
    }

    @Override
    public void deleteByModelId(Integer modelId) {
        LambdaQueryWrapper<FacilitiesEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FacilitiesEntity::getModelId, modelId);
        List<FacilitiesEntity> facilitiesEntities = facilitiesMapper.selectList(wrapper);
        List<Integer> collect = facilitiesEntities.stream().map(FacilitiesEntity::getFid).collect(Collectors.toList());

        LambdaQueryWrapper<FacCalEuiEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(FacCalEuiEntity::getFid, collect).eq(FacCalEuiEntity::getType, Constant.FAC_INPUT_RECORD);
        remove(queryWrapper);
    }

    @Override
    public long getCountByEui(String eui) {
        LambdaQueryWrapper<FacCalEuiEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FacCalEuiEntity::getEui, eui);
        return count(wrapper);
    }

    @Override
    public List<FacCalEuiEntity> getListByEui(String eui, String targetName) {
        LambdaQueryWrapper<FacCalEuiEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FacCalEuiEntity::getEui, eui).eq(FacCalEuiEntity::getTargetName, targetName);
        return list(wrapper);
    }

    @Override
    public void refreshByDeviceModelId(Integer modelId) {
        LambdaQueryWrapper<DeviceEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DeviceEntity::getModelId, modelId);
        List<DeviceEntity> deviceEntities = deviceMapper.selectList(wrapper);
        for (DeviceEntity deviceEntity : deviceEntities) {
            if (deviceEntity.getFid() != null) {
                FacilitiesEntity facilities = facilitiesMapper.selectById(deviceEntity.getFid());
                if (facilities != null) {
                    isKeyFac(facilities);
                    /*Integer lineId = facilities.getProductionLineId();
                    setByType(deviceEntity.getFid(), lineId, Constant.REPORT_LINE);
                    setByType(deviceEntity.getFid(), lineId, Constant.INPUT_RECORD);
                    setByType(deviceEntity.getFid(), lineId, Constant.FAC_INPUT_RECORD);*/
                }
            }
        }
    }


    @Override
    public void run(ApplicationArguments args) throws Exception {
        //启动时初始化
        List<Integer> lineIds = lineService.lambdaQuery()
                .select(ProductionLineEntity::getProductionLineId)
                .list().stream().map(ProductionLineEntity::getProductionLineId)
                .collect(Collectors.toList());
        FacCalEuiService bean = SpringUtil.getBean(FacCalEuiService.class);
        for (Integer lineId : lineIds) {
            bean.refreshByLine(lineId);
        }
    }
}
