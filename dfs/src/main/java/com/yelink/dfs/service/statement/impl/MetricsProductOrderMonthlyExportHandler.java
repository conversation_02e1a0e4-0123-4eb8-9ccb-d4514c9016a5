package com.yelink.dfs.service.statement.impl;

import com.alibaba.fastjson.JSON;
import com.asyncexcel.core.DataParam;
import com.asyncexcel.core.ExcelContext;
import com.asyncexcel.core.ExportPage;
import com.asyncexcel.core.annotation.ExcelHandle;
import com.asyncexcel.core.exporter.DataExportParam;
import com.asyncexcel.core.exporter.ExportContext;
import com.asyncexcel.core.exporter.ExportHandler;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.entity.target.metrics.MetricsProductOrderMonthlyEntity;
import com.yelink.dfs.entity.target.metrics.dto.MetricsProductOrderMonthlyExportDTO;
import com.yelink.dfs.entity.target.metrics.dto.MetricsProductOrderMonthlySelectDTO;
import com.yelink.dfs.service.common.CommonService;
import com.yelink.dfs.service.impl.target.metrics.MetricsProductOrderMonthlyExtendService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * 记录导出
 *
 * <AUTHOR>
 */
@Slf4j
@ExcelHandle
@RequiredArgsConstructor
public class MetricsProductOrderMonthlyExportHandler implements ExportHandler<MetricsProductOrderMonthlyExportDTO> {

    private final MetricsProductOrderMonthlyExtendService metricsProductOrderMonthlyExtendService;
    private final CommonService commonService;

    /**
     * 导出数据最大不能超过100万行
     */
    public static final Long EXPORT_MAX_ROWS = 1000000L;


    @Override
    public void init(ExcelContext ctx, DataParam param) {
        ExportContext context = (ExportContext) ctx;

        Integer templateId = (Integer) param.getParameters().get("templateId");
        String templateSheetName = (String) param.getParameters().get("productOrderMonthProgress");
        String cleanSheetNames = (String) param.getParameters().get("cleanSheetNames");
        String templateFileUrl = (String) param.getParameters().get("templateFileUrl");
        if (StringUtils.isNotBlank(templateFileUrl)) {
            // 模板文件是一个url地址
            commonService.initExcelContext(templateFileUrl, templateSheetName, cleanSheetNames, context, MetricsProductOrderMonthlyExportDTO.class, null);
        } else {
            commonService.initExcelContext(templateId,templateSheetName, cleanSheetNames, context, MetricsProductOrderMonthlyExportDTO.class, null);
        }
    }

    @Override
    public ExportPage<MetricsProductOrderMonthlyExportDTO> exportData(int startPage, int limit,DataExportParam param) {
        String filterFieldValue = (String)param.getParameters().get(this.getClass().getName());
        MetricsProductOrderMonthlySelectDTO selectDTO = StringUtils.isEmpty(filterFieldValue) ? MetricsProductOrderMonthlySelectDTO.builder().build() : JSON.parseObject(filterFieldValue, MetricsProductOrderMonthlySelectDTO.class);
        selectDTO.setCurrent(startPage);
        selectDTO.setSize(limit);
        Page<MetricsProductOrderMonthlyEntity> page = metricsProductOrderMonthlyExtendService.pageList(selectDTO);
        // 封装成DTO
        List<MetricsProductOrderMonthlyExportDTO> exportList = metricsProductOrderMonthlyExtendService.convertToExportDTO(page.getRecords());

        ExportPage<MetricsProductOrderMonthlyExportDTO> result = new ExportPage<>();
        result.setTotal(Math.min(page.getTotal(), EXPORT_MAX_ROWS));
        result.setCurrent((long) startPage);
        result.setSize((long) limit);
        result.setRecords(exportList);
        return result;
    }


    @Override
    public void beforePerPage(ExportContext ctx, DataExportParam param) {
        log.info("开始查询第{}页",Math.ceil(ctx.getSuccessCount() + ctx.getFailCount())/ctx.getLimit());
    }

    @Override
    public void afterPerPage(List<MetricsProductOrderMonthlyExportDTO> list, ExportContext ctx, DataExportParam param) {
        log.info("已完成数量：{}",ctx.getSuccessCount());
    }

    @Override
    public void callBack(ExcelContext ctx, DataParam param) {
        ExportContext context = (ExportContext) ctx;
        log.info("导出完成：{}",context.getFailMessage());
    }
}
