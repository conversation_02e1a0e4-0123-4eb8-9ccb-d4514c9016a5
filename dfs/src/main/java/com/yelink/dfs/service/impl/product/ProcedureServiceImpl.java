package com.yelink.dfs.service.impl.product;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yelink.dfs.common.UserAuthenService;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.DictTypeEnum;
import com.yelink.dfs.constant.ImportTypeEnum;
import com.yelink.dfs.constant.RedisKeyPrefix;
import com.yelink.dfs.constant.defect.DefectDefineStateEnum;
import com.yelink.dfs.constant.defect.DefectSchemeStatusEnum;
import com.yelink.dfs.constant.maintain.MaintainSchemeStatusEnum;
import com.yelink.dfs.constant.product.ControlItemEnum;
import com.yelink.dfs.constant.product.CraftStateEnum;
import com.yelink.dfs.constant.product.InspectComparatorEnum;
import com.yelink.dfs.constant.product.InspectItemTypeEnum;
import com.yelink.dfs.constant.product.ProcedureFlowTypeEnum;
import com.yelink.dfs.constant.product.ProcedureInspectTypeEnum;
import com.yelink.dfs.constant.product.ProcedureStateEnum;
import com.yelink.dfs.constant.product.ProcessParameterInputModeEnum;
import com.yelink.dfs.constant.product.ProcessParameterTypeEnum;
import com.yelink.dfs.constant.product.TimeoutThresholdTypeEnum;
import com.yelink.dfs.constant.supplier.SupplierCategoryEnum;
import com.yelink.dfs.constant.supplier.SupplierStateEnum;
import com.yelink.dfs.constant.supplier.SupplierTypeEnum;
import com.yelink.dfs.constant.user.RoleEnum;
import com.yelink.dfs.entity.common.ImportDataRecordEntity;
import com.yelink.dfs.entity.defect.DefectDefineEntity;
import com.yelink.dfs.entity.defect.DefectSchemeEntity;
import com.yelink.dfs.entity.device.ProcessAssemblyEntity;
import com.yelink.dfs.entity.maintain.MaintainSchemeEntity;
import com.yelink.dfs.entity.model.ModelEntity;
import com.yelink.dfs.entity.order.WorkOrderProcedureRelationEntity;
import com.yelink.dfs.entity.product.CraftEntity;
import com.yelink.dfs.entity.product.CraftProcedureEntity;
import com.yelink.dfs.entity.product.FacTypeProcedureEntity;
import com.yelink.dfs.entity.product.MaterialEntity;
import com.yelink.dfs.entity.product.ProcedureDefCapacityLabelRelationEntity;
import com.yelink.dfs.entity.product.ProcedureDefControllerConfigEntity;
import com.yelink.dfs.entity.product.ProcedureDefDefectDefineEntity;
import com.yelink.dfs.entity.product.ProcedureDefDefectSchemeEntity;
import com.yelink.dfs.entity.product.ProcedureDefDeviceTypeEntity;
import com.yelink.dfs.entity.product.ProcedureDefInspectionConfigDefectEntity;
import com.yelink.dfs.entity.product.ProcedureDefInspectionConfigEntity;
import com.yelink.dfs.entity.product.ProcedureDefMaintainSchemeEntity;
import com.yelink.dfs.entity.product.ProcedureDefMaterialUsedEntity;
import com.yelink.dfs.entity.product.ProcedureDefPostEntity;
import com.yelink.dfs.entity.product.ProcedureDefProcessAssemblyEntity;
import com.yelink.dfs.entity.product.ProcedureDefRelationWorkHoursEntity;
import com.yelink.dfs.entity.product.ProcedureEntity;
import com.yelink.dfs.entity.product.ProcedureFileEntity;
import com.yelink.dfs.entity.product.ProcedureInspectionEntity;
import com.yelink.dfs.entity.product.ProcedureProcessParameterEntity;
import com.yelink.dfs.entity.product.dto.ProcedureSelectDTO;
import com.yelink.dfs.entity.supplier.SupplierEntity;
import com.yelink.dfs.entity.user.SysRoleEntity;
import com.yelink.dfs.entity.user.SysUserEntity;
import com.yelink.dfs.mapper.defect.DefectDefineMapper;
import com.yelink.dfs.mapper.model.ModelMapper;
import com.yelink.dfs.mapper.model.WorkCenterMapper;
import com.yelink.dfs.mapper.product.ProcedureInspectionMapper;
import com.yelink.dfs.mapper.product.ProcedureMapper;
import com.yelink.dfs.open.v1.defect.dto.DefectSchemeSelectDTO;
import com.yelink.dfs.open.v2.procedure.dto.ProcedureQueryDTO;
import com.yelink.dfs.provider.FastDfsClientService;
import com.yelink.dfs.service.approve.config.ApproveConfigService;
import com.yelink.dfs.service.common.DictService;
import com.yelink.dfs.service.common.ImportDataRecordService;
import com.yelink.dfs.service.common.ImportProgressService;
import com.yelink.dfs.service.common.OperationLogService;
import com.yelink.dfs.service.defect.DefectDefineService;
import com.yelink.dfs.service.defect.DefectSchemeService;
import com.yelink.dfs.service.device.ProcessAssemblyService;
import com.yelink.dfs.service.impl.product.dto.ProcedureControllerConfigExcelDTO;
import com.yelink.dfs.service.impl.product.dto.ProcedureDefectExcelDTO;
import com.yelink.dfs.service.impl.product.dto.ProcedureDeviceTypeExcelDTO;
import com.yelink.dfs.service.impl.product.dto.ProcedureInspectExcelDTO;
import com.yelink.dfs.service.impl.product.dto.ProcedureMaterialUsedExcelDTO;
import com.yelink.dfs.service.impl.product.dto.ProcedureParameterExcelDTO;
import com.yelink.dfs.service.impl.product.dto.ProcedurePostExcelDTO;
import com.yelink.dfs.service.impl.product.dto.ProcedureProcessAssemblyExcelDTO;
import com.yelink.dfs.service.impl.product.dto.ProcedureRelationWorkHoursExcelDTO;
import com.yelink.dfs.service.impl.product.dto.ProcedureTempExcelDTO;
import com.yelink.dfs.service.maintain.MaintainSchemeService;
import com.yelink.dfs.service.model.ModelService;
import com.yelink.dfs.service.order.RuleSeqService;
import com.yelink.dfs.service.order.WorkOrderProcedureRelationService;
import com.yelink.dfs.service.product.CraftProcedureService;
import com.yelink.dfs.service.product.CraftService;
import com.yelink.dfs.service.product.FacTypeProcedureService;
import com.yelink.dfs.service.product.MaterialService;
import com.yelink.dfs.service.product.ProcedureDefCapacityLabelRelationService;
import com.yelink.dfs.service.product.ProcedureDefControllerConfigService;
import com.yelink.dfs.service.product.ProcedureDefDefectDefineService;
import com.yelink.dfs.service.product.ProcedureDefDefectSchemeService;
import com.yelink.dfs.service.product.ProcedureDefDeviceTypeService;
import com.yelink.dfs.service.product.ProcedureDefFileService;
import com.yelink.dfs.service.product.ProcedureDefInspectControllerService;
import com.yelink.dfs.service.product.ProcedureDefInspectionConfigDefectService;
import com.yelink.dfs.service.product.ProcedureDefInspectionConfigService;
import com.yelink.dfs.service.product.ProcedureDefMaintainSchemeService;
import com.yelink.dfs.service.product.ProcedureDefMaterialUsedService;
import com.yelink.dfs.service.product.ProcedureDefPostService;
import com.yelink.dfs.service.product.ProcedureDefProcessAssemblyService;
import com.yelink.dfs.service.product.ProcedureDefRelationWorkHoursService;
import com.yelink.dfs.service.product.ProcedureFileService;
import com.yelink.dfs.service.product.ProcedureInspectionConfigService;
import com.yelink.dfs.service.product.ProcedureInspectionService;
import com.yelink.dfs.service.product.ProcedureProcessParameterService;
import com.yelink.dfs.service.product.ProcedureService;
import com.yelink.dfs.service.rule.NumberRuleService;
import com.yelink.dfs.service.supplier.SupplierService;
import com.yelink.dfs.service.user.SysPostService;
import com.yelink.dfs.service.user.SysRoleService;
import com.yelink.dfs.service.user.SysUserService;
import com.yelink.dfs.utils.CommonUtil;
import com.yelink.dfs.utils.SpringUtil;
import com.yelink.dfs.utils.SqlUtil;
import com.yelink.dfscommon.constant.ModelEnum;
import com.yelink.dfscommon.constant.OperationType;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.constant.WhetherEnum;
import com.yelink.dfscommon.constant.approve.config.ApprovalStatusEnum;
import com.yelink.dfscommon.constant.approve.config.ApproveModuleEnum;
import com.yelink.dfscommon.constant.dfs.OutsourcingProcedureEnum;
import com.yelink.dfscommon.constant.dfs.rule.NumberCodeDTO;
import com.yelink.dfscommon.constant.model.WorkCenterTypeEnum;
import com.yelink.dfscommon.dto.ApproveBatchDTO;
import com.yelink.dfscommon.dto.DataDelSchema;
import com.yelink.dfscommon.dto.EasyExcelDataDTO;
import com.yelink.dfscommon.dto.dfs.GenerateCodeDTO;
import com.yelink.dfscommon.dto.dfs.RuleSeqDTO;
import com.yelink.dfscommon.entity.common.BatchChangeStateDTO;
import com.yelink.dfscommon.entity.dfs.DictEntity;
import com.yelink.dfscommon.entity.dfs.OperationLogEntity;
import com.yelink.dfscommon.entity.dfs.SysPostEntity;
import com.yelink.dfscommon.entity.dfs.WorkCenterEntity;
import com.yelink.dfscommon.mapper.CommonMapper;
import com.yelink.dfscommon.utils.EasyExcelUtil;
import com.yelink.dfscommon.utils.ExcelUtil;
import com.yelink.dfscommon.utils.JacksonUtil;
import com.yelink.dfscommon.utils.MathUtil;
import com.yelink.dfscommon.utils.PathUtils;
import com.yelink.dfscommon.utils.WrapperUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Service
 *
 * <AUTHOR>
 * @Date 2021-03-08 09:31
 */
@Slf4j
@Service
public class ProcedureServiceImpl extends ServiceImpl<ProcedureMapper, ProcedureEntity> implements ProcedureService {

    @Resource
    private DictService dictService;
    @Resource
    private ModelMapper modelMapper;
    @Resource
    @Lazy
    private ModelService modelService;
    @Resource
    private ImportDataRecordService importDataRecordService;
    @Resource
    private ProcedureFileService procedureFileService;
    @Resource
    private FastDfsClientService fastDfsClientService;
    @Resource
    private SysUserService sysUserService;
    @Resource
    private FacTypeProcedureService facTypeProcedureService;
    @Resource
    private ApproveConfigService approveConfigService;
    @Resource
    private SupplierService supplierService;
    @Resource
    private WorkOrderProcedureRelationService workOrderProcedureRelationService;
    @Resource
    @Lazy
    private CraftProcedureService craftProcedureService;
    @Resource
    private WorkCenterMapper workCenterMapper;
    @Resource
    private ProcedureDefDefectDefineService procedureDefDefectDefineService;
    @Resource
    private ProcedureDefInspectionConfigService procedureDefInspectionConfigService;
    @Resource
    private ProcedureProcessParameterService procedureProcessParameterService;
    @Resource
    private ProcedureInspectionMapper procedureInspectionMapper;
    @Resource
    private DefectDefineMapper defectDefineMapper;
    @Resource
    private ProcedureDefInspectionConfigDefectService procedureDefInspectionConfigDefectService;
    @Resource
    private ImportProgressService importProgressService;
    @Resource
    private ProcedureDefMaterialUsedService procedureDefMaterialUsedService;
    @Resource
    private ProcedureDefDeviceTypeService procedureDefDeviceTypeService;
    @Resource
    private ProcedureDefRelationWorkHoursService procedureDefRelationWorkHoursService;
    @Resource
    private ProcedureDefFileService procedureDefFileService;
    @Resource
    private ProcedureDefInspectControllerService procedureDefInspectControllerService;
    @Resource
    private ProcedureDefControllerConfigService procedureDefControllerConfigService;
    @Resource
    private ProcedureDefProcessAssemblyService procedureDefProcessAssemblyService;
    @Resource
    private ProcedureDefDefectSchemeService procedureDefDefectSchemeService;
    @Resource
    private ProcedureDefMaintainSchemeService procedureDefMaintainSchemeService;
    @Resource
    private ProcedureDefPostService procedureDefPostService;
    @Resource
    @Lazy
    private MaterialService materialService;
    @Resource
    private ProcessAssemblyService processAssemblyService;
    @Resource
    private SysPostService sysPostService;
    @Resource
    @Lazy
    private DefectSchemeService defectSchemeService;
    @Resource
    @Lazy
    private MaintainSchemeService maintainSchemeService;
    @Resource
    private NumberRuleService numberRuleService;
    @Resource
    private RuleSeqService ruleSeqService;
    @Resource
    @Lazy
    private CraftService craftService;
    @Resource
    private UserAuthenService userAuthenService;
    @Resource
    private SysRoleService roleService;
    @Resource
    private CommonMapper commonMapper;
    @Resource
    @Lazy
    private OperationLogService operationLogService;
    @Resource
    private SysUserService userService;
    @Resource
    private ProcedureDefCapacityLabelRelationService labelRelationService;
    @Lazy
    @Resource
    private ProcedureServiceImpl procedureService;

    @Override
    public Page<ProcedureEntity> list(ProcedureSelectDTO selectDTO) {
        LambdaQueryWrapper<ProcedureEntity> lambda = new LambdaQueryWrapper<>();
        WrapperUtil.like(lambda, ProcedureEntity::getName, selectDTO.getName());
        WrapperUtil.like(lambda, ProcedureEntity::getProcedureCode, selectDTO.getCode());
        WrapperUtil.eq(lambda, ProcedureEntity::getApprover, selectDTO.getApprover());
        lambda.between(StringUtils.isNoneEmpty(selectDTO.getCreateStartTime(), selectDTO.getCreateEndTime()), ProcedureEntity::getCreateTime, selectDTO.getCreateStartTime(), selectDTO.getCreateEndTime());
        lambda.between(StringUtils.isNoneEmpty(selectDTO.getUpdateStartTime(), selectDTO.getUpdateEndTime()), ProcedureEntity::getUpdateTime, selectDTO.getUpdateStartTime(), selectDTO.getUpdateEndTime());
        // 工位类型
        if (StringUtils.isNotBlank(selectDTO.getFacModelIds())) {
            List<Integer> facModelIds = Arrays.stream(selectDTO.getFacModelIds().split(Constant.SEP)).map(Integer::valueOf).collect(Collectors.toList());
            List<FacTypeProcedureEntity> facTypeProcedures = facTypeProcedureService.lambdaQuery().in(FacTypeProcedureEntity::getModelId, facModelIds).list();
            if (CollectionUtils.isEmpty(facTypeProcedures)) {
                return new Page<>();
            } else {
                List<Integer> procedureIds = facTypeProcedures.stream().map(FacTypeProcedureEntity::getProcedureId).collect(Collectors.toList());
                lambda.in(ProcedureEntity::getProcedureId, procedureIds);
            }
        }
        // 生产基本类型
        if (StringUtils.isNotBlank(selectDTO.getType())) {
            List<String> type = Arrays.stream(selectDTO.getType().split(Constant.SEP)).map(String::valueOf).collect(Collectors.toList());
            lambda.in(ProcedureEntity::getType, type);
        }
        // 状态
        if (StringUtils.isNotBlank(selectDTO.getState())) {
            List<Integer> states = Arrays.stream(selectDTO.getState().split(Constant.SEP)).map(Integer::valueOf).collect(Collectors.toList());
            lambda.in(ProcedureEntity::getState, states);
        }
        if (StringUtils.isNotBlank(selectDTO.getIsSubContractingOperation())) {
            List<Integer> subContractingOperations = Arrays.stream(selectDTO.getIsSubContractingOperation().split(Constant.SEP)).map(Integer::valueOf).collect(Collectors.toList());
            lambda.in(ProcedureEntity::getIsSubContractingOperation, subContractingOperations);
        }
        // 工序查询
        String procedureSearch = selectDTO.getProcedureSearch();
        if (StringUtils.isNotBlank(procedureSearch)) {
            lambda.and(o -> o.like(ProcedureEntity::getProcedureCode, procedureSearch)
                    .or().like(ProcedureEntity::getName, procedureSearch)
                    .or().like(ProcedureEntity::getAlias, procedureSearch)
            );
        }
        WrapperUtil.eq(lambda, ProcedureEntity::getIsInspectionOperation, selectDTO.getIsInspectionOperation());
        WrapperUtil.like(lambda, ProcedureEntity::getOutSourcingSupplier, selectDTO.getOutSourcingSupplier());
        WrapperUtil.like(lambda, ProcedureEntity::getWorkCenterNames, selectDTO.getWorkCenterNames());
        lambda.eq(selectDTO.getIsQualityProcess() != null, ProcedureEntity::getIsQualityProcess, selectDTO.getIsQualityProcess());
        lambda.eq(selectDTO.getIsEnable() != null, ProcedureEntity::getIsEnable, selectDTO.getIsEnable());
        lambda.in(!CollectionUtils.isEmpty(selectDTO.getProcedureIds()), ProcedureEntity::getProcedureId, selectDTO.getProcedureIds());
        lambda.orderByDesc(ProcedureEntity::getCreateTime)
                .orderByDesc(ProcedureEntity::getProcedureId);
        Page<ProcedureEntity> page;
        if (selectDTO.getCurrent() == null || selectDTO.getSize() == null) {
            //查询全部产线
            List<ProcedureEntity> list = this.list(lambda);
            page = new Page<>(1, list.size(), list.size());
            page.setRecords(list);
            page.setTotal(list.size());
            if (page.getRecords().size() > 0) {
                page.getRecords().forEach(this::getFileList);
            }
        } else {
            // 分页查询
            page = this.page(new Page<>(selectDTO.getCurrent(), selectDTO.getSize()), lambda);
        }
        List<ProcedureEntity> result = page.getRecords();
        // 获取中文名称
        if (!CollectionUtils.isEmpty(result)) {
            showName(result);
        }
        return page;
    }

    /**
     * 获取附件列表
     */
    private void getFileList(ProcedureEntity procedureEntity) {
        // 获取附件列表
        LambdaUpdateWrapper<ProcedureFileEntity> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ProcedureFileEntity::getProcedureId, procedureEntity.getProcedureId());
        List<ProcedureFileEntity> procedureFileList = procedureFileService.list(updateWrapper);
        procedureEntity.setFileList(procedureFileList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProcedureEntity saveEntity(ProcedureEntity procedure) {
        // 如果传入的编码为空，则由编码规则生成
        if (StringUtils.isBlank(procedure.getProcedureCode())) {
            if (Objects.isNull(procedure.getNumberRuleId())) {
                throw new ResponseException(RespCodeEnum.NEED_NUMBER_RULE);
            }
            NumberCodeDTO seqById = numberRuleService.getSeqById(
                    RuleSeqDTO.builder()
                            .id(procedure.getNumberRuleId())
                            .build()
            );
            procedure.setProcedureCode(seqById.getCode());
        }
        procedure.setCreateTime(new Date());
        procedure.setUpdateTime(new Date());
        //工序编号不能重复
        repeatProcedureCodeJudge(procedure);
        //工序名称不能重复
        repeatProcedureNameJudge(procedure);
        // 设置工序为创建状态
        procedure.setState(ProcedureStateEnum.CREATE.getCode());
        // 如果需要审批，则缺省为待审核状态
        if (approveConfigService.getConfigByCode(ApproveModuleEnum.PROCEDURE.getCode())) {
            procedure.setApprovalStatus(ApprovalStatusEnum.TO_BE_APPROVAL.getCode());
        }
        // 获取供应商名称
        String outSourcingSupplierName = getOutSourcingSupplierName(procedure.getOutSourcingSupplierCode());
        procedure.setOutSourcingSupplier(outSourcingSupplierName);
        // 新增工序实体
        boolean save = save(procedure);
        // 如调用到编码规则，则相对应的所有序列号加1
        if (Objects.nonNull(procedure.getNumberRuleId())) {
            ruleSeqService.updateSeqEntity(null, procedure.getNumberRuleId());
        }
        // 绑定检验项目
        bindInspectionConfig(procedure);
        // 绑定不良项目
        bindDefectDefine(procedure);
        // 绑定工序工艺参数
        bindProcedureProcessParameter(procedure);
        // 保存工序定义其他参数定义
        saveProcedureExtend(procedure);
        // 关联能力标签
        relatedCapacityLabel(procedure);
        //新增工序与工位类型关联信息（一个工序可关联多个工位）
        if (save) {
            if (procedure.getFacModelId() != null) {
                String[] facModelIds = procedure.getFacModelId().split(",");
                for (String facModelId : facModelIds) {
                    if (StringUtils.isBlank(facModelId)) {
                        continue;
                    }
                    FacTypeProcedureEntity build = FacTypeProcedureEntity.builder()
                            .modelId(Integer.parseInt(facModelId))
                            .procedureId(procedure.getProcedureId())
                            .build();
                    facTypeProcedureService.save(build);
                }
            }
        }
        return procedure;
    }

    private void bindProcedureProcessParameter(ProcedureEntity procedure) {
        if (CollectionUtils.isEmpty(procedure.getProcessParameterEntities())) {
            return;
        }
        for (ProcedureProcessParameterEntity defectDefineEntity : procedure.getProcessParameterEntities()) {
            defectDefineEntity.setId(null);
            defectDefineEntity.setProcedureCode(procedure.getProcedureCode());
        }
        procedureProcessParameterService.saveBatch(procedure.getProcessParameterEntities());

    }

    /**
     * 绑定不良项目
     */
    private void bindDefectDefine(ProcedureEntity procedure) {
        if (CollectionUtils.isEmpty(procedure.getDefectDefineEntities())) {
            return;
        }
        for (ProcedureDefDefectDefineEntity defectDefineEntity : procedure.getDefectDefineEntities()) {
            defectDefineEntity.setId(null);
            defectDefineEntity.setCreateBy(procedure.getCreateBy());
            defectDefineEntity.setCreateTime(new Date());
            defectDefineEntity.setProcedureCode(procedure.getProcedureCode());
        }
        procedureDefDefectDefineService.saveBatch(procedure.getDefectDefineEntities());
    }

    /**
     * 绑定检验项目
     */
    private void bindInspectionConfig(ProcedureEntity procedure) {
        List<ProcedureDefInspectionConfigEntity> inspectionConfigEntities = procedure.getInspectionConfigEntities();
        if (CollectionUtils.isEmpty(inspectionConfigEntities)) {
            return;
        }
        String procedureCode = procedure.getProcedureCode();
        List<Integer> list = new ArrayList<>();
        for (ProcedureDefInspectionConfigEntity inspectionConfigEntity : inspectionConfigEntities) {
            // 过滤重复的检验项
            if (list.contains(inspectionConfigEntity.getProcedureInspectionId())) {
                continue;
            }
            inspectionConfigEntity.setId(null);
            inspectionConfigEntity.setCreateBy(procedure.getCreateBy());
            inspectionConfigEntity.setCreateTime(new Date());
            inspectionConfigEntity.setProcedureCode(procedureCode);
            // 校验字段规则
            ProcedureInspectionService procedureInspectionService = SpringUtil.getBean(ProcedureInspectionService.class);
            ProcedureInspectionEntity procedureInspectionEntity = procedureInspectionService.getById(inspectionConfigEntity.getProcedureInspectionId());
            if (procedureInspectionEntity == null) {
                continue;
            }
            ProcedureInspectionConfigService inspectionConfigService = SpringUtil.getBean(ProcedureInspectionConfigService.class);
            inspectionConfigService.checkFieldRules(inspectionConfigEntity.getStandardValue(), inspectionConfigEntity.getUpperLimit(), inspectionConfigEntity.getDownLimit(), procedureInspectionEntity.getDataType());
            list.add(inspectionConfigEntity.getProcedureInspectionId());
        }
        procedureDefInspectionConfigService.saveBatch(inspectionConfigEntities);
        // 更新工序定义检验不良
        procedureDefInspectionConfigDefectService.updateDefectEntities(procedureCode, inspectionConfigEntities);
    }

    private void repeatProcedureNameJudge(ProcedureEntity procedure) {
        boolean exists = this.lambdaQuery().eq(ProcedureEntity::getName, procedure.getName()).exists();
        if (exists) {
            throw new ResponseException(RespCodeEnum.PROCEDURE_NAME_IS_NOT_REPEAT);
        }
    }

    /**
     * 新增生效工序
     *
     * @param entity
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveReleasedEntity(ProcedureEntity entity) {
        if (approveConfigService.getConfigByCode(ApproveModuleEnum.PROCEDURE.getCode())) {
            throw new ResponseException(RespCodeEnum.ORDER_NEED_TO_APPROVE);
        }
        procedureService.saveEntity(entity);
        entity.setState(ProcedureStateEnum.RELEASED.getCode());
        entity.setFileList(new ArrayList<>());
        procedureService.updateEntityById(entity);
    }

    @Override
    public ProcedureEntity getLastProcedureByCraft(Integer craftId) {
        return null;
    }

    @Override
    public ProcedureEntity getEntityById(Integer id) {
        ProcedureEntity procedureEntity = this.getById(id);
        if (Objects.isNull(procedureEntity)) {
            return null;
        }
        // 获取人名和状态名称
        showName(Stream.of(procedureEntity).collect(Collectors.toList()));
        // 获取工序定义的检验项
        getInspectionConfigList(procedureEntity);
        // 获取工序定义的不良项目
        getDefectDefineList(procedureEntity);
        // 获取工序工艺参数
        getProcessParameterList(procedureEntity);
        getProcessExtend(procedureEntity);
        // 获取附件列表
        getFileList(procedureEntity);
        // 获取关联的能力标签
        procedureEntity.setLabelRelationEntities(labelRelationService.getByProcedureDefCode(procedureEntity.getProcedureCode()));
        return procedureEntity;
    }

    private void getProcessExtend(ProcedureEntity procedure) {
        procedure.setProcessMaterialUsedEntities(
                procedureDefMaterialUsedService.listByProcedure(procedure.getProcedureCode())
        );
        procedure.setProcedureDefDeviceTypeEntities(
                procedureDefDeviceTypeService.listByProcedure(procedure.getProcedureCode())
        );
        procedure.setProcedureDefRelationWorkHoursEntity(
                procedureDefRelationWorkHoursService.oneByProcedure(procedure.getProcedureCode())
        );
        procedure.setProcedureDefFileEntities(
                procedureDefFileService.listByProcedure(procedure.getProcedureCode())
        );
        procedure.setProcedureDefInspectControllerEntities(
                procedureDefInspectControllerService.listByProcedure(procedure.getProcedureCode())
        );
        procedure.setProcedureDefControllerConfigEntity(
                procedureDefControllerConfigService.oneByProcedure(procedure.getProcedureCode())
        );
        procedure.setProcedureDefProcessAssemblyEntities(
                procedureDefProcessAssemblyService.listByProcedure(procedure.getProcedureCode())
        );
        procedure.setProcedureDefDefectSchemeEntity(
                procedureDefDefectSchemeService.oneByProcedure(procedure.getProcedureCode())
        );
        procedure.setProcedureDefMaintainSchemeEntity(
                procedureDefMaintainSchemeService.oneByProcedure(procedure.getProcedureCode())
        );
        procedure.setProcedureDefPostEntities(
                procedureDefPostService.listByProcedure(procedure.getProcedureCode())
        );
    }

    /**
     * 获取工序工艺参数
     *
     * @param procedureEntity
     */
    private void getProcessParameterList(ProcedureEntity procedureEntity) {
        List<ProcedureProcessParameterEntity> processParameterEntities = procedureProcessParameterService.lambdaQuery()
                .eq(ProcedureProcessParameterEntity::getProcedureCode, procedureEntity.getProcedureCode())
                .orderByAsc(ProcedureProcessParameterEntity::getSort)
                .list();
        if (CollectionUtils.isEmpty(processParameterEntities)) {
            return;
        }
        for (ProcedureProcessParameterEntity entity : processParameterEntities) {
            entity.setTypeName(ProcessParameterTypeEnum.getNameByCode(entity.getType()));
            entity.setInputModeName(ProcessParameterInputModeEnum.getNameByCode(entity.getInputMode()));
        }
        procedureEntity.setProcessParameterEntities(processParameterEntities);
    }

    /**
     * 获取工序定义的不良项目
     */
    private void getDefectDefineList(ProcedureEntity procedureEntity) {
        List<ProcedureDefDefectDefineEntity> defectDefineEntities = procedureDefDefectDefineService.lambdaQuery().eq(ProcedureDefDefectDefineEntity::getProcedureCode, procedureEntity.getProcedureCode()).list();
        if (CollectionUtils.isEmpty(defectDefineEntities)) {
            return;
        }
        for (ProcedureDefDefectDefineEntity defectDefineEntity : defectDefineEntities) {
            DefectDefineService defectDefineService = SpringUtil.getBean(DefectDefineService.class);
            DefectDefineEntity defineEntity = defectDefineService.getDetailById(defectDefineEntity.getDefectDefineId());
            if (defineEntity == null) {
                continue;
            }
            defectDefineEntity.setDefectCode(defineEntity.getDefectCode());
            defectDefineEntity.setDefectName(defineEntity.getDefectName());
            defectDefineEntity.setDefectTypeName(defineEntity.getDefectTypeName());
            defectDefineEntity.setDescription(defineEntity.getDescription());
        }
        procedureEntity.setDefectDefineEntities(defectDefineEntities);
    }

    /**
     * 获取工序定义的检验项
     */
    @Override
    public void getInspectionConfigList(ProcedureEntity procedureEntity) {
        List<ProcedureDefInspectionConfigEntity> inspectionConfigEntities = procedureDefInspectionConfigService.lambdaQuery().eq(ProcedureDefInspectionConfigEntity::getProcedureCode, procedureEntity.getProcedureCode()).list();
        if (CollectionUtils.isEmpty(inspectionConfigEntities)) {
            return;
        }
        for (ProcedureDefInspectionConfigEntity inspectionConfigEntity : inspectionConfigEntities) {
            ProcedureInspectionService procedureInspectionService = SpringUtil.getBean(ProcedureInspectionService.class);
            ProcedureInspectionEntity procedureInspectionEntity = procedureInspectionService.getById(inspectionConfigEntity.getProcedureInspectionId());
            if (procedureInspectionEntity == null) {
                continue;
            }
            // 检验项目代号
            inspectionConfigEntity.setInspectionCode(procedureInspectionEntity.getInspectionCode());
            // 检验项目名称
            inspectionConfigEntity.setInspectionName(procedureInspectionEntity.getInspectionName());
            // 检验标准
            inspectionConfigEntity.setInspectionStandard(procedureInspectionEntity.getInspectionStandard());
            // 检验仪器
            inspectionConfigEntity.setInspectionInstrument(procedureInspectionEntity.getInspectionInstrument());
            // 数据类型
            inspectionConfigEntity.setDataType(procedureInspectionEntity.getDataType());
            inspectionConfigEntity.setComparatorName(InspectComparatorEnum.getNameByCode(inspectionConfigEntity.getComparator()));
            DefectDefineEntity defectDefineEntity = defectDefineMapper.selectById(inspectionConfigEntity.getDefectId());
            inspectionConfigEntity.setDefectName(defectDefineEntity == null ? null : defectDefineEntity.getDefectName());
            List<ProcedureDefInspectionConfigDefectEntity> defectEntities = procedureDefInspectionConfigDefectService.lambdaQuery()
                    .eq(ProcedureDefInspectionConfigDefectEntity::getInspectionConfigId, inspectionConfigEntity.getId()).list();
            inspectionConfigEntity.setDefectEntities(defectEntities);
            // 检验类型名称 多选
            if (StringUtils.isNotBlank(inspectionConfigEntity.getInspectType())) {
                String[] inspectTypeCodes = inspectionConfigEntity.getInspectType().split(Constant.SEP);
                List<String> inspectTypeNames = new ArrayList<>();
                for (String inspectTypeCode : inspectTypeCodes) {
                    String nameByCode = ProcedureInspectTypeEnum.getNameByCode(inspectTypeCode);
                    inspectTypeNames.add(nameByCode);
                }
                inspectionConfigEntity.setInspectTypeName(String.join(Constant.SEP, inspectTypeNames));
            }
        }
        procedureEntity.setInspectionConfigEntities(inspectionConfigEntities);
    }

    @Override
    public void removeEntityById(Integer id) {
        ProcedureEntity procedureEntity = this.getById(id);
        // 废弃所有关联的工艺
        List<Integer> relatedCraftIds = craftProcedureService.list(
                Wrappers.lambdaQuery(CraftProcedureEntity.class)
                        .select(CraftProcedureEntity::getCraftId)
                        .eq(CraftProcedureEntity::getProcedureId, procedureEntity.getProcedureId())
        ).stream().map(CraftProcedureEntity::getCraftId).distinct().collect(Collectors.toList());
        for (Integer relatedCraftId : relatedCraftIds) {
            CraftEntity craftEntity = craftService.getById(relatedCraftId);
            // 赋予输入值
            List<DataDelSchema> dataDelSchemas = craftService.getCheckSchemas();
            craftService.preHandleData(craftEntity, dataDelSchemas);
            doCheck(dataDelSchemas, craftEntity.getCraftCode());
        }
        if (CollectionUtils.isNotEmpty(relatedCraftIds)) {
            craftService.update(Wrappers.lambdaUpdate(CraftEntity.class)
                    .set(CraftEntity::getState, CraftStateEnum.ABANDONED.getCode())
                    .in(CraftEntity::getCraftId, relatedCraftIds)
            );
        }
        // 删除工位类型&工序类型的关联关系
        LambdaQueryWrapper<FacTypeProcedureEntity> facTypeWrapper = new LambdaQueryWrapper<>();
        facTypeWrapper.eq(FacTypeProcedureEntity::getProcedureId, procedureEntity.getProcedureId());
        facTypeProcedureService.remove(facTypeWrapper);
        // 删除工单&工序类型的关联关系
        LambdaQueryWrapper<WorkOrderProcedureRelationEntity> workOrderWrapper = new LambdaQueryWrapper<>();
        workOrderWrapper.eq(WorkOrderProcedureRelationEntity::getProcedureId, procedureEntity.getProcedureId());
        workOrderProcedureRelationService.remove(workOrderWrapper);
        // 删除不良项目
        procedureDefDefectDefineService.lambdaUpdate()
                .eq(ProcedureDefDefectDefineEntity::getProcedureCode, procedureEntity.getProcedureCode())
                .remove();

        // 删除工艺参数
        procedureProcessParameterService.lambdaUpdate().eq(ProcedureProcessParameterEntity::getProcedureCode, procedureEntity.getProcedureCode()).remove();
        removeProcedureExtend(procedureEntity.getProcedureCode());
        // 删除工序类型
        this.removeById(id);
        // 删除工序类型绑定的附件
        removeProcedureFile(id);
        // 日志记录
        String username = userAuthenService.getUsername();
        String nickName = userService.getNicknameByUsername(username);
        operationLogService.save(OperationLogEntity.builder()
                .module("产品定义")
                .type(OperationType.DELETE)
                .des("删除了工序编码为的" + procedureEntity.getProcedureCode() + "工序")
                .username(StringUtils.isNotBlank(username) ? username : "admin")
                .nickname(StringUtils.isNotBlank(nickName) ? nickName : "管理员")
                .createTime(new Date())
                .build());
    }

    public void doCheck(List<DataDelSchema> originCheckers, String craftCode) {
        List<DataDelSchema> checkers = originCheckers.stream().filter(e -> CollUtil.isNotEmpty(e.getValues())).collect(Collectors.toList());
        for (int i = 0; i < checkers.size(); i++) {
            DataDelSchema checker = checkers.get(i);
            long l = commonMapper.countData(checker);
            if (l > 0) {
                throw new ResponseException(RespCodeEnum.CRAFT_RELATED_OTHER_DATA.fmtDes(craftCode, checker.getTableNickName()));
            }
        }
    }

    /**
     * 删除工序类型绑定的附件
     */
    private void removeProcedureFile(Integer id) {
        LambdaUpdateWrapper<ProcedureFileEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(ProcedureFileEntity::getProcedureId, id);
        List<ProcedureFileEntity> list = procedureFileService.list(wrapper);
        // 删除fastdfs的文件
        if (!list.isEmpty()) {
            list.forEach(procedureFileEntity -> {
                try {
                    fastDfsClientService.deleteFile(procedureFileEntity.getFileUrl());
                } catch (Exception e) {
                    log.error("删除文件错误", e);
                }
            });
            // 删除工序类型关联的附件
            procedureFileService.remove(wrapper);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateEntityById(ProcedureEntity procedureEntity) {
        ProcedureEntity old = getById(procedureEntity.getProcedureId());
        if(old == null) {
            throw new ResponseException(String.format("id为%s的工序不存在", procedureEntity.getProcedureId()));
        }
        if (!ProcedureStateEnum.CREATE.getCode().equals(old.getState()) && ProcedureStateEnum.CREATE.getCode().equals(procedureEntity.getState())) {
            throw new ResponseException(RespCodeEnum.NOT_ALLOW_TO_MODIFY_CREATED_STATE);
        }
        List<ModelEntity> modelEntityList = new ArrayList<>();
        StringBuffer stringBuffer = new StringBuffer();
        if (StringUtils.isNotBlank(procedureEntity.getLineModelIds())) {
            //制造单元模型名称
            getLineModelNamesByLineIds(procedureEntity, stringBuffer, modelEntityList, procedureEntity.getLineModelIds());
        }
        String outSourcingSupplierName = getOutSourcingSupplierName(procedureEntity.getOutSourcingSupplierCode());
        // 我猜测下面这个更新是为了置空处理
        LambdaUpdateWrapper<ProcedureEntity> procedureEntityLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        procedureEntityLambdaUpdateWrapper.eq(ProcedureEntity::getProcedureId, procedureEntity.getProcedureId())
                .set(ProcedureEntity::getOutSourcingSupplier, outSourcingSupplierName)
                .set(ProcedureEntity::getOutSourcingSupplierCode, procedureEntity.getOutSourcingSupplierCode())
                .set(ProcedureEntity::getLineModelIds, procedureEntity.getLineModelIds())
                .set(ProcedureEntity::getLineModelNames, procedureEntity.getLineModelNames())
                .set(ProcedureEntity::getRemark, procedureEntity.getRemark());
        this.update(procedureEntityLambdaUpdateWrapper);
        // 删除
        LambdaUpdateWrapper<ProcedureFileEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(ProcedureFileEntity::getProcedureId, procedureEntity.getProcedureId());
        List<ProcedureFileEntity> list = procedureFileService.list(wrapper);
        // 删除fastdfs的文件
        if (!list.isEmpty()) {
            list.forEach(procedureFileEntity -> {
                try {
                    fastDfsClientService.deleteFile(procedureFileEntity.getFileUrl());
                } catch (Exception e) {
                    log.error("", e);
                }
            });
            // 删除工序关联的附件
            procedureFileService.remove(wrapper);
        }
        // 新增工序关联的附件信息
        procedureEntity.getFileList().forEach(procedureFileEntity -> {
            procedureFileEntity.setProcedureId(procedureEntity.getProcedureId());
            procedureFileService.save(procedureFileEntity);
        });
        //如果是驳回状态,修改数据后变更为待审核状态。此处只要是创建状态,状态改为待审批可覆盖这个需求
        if (Objects.equals(procedureEntity.getState(), ProcedureStateEnum.CREATE.getCode())
                && approveConfigService.getConfigByCode(ApproveModuleEnum.PROCEDURE.getCode())) {
            procedureEntity.setApprovalStatus(ApprovalStatusEnum.TO_BE_APPROVAL.getCode());
        }
        if (Objects.equals(ProcedureStateEnum.RELEASED.getCode(),procedureEntity.getState()) && !Objects.equals(old.getState(), procedureEntity.getState())) {
            procedureEntity.setReleaseTime(new Date());
        }
        // 更新工序
        boolean b = this.updateById(procedureEntity);
        // 重新绑定检验项目
        procedureDefInspectionConfigService.lambdaUpdate().eq(ProcedureDefInspectionConfigEntity::getProcedureCode, procedureEntity.getProcedureCode()).remove();
        bindInspectionConfig(procedureEntity);
        // 绑定不良项目
        procedureDefDefectDefineService.lambdaUpdate().eq(ProcedureDefDefectDefineEntity::getProcedureCode, procedureEntity.getProcedureCode()).remove();
        bindDefectDefine(procedureEntity);
        // 绑定工艺参数
        procedureProcessParameterService.lambdaUpdate().eq(ProcedureProcessParameterEntity::getProcedureCode, procedureEntity.getProcedureCode()).remove();
        bindProcedureProcessParameter(procedureEntity);
        //
        updateProcedureExtend(procedureEntity);
        // 关联能力标签
        relatedCapacityLabel(procedureEntity);

        if (b) {
            QueryWrapper<FacTypeProcedureEntity> qw = new QueryWrapper<>();
            qw.lambda().eq(FacTypeProcedureEntity::getProcedureId, procedureEntity.getProcedureId());
            facTypeProcedureService.remove(qw);
            if (StringUtils.isNotEmpty(procedureEntity.getFacModelId())) {
                String[] split = procedureEntity.getFacModelId().split(",");
                for (String s : split) {
                    int modelId = Integer.parseInt(s);
                    FacTypeProcedureEntity build = FacTypeProcedureEntity.builder()
                            .modelId(modelId).procedureId(procedureEntity.getProcedureId()).build();
                    facTypeProcedureService.save(build);
                }
            }
            craftProcedureService.lambdaUpdate()
                    .eq(CraftProcedureEntity::getProcedureId, procedureEntity.getProcedureId())
                    .set(CraftProcedureEntity::getAlias, procedureEntity.getAlias())
                    .update();
        }
        return b;
    }

    /**
     * 关联能力标签
     */
    private void relatedCapacityLabel(ProcedureEntity procedureEntity) {
        labelRelationService.deleteByProcedureDefCode(procedureEntity.getProcedureCode());
        if (CollectionUtils.isNotEmpty(procedureEntity.getLabelRelationEntities())) {
            for (ProcedureDefCapacityLabelRelationEntity labelRelationEntity : procedureEntity.getLabelRelationEntities()) {
                labelRelationEntity.setId(null);
                labelRelationEntity.setProcedureDefCode(procedureEntity.getProcedureCode());
            }
            labelRelationService.saveBatch(procedureEntity.getLabelRelationEntities());
        }
    }

    private void saveProcedureExtend(ProcedureEntity procedure) {
        procedureDefMaterialUsedService.saveListByProcedure(procedure.getProcessMaterialUsedEntities(), procedure.getProcedureCode());
        procedureDefDeviceTypeService.saveListByProcedure(procedure.getProcedureDefDeviceTypeEntities(), procedure.getProcedureCode());
        procedureDefRelationWorkHoursService.saveByProcedure(procedure.getProcedureDefRelationWorkHoursEntity(), procedure.getProcedureCode());
        procedureDefFileService.saveListByProcedure(procedure.getProcedureDefFileEntities(), procedure.getProcedureCode());
        procedureDefInspectControllerService.saveListByProcedure(procedure.getProcedureDefInspectControllerEntities(), procedure.getProcedureCode());
        procedureDefControllerConfigService.saveByProcedure(procedure.getProcedureDefControllerConfigEntity(), procedure.getProcedureCode());
        procedureDefProcessAssemblyService.saveListByProcedure(procedure.getProcedureDefProcessAssemblyEntities(), procedure.getProcedureCode());
        procedureDefDefectSchemeService.saveByProcedure(procedure.getProcedureDefDefectSchemeEntity(), procedure.getProcedureCode());
        procedureDefMaintainSchemeService.saveByProcedure(procedure.getProcedureDefMaintainSchemeEntity(), procedure.getProcedureCode());
        procedureDefPostService.saveListByProcedure(procedure.getProcedureDefPostEntities(), procedure.getProcedureCode());
    }

    private void updateProcedureExtend(ProcedureEntity procedure) {
        procedureDefMaterialUsedService.updateListByProcedure(procedure.getProcessMaterialUsedEntities(), procedure.getProcedureCode());
        procedureDefDeviceTypeService.updateListByProcedure(procedure.getProcedureDefDeviceTypeEntities(), procedure.getProcedureCode());
        procedureDefRelationWorkHoursService.updateByProcedure(procedure.getProcedureDefRelationWorkHoursEntity(), procedure.getProcedureCode());
        procedureDefFileService.updateListByProcedure(procedure.getProcedureDefFileEntities(), procedure.getProcedureCode());
        procedureDefInspectControllerService.updateListByProcedure(procedure.getProcedureDefInspectControllerEntities(), procedure.getProcedureCode());
        procedureDefControllerConfigService.updateByProcedure(procedure.getProcedureDefControllerConfigEntity(), procedure.getProcedureCode());
        procedureDefProcessAssemblyService.updateListByProcedure(procedure.getProcedureDefProcessAssemblyEntities(), procedure.getProcedureCode());
        procedureDefDefectSchemeService.updateByProcedure(procedure.getProcedureDefDefectSchemeEntity(), procedure.getProcedureCode());
        procedureDefMaintainSchemeService.updateByProcedure(procedure.getProcedureDefMaintainSchemeEntity(), procedure.getProcedureCode());
        procedureDefPostService.updateListByProcedure(procedure.getProcedureDefPostEntities(), procedure.getProcedureCode());
    }

    private void removeProcedureExtend(String procedureCode) {
        procedureDefMaterialUsedService.removeByProcedureCode(procedureCode);
        procedureDefDeviceTypeService.removeByProcedureCode(procedureCode);
        procedureDefRelationWorkHoursService.removeByProcedureCode(procedureCode);
        procedureDefFileService.removeByProcedureCode(procedureCode);
        procedureDefInspectControllerService.removeByProcedureCode(procedureCode);
        procedureDefControllerConfigService.removeByProcedureCode(procedureCode);
        procedureDefProcessAssemblyService.removeByProcedureCode(procedureCode);
        procedureDefDefectSchemeService.removeByProcedureCode(procedureCode);
        procedureDefMaintainSchemeService.removeByProcedureCode(procedureCode);
        procedureDefPostService.removeByProcedureCode(procedureCode);
    }

    /**
     * 工序编号不能重复
     *
     * @param procedureEntity
     */
    private void repeatProcedureCodeJudge(ProcedureEntity procedureEntity) {
        LambdaUpdateWrapper<ProcedureEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.in(ProcedureEntity::getProcedureCode, procedureEntity.getProcedureCode());
        ProcedureEntity procedureEntity1 = this.getOne(wrapper);
        if (procedureEntity1 != null) {
            throw new ResponseException(RespCodeEnum.PROCEDURE_FAIL2SAME_CODE);
        }
    }

    @Override
    public void batchUpdateState(BatchChangeStateDTO batchApprovalDTO) {
        String stateName = ProcedureStateEnum.getNameByCode(batchApprovalDTO.getState());
        if (stateName == null) {
            throw new ResponseException("状态输入有误");
        }
        if (CollectionUtils.isEmpty(batchApprovalDTO.getIds())) {
            throw new ResponseException("ids不能为空");
        }
        List<ProcedureEntity> procedures = this.lambdaQuery().in(ProcedureEntity::getProcedureId, batchApprovalDTO.getIds()).list();
        long stateCount = procedures.stream().map(ProcedureEntity::getState).distinct().count();
        if (stateCount > 1) {
            throw new ResponseException(RespCodeEnum.MUST_BE_IN_THE_SAME_STATE_TO_MODIFY);
        }
        Integer oldState = procedures.stream().map(ProcedureEntity::getState).distinct().collect(Collectors.toList()).get(0);
        if (Objects.equals(oldState, batchApprovalDTO.getState())) {
            return;
        }
        //开启审批后不可直接将创建状态改为生效
        Boolean needApprove = approveConfigService.getConfigByCode(ApproveModuleEnum.CRAFT.getCode());

        if (oldState.equals(ProcedureStateEnum.CREATE.getCode()) && Boolean.TRUE.equals(needApprove)) {
            throw new ResponseException(RespCodeEnum.APPROVE_IS_USER_UPDATE_STATE_IS_EFFECT);
        }
        if (!ProcedureStateEnum.CREATE.getCode().equals(oldState) && ProcedureStateEnum.CREATE.getCode().equals(batchApprovalDTO.getState())) {
            throw new ResponseException(RespCodeEnum.NOT_ALLOW_TO_MODIFY_CREATED_STATE);
        }
        this.lambdaUpdate().in(ProcedureEntity::getProcedureId, batchApprovalDTO.getIds())
                .set(ProcedureEntity::getState, batchApprovalDTO.getState())
                .set(ProcedureEntity::getUpdateTime, new Date())
                .set(ProcedureEntity::getUpdateBy, userAuthenService.getUsername())
                .set(Objects.equals(ProcedureStateEnum.RELEASED.getCode(), oldState), ProcedureEntity::getReleaseTime, new Date())
                .update();
    }

    @Override
    public List<ModelEntity> getFacModelList(Integer procedureId) {
        QueryWrapper<FacTypeProcedureEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(FacTypeProcedureEntity::getProcedureId, procedureId)
                .isNotNull(FacTypeProcedureEntity::getProcedureId);
        List<FacTypeProcedureEntity> list = facTypeProcedureService.list(wrapper);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<Integer> collect = list.stream().map(FacTypeProcedureEntity::getModelId).collect(Collectors.toList());
        return modelMapper.selectBatchIds(collect);
    }

    @Override
    public void examineOrApprove(Integer approvalStatus, String approvalSuggestion, Integer id, String username) {
        if (id == null) {
            return;
        }
        LambdaQueryWrapper<ProcedureEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(ProcedureEntity::getProcedureId, ProcedureEntity::getState, ProcedureEntity::getApprovalStatus)
                .eq(ProcedureEntity::getProcedureId, id);
        ProcedureEntity entity = this.getOne(wrapper);
        if (entity == null || entity.getState() == null) {
            return;
        }
        Integer preApprovalStatus = entity.getApprovalStatus();
        if (preApprovalStatus == null) {
            preApprovalStatus = entity.getState().equals(ProcedureStateEnum.CREATE.getCode()) ?
                    ApprovalStatusEnum.TO_BE_APPROVAL.getCode() : ApprovalStatusEnum.REJECTED.getCode();
        }
        boolean toApproved = approvalStatus == ApprovalStatusEnum.APPROVED.getCode()
                && preApprovalStatus == ApprovalStatusEnum.TO_BE_APPROVAL.getCode();

        boolean toReject = approvalStatus == ApprovalStatusEnum.REJECTED.getCode()
                && preApprovalStatus == ApprovalStatusEnum.TO_BE_APPROVAL.getCode();
        if (toApproved || toReject) {
            LambdaUpdateWrapper<ProcedureEntity> uw = new LambdaUpdateWrapper<>();
            uw.eq(ProcedureEntity::getProcedureId, id)
                    .set(ProcedureEntity::getApprovalStatus, approvalStatus)
                    .set(ProcedureEntity::getApprovalSuggestion, approvalSuggestion)
                    .set(ProcedureEntity::getActualApprover, username)
                    .set(ProcedureEntity::getApprovalTime, new Date());
            if (toApproved) {
                uw.set(ProcedureEntity::getState, ProcedureStateEnum.RELEASED.getCode());
            }
            this.update(uw);
        }
    }

    @Override
    public void approveBatch(ApproveBatchDTO dto) {
        List<Integer> ids = dto.getIds();
        Integer approvalStatus = dto.getApprovalStatus();
        if (CollectionUtils.isEmpty(ids) || approvalStatus == null) {
            return;
        }
        List<ProcedureEntity> entities = this.listByIds(ids);
        List<String> approvers = entities.stream().map(ProcedureEntity::getApprover).distinct().collect(Collectors.toList());
        approvers.remove(dto.getActualApprover());
        if (!CollectionUtils.isEmpty(approvers)) {
            throw new ResponseException(RespCodeEnum.CURRENT_USER_CAN_ONLY_APPROVED_HIS_OWN);
        }
        List<ProcedureEntity> list = entities.stream().filter(o -> ProcedureStateEnum.CREATE.getCode() == o.getState())
                .peek(o -> {
                    if (o.getApprovalStatus() == null) {
                        o.setApprovalStatus(ApprovalStatusEnum.TO_BE_APPROVAL.getCode());
                    }
                })
                .filter(o -> o.getApprovalStatus().equals(ApprovalStatusEnum.TO_BE_APPROVAL.getCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Date date = new Date();
        for (ProcedureEntity entity : list) {
            Integer preApprovalStatus = entity.getApprovalStatus();
            entity.setApprovalStatus(approvalStatus);
            entity.setApprovalSuggestion(dto.getApprovalSuggestion());
            entity.setActualApprover(dto.getActualApprover());
            entity.setApprovalTime(date);

            boolean toApproved = approvalStatus == ApprovalStatusEnum.APPROVED.getCode()
                    && preApprovalStatus == ApprovalStatusEnum.TO_BE_APPROVAL.getCode();
            if (toApproved) {
                entity.setState(ProcedureStateEnum.RELEASED.getCode());
            }
        }
        procedureService.updateBatchById(list);
    }

    @Override
    public List<ModelEntity> getManufacturingUnitList(Integer procedureId) {
        List<ModelEntity> manufacturingUnitTypeList = new ArrayList<>();
        if (procedureId == null) {
            LambdaQueryWrapper<ModelEntity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ModelEntity::getPid, 2);
            manufacturingUnitTypeList.addAll(modelMapper.selectList(queryWrapper));
        } else {
            //查询工序下绑定的工位类型列表
            ProcedureEntity procedureEntity = this.getById(procedureId);
            if (procedureEntity != null) {
                String modelId = procedureEntity.getLineModelIds();
                LambdaQueryWrapper<ModelEntity> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(ModelEntity::getId, modelId);
                manufacturingUnitTypeList = modelMapper.selectList(queryWrapper);
            }

        }
        return manufacturingUnitTypeList;
    }

    @Override
    public List<SupplierEntity> getSupplierName() {
        return supplierService.lambdaQuery()
                .ne(SupplierEntity::getCategory, SupplierCategoryEnum.PURCHASE)
                .eq(SupplierEntity::getState, SupplierStateEnum.TAKE_EFFECT.getCode())
                .list();
    }


    /**
     * 获取中文名称
     */
    private void showName(List<ProcedureEntity> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<SysUserEntity> sysUserEntities = sysUserService.selectList();
        Map<String, String> nickNames = sysUserEntities.stream().collect(Collectors.toMap(SysUserEntity::getUsername, SysUserEntity::getNickname));
        Map<String, String> signatureUrlMap = sysUserService.getSignatureUrlMap(null);
        list.forEach(entity -> {
            // 获取状态名称
            entity.setStateName(ProcedureStateEnum.getNameByCode(entity.getState()));
            entity.setApprovalStatusName(ApprovalStatusEnum.getNameByCode(entity.getApprovalStatus()));
            entity.setIsSubContractingOperationName(OutsourcingProcedureEnum.getNameByCode(entity.getIsSubContractingOperation()));
            //获取工位类型名称(通过工序id)
            QueryWrapper<FacTypeProcedureEntity> qw = new QueryWrapper<>();
            qw.lambda().eq(FacTypeProcedureEntity::getProcedureId, entity.getProcedureId());
            List<FacTypeProcedureEntity> typeProcedureEntities = facTypeProcedureService.list(qw);
            List<Integer> collect = typeProcedureEntities.stream().map(FacTypeProcedureEntity::getModelId).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(collect)) {
                List<ModelEntity> modelEntities = modelMapper.selectBatchIds(collect);
                entity.setFacModel(modelEntities);
            }
            //制造单元模型支持多选
            StringBuffer s = new StringBuffer();
            List<ModelEntity> modelEntityList = new ArrayList<>();
            String lineModelIds = entity.getLineModelIds();
            getLineModelNamesByLineIds(entity, s, modelEntityList, lineModelIds);
            entity.setLineModelList(modelEntityList);
            // 获取人名
            entity.setApproverName(nickNames.get(entity.getApprover()) == null ? entity.getApprover() : nickNames.get(entity.getApprover()));
            entity.setActualApproverName(nickNames.get(entity.getActualApprover()) == null ? entity.getActualApprover() : nickNames.get(entity.getActualApprover()));
            entity.setActualApproverSignatureUrl(signatureUrlMap.get(entity.getActualApprover()));
            entity.setCreateByNickname(nickNames.get(entity.getCreateBy()) == null ? entity.getCreateBy() : nickNames.get(entity.getCreateBy()));
            entity.setUpdateByNickname(nickNames.get(entity.getUpdateBy()) == null ? entity.getUpdateBy() : nickNames.get(entity.getUpdateBy()));
            //获取生产基本单元类型名称
            entity.setTypeName(WorkCenterTypeEnum.getNameByCode(entity.getType()));
            // 工艺参数
            Long count = procedureProcessParameterService.lambdaQuery().eq(ProcedureProcessParameterEntity::getProcedureCode, entity.getProcedureCode()).count();
            if (count > 0) {
                entity.setHaveProcessParameter(true);
            } else {
                entity.setHaveProcessParameter(false);
            }
        });
    }

    /**
     * 展示关联的供应商名称
     */
    private String getOutSourcingSupplierName(String outSourcingSupplierCode) {
        if (StringUtils.isBlank(outSourcingSupplierCode)) {
            return "";
        }
        List<String> outSourcingSupplierCodes = Arrays.asList(outSourcingSupplierCode.split(Constant.SEP));
        List<SupplierEntity> supplierEntities = supplierService.lambdaQuery().in(SupplierEntity::getCode, outSourcingSupplierCodes).list();
        return !CollectionUtils.isEmpty(supplierEntities) ? supplierEntities.stream().map(SupplierEntity::getName).collect(Collectors.joining(Constant.SEP)) : "";
    }

    private void getLineModelNamesByLineIds(ProcedureEntity entity, StringBuffer s, List<ModelEntity> modelEntityList, String lineModelIds) {
        if (lineModelIds != null) {
            if (lineModelIds.contains(Constant.SEP)) {
                String[] split = lineModelIds.split(Constant.SEP);
                for (String code : split) {
                    int lineModelId = Integer.parseInt(code);
                    ModelEntity modelEntity = modelMapper.selectById(lineModelId);
                    if (modelEntity != null) {
                        modelEntityList.add(modelEntity);
                        s.append(modelEntity.getName()).append(",");
                    }
                }
                if (StringUtils.isNotBlank(s)) {
                    entity.setLineModelNames(s.substring(0, s.length() - 1));
                }
            } else {
                ModelEntity modelEntity = modelMapper.selectById(lineModelIds);
                modelEntityList.add(modelEntity);
                if (modelEntity != null) {
                    s.append(modelEntity.getName());
                    entity.setLineModelNames(s.toString());
                }
            }
        }
    }


    @Override
    public ProcedureEntity getProcedureByCode(String procedureCode) {
        LambdaQueryWrapper<ProcedureEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProcedureEntity::getProcedureCode, procedureCode)
                .last(Constant.LIMIT_ONE);
        ProcedureEntity entity = this.getOne(wrapper);
        if (entity == null) {
            return null;
        }
        String lineModelIds = entity.getLineModelIds();
        if (StringUtils.isNotBlank(lineModelIds)) {
            List<String> strings = Arrays.asList(lineModelIds.split(","));
            LambdaQueryWrapper<ModelEntity> modelWrapper = new LambdaQueryWrapper<>();
            modelWrapper.in(ModelEntity::getId, strings);
            List<ModelEntity> entities = modelMapper.selectList(modelWrapper);
            entity.setLineModelList(entities);
        }
        LambdaQueryWrapper<FacTypeProcedureEntity> facWrapper = new LambdaQueryWrapper<>();
        facWrapper.eq(FacTypeProcedureEntity::getProcedureId, entity.getProcedureId());
        List<FacTypeProcedureEntity> entities = facTypeProcedureService.list(facWrapper);
        if (!CollectionUtils.isEmpty(entities)) {
            List<Integer> ids = entities.stream().map(FacTypeProcedureEntity::getModelId).collect(Collectors.toList());
            LambdaQueryWrapper<ModelEntity> modelWrapper = new LambdaQueryWrapper<>();
            modelWrapper.in(ModelEntity::getId, ids);
            List<ModelEntity> modelEntities = modelMapper.selectList(modelWrapper);
            entity.setFacModel(modelEntities);
        }
        return entity;
    }

    @Override
    public ProcedureEntity getProcedureByName(String procedureName) {
        LambdaQueryWrapper<ProcedureEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProcedureEntity::getName, procedureName);
        return this.getOne(wrapper);
    }

    @Async
    @Override
    public void importExcel(String filename, InputStream inputStream, String username) {
        String lockKey = RedisKeyPrefix.PROCEDURE_IMPORT_LOCK, importProgressKey = RedisKeyPrefix.PROCEDURE_IMPORT_PROGRESS;
        //初始化锁 + 进度
        importProgressService.initLockProgress(lockKey, importProgressKey);

        File templateFile = null;
        try {
            // 将上传的文件保存到临时文件,方便后面多次读取
            templateFile = new File(PathUtils.getAbsolutePath(this.getClass()) + "/temp/" + UUID.randomUUID() + ExcelUtil.XLSX);
            if (!templateFile.getParentFile().exists()) {
                templateFile.getParentFile().mkdirs();
            }
            FileOutputStream fileOutputStream = new FileOutputStream(templateFile);
            IOUtils.copy(inputStream, fileOutputStream);
            fileOutputStream.close();
            inputStream.close();

            // 列表sheet的顺序不能更改,导入是按顺序的
            int sheetNo = 1;
            // 工序，读取excel第二个sheet,从第3行开始读的数据
            List<ProcedureTempExcelDTO> procedureList = EasyExcelUtil.read(new FileInputStream(templateFile), ProcedureTempExcelDTO.class, sheetNo++, 2);
            // 工序定义检验项，读取excel第3个sheet,从第3行开始读的数据
            List<ProcedureInspectExcelDTO> procedureInspects = EasyExcelUtil.read(new FileInputStream(templateFile), ProcedureInspectExcelDTO.class, sheetNo++, 2);
            // 工序不良项，读取excel第4个sheet,从第3行开始读的数据
            List<ProcedureDefectExcelDTO> procedureDefects = EasyExcelUtil.read(new FileInputStream(templateFile), ProcedureDefectExcelDTO.class, sheetNo++, 2);
            // 工艺参数，读取excel第5个sheet,从第3行开始读的数据
            List<ProcedureParameterExcelDTO> procedureParameters = EasyExcelUtil.read(new FileInputStream(templateFile), ProcedureParameterExcelDTO.class, sheetNo++, 2);
            // 工序用料
            List<ProcedureMaterialUsedExcelDTO> processMaterialUsed = EasyExcelUtil.read(Files.newInputStream(templateFile.toPath()), ProcedureMaterialUsedExcelDTO.class, sheetNo++, 2);
            // 工序设备
            List<ProcedureDeviceTypeExcelDTO> processDeviceTypes = EasyExcelUtil.read(Files.newInputStream(templateFile.toPath()), ProcedureDeviceTypeExcelDTO.class, sheetNo++, 2);
            // 工序工时
            List<ProcedureRelationWorkHoursExcelDTO> processWorkHours = EasyExcelUtil.read(Files.newInputStream(templateFile.toPath()), ProcedureRelationWorkHoursExcelDTO.class, sheetNo++, 2);
            // 工艺控制
            List<ProcedureControllerConfigExcelDTO> processControllerConfig = EasyExcelUtil.read(Files.newInputStream(templateFile.toPath()), ProcedureControllerConfigExcelDTO.class, sheetNo++, 2);
            // 工艺工装
            List<ProcedureProcessAssemblyExcelDTO> processAssembly = EasyExcelUtil.read(Files.newInputStream(templateFile.toPath()), ProcedureProcessAssemblyExcelDTO.class, sheetNo++, 2);
            // 工艺人力
            List<ProcedurePostExcelDTO> post = EasyExcelUtil.read(Files.newInputStream(templateFile.toPath()), ProcedurePostExcelDTO.class, sheetNo++, 2);


            // 校验工序模板数据
            importProgressService.updateProgress(importProgressKey, "校验工序数据中...", 0.1, false, null);
            List<ProcedureTempExcelDTO> canImportRecords = checkProcedureList(procedureList);
            // 保存工序
            saveImportDTOs(canImportRecords, username);

            // 校验工序检验配置
            importProgressService.updateProgress(importProgressKey, "校验工序定义检验项数据中...", 0.2, false, null);
            List<ProcedureInspectExcelDTO> canImportProcedureInspects = checkProcedureInspectList(procedureInspects);
            saveProcedureInspects(canImportProcedureInspects, username);

            // 校验工序不良项
            importProgressService.updateProgress(importProgressKey, "校验工序不良项数据中...", 0.3, false, null);
            List<ProcedureDefectExcelDTO> canImportProcedureDefects = checkProcedureDefectList(procedureDefects);
            saveProcedureDefects(canImportProcedureDefects, username);

            // 校验工艺参数
            importProgressService.updateProgress(importProgressKey, "校验工艺参数数据中...", 0.4, false, null);
            List<ProcedureParameterExcelDTO> canImportProcedureParameters = checkProcedureParameterList(procedureParameters);
            saveProcedureParameters(canImportProcedureParameters);

            // 校验工序用料
            importProgressService.updateProgress(importProgressKey, "校验工序用料数据中...", 0.5, false, null);
            List<ProcedureMaterialUsedExcelDTO> canImportProcessMaterialUsed = checkProcedureMaterialUsed(processMaterialUsed);
            saveProcedureMaterialUsed(canImportProcessMaterialUsed);

            // 校验工序设备
            importProgressService.updateProgress(importProgressKey, "校验工序设备数据中...", 0.55, false, null);
            List<ProcedureDeviceTypeExcelDTO> canImportProcessDeviceType = checkProcedureDeviceType(processDeviceTypes);
            saveProcedureDeviceType(canImportProcessDeviceType);

            // 校验工序工时
            importProgressService.updateProgress(importProgressKey, "校验工序工时数据中...", 0.6, false, null);
            List<ProcedureRelationWorkHoursExcelDTO> canImportProcessRelationWorkHours = checkProcedureRelationWorkHours(processWorkHours);
            saveProcedureRelationWorkHours(canImportProcessRelationWorkHours);

            // 校验工序控制
            importProgressService.updateProgress(importProgressKey, "校验工序控制数据中...", 0.65, false, null);
            List<ProcedureControllerConfigExcelDTO> canImportProcessControllerConfig = checkProcedureControllerConfig(processControllerConfig);
            saveProcedureControllerConfig(canImportProcessControllerConfig);

            // 校验工序工装
            importProgressService.updateProgress(importProgressKey, "校验工序工装数据中...", 0.7, false, null);
            List<ProcedureProcessAssemblyExcelDTO> canImportProcessAssembly = checkProcedureProcessAssembly(processAssembly);
            saveProcedureProcessAssembly(canImportProcessAssembly);

            // 校验工序人力
            importProgressService.updateProgress(importProgressKey, "校验工序工装数据中...", 0.8, false, null);
            List<ProcedurePostExcelDTO> canImportPost = checkProcedurePost(post);
            saveProcedurePost(canImportPost);

            // 质检方案
            saveProcedureDefectScheme(canImportRecords);
            // 维修方案
            saveProcedureMaintainScheme(canImportRecords);

            // 处理日志
            //验证结果上传文件服务器
            List<EasyExcelDataDTO> excelDatas = new ArrayList<>();
            excelDatas.add(EasyExcelDataDTO.builder().excelDataList(procedureList).headerClass(ProcedureTempExcelDTO.class).sheetName("工序定义").build());
            excelDatas.add(EasyExcelDataDTO.builder().excelDataList(procedureInspects).headerClass(ProcedureInspectExcelDTO.class).sheetName("工序定义检验项").build());
            excelDatas.add(EasyExcelDataDTO.builder().excelDataList(procedureDefects).headerClass(ProcedureDefectExcelDTO.class).sheetName("工序不良项").build());
            excelDatas.add(EasyExcelDataDTO.builder().excelDataList(procedureParameters).headerClass(ProcedureParameterExcelDTO.class).sheetName("工艺参数").build());
            excelDatas.add(EasyExcelDataDTO.builder().excelDataList(processMaterialUsed).headerClass(ProcedureMaterialUsedExcelDTO.class).sheetName("工艺用料").build());
            excelDatas.add(EasyExcelDataDTO.builder().excelDataList(processDeviceTypes).headerClass(ProcedureDeviceTypeExcelDTO.class).sheetName("工序设备").build());
            excelDatas.add(EasyExcelDataDTO.builder().excelDataList(processWorkHours).headerClass(ProcedureRelationWorkHoursExcelDTO.class).sheetName("工序工时").build());
            excelDatas.add(EasyExcelDataDTO.builder().excelDataList(processControllerConfig).headerClass(ProcedureControllerConfigExcelDTO.class).sheetName("工艺控制").build());
            excelDatas.add(EasyExcelDataDTO.builder().excelDataList(processAssembly).headerClass(ProcedureProcessAssemblyExcelDTO.class).sheetName("工艺工装").build());
            excelDatas.add(EasyExcelDataDTO.builder().excelDataList(post).headerClass(ProcedurePostExcelDTO.class).sheetName("工艺人力").build());
            String importUrl = importProgressService.verifyResultToExcelUploadTo(excelDatas, username);

            //4、保存导入记录
            int total = procedureList.size() + procedureInspects.size() + procedureDefects.size() + procedureParameters.size();
            int countPass = canImportRecords.size() + canImportProcedureInspects.size() + canImportProcedureDefects.size() + canImportProcedureParameters.size();
            importDataRecordService.save(ImportDataRecordEntity.builder()
                    .importFileName(filename)
                    .importType(ImportTypeEnum.PROCEDURE_IMPORT.getType())
                    .importTypeName(ImportTypeEnum.PROCEDURE_IMPORT.getTypeName())
                    .createBy(username)
                    .createTime(new Date())
                    .failNumber(total - countPass)
                    .successNumber(countPass)
                    .importTime(new Date())
                    .logUrl(importUrl)
                    .operator(username).build());
            importProgressService.updateProgress(importProgressKey, importUrl, true, total, countPass, total - countPass);
        } catch (Exception e) {
            importProgressService.importProgressException(importProgressKey, e);
            log.warn("excel导入数据错误", e);
        } finally {
            // 释放锁
            IOUtils.closeQuietly(inputStream);
            FileUtils.deleteQuietly(templateFile);
            importProgressService.releaseLock(lockKey);
        }
    }

    private void saveProcedureDefectScheme(List<ProcedureTempExcelDTO> canImportRecords) {
        List<ProcedureDefDefectSchemeEntity> saver = canImportRecords.stream().filter(e -> e.getDefectSchemeId() != null).map(e -> {
            ProcedureDefDefectSchemeEntity r = ProcedureDefDefectSchemeEntity.builder()
                    .schemeId(e.getDefectSchemeId())
                    .schemeName(e.getDefectSchemeName())
                    .build();
            r.setProcedureCode(e.getProcedureCode());
            return r;
        }).collect(Collectors.toList());
        procedureDefDefectSchemeService.saveBatch(saver);
    }

    private void saveProcedureMaintainScheme(List<ProcedureTempExcelDTO> canImportRecords) {
        List<ProcedureDefMaintainSchemeEntity> saver = canImportRecords.stream().filter(e -> e.getMaintainSchemeId() != null).map(e -> {
            ProcedureDefMaintainSchemeEntity r = ProcedureDefMaintainSchemeEntity.builder()
                    .maintainId(e.getMaintainSchemeId())
                    .maintainName(e.getMaintainSchemeName())
                    .build();
            r.setProcedureCode(e.getProcedureCode());
            return r;
        }).collect(Collectors.toList());
        procedureDefMaintainSchemeService.saveBatch(saver);
    }


    private List<ProcedurePostExcelDTO> checkProcedurePost(List<ProcedurePostExcelDTO> excels) {
        if (CollectionUtils.isEmpty(excels)) {
            return new ArrayList<>();
        }
        List<ProcedurePostExcelDTO> canImports = new ArrayList<>();
        for (ProcedurePostExcelDTO importDTO : excels) {
            StringBuilder importResult = new StringBuilder();
            boolean canImport = true;
            if (StringUtils.isBlank(importDTO.getProcedureName())) {
                importResult.append("工序名称未填写;");
                canImport = false;
            } else {
                ProcedureEntity procedureEntity = this.lambdaQuery().eq(ProcedureEntity::getName, importDTO.getProcedureName()).one();
                if (procedureEntity == null) {
                    importResult.append("工序名称在系统中不存在;");
                    canImport = false;
                } else {
                    importDTO.setProcedureCode(procedureEntity.getProcedureCode());
                }
            }
            // 工装编号
            if (StringUtils.isBlank(importDTO.getPostCode())) {
                importResult.append("岗位编号未填写;");
                canImport = false;
            } else {
                SysPostEntity post = sysPostService.lambdaQuery().eq(SysPostEntity::getPostCode, importDTO.getPostCode()).last(Constant.LIMIT_ONE).one();
                if (post == null) {
                    importResult.append("岗位编号在系统中不存在;");
                    canImport = false;
                } else {
                    importDTO.setPostId(post.getId());
                    importDTO.setPostName(post.getPostName());
                    importDTO.setPostLevel(post.getPostLevel());
                }
            }
            if (StringUtils.isNotBlank(importDTO.getNumber())) {
                if (!isNonNegativeNumber(importDTO.getNumber())) {
                    importResult.append("数量只能为非负数;");
                    canImport = false;
                }
            } else {
                importResult.append("数量未填写;");
                canImport = false;
            }
            // 收尾
            importDTO.setVerifyPass(canImport);
            if (Boolean.TRUE.equals(importDTO.getVerifyPass())) {
                canImports.add(importDTO);
                importDTO.setImportResult("数据校验通过");
            } else {
                importDTO.setImportResult(importResult.toString());
            }
        }
        return canImports;
    }


    private List<ProcedureProcessAssemblyExcelDTO> checkProcedureProcessAssembly(List<ProcedureProcessAssemblyExcelDTO> excels) {
        if (CollectionUtils.isEmpty(excels)) {
            return new ArrayList<>();
        }
        List<ProcedureProcessAssemblyExcelDTO> canImports = new ArrayList<>();
        for (ProcedureProcessAssemblyExcelDTO importDTO : excels) {
            StringBuilder importResult = new StringBuilder();
            boolean canImport = true;
            if (StringUtils.isBlank(importDTO.getProcedureName())) {
                importResult.append("工序名称未填写;");
                canImport = false;
            } else {
                ProcedureEntity procedureEntity = this.lambdaQuery().eq(ProcedureEntity::getName, importDTO.getProcedureName()).one();
                if (procedureEntity == null) {
                    importResult.append("工序名称在系统中不存在;");
                    canImport = false;
                } else {
                    importDTO.setProcedureCode(procedureEntity.getProcedureCode());
                }
            }
            // 工装编号
            if (StringUtils.isBlank(importDTO.getProcessAssemblyCode())) {
                importResult.append("工装编号未填写;");
                canImport = false;
            } else {
                ProcessAssemblyEntity processAssembly = processAssemblyService.lambdaQuery().eq(ProcessAssemblyEntity::getCode, importDTO.getProcessAssemblyCode()).last(Constant.LIMIT_ONE).one();
                if (processAssembly == null) {
                    importResult.append("工装编号在系统中不存在;");
                    canImport = false;
                } else {
                    importDTO.setProcessAssemblyName(processAssembly.getName());
                    importDTO.setProcessAssemblyType(processAssembly.getType());
                    importDTO.setUnit(processAssembly.getUnit());
                }
            }
            if (StringUtils.isNotBlank(importDTO.getNumber())) {
                if (!isNonNegativeNumber(importDTO.getNumber())) {
                    importResult.append("数量只能为非负数;");
                    canImport = false;
                }
            } else {
                importResult.append("数量未填写;");
                canImport = false;
            }

            // 收尾
            importDTO.setVerifyPass(canImport);
            if (Boolean.TRUE.equals(importDTO.getVerifyPass())) {
                canImports.add(importDTO);
                importDTO.setImportResult("数据校验通过");
            } else {
                importDTO.setImportResult(importResult.toString());
            }
        }
        return canImports;
    }


    private List<ProcedureControllerConfigExcelDTO> checkProcedureControllerConfig(List<ProcedureControllerConfigExcelDTO> excels) {
        if (CollectionUtils.isEmpty(excels)) {
            return new ArrayList<>();
        }
        List<ProcedureControllerConfigExcelDTO> canImports = new ArrayList<>();
        for (ProcedureControllerConfigExcelDTO importDTO : excels) {
            StringBuilder importResult = new StringBuilder();
            boolean canImport = true;
            if (StringUtils.isBlank(importDTO.getProcedureName())) {
                importResult.append("工序名称未填写;");
                canImport = false;
            } else {
                ProcedureEntity procedureEntity = this.lambdaQuery().eq(ProcedureEntity::getName, importDTO.getProcedureName()).one();
                if (procedureEntity == null) {
                    importResult.append("工序名称在系统中不存在;");
                    canImport = false;
                } else {
                    importDTO.setProcedureCode(procedureEntity.getProcedureCode());
                }
            }
            // 工序流转条件类型
            if (StringUtils.isNotEmpty(importDTO.getStandardCirculationDurationTypeName())
                    && Objects.isNull(ProcedureFlowTypeEnum.getCodeByName(importDTO.getStandardCirculationDurationTypeName()))) {
                importResult.append("工序流转条件类型填写有误；");
                canImport = false;
            } else {
                importDTO.setStandardCirculationDurationType(StringUtils.isBlank(importDTO.getStandardCirculationDurationTypeName()) ? ProcedureFlowTypeEnum.TIME.getCode() : ProcedureFlowTypeEnum.getCodeByName(importDTO.getStandardCirculationDurationTypeName()));
            }
            if (StringUtils.isNotBlank(importDTO.getStandardCirculationDuration()) && !isNonNegativeNumber(importDTO.getStandardCirculationDuration())) {
                importResult.append("工序流转条件只能为非负数;");
                canImport = false;
            }
            if (StringUtils.isNotBlank(importDTO.getMaxFailCount()) && !isNonNegativeInteger(importDTO.getMaxFailCount())) {
                importResult.append("最大失败次数只能为非负整数;");
                canImport = false;
            }
            if (StringUtils.isNotBlank(importDTO.getConversionFactor()) && !isNonNegativeNumber(importDTO.getConversionFactor())) {
                importResult.append("换算系数只能为非负数;");
                canImport = false;
            }

            // 检查项
            if (StringUtils.isNotBlank(importDTO.getCheckItems())) {
                List<String> checkNames = Arrays.stream(importDTO.getCheckItems().split(Constant.SEP)).map(String::trim).collect(Collectors.toList());
                for (String checkName : checkNames) {
                    ControlItemEnum checkEnum = ControlItemEnum.getByName(checkName);
                    if (checkEnum == null) {
                        importResult.append("工序检查项:").append(checkName).append("不存在;");
                        canImport = false;
                    }
                }
                Set<ControlItemEnum> itemSet = checkNames.stream().map(ControlItemEnum::getByName).collect(Collectors.toSet());
                importDTO.setJumpStationCheck(itemSet.contains(ControlItemEnum.JUMP_STATION_CHECK));
                importDTO.setReformCheck(itemSet.contains(ControlItemEnum.REFORM_CHECK));
                importDTO.setMaterialCheck(itemSet.contains(ControlItemEnum.MATERIAL_CHECK));
                importDTO.setProductionCheck(itemSet.contains(ControlItemEnum.PRODUCTION_CHECK));
                importDTO.setLastValueCheck(itemSet.contains(ControlItemEnum.LAST_VALUE_CHECK));
                importDTO.setSignatureCheck(itemSet.contains(ControlItemEnum.SIGNATURE_CHECK));
                importDTO.setInputCheck(itemSet.contains(ControlItemEnum.INPUT_CHECK));
            }
            // 生产超时配置数值
            String productionUnitName = importDTO.getProductionTimeoutThresholdConfigurationUnitName();
            if (StringUtils.isNotBlank(productionUnitName)) {
                String productionUnit = TimeoutThresholdTypeEnum.getCodeByName(productionUnitName);
                if (StringUtils.isEmpty(productionUnit)) {
                    importResult.append("生产超时配置数值类型:").append(productionUnitName).append("不存在;");
                    canImport = false;
                } else {
                    importDTO.setProductionTimeoutThresholdConfigurationUnit(productionUnit);
                }
            } else {
                importDTO.setProductionTimeoutThresholdConfigurationUnit(TimeoutThresholdTypeEnum.FIXED_VALUE.getCode());
            }
            if (StringUtils.isNotBlank(importDTO.getProductionTimeoutThresholdConfiguration()) && !isNonNegativeNumber(importDTO.getProductionTimeoutThresholdConfiguration())) {
                importResult.append("生产超时配置只能为非负数;");
                canImport = false;
            }

            // 流转超时阈值配置
            String flowUnitName = importDTO.getFlowTimeoutThresholdConfigurationUnitTypeName();
            if (StringUtils.isNotBlank(flowUnitName)) {
                String flowUnit = TimeoutThresholdTypeEnum.getCodeByName(flowUnitName);
                if (StringUtils.isEmpty(flowUnit)) {
                    importResult.append("流转超时阈值配置类型:").append(flowUnitName).append("不存在;");
                    canImport = false;
                } else {
                    importDTO.setFlowTimeoutThresholdConfigurationUnitType(flowUnit);
                }
            } else {
                importDTO.setFlowTimeoutThresholdConfigurationUnitType(TimeoutThresholdTypeEnum.FIXED_VALUE.getCode());
            }
            if (StringUtils.isNotBlank(importDTO.getFlowTimeoutThresholdConfiguration()) && !isNonNegativeNumber(importDTO.getFlowTimeoutThresholdConfiguration())) {
                importResult.append("流转超时阈值配置只能为非负数;");
                canImport = false;
            }

            // 收尾
            importDTO.setVerifyPass(canImport);
            if (Boolean.TRUE.equals(importDTO.getVerifyPass())) {
                canImports.add(importDTO);
                importDTO.setImportResult("数据校验通过");
            } else {
                importDTO.setImportResult(importResult.toString());
            }
        }
        return canImports;
    }


    private List<ProcedureRelationWorkHoursExcelDTO> checkProcedureRelationWorkHours(List<ProcedureRelationWorkHoursExcelDTO> excels) {
        if (CollectionUtils.isEmpty(excels)) {
            return new ArrayList<>();
        }
        List<ProcedureRelationWorkHoursExcelDTO> canImports = new ArrayList<>();
        for (ProcedureRelationWorkHoursExcelDTO importDTO : excels) {
            StringBuilder importResult = new StringBuilder();
            boolean canImport = true;
            if (StringUtils.isBlank(importDTO.getProcedureName())) {
                importResult.append("工序名称未填写;");
                canImport = false;
            } else {
                ProcedureEntity procedureEntity = this.lambdaQuery().eq(ProcedureEntity::getName, importDTO.getProcedureName()).one();
                if (procedureEntity == null) {
                    importResult.append("工序名称在系统中不存在;");
                    canImport = false;
                } else {
                    importDTO.setProcedureCode(procedureEntity.getProcedureCode());
                    if(procedureDefRelationWorkHoursService.oneByProcedure(procedureEntity.getProcedureCode())!=null) {
                        importResult.append("该项已在系统中存在;");
                        canImport = false;
                    }
                }
            }
            if (StringUtils.isNotBlank(importDTO.getPreparationTime()) && !isNonNegativeNumber(importDTO.getPreparationTime())) {
                importResult.append("标准调试工时只能为非负数;");
                canImport = false;
            }
            if (StringUtils.isNotBlank(importDTO.getProcessingHours()) && !isNonNegativeNumber(importDTO.getProcessingHours())) {
                importResult.append("单件加工工时只能为非负数;");
                canImport = false;
            }
            if (StringUtils.isNotBlank(importDTO.getDefaultTime()) && !isNonNegativeNumber(importDTO.getDefaultTime())) {
                importResult.append("缺省工時只能为非负数;");
                canImport = false;
            }
            if (StringUtils.isNotBlank(importDTO.getInputNum()) && !isNonNegativeInteger(importDTO.getInputNum())) {
                importResult.append("投入人数只能为非负整数;");
                canImport = false;
            }

            if (StringUtils.isEmpty(importDTO.getPreparationTimeUnit())) {
                importDTO.setPreparationTimeUnit(Constant.HOUR);
            } else {
                if (!Constant.HOUR.equals(importDTO.getPreparationTimeUnit()) && !Constant.MINUTES.equals(importDTO.getPreparationTimeUnit()) && !Constant.SECOND.equals(importDTO.getPreparationTimeUnit())) {
                    importResult.append("标准调试工时单位错误格式错误;");
                    canImport = false;
                }
            }
            if (StringUtils.isEmpty(importDTO.getProcessingHoursUnit())) {
                importDTO.setProcessingHoursUnit(Constant.HOUR);
            } else {
                if (!Constant.HOUR.equals(importDTO.getProcessingHoursUnit()) && !Constant.MINUTES.equals(importDTO.getProcessingHoursUnit()) && !Constant.SECOND.equals(importDTO.getProcessingHoursUnit())) {
                    importResult.append("加工工时单位格式错误;");
                    canImport = false;
                }
            }
            if (StringUtils.isEmpty(importDTO.getDefaultTimeUnit())) {
                importDTO.setDefaultTimeUnit(Constant.HOUR);
            } else {
                if (!Constant.HOUR.equals(importDTO.getDefaultTimeUnit()) && !Constant.MINUTES.equals(importDTO.getDefaultTimeUnit()) && !Constant.SECOND.equals(importDTO.getDefaultTimeUnit())) {
                    importResult.append("缺省工时单位格式错误;");
                    canImport = false;
                }
            }
            if (StringUtils.isNotBlank(importDTO.getDegreeOfDifficulty()) && !isNonNegativeNumber(importDTO.getDegreeOfDifficulty())) {
                importResult.append("缺省工時只能为非负数;");
                canImport = false;
            }
            if (StringUtils.isEmpty(importDTO.getIsByWorkName())) {
                importDTO.setIsByWork(false);
            } else {
                Boolean isByWork = WhetherEnum.getCodeByName(importDTO.getIsByWorkName());
                if (isByWork == null) {
                    importResult.append("是否按工时计算格式错误;");
                    canImport = false;
                } else {
                    importDTO.setIsByWork(isByWork);
                }
            }
            // 收尾
            importDTO.setVerifyPass(canImport);
            if (Boolean.TRUE.equals(importDTO.getVerifyPass())) {
                canImports.add(importDTO);
                importDTO.setImportResult("数据校验通过");
            } else {
                importDTO.setImportResult(importResult.toString());
            }
        }
        return canImports;
    }

    private List<ProcedureDeviceTypeExcelDTO> checkProcedureDeviceType(List<ProcedureDeviceTypeExcelDTO> excels) {
        if (CollectionUtils.isEmpty(excels)) {
            return new ArrayList<>();
        }
        List<ProcedureDeviceTypeExcelDTO> canImports = new ArrayList<>();
        for (ProcedureDeviceTypeExcelDTO importDTO : excels) {
            StringBuilder importResult = new StringBuilder();
            boolean canImport = true;
            if (StringUtils.isBlank(importDTO.getProcedureName())) {
                importResult.append("工序名称未填写;");
                canImport = false;
            } else {
                ProcedureEntity procedureEntity = this.lambdaQuery().eq(ProcedureEntity::getName, importDTO.getProcedureName()).one();
                if (procedureEntity == null) {
                    importResult.append("工序名称在系统中不存在;");
                    canImport = false;
                } else {
                    importDTO.setProcedureCode(procedureEntity.getProcedureCode());
                }
            }

            if (StringUtils.isBlank(importDTO.getDeviceTypeName())) {
                importResult.append("设备类型未填写;");
                canImport = false;
            } else {
                ModelEntity model = modelService.getDeviceModelByName(importDTO.getDeviceTypeName());
                if (model == null) {
                    importResult.append("设备类型不存在;");
                    canImport = false;
                } else {
                    importDTO.setDeviceModelId(model.getId());
                }
            }
            if (StringUtils.isNotBlank(importDTO.getNumber()) && !isNonNegativeInteger(importDTO.getNumber())) {
                importResult.append("用量只能为非负整数;");
                canImport = false;
            }

            // 收尾
            importDTO.setVerifyPass(canImport);
            if (Boolean.TRUE.equals(importDTO.getVerifyPass())) {
                canImports.add(importDTO);
                importDTO.setImportResult("数据校验通过");
            } else {
                importDTO.setImportResult(importResult.toString());
            }
        }
        return canImports;
    }

    private List<ProcedureMaterialUsedExcelDTO> checkProcedureMaterialUsed(List<ProcedureMaterialUsedExcelDTO> excels) {
        if (CollectionUtils.isEmpty(excels)) {
            return new ArrayList<>();
        }
        List<ProcedureMaterialUsedExcelDTO> canImports = new ArrayList<>();
        for (ProcedureMaterialUsedExcelDTO importDTO : excels) {
            StringBuilder importResult = new StringBuilder();
            boolean canImport = true;
            if (StringUtils.isBlank(importDTO.getProcedureName())) {
                importResult.append("工序名称未填写;");
                canImport = false;
            } else {
                ProcedureEntity procedureEntity = this.lambdaQuery().eq(ProcedureEntity::getName, importDTO.getProcedureName()).one();
                if (procedureEntity == null) {
                    importResult.append("工序名称在系统中不存在;");
                    canImport = false;
                } else {
                    importDTO.setProcedureCode(procedureEntity.getProcedureCode());
                }
            }
            if (StringUtils.isBlank(importDTO.getMaterialCode())) {
                importResult.append("物料编码未填写;");
                canImport = false;
            }
            MaterialEntity material = materialService.getSimpleMaterialByCode(importDTO.getMaterialCode());
            if (Objects.isNull(material)) {
                importResult.append("物料编码不存在;");
                canImport = false;
            } else {
                importDTO.setMaterialName(material.getName());
                importDTO.setUnit(material.getComp());
            }
            if (StringUtils.isNotBlank(importDTO.getNumber()) && !isNonNegativeNumber(importDTO.getNumber())) {
                importResult.append("用量只能为非负数;");
                canImport = false;
            }

            // 收尾
            importDTO.setVerifyPass(canImport);
            if (Boolean.TRUE.equals(importDTO.getVerifyPass())) {
                canImports.add(importDTO);
                importDTO.setImportResult("数据校验通过");
            } else {
                importDTO.setImportResult(importResult.toString());
            }
        }
        return canImports;
    }

    private void saveProcedurePost(List<ProcedurePostExcelDTO> excels) {
        procedureDefPostService.saveBatch(JacksonUtil.convertArray(excels, ProcedureDefPostEntity.class));
    }

    private void saveProcedureProcessAssembly(List<ProcedureProcessAssemblyExcelDTO> excels) {
        procedureDefProcessAssemblyService.saveBatch(JacksonUtil.convertArray(excels, ProcedureDefProcessAssemblyEntity.class));
    }

    private void saveProcedureControllerConfig(List<ProcedureControllerConfigExcelDTO> excels) {
        procedureDefControllerConfigService.saveBatch(JacksonUtil.convertArray(excels, ProcedureDefControllerConfigEntity.class));
    }

    private void saveProcedureRelationWorkHours(List<ProcedureRelationWorkHoursExcelDTO> excels) {
        List<ProcedureDefRelationWorkHoursEntity> workHoursEntities = JacksonUtil.convertArray(excels, ProcedureDefRelationWorkHoursEntity.class);
        for (ProcedureDefRelationWorkHoursEntity workHoursEntity : workHoursEntities) {
            if (workHoursEntity.getProcessingHours() != null && workHoursEntity.getInputNum() != null) {
                double theoryHours = MathUtil.mul(workHoursEntity.getProcessingHours(), workHoursEntity.getInputNum());
                workHoursEntity.setTheoryHours(theoryHours);
            }
            workHoursEntity.setTheoryHoursUnit(workHoursEntity.getProcessingHoursUnit());
        }
        procedureDefRelationWorkHoursService.saveBatch(workHoursEntities);
    }

    private void saveProcedureDeviceType(List<ProcedureDeviceTypeExcelDTO> excels) {
        procedureDefDeviceTypeService.saveBatch(JacksonUtil.convertArray(excels, ProcedureDefDeviceTypeEntity.class));
    }

    private void saveProcedureMaterialUsed(List<ProcedureMaterialUsedExcelDTO> excels) {
        procedureDefMaterialUsedService.saveBatch(JacksonUtil.convertArray(excels, ProcedureDefMaterialUsedEntity.class));
    }


    /**
     * 保存工艺参数
     */
    private void saveProcedureParameters(List<ProcedureParameterExcelDTO> parameterExcels) {
        for (ProcedureParameterExcelDTO dto : parameterExcels) {
            String procedureCode = dto.getProcedureCode();
            ProcedureProcessParameterEntity build = ProcedureProcessParameterEntity.builder()
                    .procedureCode(procedureCode)
                    .parameterCoding(dto.getParameterCoding())
                    .parameterName(dto.getParameterName())
                    .type(dto.getType())
                    .unit(dto.getUnit())
                    .inputMode(dto.getInputMode())
                    .optionalValue(dto.getOptionalValue())
                    .computingMethod(dto.getComputingMethod())
                    .remark(dto.getRemark())
                    .build();
            // 查看数据库是否存在，存更不存增
            ProcedureProcessParameterEntity parameterEntity = procedureProcessParameterService.lambdaQuery()
                    .eq(ProcedureProcessParameterEntity::getProcedureCode, procedureCode)
                    .eq(ProcedureProcessParameterEntity::getParameterCoding, dto.getParameterCoding())
                    .one();
            if (parameterEntity != null) {
                procedureProcessParameterService.lambdaUpdate()
                        .eq(ProcedureProcessParameterEntity::getId, parameterEntity.getId())
                        .set(ProcedureProcessParameterEntity::getParameterName, build.getParameterName())
                        .set(ProcedureProcessParameterEntity::getType, build.getType())
                        .set(ProcedureProcessParameterEntity::getUnit, build.getUnit())
                        .set(ProcedureProcessParameterEntity::getInputMode, build.getInputMode())
                        .set(ProcedureProcessParameterEntity::getOptionalValue, build.getOptionalValue())
                        .set(ProcedureProcessParameterEntity::getComputingMethod, build.getComputingMethod())
                        .set(ProcedureProcessParameterEntity::getRemark, build.getRemark())
                        .update();
            } else {
                procedureProcessParameterService.save(build);
            }
        }
    }

    private boolean isNonNegativeInteger(String val) {
        if (StringUtils.isEmpty(val)) {
            return false;
        }
        if (!NumberUtil.isInteger(val)) {
            return false;
        }
        BigDecimal b = new BigDecimal(val);
        return b.compareTo(BigDecimal.ZERO) >= 0;
    }

    private boolean isNonNegativeNumber(String val) {
        if (StringUtils.isEmpty(val)) {
            return false;
        }
        if (!NumberUtil.isNumber(val)) {
            return false;
        }
        BigDecimal b = new BigDecimal(val);
        return b.compareTo(BigDecimal.ZERO) >= 0;
    }

    /**
     * 校验工艺参数
     */
    private List<ProcedureParameterExcelDTO> checkProcedureParameterList(List<ProcedureParameterExcelDTO> procedureParameters) {
        if (CollectionUtils.isEmpty(procedureParameters)) {
            return new ArrayList<>();
        }
        List<String> typeNames = ProcessParameterTypeEnum.getNames();
        List<String> inputNames = ProcessParameterInputModeEnum.getNames();

        List<ProcedureParameterExcelDTO> canImports = new ArrayList<>();
        boolean canImport;
        StringBuilder importResult;
        for (ProcedureParameterExcelDTO importDTO : procedureParameters) {
            importResult = new StringBuilder();
            canImport = true;

            if (StringUtils.isBlank(importDTO.getProcedureName())) {
                importResult.append("工序名称未填写;");
                canImport = false;
            }
            ProcedureEntity procedureEntity = this.lambdaQuery().eq(ProcedureEntity::getName, importDTO.getProcedureName()).one();
            if (procedureEntity == null) {
                importResult.append("工序名称在系统中不存在;");
                canImport = false;
            } else {
                importDTO.setProcedureCode(procedureEntity.getProcedureCode());
            }
            // 类型校验
            if (!typeNames.contains(importDTO.getTypeName())) {
                importResult.append("类型必须是 字符/数值/布尔/列表;");
                canImport = false;
            } else {
                importDTO.setType(ProcessParameterTypeEnum.getCodeByName(importDTO.getTypeName()));
            }
            // 输入方式校验
            if (!inputNames.contains(importDTO.getInputModeName())) {
                importResult.append("输入方式必须是 下拉单选/下拉多选/输入;");
                canImport = false;
            } else {
                importDTO.setInputMode(ProcessParameterInputModeEnum.getCodeByName(importDTO.getInputModeName()));
            }
            // 单位
            if (StringUtils.isNotEmpty(importDTO.getUnit())) {
                DictEntity one = dictService.lambdaQuery()
                        .eq(DictEntity::getName, importDTO.getUnit())
                        .eq(DictEntity::getType, DictTypeEnum.UNIT.getType())
                        .last(Constant.LIMIT_ONE).one();
                if (Objects.isNull(one)) {
                    importResult.append("单位必须在系统中存在；");
                    canImport = false;
                }
            }

            importDTO.setVerifyPass(canImport);
            if (Boolean.TRUE.equals(importDTO.getVerifyPass())) {
                canImports.add(importDTO);
                importDTO.setImportResult("数据校验通过");
            } else {
                importDTO.setImportResult(importResult.toString());
            }
        }
        return canImports;
    }

    /**
     * 保存不良项目
     */
    private void saveProcedureDefects(List<ProcedureDefectExcelDTO> defectExcels, String username) {
        Date importTime = new Date();
        for (ProcedureDefectExcelDTO dto : defectExcels) {
            String procedureCode = dto.getProcedureCode();
            ProcedureDefDefectDefineEntity build = ProcedureDefDefectDefineEntity.builder()
                    .procedureCode(procedureCode)
                    .defectDefineId(dto.getDefectDefineId())
                    .remark(dto.getRemark())
                    .createTime(importTime)
                    .createBy(username)
                    .build();
            // 查看数据库是否存在，存更不存增
            ProcedureDefDefectDefineEntity inspectionConfigEntity = procedureDefDefectDefineService.lambdaQuery()
                    .eq(ProcedureDefDefectDefineEntity::getProcedureCode, procedureCode)
                    .eq(ProcedureDefDefectDefineEntity::getDefectDefineId, dto.getDefectDefineId())
                    .one();
            if (inspectionConfigEntity != null) {
                procedureDefDefectDefineService.lambdaUpdate()
                        .eq(ProcedureDefDefectDefineEntity::getId, inspectionConfigEntity.getId())
                        .set(ProcedureDefDefectDefineEntity::getRemark, build.getRemark())
                        .update();
            } else {
                procedureDefDefectDefineService.save(build);
            }
        }
    }

    private List<ProcedureDefectExcelDTO> checkProcedureDefectList(List<ProcedureDefectExcelDTO> procedureDefects) {
        if (CollectionUtils.isEmpty(procedureDefects)) {
            return new ArrayList<>();
        }

        List<String> defectCodes = procedureDefects.stream().map(ProcedureDefectExcelDTO::getDefectCode).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        Map<String, Integer> defectCodeIdMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(defectCodes)) {
            LambdaQueryWrapper<DefectDefineEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(DefectDefineEntity::getState, DefectDefineStateEnum.RELEASED.getCode())
                    .in(DefectDefineEntity::getDefectCode, defectCodes);
            defectCodeIdMap = defectDefineMapper.selectList(wrapper).stream()
                    .collect(Collectors.toMap(DefectDefineEntity::getDefectCode, DefectDefineEntity::getDefectId));
        }

        List<ProcedureDefectExcelDTO> canImports = new ArrayList<>();
        boolean canImport;
        StringBuilder importResult;
        for (ProcedureDefectExcelDTO importDTO : procedureDefects) {
            importResult = new StringBuilder();
            canImport = true;

            if (StringUtils.isBlank(importDTO.getProcedureName())) {
                importResult.append("工序名称未填写;");
                canImport = false;
            }
            ProcedureEntity procedureEntity = this.lambdaQuery().eq(ProcedureEntity::getName, importDTO.getProcedureName()).one();
            if (procedureEntity == null) {
                importResult.append("工序名称在系统中不存在;");
                canImport = false;
            } else {
                importDTO.setProcedureCode(procedureEntity.getProcedureCode());
            }
            if (StringUtils.isBlank(importDTO.getDefectCode())) {
                importResult.append("不良代号未填写;");
                canImport = false;
            } else {
                Integer defectId = defectCodeIdMap.get(importDTO.getDefectCode());
                if (Objects.isNull(defectId)) {
                    importResult.append("不良代号系统中未定义;");
                    canImport = false;
                }
                importDTO.setDefectDefineId(defectId);
            }

            importDTO.setVerifyPass(canImport);
            if (Boolean.TRUE.equals(importDTO.getVerifyPass())) {
                canImports.add(importDTO);
                importDTO.setImportResult("数据校验通过");
            } else {
                importDTO.setImportResult(importResult.toString());
            }
        }
        return canImports;
    }

    /**
     * 保存工序检验
     */
    private void saveProcedureInspects(List<ProcedureInspectExcelDTO> inspectExcels, String username) {
        Date importTime = new Date();
        for (ProcedureInspectExcelDTO dto : inspectExcels) {
            String procedureCode = dto.getProcedureCode();

            List<String> codes = new ArrayList<>();
            if (StringUtils.isNotBlank(dto.getInspectTypeName())) {
                String[] names = dto.getInspectTypeName().split(Constant.SEP);
                for (String name : names) {
                    codes.add(InspectItemTypeEnum.getCodeByName(name));
                }
            }
            String inspectType = null;
            if (!CollectionUtils.isEmpty(codes)) {
                inspectType = String.join(Constant.SEP, codes);
            }
            ProcedureDefInspectionConfigEntity build = ProcedureDefInspectionConfigEntity.builder()
                    .procedureCode(procedureCode)
                    .procedureInspectionId(dto.getInspectId())
                    .description(dto.getDescription())
                    .comparator(InspectComparatorEnum.getCodeByName(dto.getComparatorName()))
                    .standardValue(dto.getStandardValue())
                    .upperLimit(dto.getUpperLimit())
                    .downLimit(dto.getDownLimit())
                    .ngController(dto.getNgController())
                    .unit(dto.getUnit())
                    .inspectType(inspectType)
                    .createTime(importTime)
                    .createBy(username)
                    .build();

            // 查看数据库是否存在，存更不存增
            ProcedureDefInspectionConfigEntity inspectionConfigEntity = procedureDefInspectionConfigService.lambdaQuery()
                    .eq(ProcedureDefInspectionConfigEntity::getProcedureCode, procedureCode)
                    .eq(ProcedureDefInspectionConfigEntity::getProcedureInspectionId, dto.getInspectId())
                    .one();
            if (inspectionConfigEntity != null) {
                procedureDefInspectionConfigService.lambdaUpdate()
                        .eq(ProcedureDefInspectionConfigEntity::getId, inspectionConfigEntity.getId())
                        .set(ProcedureDefInspectionConfigEntity::getDescription, build.getDescription())
                        .set(ProcedureDefInspectionConfigEntity::getComparator, build.getComparator())
                        .set(ProcedureDefInspectionConfigEntity::getStandardValue, build.getStandardValue())
                        .set(ProcedureDefInspectionConfigEntity::getUpperLimit, build.getUpperLimit())
                        .set(ProcedureDefInspectionConfigEntity::getDownLimit, build.getDownLimit())
                        .set(ProcedureDefInspectionConfigEntity::getUpdateTime, build.getUpdateTime())
                        .set(ProcedureDefInspectionConfigEntity::getInspectType, build.getInspectType())
                        .set(ProcedureDefInspectionConfigEntity::getNgController, build.getNgController())
                        .set(ProcedureDefInspectionConfigEntity::getUnit, build.getUnit())
                        .set(ProcedureDefInspectionConfigEntity::getUpdateTime, importTime)
                        .set(ProcedureDefInspectionConfigEntity::getUpdateBy, username)
                        .update();
            } else {
                procedureDefInspectionConfigService.save(build);
            }
        }
    }

    /**
     * 校验工序检验配置
     */
    private List<ProcedureInspectExcelDTO> checkProcedureInspectList(List<ProcedureInspectExcelDTO> procedureInspects) {
        List<ProcedureInspectExcelDTO> canImports = new ArrayList<>();
        boolean canImport;
        StringBuilder importResult;
        // 系统中所有单位
        List<String> allUnitList = dictService.lambdaQuery().eq(DictEntity::getType, DictTypeEnum.UNIT.getType()).list()
                .stream().map(DictEntity::getName).collect(Collectors.toList());
        for (ProcedureInspectExcelDTO importDTO : procedureInspects) {
            importResult = new StringBuilder();
            canImport = true;

            if (StringUtils.isBlank(importDTO.getProcedureName())) {
                importResult.append("工序名称未填写;");
                canImport = false;
            }
            ProcedureEntity procedureEntity = this.lambdaQuery().eq(ProcedureEntity::getName, importDTO.getProcedureName()).one();
            if (procedureEntity == null) {
                importResult.append("工序名称在系统中不存在;");
                canImport = false;
            } else {
                importDTO.setProcedureCode(procedureEntity.getProcedureCode());
            }
            // 比较符校验
            List<String> inspectNames = InspectComparatorEnum.getNames();
            if (!inspectNames.contains(importDTO.getComparatorName())) {
                importResult.append("比较符输入错误，只能是（大于/小于/大于等于/小于等于/范围内/范围外/相等/不等/包含在内/不包含在内/等于）;");
                canImport = false;
            }
            // 上限不能小于下限
            if (StringUtils.isNotBlank(importDTO.getUpperLimit()) && !NumberUtil.isNumber(importDTO.getUpperLimit())) {
                importResult.append("上限值只能为数字;");
                canImport = false;
            }
            if (StringUtils.isNotBlank(importDTO.getDownLimit()) && !NumberUtil.isNumber(importDTO.getDownLimit())) {
                importResult.append("下限值只能为数字;");
                canImport = false;
            }
            if (canImport && StringUtils.isNotBlank(importDTO.getDownLimit()) && StringUtils.isNotBlank(importDTO.getUpperLimit()) && Double.parseDouble(importDTO.getUpperLimit()) < Double.parseDouble(importDTO.getDownLimit())) {
                importResult.append("上限值不能小于下限值;");
                canImport = false;
            }
            // NG控制项校验, 默认为否
            importDTO.setNgController(StringUtils.isNotBlank(importDTO.getNgControllerName()) && Constant.TRUE.equals(importDTO.getNgControllerName()));
            // 单位校验
            if (StringUtils.isNotBlank(importDTO.getUnit()) && !allUnitList.contains(importDTO.getUnit())) {
                importResult.append("单位必须在系统中存在；");
                canImport = false;
            }

            // 校验检验项目，检验代号
            if (StringUtils.isBlank(importDTO.getInspectCode()) || StringUtils.isBlank(importDTO.getInspectItem())) {
                importResult.append("检验代号、检验项目未填写;");
                canImport = false;
            } else {
                LambdaQueryWrapper<ProcedureInspectionEntity> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(ProcedureInspectionEntity::getInspectionCode, importDTO.getInspectCode())
                        .eq(ProcedureInspectionEntity::getInspectionName, importDTO.getInspectItem())
                        .last(Constant.LIMIT_ONE);
                ProcedureInspectionEntity inspectionEntity = procedureInspectionMapper.selectOne(wrapper);
                if (inspectionEntity == null) {
                    importResult.append("未找到对应的检验项目;");
                    canImport = false;
                } else {
                    importDTO.setInspectId(inspectionEntity.getId());
                }
            }

            importDTO.setVerifyPass(canImport);
            if (Boolean.TRUE.equals(importDTO.getVerifyPass())) {
                canImports.add(importDTO);
                importDTO.setImportResult("数据校验通过");
            } else {
                importDTO.setImportResult(importResult.toString());
            }
        }
        return canImports;
    }


    /**
     * 获取可选工作中心列表
     *
     * @param id
     * @return
     */
    @Override
    public List<WorkCenterEntity> listWorkCenter(Integer id) {
        ProcedureEntity procedureEntity = this.getById(id);
        if (StringUtils.isBlank(procedureEntity.getWorkCenterIds())) {
            return null;
        }
        String[] split = procedureEntity.getWorkCenterIds().split(Constant.SEP);
        LambdaQueryWrapper<WorkCenterEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(WorkCenterEntity::getId, Arrays.asList(split));
        return workCenterMapper.selectList(lambdaQueryWrapper);
    }

    @Override
    public Boolean removeBeforeJudge(String procedureCode) {
        // 只有管理员才能删除
        String username = userAuthenService.getUsername();
        List<SysRoleEntity> roleEntities = roleService.selectByUsername(username);
        long count = roleEntities.stream().filter(r -> r.getId().equals(RoleEnum.ADMIN.getCode())).count();
        if (count == 0) {
            throw new ResponseException(RespCodeEnum.NEED_ADMIN_PERMISSION);
        }
        ProcedureEntity procedureEntity = this.lambdaQuery().eq(ProcedureEntity::getProcedureCode, procedureCode).one();
        // 判断工艺是否关联工序定义的数据
        List<CraftProcedureEntity> craftProcedureEntities = craftProcedureService.list(Wrappers.lambdaQuery(CraftProcedureEntity.class)
                .eq(CraftProcedureEntity::getProcedureId, procedureEntity.getProcedureId()));
        if (CollectionUtils.isEmpty(craftProcedureEntities)) {
            return false;
        }
        return true;
    }

    /**
     * 保存数据
     */
    private void saveImportDTOs(List<ProcedureTempExcelDTO> dtos, String username) {
        if (CollectionUtils.isEmpty(dtos)) {
            return;
        }
        Date date = new Date();
        //相同编号归并
//        Map<String, List<ProcedureTempExcelDTO>> map = dtos.stream().collect(Collectors.groupingBy(ProcedureTempExcelDTO::getProcedureCode));
//        Set<Map.Entry<String, List<ProcedureTempExcelDTO>>> entrySet = map.entrySet();
        List<ProcedureEntity> entities = new ArrayList<>();
        Boolean needApprove = approveConfigService.getConfigByCode(ApproveModuleEnum.PROCEDURE.getCode());
        for (ProcedureTempExcelDTO dto : dtos) {
            String procedureCode = dto.getProcedureCode();
            List<ProcedureTempExcelDTO> value = Collections.singletonList(dto);
            ProcedureEntity entity = ProcedureEntity.builder()
                    .procedureCode(procedureCode)
                    .name(dto.getProcedureName())
                    .alias(dto.getAlias())
                    .approver(dto.getApprover())
                    .approvalStatus(dto.getApprovalState())
                    .isSubContractingOperation(dto.getIsOutsourcing())
                    .outSourcingSupplier(dto.getOutsourcingSupplierName())
                    .outSourcingSupplierCode(dto.getOutsourcingSupplierCode())
                    //检验工序
                    .isInspectionOperation(dto.getIsInspection())
                    //质检工序
                    .isQualityProcess(dto.getIsQualityInspection())
                    //维修工序
                    .isMaintenanceProcedure(dto.getIsMaintain())
                    .state(dto.getState())
                    .remark(dto.getRemark())
                    .build();
            if (ProcedureStateEnum.CREATE.getCode().equals(entity.getState()) && Boolean.TRUE.equals(needApprove)) {
                entity.setApprovalStatus(ApprovalStatusEnum.TO_BE_APPROVAL.getCode());
            }
            // 处理状态，创建时间，更新时间，创建人更新人，工作中心ids/名称
            entity.setCreateBy(username);
            entity.setUpdateBy(username);
            entity.setCreateTime(Objects.nonNull(dto.getCreateTime()) ? dto.getCreateTime() : new Date());
            entity.setUpdateTime(date);
            // 生产基本单元类型
            entity.setType(WorkCenterTypeEnum.getCodeByName(dto.getProductionBasicUnit()));
            entity.setWorkCenterIds(dto.getWorkCenterIds());
            entity.setWorkCenterNames(dto.getWorkCenterName());
            //制造单元模型名称/ids
            String lineModelIds = value.stream().filter(o -> o.getLineModelId() != null).map(o -> o.getLineModelId().toString()).collect(Collectors.joining(Constant.SEP));
            String lineModelNames = value.stream().map(ProcedureTempExcelDTO::getLineModelName).filter(StringUtils::isNotBlank).collect(Collectors.joining(Constant.SEP));
            entity.setLineModelIds(lineModelIds);
            entity.setLineModelNames(lineModelNames);
            //前端展示数据
            StringBuilder builder = new StringBuilder();
            StringBuilder facs = new StringBuilder();
            for (ProcedureTempExcelDTO excelDTO : value) {
                Integer lineModelId = excelDTO.getLineModelId();
                List<Integer> facModelIds = excelDTO.getFacModelIds();
                if (!CollectionUtils.isEmpty(facModelIds)) {
                    for (Integer facModelId : facModelIds) {
                        builder.append("[").append(lineModelId).append(Constant.SEP).append(facModelId).append("],");
                        facs.append(facModelId).append(Constant.SEP);
                    }
                }
            }
            entity.setFacModelId(facs.toString());
            if (builder.length() > 0) {
                builder.deleteCharAt(builder.lastIndexOf(","));
            }
            entity.setCascaderJsonStr(builder.insert(0, "[").append("]").toString());
            entities.add(entity);
        }
        procedureService.saveBatch(entities);
        //保存关联工位类型
        ArrayList<FacTypeProcedureEntity> facTypes = new ArrayList<>();
        for (ProcedureEntity entity : entities) {
            Integer procedureId = entity.getProcedureId();
            String facModelId = entity.getFacModelId();
            if (StringUtils.isNotBlank(facModelId)) {
                List<FacTypeProcedureEntity> collect = Stream.of(facModelId.split(Constant.SEP))
                        .map(o -> FacTypeProcedureEntity.builder()
                                .procedureId(procedureId)
                                .modelId(Integer.valueOf(o))
                                .build()).collect(Collectors.toList());
                facTypes.addAll(collect);
            }
        }
        facTypeProcedureService.saveBatch(facTypes);
    }

    /**
     * 校验工序数据
     */
    private List<ProcedureTempExcelDTO> checkProcedureList(List<ProcedureTempExcelDTO> list) {
        List<String> trueOrFalse = Arrays.asList("是", "否");
        //校验制造单元类型，工位类型、工作中心
        List<String> lineModelNames = list.stream().map(ProcedureTempExcelDTO::getLineModelName).distinct().collect(Collectors.toList());
        //工位类型、工作中心会有拼接形式
        List<String> facModelNames = new ArrayList<>(), workCenterNames = new ArrayList<>();
        for (ProcedureTempExcelDTO dto : list) {
            String facModelName = dto.getFacModelName();
            if (StringUtils.isNotBlank(facModelName)) {
                facModelNames.addAll(Arrays.asList(facModelName.split(Constant.SEP)));
            }
            String workCenter = dto.getWorkCenterName();
            if (StringUtils.isNotBlank(workCenter)) {
                workCenterNames.addAll(Arrays.asList(workCenter.split(Constant.SEP)));
            }
        }
        facModelNames = facModelNames.stream().distinct().collect(Collectors.toList());
        // 产线模型
        List<ModelEntity> lineModels = getModelNameByType(lineModelNames, ModelEnum.LINE.getType());
        // 工位模型
        List<ModelEntity> facModels = getModelNameByType(facModelNames, ModelEnum.FACILITY.getType());
        workCenterNames = workCenterNames.stream().distinct().collect(Collectors.toList());
        Map<String, WorkCenterEntity> workCenterNameMap = getWorkCenterNameMap(workCenterNames);

        // 重复的编码
        Set<String> duplicateProcedureNames = CommonUtil.duplicateElement(list, ProcedureTempExcelDTO::getProcedureName);
        Set<String> duplicateProcedureCodes = CommonUtil.duplicateElement(list, ProcedureTempExcelDTO::getProcedureCode);
        List<ProcedureTempExcelDTO> canImports = new ArrayList<>();
        boolean canImport;
        StringBuilder importResult;
        for (ProcedureTempExcelDTO importDTO : list) {
            importResult = new StringBuilder();
            canImport = true;

            // 状态
            if (StringUtils.isNotBlank(importDTO.getStateName())) {
                Integer state = ProcedureStateEnum.getCodeByName(importDTO.getStateName());
                if (state == null || ProcedureStateEnum.STOP_USING.getCode().equals(state)) {
                    importResult.append("状态错误, 可选值：创建、启用；");
                    canImport = false;
                }
                importDTO.setState(state);
            } else {
                importDTO.setState(ProcedureStateEnum.CREATE.getCode());
            }

            // 工序编号，如果工序编号为空，则调用编码规则，如果存在人工输入部分，则报错
            if (StringUtils.isBlank(importDTO.getProcedureCode())) {
                GenerateCodeDTO build = GenerateCodeDTO.builder()
                        .type("98")
                        .build();
                String procedureCode = numberRuleService.generateCodeByType(build);
                if (StringUtils.isBlank(procedureCode)) {
                    importResult.append("自动生成的工序编号为空，请检查编码规则配置(编码规则中必须存在自动生成序号)；");
                    canImport = false;
                }
                importDTO.setProcedureCode(procedureCode);
            } else {
                if(duplicateProcedureCodes.contains(importDTO.getProcedureCode())) {
                    importResult.append("导入的工序编号存在重复;");
                    canImport = false;
                } else {
                    ProcedureEntity procedureEntity = this.lambdaQuery()
                            .eq(ProcedureEntity::getProcedureCode, importDTO.getProcedureCode())
                            .last(Constant.LIMIT_ONE).one();
                    if (Objects.nonNull(procedureEntity)) {
                        importResult.append("系统已存在该工序编号;");
                        canImport = false;
                    }
                }
            }
            if (StringUtils.isBlank(importDTO.getProcedureName())) {
                importResult.append("工序名称不能为空;");
                canImport = false;
            } else {
                if(duplicateProcedureNames.contains(importDTO.getProcedureName())) {
                    importResult.append("导入的工序名称存在重复;");
                    canImport = false;
                } else {
                    ProcedureEntity procedureEntity = this.lambdaQuery()
                            .eq(ProcedureEntity::getName, importDTO.getProcedureName())
                            .last(Constant.LIMIT_ONE).one();
                    if (Objects.nonNull(procedureEntity)) {
                        importResult.append("系统已存在该工序名称;");
                        canImport = false;
                    }
                    // 工序名称重复
                    List<ProcedureTempExcelDTO> repeatProcedureNames = list.stream().filter(res -> importDTO.getProcedureName().equals(res.getProcedureName())).collect(Collectors.toList());
                    if (repeatProcedureNames.size() >= 2) {
                        importResult.append("不能导入相同的工序名称;");
                        canImport = false;
                    }
                }
            }

            // 校验单元格内容:生产基本单元\制造单元模型名称\工位类型名称\工作中心
            String basicUnit = importDTO.getProductionBasicUnit();
            WorkCenterTypeEnum basicUnitEnum = WorkCenterTypeEnum.getByName(basicUnit);
            if (basicUnitEnum == null) {
                importResult.append("启用工序时生产基本单元有误;");
                canImport = false;
            }
            //基本生产单元为产线时，制造单元模型名称、工位类型名称不能为空，工作中心为空
            if (WorkCenterTypeEnum.LINE == basicUnitEnum &&
                    (StringUtils.isAnyBlank(importDTO.getLineModelName(), importDTO.getFacModelName())
                            || StringUtils.isNotBlank(importDTO.getWorkCenterName()))) {
                importResult.append("生产基本单元为制造单元时,需指定制造单元类型和工位类型，且工作中心应为空;");
                canImport = false;
            }
            //为班组时，制造单元模型名称、工位类型名称为空，工作中心不能为空
            if (WorkCenterTypeEnum.TEAM == basicUnitEnum &&
                    (!StringUtils.isAllBlank(importDTO.getLineModelName(), importDTO.getFacModelName())
                            || StringUtils.isBlank(importDTO.getWorkCenterName()))) {
                importResult.append("生产基本单元为班组时,制造单元类型和工位类型应为空，且工作中心不应为空;");
                canImport = false;
            }
            //制造单元类型名称
            if (StringUtils.isNotBlank(importDTO.getLineModelName())) {
                List<ModelEntity> tempLineModels = lineModels.stream().filter(lineModel -> lineModel.getName().equals(importDTO.getLineModelName())).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(tempLineModels)) {
                    importDTO.setLineModelId(tempLineModels.get(0).getId());
                } else {
                    importResult.append("制造单元类型名称未在系统中找到;");
                    canImport = false;
                }
            }
            //工位类型名称,会有多个“,”分割
            if (StringUtils.isNotBlank(importDTO.getFacModelName())) {
                String[] strings = importDTO.getFacModelName().split(Constant.SEP);
                List<Integer> facModelIds = new ArrayList<>();
                for (String tempFacModelName : strings) {
                    List<ModelEntity> tempFacModels = facModels.stream()
                            .filter(facModel -> facModel.getName().equals(tempFacModelName) && facModel.getPid().equals(importDTO.getLineModelId()))
                            .collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(tempFacModels)) {
                        facModelIds.add(tempFacModels.get(0).getId());
                    } else {
                        importResult.append("工位类型名称未在系统中找到;");
                        canImport = false;
                    }
                }
                importDTO.setFacModelIds(facModelIds);
            }
            // 工作中心
            if (StringUtils.isNotBlank(importDTO.getWorkCenterName())) {
                String[] tempWorkCenterNames = importDTO.getWorkCenterName().split(Constant.SEP);
                List<String> ids = new ArrayList<>();
                for (String name : tempWorkCenterNames) {
                    WorkCenterEntity workCenter = workCenterNameMap.get(name);
                    if (workCenter == null) {
                        importResult.append("工作中心名称未在系统中找到;");
                        canImport = false;
                    } else if (!Objects.equals(Optional.ofNullable(basicUnitEnum).map(WorkCenterTypeEnum::getCode).orElse(null), workCenter.getType())) {
                        importResult.append("工作中心类型与生产基本单元类型不匹配;");
                        canImport = false;
                    } else {
                        ids.add(String.valueOf(workCenter.getId()));
                    }
                }
                if (!ids.isEmpty()) {
                    importDTO.setWorkCenterIds(String.join(Constant.SEP, ids));
                }
            }
            // 开启审批功能时才进行校验审批人
            if (approveConfigService.getConfigByCode(ApproveModuleEnum.PROCEDURE.getCode())) {
                if (StringUtils.isEmpty(importDTO.getApproverName()) && Constant.TRUE.equals(importDTO.getIsEffectStr())) {
                    importResult.append("开启审批功能,生效,审批人不能为空;");
                    canImport = false;
                } else {
                    if (StringUtils.isNotBlank(importDTO.getApproverName())) {
                        SysUserEntity user = sysUserService.lambdaQuery()
                                .eq(SysUserEntity::getNickname, importDTO.getApproverName())
                                .last(Constant.LIMIT_ONE).one();
                        if (Objects.isNull(user)) {
                            importResult.append("审批人未在系统中找到;");
                            canImport = false;
                        } else {
                            importDTO.setApprover(user.getUsername());
                        }
                    }
                }
                if (Constant.TRUE.equals(importDTO.getIsEffectStr())) {
                    importDTO.setApprovalState(ApprovalStatusEnum.APPROVED.getCode());
                } else {
                    importDTO.setApprovalState(ApprovalStatusEnum.TO_BE_APPROVAL.getCode());
                }
            }
            //是否委外, 默认为否
            if (StringUtils.isNotBlank(importDTO.getIsOutsourcingStr())) {
                Integer code = OutsourcingProcedureEnum.getCodeByName(importDTO.getIsOutsourcingStr());
                if (Objects.isNull(code)) {
                    importResult.append("是否委外填写有误,只能是“否、完全、部分”;");
                    canImport = false;
                } else {
                    importDTO.setIsOutsourcing(code);
                }
            } else {
                importDTO.setIsOutsourcing(OutsourcingProcedureEnum.NO.getCode());
            }
            //校验委外供应商
            if (Objects.nonNull(importDTO.getIsOutsourcing()) && OutsourcingProcedureEnum.NO.getCode() != importDTO.getIsOutsourcing()
                    && StringUtils.isNotBlank(importDTO.getOutsourcingSupplierName())) {
                List<String> outSourcingSupplierNames = Arrays.asList(importDTO.getOutsourcingSupplierName().split(Constant.SEP));
                List<SupplierEntity> supplierEntities = supplierService.lambdaQuery()
                        .in(SupplierEntity::getName, outSourcingSupplierNames)
                        .like(SupplierEntity::getType, SupplierTypeEnum.OUTSOURCING_SUPPLIER.getName())
                        .list();
                // 如果查询的供应商不等于导入的数量，直接报找不到数据
                if (supplierEntities.size() == outSourcingSupplierNames.size()) {
                    String outSourcingSupplierCodes = supplierEntities.stream().map(SupplierEntity::getCode).collect(Collectors.joining(Constant.SEP));
                    importDTO.setOutsourcingSupplierCode(outSourcingSupplierCodes);
                } else {
                    importResult.append("委外供应商未在系统中找到;");
                    canImport = false;
                }
            }
            //是否检验工序, 默认为否
            importDTO.setIsInspection(StringUtils.isNotBlank(importDTO.getIsInspectionStr()) && Constant.TRUE.equals(importDTO.getIsInspectionStr()));
            //是否质检工序, 默认为否
            importDTO.setIsQualityInspection(StringUtils.isNotBlank(importDTO.getIsQualityInspectionStr()) && Constant.TRUE.equals(importDTO.getIsQualityInspectionStr()));
            if (importDTO.getIsQualityInspection() && StringUtils.isNotBlank(importDTO.getDefectSchemeName())) {
                // 查询质检方案
                DefectSchemeSelectDTO selectDTO = DefectSchemeSelectDTO.builder()
                        .status(String.valueOf(DefectSchemeStatusEnum.RELEASED.getCode()))
                        .schemeAllName(importDTO.getDefectSchemeName())
                        .type(WorkCenterTypeEnum.getCodeByName(importDTO.getProductionBasicUnit()))
                        .build();
                Page<DefectSchemeEntity> schemeList = defectSchemeService.getList(selectDTO);
                if (CollectionUtils.isEmpty(schemeList.getRecords())) {
                    importResult.append("质检方案不存在或未生效或质检方案类型不匹配;");
                    canImport = false;
                } else {
                    importDTO.setDefectSchemeId(schemeList.getRecords().get(0).getSchemeId());
                }
            }
            //是否维修工序, 默认为否
            importDTO.setIsMaintain(StringUtils.isNotBlank(importDTO.getIsMaintainStr()) && Constant.TRUE.equals(importDTO.getIsMaintainStr()));
            if (importDTO.getIsMaintain() && StringUtils.isNotBlank(importDTO.getMaintainSchemeName())) {
                // 检查质检方案名称
                MaintainSchemeEntity maintainScheme = maintainSchemeService.lambdaQuery().eq(MaintainSchemeEntity::getSchemeName, importDTO.getMaintainSchemeName())
                        .eq(MaintainSchemeEntity::getStatus, MaintainSchemeStatusEnum.RELEASED.getCode()).last(Constant.LIMIT_ONE).one();
                if (maintainScheme == null) {
                    importResult.append("维修方案不存在或未生效;");
                    canImport = false;
                } else {
                    importDTO.setMaintainSchemeId(maintainScheme.getSchemeId());
                }
            }
            //是否生效, 默认为否
            importDTO.setIsEffect(StringUtils.isNotBlank(importDTO.getIsEffectStr()) && Constant.TRUE.equals(importDTO.getIsEffectStr()));

            importDTO.setVerifyPass(canImport);
            if (Boolean.TRUE.equals(importDTO.getVerifyPass())) {
                canImports.add(importDTO);
                importDTO.setImportResult("数据校验通过");
            } else {
                importDTO.setImportResult(importResult.toString());
            }
        }
        return canImports;
    }

    private Map<String, WorkCenterEntity> getWorkCenterNameMap(List<String> workCenterNames) {
        if (CollectionUtils.isEmpty(workCenterNames)) {
            return new HashMap<>();
        }
        LambdaQueryWrapper<WorkCenterEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(WorkCenterEntity::getName, workCenterNames);
        List<WorkCenterEntity> workCenterEntities = workCenterMapper.selectList(wrapper);
        return workCenterEntities.stream().collect(Collectors.toMap(WorkCenterEntity::getName, Function.identity()));
    }

    /**
     * 获取模型名称
     *
     * @param modelNames
     * @param type
     * @return
     */
    private List<ModelEntity> getModelNameByType(List<String> modelNames, String type) {
        if (CollectionUtils.isEmpty(modelNames)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<ModelEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ModelEntity::getType, type)
                .in(ModelEntity::getName, modelNames);
        return modelMapper.selectList(wrapper);
    }

    @Override
    public Page<ProcedureEntity> getList(ProcedureQueryDTO dto) {
        String sql = SqlUtil.getSql(dto);
        Page<ProcedureEntity> page = this.baseMapper.getList(sql, dto.getPage());
        this.showName(page.getRecords());
        return page;
    }


}
