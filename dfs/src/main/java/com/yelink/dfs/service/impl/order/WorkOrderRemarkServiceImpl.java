package com.yelink.dfs.service.impl.order;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yelink.dfs.common.UserAuthenService;
import com.yelink.dfs.entity.order.WorkOrderRemarkEntity;
import com.yelink.dfs.entity.order.dto.WorkOrderRemarkInsertDTO;
import com.yelink.dfs.entity.order.dto.WorkOrderRemarkUpdateDTO;
import com.yelink.dfs.mapper.order.WorkOrderRemarkMapper;
import com.yelink.dfs.open.v2.order.dto.WorkOrderRemarkQueryDTO;
import com.yelink.dfs.service.order.WorkOrderRemarkService;
import com.yelink.dfs.service.user.SysUserService;
import com.yelink.dfs.utils.SqlUtil;
import com.yelink.dfscommon.constant.ResponseException;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Service
public class WorkOrderRemarkServiceImpl extends ServiceImpl<WorkOrderRemarkMapper, WorkOrderRemarkEntity> implements WorkOrderRemarkService {
    @Resource
    private SysUserService userService;
    @Resource
    private UserAuthenService userAuthenService;

    @Override
    public List<WorkOrderRemarkEntity> listWorkOrderRemark(String workOrderNumber) {
        List<WorkOrderRemarkEntity> list = this.lambdaQuery()
                .eq(WorkOrderRemarkEntity::getWorkOrderNumber, workOrderNumber)
                .orderByDesc(WorkOrderRemarkEntity::getId)
                .list();
        this.showName(list);
        return list;
    }

    private void showName(List<WorkOrderRemarkEntity> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Set<String> usernames = list.stream().map(WorkOrderRemarkEntity::getUsername).filter(Objects::nonNull).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(usernames)) {
            Map<String, String> userNameNickMap = userService.getUserNameNickMap(new ArrayList<>(usernames));
            list.forEach(res -> res.setUsernameNick(userNameNickMap.get(res.getUsername())));
        }
    }

    @Override
    public WorkOrderRemarkEntity add(WorkOrderRemarkInsertDTO dto) {
        WorkOrderRemarkEntity workOrderRemark = WorkOrderRemarkEntity.builder()
                .workOrderNumber(dto.getWorkOrderNumber())
                .remark(dto.getRemark())
                .username(userAuthenService.getUsername())
                .createTime(new Date())
                .build();
        save(workOrderRemark);
        return workOrderRemark;
    }

    @Override
    public void edit(WorkOrderRemarkUpdateDTO dto) {
        WorkOrderRemarkEntity workOrderRemark = getById(dto.getId());
        if(workOrderRemark == null) {
            throw new ResponseException("不存在该备注信息");
        }
        workOrderRemark.setRemark(dto.getRemark());
        workOrderRemark.setUpdateTime(new Date());
        updateById(workOrderRemark);
    }

    @Override
    public Page<WorkOrderRemarkEntity> getList(WorkOrderRemarkQueryDTO dto) {
        String sql = SqlUtil.getSql(dto);
        Page<WorkOrderRemarkEntity> page = this.baseMapper.getList(sql, dto.getPage());
        this.showName(page.getRecords());
        return page;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<Integer> batchAdd(List<WorkOrderRemarkInsertDTO> list) {
        List<Integer> ids = new ArrayList<>();
        for (WorkOrderRemarkInsertDTO dto : list) {
            WorkOrderRemarkEntity entity = this.add(dto);
            ids.add(entity.getId());
        }
        return ids;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchUpdate(List<WorkOrderRemarkUpdateDTO> list) {
        for (WorkOrderRemarkUpdateDTO dto : list) {
            this.edit(dto);
        }
    }

}
