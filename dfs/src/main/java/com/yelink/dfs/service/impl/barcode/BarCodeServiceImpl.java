package com.yelink.dfs.service.impl.barcode;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.OperationType;
import com.yelink.dfs.constant.RedisKeyPrefix;
import com.yelink.dfs.constant.barcode.BarCodeStateEnum;
import com.yelink.dfs.constant.rule.AutoIncrementConfigureTypeEnum;
import com.yelink.dfs.entity.barcode.BarCodeEntity;
import com.yelink.dfs.entity.barcode.dto.BarCodeQuantityVerifyDTO;
import com.yelink.dfs.entity.code.ProductFlowCodeEntity;
import com.yelink.dfs.entity.code.dto.BarCodeAndValueDTO;
import com.yelink.dfs.entity.code.dto.BarCodeSelectDTO;
import com.yelink.dfs.entity.code.dto.CodeTargetSubmitDTO;
import com.yelink.dfs.entity.common.config.dto.QuantityVerifyConfigDTO;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.entity.product.MaterialEntity;
import com.yelink.dfs.entity.product.dto.MaterialEntitySelectDTO;
import com.yelink.dfs.entity.supplier.SupplierEntity;
import com.yelink.dfs.entity.supplier.SupplierMaterialEntity;
import com.yelink.dfs.entity.target.record.dto.CheckReportBackDTO;
import com.yelink.dfs.entity.task.dto.ProductOrderTaskDTO;
import com.yelink.dfs.entity.task.dto.WorkOrderTaskDTO;
import com.yelink.dfs.entity.user.SysUserEntity;
import com.yelink.dfs.mapper.barcode.BarCodeMapper;
import com.yelink.dfs.mapper.order.RecordWorkOrderDayCountMapper;
import com.yelink.dfs.open.v1.aksk.ams.ExtProductOrderInterface;
import com.yelink.dfs.open.v1.aksk.ams.ExtPurchaseInterface;
import com.yelink.dfs.open.v1.barcode.dto.BarCodeSimpleDTO;
import com.yelink.dfs.open.v1.barcode.dto.InsertBarCodeDTO;
import com.yelink.dfs.open.v1.barcode.dto.UpdateBarCodeDTO;
import com.yelink.dfs.open.v2.barcode.dto.BarCodeBatchDeleteDTO;
import com.yelink.dfs.open.v2.barcode.dto.BarCodeQueryDTO;
import com.yelink.dfs.provider.constant.KafkaMessageTypeEnum;
import com.yelink.dfs.service.barcode.BarCodeAnalysisService;
import com.yelink.dfs.service.barcode.BarCodeService;
import com.yelink.dfs.service.barcode.LabelService;
import com.yelink.dfs.service.barcode.LabelTypeConfigService;
import com.yelink.dfs.service.code.CodeLogService;
import com.yelink.dfs.service.code.ProductFlowCodeService;
import com.yelink.dfs.service.common.CommonService;
import com.yelink.dfs.service.common.config.BusinessConfigService;
import com.yelink.dfs.service.common.config.OrderPushDownRecordService;
import com.yelink.dfs.service.order.RuleSeqService;
import com.yelink.dfs.service.order.WorkOrderExtendService;
import com.yelink.dfs.service.order.WorkOrderService;
import com.yelink.dfs.service.product.MaterialService;
import com.yelink.dfs.service.product.SkuService;
import com.yelink.dfs.service.rule.NumberRuleService;
import com.yelink.dfs.service.supplier.SupplierMaterialService;
import com.yelink.dfs.service.supplier.SupplierService;
import com.yelink.dfs.service.user.SysUserService;
import com.yelink.dfs.utils.SpringUtil;
import com.yelink.dfs.utils.SqlUtil;
import com.yelink.dfscommon.constant.Constants;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.constant.dfs.barcode.BarCodeTypeEnum;
import com.yelink.dfscommon.constant.dfs.barcode.LabelInfoEnum;
import com.yelink.dfscommon.constant.dfs.code.FlowCodeTypeEnum;
import com.yelink.dfscommon.constant.dfs.code.ProductFlowCodeTypeEnum;
import com.yelink.dfscommon.constant.dfs.config.ConfigConstant;
import com.yelink.dfscommon.constant.dfs.rule.NumberCodeDTO;
import com.yelink.dfscommon.constant.dfs.rule.NumberRulesConfigEntity;
import com.yelink.dfscommon.constant.wms.StockInputOrOutputTypeEnum;
import com.yelink.dfscommon.dto.ImportProgressDTO;
import com.yelink.dfscommon.dto.MaterialCodeAndSkuIdSelectDTO;
import com.yelink.dfscommon.dto.ams.open.order.ProductOrderDetailDTO;
import com.yelink.dfscommon.dto.common.config.FullPathCodeDTO;
import com.yelink.dfscommon.dto.dfs.CodeInfoSelectDTO;
import com.yelink.dfscommon.dto.dfs.PrintInfoDTO;
import com.yelink.dfscommon.dto.dfs.RuleDetailDTO;
import com.yelink.dfscommon.entity.ams.ProductOrderEntity;
import com.yelink.dfscommon.entity.ams.ProductOrderMaterialEntity;
import com.yelink.dfscommon.entity.ams.PurchaseEntity;
import com.yelink.dfscommon.entity.ams.dto.PurchaseDetailDTO;
import com.yelink.dfscommon.entity.dfs.OrderPushDownRecordEntity;
import com.yelink.dfscommon.entity.dfs.SkuEntity;
import com.yelink.dfscommon.entity.dfs.barcode.LabelEntity;
import com.yelink.dfscommon.entity.dfs.barcode.LabelTypeConfigEntity;
import com.yelink.dfscommon.entity.dfs.barcode.PrintDTO;
import com.yelink.dfscommon.pojo.Result;
import com.yelink.dfscommon.provider.MessagePushToKafkaService;
import com.yelink.dfscommon.utils.ColumnUtil;
import com.yelink.dfscommon.utils.ExcelUtil;
import com.yelink.dfscommon.utils.FieldUtil;
import com.yelink.dfscommon.utils.JacksonUtil;
import com.yelink.dfscommon.utils.MathUtil;
import com.yelink.dfscommon.utils.WrapperUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Service
 *
 * <AUTHOR>
 * @Date 2021-08-05 15:34
 */
@Slf4j
@Service("barCodeService")
public class BarCodeServiceImpl extends ServiceImpl<BarCodeMapper, BarCodeEntity> implements BarCodeService {

    @Resource
    @Lazy
    private LabelService labelService;
    @Autowired
    private MaterialService materialService;
    @Autowired
    private NumberRuleService numberRuleService;
    @Autowired
    private SysUserService userService;
    @Autowired
    private SupplierService supplierService;
    @Autowired
    private SupplierMaterialService supplierMaterialService;
    @Lazy
    @Resource
    private WorkOrderService workOrderService;
    @Resource
    private WorkOrderExtendService workOrderExtendService;
    @Resource
    private RuleSeqService ruleSeqService;
    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private MessagePushToKafkaService messagePushToKafkaService;
    @Resource
    private ExtProductOrderInterface extProductOrderInterface;
    @Resource
    private SkuService skuService;
    @Autowired
    private BusinessConfigService businessConfigService;
    @Autowired
    private ProductFlowCodeService productFlowCodeService;
    @Autowired
    private RecordWorkOrderDayCountMapper recordWorkOrderDayCountMapper;
    @Autowired
    private ExtPurchaseInterface extPurchaseInterface;
    @Resource
    private LabelTypeConfigService labelTypeConfigService;
    @Resource
    private CodeLogService codeLogService;
    @Resource
    private OrderPushDownRecordService orderPushDownRecordService;

    /**
     * 打印时需要替换的占位符 ${ }
     */
    private static final Pattern PATTERN = Pattern.compile("\\$\\{(.*?)\\}");

    @Override
    public Page<BarCodeEntity> getListByOrder(String orderNumber, String materialCode, Integer current, Integer size) {
        Page<BarCodeEntity> page = new Page<>();
        if (StringUtils.isBlank(orderNumber)) {
            return page;
        }
        LambdaQueryWrapper<BarCodeEntity> wrapper = new LambdaQueryWrapper<>();
        WrapperUtil.eq(wrapper, BarCodeEntity::getMaterialCode, materialCode);
        wrapper.eq(BarCodeEntity::getRelateNumber, orderNumber)
                .orderByDesc(BarCodeEntity::getCreateTime)
                .orderByDesc(BarCodeEntity::getBarCodeId);
        if (current == null || size == null) {
            List<BarCodeEntity> list = this.list(wrapper);
            page.setRecords(list).setTotal(list.size()).setCurrent(1).setSize(list.size());
        } else {
            page = this.page(new Page<>(current, size), wrapper);
        }
        List<BarCodeEntity> records = page.getRecords();
        records.forEach(o -> o.setStateName(BarCodeStateEnum.getNameByCode(o.getState())));
        return page;
    }

    @Override
    @Async
    @Transactional(rollbackFor = Exception.class)
    public void generateAndAddBarCode(BarCodeEntity entity) {
        //初始化进度
        ImportProgressDTO build = ImportProgressDTO.builder()
                .progress(Double.valueOf(com.yelink.dfscommon.constant.Constant.ZERO_VALUE))
                .executionDescription("正在处理中，请耐心等候...")
                .executionStatus(false)
                .code(Result.SUCCESS_CODE)
                .build();
        // 获取批次锁
        List<String> keyAndLock = getKeyAndLock(entity);
        String redisKey = keyAndLock.get(0);
        String redisLock = keyAndLock.get(1);
        // 重置进度
        redisTemplate.opsForValue().set(redisKey, JSONObject.toJSONString(build), 1, TimeUnit.HOURS);
        // 加方法锁
        Boolean lockStatus = redisTemplate.opsForValue().setIfAbsent(redisLock, new Date(), 1, TimeUnit.HOURS);
        if (lockStatus == null || !lockStatus) {
            build = ImportProgressDTO.builder().executionDescription("当前方法已被占用，稍后再试").executionStatus(false).progress(1.0).code(Result.FAIL_CODE).build();
            redisTemplate.opsForValue().set(redisKey, JSONObject.toJSONString(build), 1, TimeUnit.HOURS);
            throw new ResponseException(RespCodeEnum.LOCK_TAKES_UP);
        }

        try {
            // 检验和设置值
            this.checkAndProcess(entity);
            int num = Objects.isNull(entity.getNum()) ? 1 : entity.getNum();
            NumberRulesConfigEntity numberRulesConfigEntity = numberRuleService.getById(entity.getNumberRuleId());
            //采购收料批量加批次entity.getRuleDetail()不为空不需要走下面逻辑否则序号不会加一
            List<RuleDetailDTO> ruleDetailDTOs = JSONArray.parseArray(entity.getRuleDetail(), RuleDetailDTO.class);
            if (num == 1) {
                entity.setSeq(1);
                // 判断批次号是否存在（该字段存放在barCode字段）
                NumberCodeDTO seqById = numberRuleService.generateRules(numberRulesConfigEntity.getId(), ruleDetailDTOs, entity.getRelatedMap());
                judgeIsExistBarCode(seqById.getCode(), redisKey);
                entity.setBarCode(seqById.getCode());
                this.save(entity);
                this.saveBatchProductFlowCode(entity);
                Double processPercent = MathUtil.divideDouble(1, 1, 2);
                build = ImportProgressDTO.builder().progress(processPercent).executionDescription(processPercent.compareTo(1.0) == 0 ? String.format("新增完成：成功%s条；失败%s条", 1, 0) : String.format("正在处理中，已完成%s条；", 1)).executionStatus(processPercent.compareTo(1.0) == 0).code(Result.SUCCESS_CODE).build();
                //工单批次、采购批次
                redisTemplate.opsForValue().set(redisKey, JSONObject.toJSONString(build), 1, TimeUnit.HOURS);
                // 编码规则有自动生成序号的，seq才加1
                ruleSeqService.updateSeqEntity(entity.getRelatedMap(), numberRulesConfigEntity.getId(), entity.isCheckTimesNeedAddOne());
            } else {
                // 因为是批量，所以需要进行最后一个后缀是否是自增的判断
                for (int i = 1; i <= num; i++) {
                    NumberCodeDTO seqById = numberRuleService.generateRules(numberRulesConfigEntity.getId(), ruleDetailDTOs, entity.getRelatedMap());
                    if (seqById.getSeq() == null) {
                        throw new ResponseException(RespCodeEnum.BAR_CODE_BATCH_ADD_NEED_AUTO_INCREMENT);
                    }
                    entity.setBarCodeId(null);
                    // 判断批次号是否存在（该字段存放在barCode字段）
                    judgeIsExistBarCode(seqById.getCode(), redisKey);
                    entity.setBarCode(seqById.getCode());
                    entity.setSeq(seqById.getSeq());
                    //如果指定了批次总数和每批次个数，最后一个生成的批次需要计算得到
                    if (null != entity.getBatchCount() && num == i) {
                        double count = (entity.getBatchCount() % entity.getCount());
                        entity.setCount(count == 0.0 ? entity.getCount() : count);
                    }
                    this.save(entity);
                    this.saveBatchProductFlowCode(entity);
                    // 设置进度
                    Double processPercent = MathUtil.divideDouble(i, num, 2);
                    build = ImportProgressDTO.builder().progress(processPercent).executionDescription(processPercent.compareTo(1.0) == 0 ? String.format("新增完成：成功%s条；失败%s条", i, num - i) : String.format("正在处理中，已完成%s条；", i)).executionStatus(processPercent.compareTo(1.0) == 0).code(Result.SUCCESS_CODE).build();
                    redisTemplate.opsForValue().set(redisKey, JSONObject.toJSONString(build), 1, TimeUnit.HOURS);
                    // 编码规则有自动生成序号的，seq才加1
                    ruleSeqService.updateSeqEntity(entity.getRelatedMap(), numberRulesConfigEntity.getId(), entity.isCheckTimesNeedAddOne());
                }
            }
            // 推送任务中心消息
            sendTaskInfo(entity);
        } catch (Exception e) {
            log.error("批次号新增数据错误", e);
            CommonService commonService = SpringUtil.getBean(CommonService.class);
            build = commonService.getImportProgressDTO(redisKey, e);
            redisTemplate.opsForValue().set(redisKey, JSONObject.toJSONString(build), 1, TimeUnit.HOURS);
            // 手动触发事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        } finally {
            // 释放锁
            redisTemplate.delete(redisLock);
        }
    }

    /**
     * 推送任务中心消息
     */
    private void sendTaskInfo(BarCodeEntity entity) {
        if (BarCodeTypeEnum.FINISHED.getCode().equals(entity.getModuleType())) {
            // 给任务中心推送报工消息
            WorkOrderTaskDTO taskDTO = WorkOrderTaskDTO.builder()
                    .workOrderNumber(entity.getRelateNumber())
                    .barCodeCreateBy(entity.getCreateBy())
                    .log(entity.getCreateBy() + "添加了批次")
                    .build();
            workOrderExtendService.pushToTask(true, taskDTO);
        } else if (BarCodeTypeEnum.PRODUCT_BAR_CODE.getCode().equals(entity.getModuleType())) {
            ProductOrderTaskDTO taskDTO = ProductOrderTaskDTO.builder()
                    .productOrderNumber(entity.getRelateNumber())
                    .barCodeCreateBy(entity.getCreateBy())
                    .log(entity.getCreateBy() + "添加了批次")
                    .build();
            extProductOrderInterface.pushToTask(taskDTO);
        }
    }

    /**
     * (对外接口)根据编码规则生成并新增批次
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public List<String> generateAndAddBarCodeByOpen(BarCodeEntity entity) {
        // 获取批次锁
        List<String> keyAndLock = getKeyAndLock(entity);
        String redisKey = keyAndLock.get(0);
        String redisLock = keyAndLock.get(1);
        // 加方法锁
        Boolean lockStatus = redisTemplate.opsForValue().setIfAbsent(redisLock, new Date(), 1, TimeUnit.HOURS);
        if (lockStatus == null || !lockStatus) {
            throw new ResponseException(RespCodeEnum.LOCK_TAKES_UP);
        }

        try {
            // 检验和设置值
            this.checkAndProcess(entity);
            // 获取编码规则配置
            NumberRulesConfigEntity numberRulesConfigEntity = numberRuleService.getById(entity.getNumberRuleId());
            if (numberRulesConfigEntity == null) {
                throw new ResponseException(RespCodeEnum.DFS_NUMBER_RULE_NOT_EXIST);
            }
            List<String> newBarCodes = new ArrayList<>();
            // 解析编码规则
            String ruleDetail = StringUtils.isNotBlank(entity.getRuleDetail()) ? entity.getRuleDetail() : numberRulesConfigEntity.getPrefixDetail();
            List<RuleDetailDTO> ruleDetailDTOs = JSONArray.parseArray(ruleDetail, RuleDetailDTO.class);
            for (int i = 1; i <= entity.getNum(); i++) {
                // 生成编码
                NumberCodeDTO seqById = numberRuleService.generateRules(numberRulesConfigEntity.getId(), ruleDetailDTOs, entity.getRelatedMap());
                if (seqById == null || seqById.getSeq() == null) {
                    throw new ResponseException(RespCodeEnum.BAR_CODE_BATCH_ADD_NEED_AUTO_INCREMENT);
                }
                if (StringUtils.isBlank(seqById.getCode())) {
                    throw new ResponseException(RespCodeEnum.DFS_RULE_DETAIL_PARSE_ERROR);
                }

                entity.setBarCodeId(null);
                judgeIsExistBarCode(seqById.getCode(), redisKey);
                entity.setBarCode(seqById.getCode());
                entity.setSeq(seqById.getSeq());

                //如果指定了批次总数和每批次个数，最后一个生成的批次需要计算得到
                if (null != entity.getBatchCount() && entity.getNum() == i) {
                    double count = (entity.getBatchCount() % entity.getCount());
                    entity.setCount(count == 0.0 ? entity.getCount() : count);
                }

                this.save(entity);
                this.saveBatchProductFlowCode(entity);
                newBarCodes.add(entity.getBarCode());

                // 编码规则有自动生成序号的，seq才加1
                Map<String, String> relatedMap = entity.getRelatedMap();
                if (relatedMap != null) {
                    ruleSeqService.updateSeqEntity(relatedMap, numberRulesConfigEntity.getId(), entity.isCheckTimesNeedAddOne());
                }
            }
            return newBarCodes;
        } catch (Exception e) {
            log.error("批次号新增数据错误", e);
            // 手动触发事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            throw e; // 重新抛出异常，让调用方知道具体错误
        } finally {
            // 释放锁
            redisTemplate.delete(redisLock);
        }
    }

    /**
     * 检验和设置值
     * @param entity
     */
    private void checkAndProcess(BarCodeEntity entity) {
        // 异常分析
        abnormalAnalysis(entity);
        entity.setState(BarCodeStateEnum.AVAILABLE.getCode());
        entity.setBarCodeId(null);
        entity.setRuleType(entity.getModuleType());
        if (StringUtils.isNotBlank(entity.getSupplierCode())) {
            SupplierEntity supplierEntity = supplierService.lambdaQuery().eq(SupplierEntity::getCode, entity.getSupplierCode()).one();
            entity.setSupplier(supplierEntity == null ? null : supplierEntity.getName());
        }
        // 查询采购时间
        if (BarCodeTypeEnum.PURCHASE.getCode().equals(entity.getModuleType())) {
            String purchaseCode = entity.getRelatedMap() == null ? null : entity.getRelatedMap().get(AutoIncrementConfigureTypeEnum.PURCHASE.getCode());
            if (StringUtils.isNotBlank(purchaseCode)) {
                PurchaseEntity purchaseEntity = extPurchaseInterface.getPurchaseByCode(PurchaseDetailDTO.builder().purchaseCode(purchaseCode).build());
                if (purchaseEntity != null) {
                    entity.setPurchaseTime(purchaseEntity.getPlanTime());
                }
            }
        } else if (BarCodeTypeEnum.PRODUCT_BAR_CODE.getCode().equals(entity.getModuleType())) {
            ProductOrderEntity orderEntity = extProductOrderInterface.selectProductOrderByNumber(ProductOrderDetailDTO.builder().productOrderNumber(entity.getRelateNumber()).build());
            ProductOrderMaterialEntity orderMaterialEntity = orderEntity.getProductOrderMaterials().get(0);
            entity.setCustomerCode(orderEntity.getCustomerCode());
            entity.setCustomerName(orderEntity.getCustomerName());
            entity.setCustomerMaterialCode(orderMaterialEntity.getCustomerMaterialCode());
            entity.setCustomerMaterialName(orderMaterialEntity.getCustomerMaterialName());
            entity.setCustomerSpecification(orderMaterialEntity.getCustomerSpecification());
        } else if (BarCodeTypeEnum.FINISHED.getCode().equals(entity.getModuleType())) {
            WorkOrderEntity workOrderEntity = workOrderService.getSimpleWorkOrderByNumber(entity.getRelateNumber());
            entity.setCustomerCode(workOrderEntity.getCustomerCode());
            entity.setCustomerName(workOrderEntity.getCustomerName());
            entity.setCustomerMaterialCode(workOrderEntity.getCustomerMaterialCode());
            entity.setCustomerMaterialName(workOrderEntity.getCustomerMaterialName());
            entity.setCustomerSpecification(workOrderEntity.getCustomerSpecification());
        }
    }

    /**
     * 异常分析
     *
     * @param
     * @return
     */
    private void abnormalAnalysis(BarCodeEntity entity) {
        // 关联单据和物料编码都不能为空
        if (StringUtils.isBlank(entity.getRelateNumber())) {
            throw new ResponseException(RespCodeEnum.BAR_CODE_RELATE_NUMBER_IS_BLANK);
        }
        if (StringUtils.isBlank(entity.getMaterialCode())) {
            throw new ResponseException(RespCodeEnum.BAR_CODE_RELATE_MATERIAL_IS_BLANK);
        }
        // 如果不是按批次管理物料，不能新增批次
        MaterialEntity materialEntity = materialService.getSimpleMaterialByCode(entity.getMaterialCode());
        if (!materialEntity.getIsBatchMag()) {
            throw new ResponseException(RespCodeEnum.BAR_CODE_IS_NOT_BATCH_MNG);
        }
    }

    /**
     * 获取批次锁
     */
    private static List<String> getKeyAndLock(BarCodeEntity entity) {
        Map<String, List<String>> keyMap = new HashMap<>();
        //工单批次
        keyMap.put(BarCodeTypeEnum.FINISHED.getCode(),
                Arrays.asList(RedisKeyPrefix.WORK_ORDER_BATCH_PROGRESS + entity.getRelateNumber(),
                        RedisKeyPrefix.WORK_ORDER_BATCH_PROGRESS_LOCK + entity.getRelateNumber()));
        //订单批次
        keyMap.put(BarCodeTypeEnum.PRODUCT_BAR_CODE.getCode(),
                Arrays.asList(RedisKeyPrefix.PRODUCT_ORDER_BATCH_PROGRESS + entity.getRelateNumber(),
                        RedisKeyPrefix.PRODUCT_ORDER_BATCH_PROGRESS_LOCK + entity.getRelateNumber()));
        //采购收货批次
        keyMap.put(BarCodeTypeEnum.PURCHASE.getCode(),
                Arrays.asList(RedisKeyPrefix.PURCHASE_BATCH_PROGRESS + entity.getRelateNumber(),
                        RedisKeyPrefix.PURCHASE_BATCH_PROGRESS_LOCK + entity.getRelateNumber()));
        //委外收货批次
        keyMap.put(BarCodeTypeEnum.SUBCONTRACTING_RECEIPT_BATCH.getCode(),
                Arrays.asList(RedisKeyPrefix.SUBCONTRACTING_RECEIPT_BATCH_PROGRESS + entity.getRelateNumber(),
                        RedisKeyPrefix.SUBCONTRACTING_RECEIPT_BATCH_LOCK + entity.getRelateNumber()));
        //销售退料批次
        keyMap.put(BarCodeTypeEnum.SALE_RETURN_BAR_CODE.getCode(),
                Arrays.asList(RedisKeyPrefix.SALE_RETURN_BATCH_PROGRESS + entity.getRelateNumber(),
                        RedisKeyPrefix.SALE_RETURN_BATCH_LOCK + entity.getRelateNumber()));
        //模块数据如果为空,走默认逻辑
        List<String> keyAndLock = keyMap.getOrDefault(entity.getModuleType(),
                Arrays.asList(RedisKeyPrefix.OTHER_BATCH_PROGRESS + entity.getRelateNumber(),
                        RedisKeyPrefix.OTHER_BATCH_LOCK + entity.getRelateNumber()));
        return keyAndLock;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void synBarCode(List<BarCodeEntity> barCodeEntityList) {
        //初始化进度
        for (BarCodeEntity barCodeEntity : barCodeEntityList) {
            if (StringUtils.isBlank(barCodeEntity.getMaterialCode())) {
                throw new ResponseException(RespCodeEnum.BAR_CODE_RELATE_MATERIAL_IS_BLANK);
            }
            // 如果不是按批次管理物料，不能新增批次
            MaterialEntity materialEntity = materialService.getSimpleMaterialByCode(barCodeEntity.getMaterialCode());
            if (!materialEntity.getIsBatchMag()) {
                throw new ResponseException(RespCodeEnum.BAR_CODE_IS_NOT_BATCH_MNG);
            }
            if (barCodeEntity.getState() == null) {
                barCodeEntity.setState(BarCodeStateEnum.AVAILABLE.getCode());
            }
            BarCodeEntity barCodeOldEntity = getBarCodeByCode(barCodeEntity.getBarCode());
            if (barCodeOldEntity != null) {
                barCodeEntity.setBarCodeId(barCodeOldEntity.getBarCodeId());
                barCodeEntity.setUpdateTime(new Date());
            } else {
                barCodeEntity.setBarCodeId(null);
                barCodeEntity.setCreateTime(new Date());
            }
            if (barCodeEntity.getCount() == null) {
                barCodeEntity.setCount(1.0);
            }
            this.saveOrUpdate(barCodeEntity);
            this.saveBatchProductFlowCode(barCodeEntity);
        }
    }


    /**
     * 判断批次号是否存在
     */
    private void judgeIsExistBarCode(String barCode, String redisKey) {
        ImportProgressDTO build;
        BarCodeEntity barCodeEntity = getBarCodeByCode(barCode);
        if (barCodeEntity != null) {
            build = new ImportProgressDTO();
            build.setExecutionDescription("批次已存在，请重新设置");
            build.setExecutionStatus(false);
            build.setProgress(1.0);
            build.setCode(Result.FAIL_CODE);
            redisTemplate.opsForValue().set(redisKey, JSONObject.toJSONString(build), 1, TimeUnit.HOURS);
            throw new ResponseException(RespCodeEnum.BAR_CODE_IS_EXIST);
        }
    }

    @Override
    public BarCodeEntity updateBarCode(BarCodeEntity entity) {
        //校验状态
        BarCodeEntity preEntity = this.lambdaQuery().eq(BarCodeEntity::getBarCode, entity.getBarCode()).one();
        if (preEntity == null) {
            throw new ResponseException(RespCodeEnum.BAR_CODE_NOT_EXIST);
        }
        LambdaUpdateWrapper<BarCodeEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(BarCodeEntity::getBarCode, entity.getBarCode())
                .set(BarCodeEntity::getCodeInfo, entity.getCodeInfo())
                .set(StringUtils.isNotBlank(entity.getRuleType()), BarCodeEntity::getRuleType, entity.getRuleType())
                .set(StringUtils.isNotBlank(entity.getRelateNumber()), BarCodeEntity::getRelateNumber, entity.getRelateNumber())
                .set(StringUtils.isNotBlank(entity.getMaterialCode()), BarCodeEntity::getMaterialCode, entity.getMaterialCode())
                .set(Objects.nonNull(entity.getRelateMaterialId()), BarCodeEntity::getRelateMaterialId, entity.getRelateMaterialId())
                .set(BarCodeEntity::getGrade, entity.getGrade())
                .set(BarCodeEntity::getRemarks, entity.getRemarks())
                .set(BarCodeEntity::getBarCodeExtendFieldOne, entity.getBarCodeExtendFieldOne())
                .set(Objects.nonNull(entity.getCount()), BarCodeEntity::getCount, entity.getCount())
                .set(Objects.nonNull(entity.getProductionTime()),BarCodeEntity::getProductionTime,entity.getProductionTime());
        this.update(wrapper);
        BarCodeEntity aftEntity = this.lambdaQuery().eq(BarCodeEntity::getBarCode, entity.getBarCode()).one();
        saveBatchProductFlowCode(entity);
        // 推送消息
        messagePushToKafkaService.pushNewMessage(entity, Constants.KAFKA_VALUE_CHAIN_TOPIC, KafkaMessageTypeEnum.BARCODE_UPDATE_MESSAGE);
        return aftEntity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeBarCode(Integer barCodeId) {
        BarCodeEntity entity = this.getById(barCodeId);
        if (Objects.isNull(entity)) {
            return;
        }
        // 校验批次是否可以被删除
        verifyBarCodeDelete(entity);

        this.removeById(barCodeId);
        romoveFlowCode(entity);
        // 推送消息
        messagePushToKafkaService.pushNewMessage(entity, Constants.KAFKA_VALUE_CHAIN_TOPIC, KafkaMessageTypeEnum.BARCODE_DELETE_MESSAGE);
    }

    @Override
    public void batchRemoveBarCode(List<Integer> barCodeIds) {
        if (CollectionUtils.isEmpty(barCodeIds)) {
            return;
        }
        List<BarCodeEntity> barCodes = this.listByIds(barCodeIds);
        // 校验批次是否可以被删除
        for (BarCodeEntity barCodeEntity : barCodes) {
            verifyBarCodeDelete(barCodeEntity);
            romoveFlowCode(barCodeEntity);
        }
        this.removeByIds(barCodeIds);
    }

    /**
     * 校验批次是否可以被删除
     */
    private void verifyBarCodeDelete(BarCodeEntity entity) {
        if (entity.getState() == BarCodeStateEnum.OCCUPIED.getCode() ||
                entity.getState() == BarCodeStateEnum.FINISH.getCode() ||
                entity.getState() == BarCodeStateEnum.STOCKED.getCode()) {
            throw new ResponseException(RespCodeEnum.BAR_CODE_IS_OCCUPIED);
        }
        if (entity.getState().equals(BarCodeStateEnum.NOT_AVAILABLE.getCode())) {
            throw new ResponseException(RespCodeEnum.THE_BATCH_HAS_BEEN_SPLIT);
        }
        // 如果批次绑定了流水码，则不能删除
        Long count = productFlowCodeService.lambdaQuery().eq(ProductFlowCodeEntity::getBatch, entity.getBarCode()).count();
        if (count > 0) {
            throw new ResponseException(RespCodeEnum.BATCH_IS_BIND_PRODUCT_FLOW_CODE);
        }
    }

    @Override
    public BarCodeEntity getDetail(Integer barCodeId) {
        BarCodeEntity entity = this.getById(barCodeId);
        // 设置详情数据
        setDetailInfo(entity);
        return entity;
    }

    @Override
    public BarCodeEntity getBarCodeByCode(String barCode) {
        LambdaQueryWrapper<BarCodeEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BarCodeEntity::getBarCode, barCode);
        return this.getOne(wrapper);
    }


    /**
     * 导出excel
     *
     * @param barCodeIds
     * @return
     */
    @Override
    public void exportExcel(String barCodeIds, HttpServletResponse response) {
        if (StringUtils.isBlank(barCodeIds)) {
            throw new ResponseException("请先勾选需要导出的标签");
        }
        List<String> ids = Arrays.stream(barCodeIds.split(",")).collect(Collectors.toList());
        LambdaQueryWrapper<BarCodeEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(BarCodeEntity::getBarCodeId, ids);
        List<BarCodeEntity> barCodeEntities = this.list(wrapper);
        List<List<String>> strings = new ArrayList<>();
        //设置字段值
        for (BarCodeEntity codeEntity : barCodeEntities) {
            List<String> stringList = new ArrayList<>();
            //加上批次号
            stringList.add(codeEntity.getBarCode());
            strings.add(stringList);
        }
        if (CollectionUtils.isEmpty(strings)) {
            strings.add(new ArrayList<>());
        }
        try {
            ExcelUtil.exportWithContent(strings, response);
        } catch (IOException e) {
            log.error("导出excel异常", e);
        }
    }


    @Override
    public Page<BarCodeEntity> getList(BarCodeSelectDTO selectDTO) {
        Integer current = selectDTO.getCurrent();
        Integer size = selectDTO.getSize();
        LambdaQueryWrapper<BarCodeEntity> wrapper = conditionQuery(selectDTO);
        // 根据创建时间，批次ID倒排
        List<SFunction<BarCodeEntity, ?>> sFunctions = Arrays.asList(BarCodeEntity::getCreateTime, BarCodeEntity::getBarCodeId);
        wrapper.orderByDesc(sFunctions);
        Page<BarCodeEntity> page;
        if (current == null || size == null) {
            List<BarCodeEntity> barCodeEntities = this.list(wrapper);
            page = new Page<>(1, barCodeEntities.size(), barCodeEntities.size());
            page.setRecords(barCodeEntities);
        } else {
            page = this.page(new Page<>(current, size), wrapper);
        }
        // 设置批次扩展信息
        this.setBarCodeExtendMap(page.getRecords());
        // 为提升查询效率，如果仅查询简单信息，直接返回
        if (Objects.nonNull(selectDTO.getIsShowSimpleInfo()) && selectDTO.getIsShowSimpleInfo()) {
            return page;
        }
        this.showName(page.getRecords());
        return page;

    }

    /**
     * 设置名称和其他信息
     * @param records
     */
    private void showName(List<BarCodeEntity> records) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }
        List<SysUserEntity> sysUserEntities = userService.selectList();
        Map<String, String> nickNames = sysUserEntities.stream().collect(Collectors.toMap(SysUserEntity::getUsername, SysUserEntity::getNickname));
        List<String> materialCodes = records.stream().map(BarCodeEntity::getMaterialCode).distinct().collect(Collectors.toList());
        Map<String, MaterialEntity> materialMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(materialCodes)) {
            List<MaterialEntity> materials = materialService.lambdaQuery().in(MaterialEntity::getCode, materialCodes).list();
            materialMap = materials.stream().collect(Collectors.toMap(MaterialEntity::getCode, o -> o));
        }
        // 查询sku信息
        Set<Integer> skuIds = records.stream().map(BarCodeEntity::getSkuId).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<Integer, SkuEntity> skuMap = skuService.getSkusByIds(skuIds)
                .stream().collect(Collectors.toMap(SkuEntity::getSkuId, v -> v));
        for (BarCodeEntity record : records) {
            record.setStateName(BarCodeStateEnum.getNameByCode(record.getState()));
            // 获取关联单据的类型，该类型当前只记录第一次入库的类型
            Map<String, String> map = BarCodeTypeEnum.map;
            record.setRuleTypeName(StockInputOrOutputTypeEnum.getNameByCode(map.get(record.getRuleType())));
            // 显示创建人名称
            record.setCreateByNickName(StringUtils.isBlank(nickNames.get(record.getCreateBy())) ? record.getCreateBy() : nickNames.get(record.getCreateBy()));
            // 物料字段
            MaterialEntity materialEntity = JacksonUtil.convertObject(materialMap.get(record.getMaterialCode()), MaterialEntity.class);
            if (materialEntity != null) {
                materialEntity.setSkuEntity(skuMap.get(record.getSkuId()));
            }
            record.setMaterialFields(materialEntity);
            if (BarCodeTypeEnum.FINISHED.getCode().equals(record.getRuleType())) {
                // 获取关联流水码的生产数和完成数，刷新关联批次的生产数量和不良数量，涉及算法：
                // 生产数量 = 报工时的完成数 + 流水码绑定该批次的完成数
                // 不良数量 = 报工时的不良数 + 流水码绑定该批次的不良数
                List<String> bindProductFlowCodes = productFlowCodeService.lambdaQuery()
                        .select(ProductFlowCodeEntity::getProductFlowCode)
                        .eq(ProductFlowCodeEntity::getBatch, record.getBarCode())
                        .list().stream()
                        .map(ProductFlowCodeEntity::getProductFlowCode).collect(Collectors.toList());
                Double productFlowCodeFinishCount = CollectionUtils.isEmpty(bindProductFlowCodes) ? 0.0 : recordWorkOrderDayCountMapper.selectReportCount(record.getRelateNumber(), null, bindProductFlowCodes);
                Double productFlowCodeUnqualifiedCount = CollectionUtils.isEmpty(bindProductFlowCodes) ? 0.0 : recordWorkOrderDayCountMapper.selectUnqualifiedCount(record.getRelateNumber(), null, bindProductFlowCodes);
                record.setFinishCount(MathUtil.add(Objects.isNull(record.getFinishCount()) ? 0.0 : record.getFinishCount(), productFlowCodeFinishCount));
                record.setUnqualified(MathUtil.add(Objects.isNull(record.getUnqualified()) ? 0.0 : record.getUnqualified(), productFlowCodeUnqualifiedCount));
            }
        }
    }

    private void showName2(List<BarCodeEntity> records) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }
        List<SysUserEntity> sysUserEntities = userService.selectList();
        Map<String, String> nickNames = sysUserEntities.stream().collect(Collectors.toMap(SysUserEntity::getUsername, SysUserEntity::getNickname));
        for (BarCodeEntity record : records) {
            record.setStateName(BarCodeStateEnum.getNameByCode(record.getState()));
            // 获取关联单据的类型，该类型当前只记录第一次入库的类型
            Map<String, String> map = BarCodeTypeEnum.map;
            record.setRuleTypeName(StockInputOrOutputTypeEnum.getNameByCode(map.get(record.getRuleType())));
            // 显示创建人名称
            record.setCreateByNickName(StringUtils.isBlank(nickNames.get(record.getCreateBy())) ? record.getCreateBy() : nickNames.get(record.getCreateBy()));
            if (BarCodeTypeEnum.FINISHED.getCode().equals(record.getRuleType())) {
                // 获取关联流水码的生产数和完成数，刷新关联批次的生产数量和不良数量，涉及算法：
                // 生产数量 = 报工时的完成数 + 流水码绑定该批次的完成数
                // 不良数量 = 报工时的不良数 + 流水码绑定该批次的不良数
                List<String> bindProductFlowCodes = productFlowCodeService.lambdaQuery()
                        .select(ProductFlowCodeEntity::getProductFlowCode)
                        .eq(ProductFlowCodeEntity::getBatch, record.getBarCode())
                        .list().stream()
                        .map(ProductFlowCodeEntity::getProductFlowCode).collect(Collectors.toList());
                Double productFlowCodeFinishCount = CollectionUtils.isEmpty(bindProductFlowCodes) ? 0.0 : recordWorkOrderDayCountMapper.selectReportCount(record.getRelateNumber(), null, bindProductFlowCodes);
                Double productFlowCodeUnqualifiedCount = CollectionUtils.isEmpty(bindProductFlowCodes) ? 0.0 : recordWorkOrderDayCountMapper.selectUnqualifiedCount(record.getRelateNumber(), null, bindProductFlowCodes);
                record.setFinishCount(MathUtil.add(Objects.isNull(record.getFinishCount()) ? 0.0 : record.getFinishCount(), productFlowCodeFinishCount));
                record.setUnqualified(MathUtil.add(Objects.isNull(record.getUnqualified()) ? 0.0 : record.getUnqualified(), productFlowCodeUnqualifiedCount));
            }
        }
    }

    /**
     * 设置批次扩展信息
     * @param records
     */
    public void setBarCodeExtendMap(List<BarCodeEntity> records) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }
        List<String> barCodes = records.stream().map(BarCodeEntity::getBarCode).collect(Collectors.toList());
        Map<String, Map<String, String>> targetMap = productFlowCodeService.getCodeExtendMap(barCodes);
        // 下推数
        List<OrderPushDownRecordEntity> orderPushDownRecords = orderPushDownRecordService.lambdaQuery()
                .in(OrderPushDownRecordEntity::getSourceOrderBatch, barCodes)
                .select(OrderPushDownRecordEntity::getSourceOrderBatch, OrderPushDownRecordEntity::getPushDownQuantity)
                .list();
        Map<String, Double> barCodePushDownQuantityMap = orderPushDownRecords.stream().collect(Collectors.groupingBy(
                OrderPushDownRecordEntity::getSourceOrderBatch,
                Collectors.summingDouble(OrderPushDownRecordEntity::getPushDownQuantity)
        ));
        for (BarCodeEntity record : records) {
            // 设置扩展属性信息
            record.setExtendMap(targetMap.getOrDefault(record.getBarCode(), new HashMap<>()));
            record.setPushDownQuantity(barCodePushDownQuantityMap.getOrDefault(record.getBarCode(), 0d));
        }
    }

    /**
     * 条件查询
     */
    private LambdaQueryWrapper<BarCodeEntity> conditionQuery(BarCodeSelectDTO selectDTO) {
        QueryWrapper<BarCodeEntity> queryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<BarCodeEntity> wrapper = queryWrapper.lambda();
        List<String> materialCode = null;
        if (!FieldUtil.objectIsNull(selectDTO.getMaterialFields())) {
            MaterialEntitySelectDTO materialEntitySelectDTO = new MaterialEntitySelectDTO();
            BeanUtils.copyProperties(selectDTO.getMaterialFields(), materialEntitySelectDTO);
            materialCode = materialService.materialCodesByMaterialFields(materialEntitySelectDTO);
            if (CollectionUtils.isEmpty(materialCode)) {
                wrapper.isNull(BarCodeEntity::getBarCode);
                return wrapper;
            }
        }
        wrapper
                // 物料编码
                .like(StringUtils.isNotBlank(selectDTO.getMaterialCode()), BarCodeEntity::getMaterialCode, selectDTO.getMaterialCode())
                // 物料特征参数
                .eq(selectDTO.getSkuId() != null, BarCodeEntity::getSkuId, selectDTO.getSkuId())
                // 批次号
                .like(StringUtils.isNotBlank(selectDTO.getBarCode()), BarCodeEntity::getBarCode, selectDTO.getBarCode())
                //多个批次号
                .in(!CollectionUtils.isEmpty(selectDTO.getBarCodes()), BarCodeEntity::getBarCode, selectDTO.getBarCodes())
                // 关联单据
                .like(StringUtils.isNotBlank(selectDTO.getRelateNumber()), BarCodeEntity::getRelateNumber, selectDTO.getRelateNumber())
                .in(!CollectionUtils.isEmpty(selectDTO.getRelateNumbers()), BarCodeEntity::getRelateNumber, selectDTO.getRelateNumbers())
                // 关联单据(精准查询)
                .eq(StringUtils.isNotBlank(selectDTO.getRelateFullNumber()), BarCodeEntity::getRelateNumber, selectDTO.getRelateFullNumber())
                //关联物料行
                .eq(selectDTO.getRelateMaterialId() != null, BarCodeEntity::getRelateMaterialId, selectDTO.getRelateMaterialId())
                // 物料名称
                .in(!CollectionUtils.isEmpty(materialCode), BarCodeEntity::getMaterialCode, materialCode)
                // 打印状态
                .eq(!ObjectUtils.isEmpty(selectDTO.getIsPrint()), BarCodeEntity::getIsPrint, selectDTO.getIsPrint())
                //条码类型
                .eq(StringUtils.isNotBlank(selectDTO.getRuleType()), BarCodeEntity::getRuleType, selectDTO.getRuleType())
                // 创建时间
                .between(StringUtils.isNoneBlank(selectDTO.getStartTime(), selectDTO.getEndTime()), BarCodeEntity::getCreateTime, selectDTO.getStartTime(), selectDTO.getEndTime())
                // 批次扩展字段1
                .like(StringUtils.isNotBlank(selectDTO.getBarCodeExtendFieldOne()), BarCodeEntity::getBarCodeExtendFieldOne, selectDTO.getBarCodeExtendFieldOne())
                // 自增字段进行区间查询
                .between(selectDTO.getSeqStart() != null && selectDTO.getSeqEnd() != null, BarCodeEntity::getSeq, selectDTO.getSeqStart(), selectDTO.getSeqEnd());
        if (StringUtils.isNotBlank(selectDTO.getMaterialName())) {
            List<String> codes = materialService.lambdaQuery().like(MaterialEntity::getName, selectDTO.getMaterialName()).list().stream().map(MaterialEntity::getCode).collect(Collectors.toList());
            wrapper.in(BarCodeEntity::getMaterialCode, codes);
        }
        // 物料规格
        if (StringUtils.isNotBlank(selectDTO.getMaterialStandard())) {
            List<String> codes = materialService.lambdaQuery().like(MaterialEntity::getStandard, selectDTO.getMaterialStandard()).list().stream().map(MaterialEntity::getCode).collect(Collectors.toList());
            wrapper.in(BarCodeEntity::getMaterialCode, codes);
        }
        // 工单状态查询 或 产线查询
        if (StringUtils.isNotBlank(selectDTO.getWorkOrderStates()) || !Objects.isNull(selectDTO.getLineId())) {
            List<Integer> workOrderStates = StringUtils.isNotBlank(selectDTO.getWorkOrderStates()) ? Arrays.stream(selectDTO.getWorkOrderStates().split(Constant.SEP)).map(Integer::valueOf).collect(Collectors.toList()) : new ArrayList<>();
            List<String> workOrderNumbers = workOrderService.lambdaQuery()
                    .select(WorkOrderEntity::getWorkOrderNumber)
                    .in(!CollectionUtils.isEmpty(workOrderStates), WorkOrderEntity::getState, workOrderStates)
                    .eq(!Objects.isNull(selectDTO.getLineId()), WorkOrderEntity::getLineId, selectDTO.getLineId())
                    .list().stream().map(WorkOrderEntity::getWorkOrderNumber).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(workOrderNumbers)) {
                wrapper.isNull(BarCodeEntity::getBarCode);
                return wrapper;
            }
            wrapper.in(BarCodeEntity::getRelateNumber, workOrderNumbers);
        }
        // 供应商物料编号查询
        if (StringUtils.isNotBlank(selectDTO.getSupplierMaterialCode())) {
            List<SupplierMaterialEntity> supplierMaterialEntities = supplierMaterialService.lambdaQuery().eq(SupplierMaterialEntity::getSupplierMaterialCode, selectDTO.getSupplierMaterialCode()).list();
            if (CollectionUtils.isEmpty(supplierMaterialEntities)) {
                wrapper.isNull(BarCodeEntity::getBarCode);
                return wrapper;
            }
            List<Integer> materialIds = supplierMaterialEntities.stream().map(SupplierMaterialEntity::getMaterialId).collect(Collectors.toList());
            List<String> materialCodes = materialService.lambdaQuery().in(MaterialEntity::getId, materialIds).list().stream().map(MaterialEntity::getCode).collect(Collectors.toList());
            wrapper.in(BarCodeEntity::getMaterialCode, materialCodes);
        }
        return wrapper;
    }

    @Override
    public Page<BarCodeEntity> getSimpleList(BarCodeSelectDTO barCodeSelectDTO) {
        LambdaQueryWrapper<BarCodeEntity> wrapper = new LambdaQueryWrapper<>();
        List<String> materialCode = null;
        if (!FieldUtil.objectIsNull(barCodeSelectDTO.getMaterialFields())) {
            MaterialEntitySelectDTO materialEntitySelectDTO = new MaterialEntitySelectDTO();
            BeanUtils.copyProperties(barCodeSelectDTO.getMaterialFields(), materialEntitySelectDTO);
            materialCode = materialService.materialCodesByMaterialFields(materialEntitySelectDTO);
        }
        wrapper
                // 物料编码
                .like(StringUtils.isNotBlank(barCodeSelectDTO.getMaterialCode()), BarCodeEntity::getMaterialCode, barCodeSelectDTO.getMaterialCode())
                // 物料特征参数
                .eq(barCodeSelectDTO.getSkuId() != null, BarCodeEntity::getSkuId, barCodeSelectDTO.getSkuId())
                // 批次号
                .like(StringUtils.isNotBlank(barCodeSelectDTO.getBarCode()), BarCodeEntity::getBarCode, barCodeSelectDTO.getBarCode())
                //多个批次号
                .in(!CollectionUtils.isEmpty(barCodeSelectDTO.getBarCodes()), BarCodeEntity::getBarCode, barCodeSelectDTO.getBarCodes())
                // 关联单据
                .like(StringUtils.isNotBlank(barCodeSelectDTO.getRelateNumber()), BarCodeEntity::getRelateNumber, barCodeSelectDTO.getRelateNumber())
                .eq(Objects.nonNull(barCodeSelectDTO.getRelateMaterialId()), BarCodeEntity::getRelateMaterialId, barCodeSelectDTO.getRelateMaterialId())
                // 关联单据(精准查询)
                .eq(StringUtils.isNotBlank(barCodeSelectDTO.getRelateFullNumber()), BarCodeEntity::getRelateNumber, barCodeSelectDTO.getRelateFullNumber())
                // 物料名称
                .in(!CollectionUtils.isEmpty(materialCode), BarCodeEntity::getMaterialCode, materialCode)
                //条码类型
                .eq(StringUtils.isNotBlank(barCodeSelectDTO.getRuleType()), BarCodeEntity::getRuleType, barCodeSelectDTO.getRuleType())
                // 创建时间
                .between(StringUtils.isNoneBlank(barCodeSelectDTO.getStartTime(), barCodeSelectDTO.getEndTime()), BarCodeEntity::getCreateTime, barCodeSelectDTO.getStartTime(), barCodeSelectDTO.getEndTime())
                // 批次扩展字段1
                .like(StringUtils.isNotBlank(barCodeSelectDTO.getBarCodeExtendFieldOne()), BarCodeEntity::getBarCodeExtendFieldOne, barCodeSelectDTO.getBarCodeExtendFieldOne())
                // 自增字段进行区间查询
                .between(barCodeSelectDTO.getSeqStart() != null && barCodeSelectDTO.getSeqEnd() != null, BarCodeEntity::getSeq, barCodeSelectDTO.getSeqStart(), barCodeSelectDTO.getSeqEnd());
        return this.page(barCodeSelectDTO.sortPage(), wrapper);
    }

    @Override
    public PrintDTO print(BarCodeSelectDTO selectDTO) {
        List<PrintDTO.ParamDto> printDTOS = new ArrayList<>();
        // 必填项如果未填，则直接返回，防止前端误传导致CPU飙高、以及未打印的数据显示已打印
        boolean pageParams = Objects.isNull(selectDTO.getCurrent()) || Objects.isNull(selectDTO.getSize());
        if (StringUtils.isBlank(selectDTO.getBarCode()) && pageParams) {
            throw new ResponseException(RespCodeEnum.PRINT_IS_REQUIRE);
        }
        // 1、查询批次信息
        List<BarCodeEntity> barCodeList = getBarCodeEntities(selectDTO);
        //处理前端传递的批次关联对象
        Map<String, String> collect = new HashMap<>();
        if (!CollectionUtils.isEmpty(selectDTO.getBarCodeAndValueDTOS())) {
            collect = selectDTO.getBarCodeAndValueDTOS().stream().filter(dto -> dto.getRelateInspectOrder() != null).collect(Collectors.toMap(BarCodeAndValueDTO::getBarCode, BarCodeAndValueDTO::getRelateInspectOrder));
        }
        // 2、查询标签信息
        // 新版本通过前端选择的标签规则进行打印
        LabelEntity labelEntity = labelService.lambdaQuery().eq(LabelEntity::getRuleId, selectDTO.getRuleId()).one();
        if (labelEntity == null) {
            throw new ResponseException(RespCodeEnum.NOT_FIND_BAR_CODE_RULE);
        }
        LabelTypeConfigEntity labelTypeConfigEntity = labelTypeConfigService.detail(labelEntity.getCodeType());
        BarCodeAnalysisService barCodeAnalysisService = SpringUtil.getBean(BarCodeAnalysisService.class);
        // 3、查询标签单据打印信息
        Map<String, PrintInfoDTO> printInfoMap = barCodeAnalysisService.getPrintBarCodeInfoMap(labelTypeConfigEntity.getRelateType(), barCodeList);
        // 每次打印，序号初始化为1
        int i = 1;
        // 4、获取打印模板, 替换打印模板里${ }的数据
        for (BarCodeEntity barCodeEntity : barCodeList) {
            PrintDTO printDTO = JSON.parseObject(labelEntity.getContent(), PrintDTO.class);
            List<PrintDTO.ParamDto.Content> printElements = printDTO.getPanels().get(0).getPrintElements();
            for (PrintDTO.ParamDto.Content content : printElements) {
                String data = content.getOptions().getTitle();
                if (StringUtils.isBlank(data)) {
                    continue;
                }
                // 替换占位符, 替换值为查询对象的某个属性值
                Matcher matcher = PATTERN.matcher(data);
                while (matcher.find()) {
                    String placeholder = matcher.group();
                    // 序号加1
                    if (placeholder.equals(LabelInfoEnum.SERIAL_NO.getPlaceholder())) {
                        data = String.valueOf(i);
                        i++;
                        continue;
                    }
                    PrintInfoDTO printInfoDto = printInfoMap.getOrDefault(barCodeEntity.getRelateNumber(), new PrintInfoDTO());
                    // 设置批次信息
                    printInfoDto.setBarCodeEntity(JacksonUtil.convertObject(barCodeEntity, com.yelink.dfscommon.entity.dfs.barcode.BarCodeEntity.class));
                    CodeInfoSelectDTO build = CodeInfoSelectDTO.builder()
                            .ruleType(labelEntity.getCodeType())
                            .materialCode(barCodeEntity.getMaterialCode())
                            .relateType(labelTypeConfigEntity.getRelateType())
                            .relateNumber(barCodeEntity.getRelateNumber())
                            .productBatch(barCodeEntity.getBarCode())
                            .batch(barCodeEntity.getBarCode())
                            .customerCode(selectDTO.getCustomerCode())
                            .customerNames(selectDTO.getCustomerName())
                            .relateInspectOrder(collect.get(barCodeEntity.getBarCode()))
                            .purchaseReceiptBatch(barCodeEntity.getBarCode())
                            .supplierName(barCodeEntity.getSupplier())
                            .placeholder(placeholder)
                            .sliceDigits(content.getOptions().getSliceDigits())
                            .printInfoDto(printInfoDto)
                            .build();
                    // 判断该标签是否存在公式，如果存在运算公式，将对数据进行四则运算
                    // 如果公式为空，则直接进行真实值替换占位符的逻辑
                    String placeholderValue = barCodeAnalysisService.replacePlaceholder(content, build);
                    data = data.replace(placeholder, placeholderValue);
                }
                content.getOptions().setTitle(data);
                // 字体大小如果为空,则设置默认值
                if (content.getOptions().getFontSize() == null) {
                    content.getOptions().setFontSize(9.0);
                }
            }
            printDTOS.addAll(printDTO.getPanels());
        }
        // 5、调用第三方api获取标签打印内容
        PrintDTO printDTO = PrintDTO.builder().ruleType(labelEntity.getCodeType()).ruleCode(labelEntity.getRuleCode()).panels(printDTOS).build();
        PrintDTO dto = labelService.getPrintDtoByOpenApi(printDTO);
        if (Objects.nonNull(selectDTO.getIsPreview()) && selectDTO.getIsPreview()) {
            return dto;
        }
        // 6、标记为已打印
        List<String> printBarCodes = barCodeList.stream().map(BarCodeEntity::getBarCode).collect(Collectors.toList());
        this.lambdaUpdate().in(BarCodeEntity::getBarCode, printBarCodes).set(BarCodeEntity::getIsPrint, true).update();
        // 记录打印的编号
        dto.setPrintCodes(StringUtils.join(printBarCodes, Constant.SEP));
        return dto;
    }

    @Override
    public BarCodeEntity getByBarCode(String scanCode) {
        QueryWrapper<BarCodeEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BarCodeEntity::getBarCode, scanCode);
        return getOne(queryWrapper);
    }

    @Override
    public String getDifferentBatchAndCodeProgress(String type, String relatedNumber) {
        String redisKey;
        if (relatedNumber == null) {
            relatedNumber = "";
        }
        // 批次类型相关枚举
        if (type.equals(BarCodeTypeEnum.FINISHED.getCode())) {
            redisKey = RedisKeyPrefix.WORK_ORDER_BATCH_PROGRESS + relatedNumber;
            return getProgress(redisKey);
        }
        if (type.equals(BarCodeTypeEnum.PURCHASE.getCode())) {
            redisKey = RedisKeyPrefix.PURCHASE_BATCH_PROGRESS + relatedNumber;
            return getProgress(redisKey);
        }
        if (type.equals(BarCodeTypeEnum.PRODUCT_BAR_CODE.getCode())) {
            redisKey = RedisKeyPrefix.PRODUCT_ORDER_BATCH_PROGRESS + relatedNumber;
            return getProgress(redisKey);
        }
        if (type.equals(BarCodeTypeEnum.SUBCONTRACTING_RECEIPT_BATCH.getCode())) {
            redisKey = RedisKeyPrefix.SUBCONTRACTING_RECEIPT_BATCH_PROGRESS + relatedNumber;
            return getProgress(redisKey);
        }
        if (type.equals(BarCodeTypeEnum.OTHER_IN_STOCK_BATCH.getCode())) {
            redisKey = RedisKeyPrefix.OTHER_BATCH_PROGRESS + relatedNumber;
            return getProgress(redisKey);
        }
        // 流水码类型相关枚举
        if (type.equals(FlowCodeTypeEnum.PRODUCT_FLOW_CODE.getCode())) {
            redisKey = RedisKeyPrefix.PRODUCTION_SEQUENCE_CODE_PROGRESS + relatedNumber;
            return getProgress(redisKey);
        }
        if (type.equals(FlowCodeTypeEnum.FINISHED_PRODUCT_CODE.getCode())) {
            redisKey = RedisKeyPrefix.FINISHED_PRODUCT_CODE_PROGRESS + relatedNumber;
            return getProgress(redisKey);
        }
        if (type.equals(FlowCodeTypeEnum.PRODUCT_ORDER_FLOW_CODE.getCode())) {
            redisKey = RedisKeyPrefix.ORDER_PRODUCT_CODE_PROGRESS + relatedNumber;
            return getProgress(redisKey);
        }
        if (type.equals(FlowCodeTypeEnum.PURCHASE_SINGLE_PRODUCT_CODE.getCode())) {
            redisKey = RedisKeyPrefix.PURCHASE_PRODUCT_CODE_PROGRESS + relatedNumber;
            return getProgress(redisKey);
        }
        if (type.equals(BarCodeTypeEnum.SALE_RETURN_BAR_CODE.getCode())) {
            redisKey = RedisKeyPrefix.SALE_RETURN_BATCH_PROGRESS + relatedNumber;
            return getProgress(redisKey);
        }
        return null;
    }

    private String getProgress(String redisKey) {
        return (String) redisTemplate.opsForValue().get(redisKey);
    }

    @Override
    public BarCodeSimpleDTO getSimpleDetail(String barCode) {
        BarCodeEntity entity = this.lambdaQuery().eq(BarCodeEntity::getBarCode, barCode).one();
        if (Objects.isNull(entity)) {
            return BarCodeSimpleDTO.builder().build();
        }
        // 设置详情数据
        setDetailInfo(entity);
        return JSON.parseObject(JSON.toJSONString(entity), BarCodeSimpleDTO.class);
    }

    @Override
    public void judgeBeforePrint(BarCodeSelectDTO barCodeSelectDTO) {
        long count;
        if (StringUtils.isNotBlank(barCodeSelectDTO.getBarCode())) {
            List<String> barCodeLists = Arrays.asList(barCodeSelectDTO.getBarCode().split(Constant.SEP));
            count = this.lambdaQuery().in(BarCodeEntity::getBarCode, barCodeLists).eq(BarCodeEntity::getIsPrint, true).count();
        } else {
            LambdaQueryWrapper<BarCodeEntity> wrapper = conditionQuery(barCodeSelectDTO);
            // 如果存在已打印的数据，抛出异常
            wrapper.eq(BarCodeEntity::getIsPrint, true);
            count = this.count(wrapper);
        }
        if (count > 0) {
            throw new ResponseException(RespCodeEnum.FOUND_PRINTED_DATA);
        }
    }

    @Override
    public CheckReportBackDTO quantityVerify(BarCodeQuantityVerifyDTO quantityVerifyDTO) {
        // 获取生产工单业务配置
        FullPathCodeDTO dto = FullPathCodeDTO.builder().fullPathCode(ConfigConstant.WORK_ORDER_BATCH_VERIFICATION_QUANTITY_CONFIG).build();
        QuantityVerifyConfigDTO configDTO = businessConfigService.getValueDto(dto, QuantityVerifyConfigDTO.class);
        CheckReportBackDTO checkBackDTO = CheckReportBackDTO.builder().isLimit(false).isTips(false).build();
        if (Constants.NULL_STR.equals(configDTO.getVerCompletedEnable())) {
            return checkBackDTO;
        }
        double historyBarCodeSum = this.lambdaQuery()
                .eq(BarCodeEntity::getRelateNumber, quantityVerifyDTO.getRelateNumber())
                .eq(BarCodeEntity::getRuleType, quantityVerifyDTO.getRelateType())
                .list().stream().mapToDouble(BarCodeEntity::getCount).sum();

        // 总数 = 关联单据的所有批次的计划数量之和 + 本次单据添加的数量
        double sum = historyBarCodeSum + quantityVerifyDTO.getAddQuantity();
        if (quantityVerifyDTO.getPlanQuantity() < sum) {
            if (Constants.LIMIT.equals(configDTO.getVerCompletedEnable())) {
                checkBackDTO.setIsLimit(true);
                checkBackDTO.setLimitMessage(configDTO.getVerCompletedLimit());
                return checkBackDTO;
            } else if (Constants.TIPS.equals(configDTO.getVerCompletedEnable())) {
                checkBackDTO.setIsTips(true);
                checkBackDTO.setTipsMessage(configDTO.getVerCompletedTips());
                return checkBackDTO;
            }
        }
        return checkBackDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByCode(BarCodeSelectDTO barCodeSelectDTO) {
        barCodeSelectDTO.setSize(Integer.MAX_VALUE);
        Page<BarCodeEntity> list = getSimpleList(barCodeSelectDTO);
        if (CollectionUtils.isEmpty(list.getRecords())) {
            return;
        }
        list.getRecords().forEach(barCodeEntity -> {
            removeBarCode(barCodeEntity.getBarCodeId());
            romoveFlowCode(barCodeEntity);
        });
    }

    @Override
    public List<String> getRelationNumberList(String relatedType, String relationNumber) {
        return this.baseMapper.getRelationNumberListByRelatedType(relatedType, relationNumber);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchUpdateBarCode(List<UpdateBarCodeDTO> barCodeUpdateDTOS, String username) {
        List<BarCodeEntity> barCodeEntities = JacksonUtil.convertArray(barCodeUpdateDTOS, BarCodeEntity.class);
        List<BarCodeEntity> updateBarCodeList = new ArrayList<>();
        for (BarCodeEntity entity : barCodeEntities) {
            BarCodeEntity preEntity = this.lambdaQuery().eq(BarCodeEntity::getBarCode, entity.getBarCode()).one();
            if (preEntity == null) {
                throw new ResponseException(RespCodeEnum.BAR_CODE_NOT_EXIST);
            }
            // 设置更新值
            preEntity.setCodeInfo(entity.getCodeInfo());
            if (StringUtils.isNotBlank(entity.getRuleType())) {
                preEntity.setRuleType(entity.getRuleType());
            }
            if (StringUtils.isNotBlank(entity.getRelateNumber())) {
                preEntity.setRelateNumber(entity.getRelateNumber());
            }
            if (StringUtils.isNotBlank(entity.getMaterialCode())) {
                preEntity.setMaterialCode(entity.getMaterialCode());
            }
            if (Objects.nonNull(entity.getRelateMaterialId())) {
                preEntity.setRelateMaterialId(entity.getRelateMaterialId());
            }
            preEntity.setGrade(entity.getGrade());
            preEntity.setRemarks(entity.getRemarks());
            preEntity.setBarCodeExtendFieldOne(entity.getBarCodeExtendFieldOne());
            if (Objects.nonNull(entity.getCount())) {
                preEntity.setCount(entity.getCount());
            }
            if (Objects.nonNull(entity.getProductionTime())) {
                preEntity.setProductionTime(entity.getProductionTime());
            }
            updateBarCodeList.add(preEntity);
        }
        this.updateBatchById(updateBarCodeList);
        saveBatchProductFlowCode(updateBarCodeList);
        for (BarCodeEntity entity : updateBarCodeList) {
            // 推送消息
            messagePushToKafkaService.pushNewMessage(entity, Constants.KAFKA_VALUE_CHAIN_TOPIC, KafkaMessageTypeEnum.BARCODE_UPDATE_MESSAGE);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchAddBarCode(List<InsertBarCodeDTO> barCodeInsertDTOS, String username) {
        // 大批量新增可能会造成内存溢出，这里需要分批处理
        int totalSize = barCodeInsertDTOS.size();
        int batchCount = (totalSize + 99) / 100;
        for (int i = 0; i < batchCount; i++) {
            int fromIndex = i * 100;
            int toIndex = Math.min((i + 1) * 100, totalSize);
            List<InsertBarCodeDTO> insertBarCodeDTOS = barCodeInsertDTOS.subList(fromIndex, toIndex);
            List<String> barCodes = insertBarCodeDTOS.stream().map(InsertBarCodeDTO::getBarCode).collect(Collectors.toList());
            Long count = lambdaQuery().in(BarCodeEntity::getBarCode, barCodes).count();
            if (count > 0) {
                throw new ResponseException(RespCodeEnum.BAR_CODE_IS_REPEAT);
            }
            List<BarCodeEntity> barCodeEntities = JacksonUtil.convertArray(insertBarCodeDTOS, BarCodeEntity.class);
            Date date = new Date();
            int seq = 1;
            for (BarCodeEntity barCodeEntity : barCodeEntities) {
                barCodeEntity.setState(BarCodeStateEnum.AVAILABLE.getCode());
                barCodeEntity.setCreateBy(username);
                barCodeEntity.setCreateTime(date);
                barCodeEntity.setUpdateTime(date);
                barCodeEntity.setSeq(seq);
                barCodeEntity.setRuleType(barCodeEntity.getModuleType());
                seq = seq + 1;
            }
            this.saveBatch(barCodeEntities);
            this.saveBatchProductFlowCode(barCodeEntities);
        }
    }

    /**
     * 获取批次列表
     */
    private List<BarCodeEntity> getBarCodeEntities(BarCodeSelectDTO barCodeSelectDTO) {
        List<BarCodeEntity> barCodeList;
        List<String> barCodeLists;
        if (StringUtils.isBlank(barCodeSelectDTO.getBarCode())) {
            barCodeSelectDTO.setIsShowSimpleInfo(true);
            Page<BarCodeEntity> page = this.getList(barCodeSelectDTO);
            barCodeList = page.getRecords();
        } else {
            barCodeLists = Arrays.asList(barCodeSelectDTO.getBarCode().split(Constant.SEP));
            barCodeList = this.lambdaQuery().in(BarCodeEntity::getBarCode, barCodeLists).list();
            // 设置批次扩展信息
            this.setBarCodeExtendMap(barCodeList);
        }
        if (CollectionUtils.isEmpty(barCodeList)) {
            throw new ResponseException(RespCodeEnum.PRINT_DATA_IS_LOSE);
        }
        return barCodeList;
    }

    /**
     * 设置详情数据
     */
    private void setDetailInfo(BarCodeEntity entity) {
        // 设置批次扩展信息
        this.setBarCodeExtendMap(Stream.of(entity).collect(Collectors.toList()));
        entity.setRuleTypeName(BarCodeTypeEnum.getNameByCode(entity.getRuleType()));
        entity.setStateName(BarCodeStateEnum.getNameByCode(entity.getState()));
        entity.setCreateByNickName(Optional.ofNullable(userService.getNicknameByUsername(entity.getCreateBy())).orElse(entity.getCreateBy()));
        entity.setMaterialFields(materialService.getEntityByCodeAndSkuId(entity.getMaterialCode(), entity.getSkuId()));
    }

    /**
     * 拿到批次计划总数
     */
    @Override
    public Map<String, BigDecimal> getBarCodeCount(String relatedType, String relationNumber) {
        LambdaQueryWrapper<BarCodeEntity> barCodeWrapper = new LambdaQueryWrapper<>();
        barCodeWrapper.eq(BarCodeEntity::getRelateNumber, relationNumber)
                .eq(BarCodeEntity::getRuleType, relatedType);
        List<BarCodeEntity> barCodeEntities = this.list(barCodeWrapper);
        BigDecimal count = barCodeEntities.stream().map(BarCodeEntity::getCount).map(BigDecimal::valueOf).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal finishCount = barCodeEntities.stream().map(BarCodeEntity::getFinishCount).map(BigDecimal::valueOf).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal unqualified = barCodeEntities.stream().map(BarCodeEntity::getUnqualified).map(BigDecimal::valueOf).reduce(BigDecimal.ZERO, BigDecimal::add);
        Map<String, BigDecimal> map = new HashMap<>();
        map.put("count", count);
        map.put("finishCount", finishCount);
        map.put("unqualified", unqualified);
        return map;
    }


    /**
     * 同步在条码表添加记录
     */
    private synchronized void saveBatchProductFlowCode(List<BarCodeEntity> barCodeEntityList) {
        if (CollectionUtils.isEmpty(barCodeEntityList)) {
            return;
        }
        List<String> barCodes = barCodeEntityList.stream().map(BarCodeEntity::getBarCode).collect(Collectors.toList());
        Map<String, ProductFlowCodeEntity> productFlowCodeEntityMap = productFlowCodeService.lambdaQuery()
                .in(ProductFlowCodeEntity::getProductFlowCode, barCodes)
                .list().stream().collect(Collectors.toMap(ProductFlowCodeEntity::getProductFlowCode, v -> v));
        List<String> materialCodes = barCodeEntityList.stream().map(BarCodeEntity::getMaterialCode).distinct().collect(Collectors.toList());
        Map<String, MaterialEntity> materialCodeEntityMap = materialService.lambdaQuery()
                .in(MaterialEntity::getCode, materialCodes)
                .list().stream().collect(Collectors.toMap(MaterialEntity::getCode, v -> v));

        List<ProductFlowCodeEntity> saveOrUpdateBatchList = new ArrayList<>();
        List<CodeTargetSubmitDTO> codeTargetSubmitList = new ArrayList<>();
        for (BarCodeEntity barCodeEntity : barCodeEntityList) {
            ProductFlowCodeEntity productFlowCodeEntity = productFlowCodeEntityMap.get(barCodeEntity.getBarCode());
            MaterialEntity materialEntity = materialCodeEntityMap.getOrDefault(barCodeEntity.getMaterialCode(), new MaterialEntity());

            productFlowCodeEntity = ProductFlowCodeEntity.builder()
                    .id(productFlowCodeEntity == null ? null : productFlowCodeEntity.getId())
                    .productFlowCode(barCodeEntity.getBarCode())
                    .state(barCodeEntity.getState())
                    .relationNumber(barCodeEntity.getRelateNumber())
                    .materialCode(barCodeEntity.getMaterialCode())
                    .materialName(materialEntity.getName())
                    .createBy(barCodeEntity.getCreateBy())
                    .createTime(barCodeEntity.getCreateTime())
                    .type(ProductFlowCodeTypeEnum.getCodeByType(barCodeEntity.getRuleType()))
                    .seq(barCodeEntity.getSeq())
                    .skuId(barCodeEntity.getSkuId())
                    .isPrint(barCodeEntity.getIsPrint())
                    .materialType(materialEntity.getType())
                    .relationType(ProductFlowCodeTypeEnum.getRelationTypeByType(barCodeEntity.getRuleType()))
                    .supplierCode(barCodeEntity.getSupplierCode())
                    .supplierName(barCodeEntity.getSupplier())
                    .purchaseTime(barCodeEntity.getPurchaseTime())
                    .customerCode(barCodeEntity.getCustomerCode())
                    .customerName(barCodeEntity.getCustomerName())
                    .customerMaterialCode(barCodeEntity.getCustomerMaterialCode())
                    .customerMaterialName(barCodeEntity.getCustomerMaterialName())
                    .customerSpecification(barCodeEntity.getCustomerSpecification())
                    .supplierMaterialCode(barCodeEntity.getSupplierMaterialCode())
                    .supplierMaterialName(barCodeEntity.getSupplierMaterialName())
                    .supplierSpecification(barCodeEntity.getSupplierSpecification())
                    .codeLogDes(productFlowCodeEntity == null ? OperationType.ADD : OperationType.UPDATE)
                    .build();
            saveOrUpdateBatchList.add(productFlowCodeEntity);

            CodeTargetSubmitDTO submitDTO = CodeTargetSubmitDTO.builder().code(barCodeEntity.getBarCode())
                    .target(barCodeEntity.getExtendMap()).build();
            codeTargetSubmitList.add(submitDTO);
        }
        productFlowCodeService.saveOrUpdateBatch(saveOrUpdateBatchList);
        // 添加日志
        codeLogService.addCodeLog(saveOrUpdateBatchList);
        // 同步条码属性
        productFlowCodeService.addCodeTarget(codeTargetSubmitList);
    }

    private void saveBatchProductFlowCode(BarCodeEntity barCodeEntity) {
        ProductFlowCodeEntity productFlowCodeEntity = productFlowCodeService.getByProductFlowCode(barCodeEntity.getBarCode());
        MaterialEntity materialEntity = materialService.getSimpleMaterialByCode(barCodeEntity.getMaterialCode());
        productFlowCodeEntity = ProductFlowCodeEntity.builder()
                .id(productFlowCodeEntity == null ? null : productFlowCodeEntity.getId())
                .productFlowCode(barCodeEntity.getBarCode())
                .state(barCodeEntity.getState())
                .relationNumber(barCodeEntity.getRelateNumber())
                .materialCode(barCodeEntity.getMaterialCode())
                .materialName(materialEntity == null ? null : materialEntity.getName())
                .createBy(barCodeEntity.getCreateBy())
                .createTime(barCodeEntity.getCreateTime())
                .type(ProductFlowCodeTypeEnum.getCodeByType(barCodeEntity.getRuleType()))
                .seq(barCodeEntity.getSeq())
                .skuId(barCodeEntity.getSkuId())
                .isPrint(barCodeEntity.getIsPrint())
                .materialType(materialEntity==null?null:materialEntity.getType())
                .relationType(ProductFlowCodeTypeEnum.getRelationTypeByType(barCodeEntity.getRuleType()))
                .supplierCode(barCodeEntity.getSupplierCode())
                .supplierName(barCodeEntity.getSupplier())
                .purchaseTime(barCodeEntity.getPurchaseTime())
                .customerCode(barCodeEntity.getCustomerCode())
                .customerName(barCodeEntity.getCustomerName())
                .customerMaterialCode(barCodeEntity.getCustomerMaterialCode())
                .customerMaterialName(barCodeEntity.getCustomerMaterialName())
                .customerSpecification(barCodeEntity.getCustomerSpecification())
                .supplierMaterialCode(barCodeEntity.getSupplierMaterialCode())
                .supplierMaterialName(barCodeEntity.getSupplierMaterialName())
                .supplierSpecification(barCodeEntity.getSupplierSpecification())
                .build();
        productFlowCodeService.saveOrUpdate(productFlowCodeEntity);
        String des;
        if (productFlowCodeEntity.getId() == null) {
            des = OperationType.ADD;
        } else {
            des = OperationType.UPDATE;
        }
        // 添加日志
        codeLogService.addCodeLog(Stream.of(productFlowCodeEntity).collect(Collectors.toList()), des);
        CodeTargetSubmitDTO submitDTO = CodeTargetSubmitDTO.builder().code(barCodeEntity.getBarCode())
                .target(barCodeEntity.getExtendMap()).build();
        // 同步条码属性
        productFlowCodeService.addCodeTarget(Stream.of(submitDTO).collect(Collectors.toList()));
    }

    /**
     * 同步删除条码表
     */
    private void romoveFlowCode(BarCodeEntity barCodeEntity) {
        ProductFlowCodeEntity productFlowCodeEntity = productFlowCodeService.getByProductFlowCode(barCodeEntity.getBarCode());
        if (productFlowCodeEntity != null) {
            productFlowCodeService.removeProductFlowCode(productFlowCodeEntity.getId());
        }
    }

    @Override
    public Page<BarCodeEntity> getList2(BarCodeQueryDTO dto) {
        String sql = SqlUtil.getSql(dto);
        Page<BarCodeEntity> page = this.baseMapper.getList(sql, dto.getPage());
        List<BarCodeEntity> barCodes = page.getRecords();
        // 设置批次扩展信息
        this.setBarCodeExtendMap(barCodes);
        // 展示详情
        this.showName2(barCodes);
        List<String> queryAddition = dto.getQueryAddition();
        if (CollectionUtils.isEmpty(queryAddition)) {
            return page;
        }
        // 展示物料信息
        if (queryAddition.contains("materialFields")) {
            List<MaterialCodeAndSkuIdSelectDTO> materialCodes = barCodes.stream()
                    .map(entity -> MaterialCodeAndSkuIdSelectDTO.builder()
                            .materialCode(entity.getMaterialCode())
                            .skuId(entity.getSkuId()).build())
                    .collect(Collectors.toList());
            Map<String, com.yelink.dfs.entity.product.MaterialEntity> codeMaterialMap = materialService.getMaterialEntity(materialCodes);
            for (BarCodeEntity record : page.getRecords()) {
                MaterialEntity materialEntity = codeMaterialMap.get(ColumnUtil.getMaterialSku(record.getMaterialCode(), record.getSkuId()));
                record.setMaterialFields(materialEntity);
            }
        }
        return page;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDeleteBarCode(BarCodeBatchDeleteDTO deleteDTO) {
        if (CollectionUtils.isEmpty(deleteDTO.getBarCodes())) {
            return;
        }
        List<BarCodeEntity> barCodeEntities = this.lambdaQuery().in(BarCodeEntity::getBarCode, deleteDTO.getBarCodes()).list();
        if (CollectionUtils.isEmpty(barCodeEntities)) {
            return;
        }
        for (BarCodeEntity barCodeEntity : barCodeEntities) {
            removeBarCode(barCodeEntity.getBarCodeId());
        }
    }

}



