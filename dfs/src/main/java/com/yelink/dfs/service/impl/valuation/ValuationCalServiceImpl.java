package com.yelink.dfs.service.impl.valuation;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import com.yelink.dfs.common.UserAuthenService;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.ReportType;
import com.yelink.dfs.constant.valuation.ValuationCalStateEnum;
import com.yelink.dfs.constant.valuation.ValuationConfigMethodEnum;
import com.yelink.dfs.constant.valuation.ValuationConfigStateEnum;
import com.yelink.dfs.constant.valuation.ValuationConfigTypeEnum;
import com.yelink.dfs.entity.attendance.dto.AttendanceRecordDTO;
import com.yelink.dfs.entity.attendance.vo.AttendanceRecordVO;
import com.yelink.dfs.entity.code.ProductFlowCodeRecordEntity;
import com.yelink.dfs.entity.manufacture.ProductionLineEntity;
import com.yelink.dfs.entity.model.GridEntity;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.entity.order.WorkOrderProcedureRelationEntity;
import com.yelink.dfs.entity.order.dto.ShowExtraNameDTO;
import com.yelink.dfs.entity.order.dto.WorkOrderWrapperDTO;
import com.yelink.dfs.entity.product.CraftEntity;
import com.yelink.dfs.entity.product.CraftProcedureEntity;
import com.yelink.dfs.entity.product.MaterialEntity;
import com.yelink.dfs.entity.target.metrics.MetricsValuationCalEntity;
import com.yelink.dfs.entity.target.record.ReportLineEntity;
import com.yelink.dfs.entity.user.SysUserEntity;
import com.yelink.dfs.entity.valuation.ValuationConfigEntity;
import com.yelink.dfs.entity.valuation.dto.ValuationCalAuditDTO;
import com.yelink.dfs.entity.valuation.dto.ValuationCalReportSelectDTO;
import com.yelink.dfs.entity.valuation.dto.ValuationCalSelectDTO;
import com.yelink.dfs.entity.valuation.dto.ValuationManualReportAddDTO;
import com.yelink.dfs.entity.valuation.dto.ValuationManualReportEditDTO;
import com.yelink.dfs.entity.valuation.vo.ValuationCalVO;
import com.yelink.dfs.entity.valuation.vo.ValuationReportUserVO;
import com.yelink.dfs.entity.valuation.vo.ValuationReportWorkCenterVO;
import com.yelink.dfs.mapper.target.metrics.MetricsValuationCalMapper;
import com.yelink.dfs.service.attendance.AttendanceRecordService;
import com.yelink.dfs.service.code.ProductFlowCodeRecordService;
import com.yelink.dfs.service.common.DictService;
import com.yelink.dfs.service.manufacture.ProductionLineService;
import com.yelink.dfs.service.model.GridService;
import com.yelink.dfs.service.model.WorkCenterService;
import com.yelink.dfs.service.order.WorkOrderProcedureRelationService;
import com.yelink.dfs.service.order.WorkOrderService;
import com.yelink.dfs.service.product.CraftProcedureService;
import com.yelink.dfs.service.product.CraftService;
import com.yelink.dfs.service.product.MaterialService;
import com.yelink.dfs.service.target.metrics.MetricsValuationCalService;
import com.yelink.dfs.service.target.record.ReportLineService;
import com.yelink.dfs.service.user.SysUserService;
import com.yelink.dfs.service.valuation.ValuationCalService;
import com.yelink.dfs.service.valuation.ValuationConfigService;
import com.yelink.dfscommon.constant.Constants;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.entity.dfs.WorkCenterEntity;
import com.yelink.dfscommon.utils.DateUtil;
import com.yelink.dfscommon.utils.JacksonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ValuationCalServiceImpl implements ValuationCalService {
    @Resource
    private MetricsValuationCalService metricsValuationCalService;

    @Resource
    private MetricsValuationCalMapper metricsValuationCalMapper;
    @Resource
    private AttendanceRecordService attendanceRecordService;

    @Resource
    private ValuationConfigService valuationConfigService;

    @Resource
    private WorkOrderService workOrderService;

    @Resource
    private MaterialService materialService;

    @Resource
    private DictService dictService;

    @Resource
    private UserAuthenService userAuthenService;

    @Resource
    private SysUserService sysUserService;
    @Resource
    private ProductFlowCodeRecordService productFlowCodeRecordService;

    @Resource
    private WorkOrderProcedureRelationService workOrderProcedureRelationService;

    @Resource
    private RedissonClient redissonClient;
    @Resource
    private CraftProcedureService craftProcedureService;
    @Resource
    private CraftService craftService;
    @Resource
    private GridService gridService;
    @Resource
    private ProductionLineService productionLineService;
    @Resource
    private ReportLineService reportLineService;

    @Resource
    private WorkCenterService workCenterService;

    private static final String TASK_LOCK = "VALUATION_CAL_TASK_LOCK";

    @Override
    public void calTask() {
        Date now = new Date();
        Date curRecordDate = dictService.getRecordDate(now);
        // 要计算昨天的，故 当前的首班时间 为 计算的结束时间
        Date lastRecordDate = DateUtil.addDate(curRecordDate, -1);
        List<Date> dates = Collections.singletonList(lastRecordDate);
        calTask(dates);
    }

    @Override
    public void calTask(Collection<Date> recordDates) {
        RLock lock = redissonClient.getLock(TASK_LOCK);
        try {
            int waitTime = 1;
            int leaseTime = 120;
            if (lock.tryLock(waitTime, leaseTime, TimeUnit.SECONDS)) {
                durationTask(recordDates);
                countTask(recordDates);
            } else {
                throw new ResponseException("当前正在计算, 请稍后");
            }
        } catch (InterruptedException e) {
            log.error("处理工资计算的锁遇到问题: {}", e.getMessage());
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Override
    public void durationTask(Collection<Date> recordDates) {
        // 获取考勤记录
        List<AttendanceRecordVO> attendanceRecords = attendanceRecordService.recordList(
                AttendanceRecordDTO.builder()
                        .recordDates(recordDates)
                        .simple(true)
                        .smartCal(true)
                        .build()
        ).getRecords();
        // 过滤临时工
        attendanceRecords = attendanceRecords.stream().filter(e -> e.getUserName() != null).collect(Collectors.toList());
        if (CollUtil.isEmpty(attendanceRecords)) {
            log.info("工资计算-工时：时间[ {} ], 无记录", recordDates);
            return;
        }

        // 工资配置 Map
        Set<String> workOrderNumberSet = attendanceRecords.stream().map(AttendanceRecordVO::getWorkOrderNumber).collect(Collectors.toSet());
        CalWorkOrderMap calDurationMap = buildCalWorkOrderMap(workOrderNumberSet, recordDates, ValuationConfigMethodEnum.DURATION);

        List<MetricsValuationCalEntity> valuationCalEntities = new ArrayList<>();
        // 2.1 按日期分组
        Map<Date, List<AttendanceRecordVO>> recordDateRecordMap = attendanceRecords.stream().collect(Collectors.groupingBy(AttendanceRecordVO::getRecordDate));
        for (Map.Entry<Date, List<AttendanceRecordVO>> recordDateEntry : recordDateRecordMap.entrySet()) {
            Date recordDate = recordDateEntry.getKey();
            // 2.2 按人员分组
            Map<String, List<AttendanceRecordVO>> usernameRecordMap = recordDateEntry.getValue().stream().collect(Collectors.groupingBy(AttendanceRecordVO::getUserName));
            for (Map.Entry<String, List<AttendanceRecordVO>> usernameEntry : usernameRecordMap.entrySet()) {
                String username = usernameEntry.getKey();
                // 2.3 按日期分组
                Map<String, List<AttendanceRecordVO>> workOrderRecordMap = usernameEntry.getValue().stream().collect(Collectors.groupingBy(AttendanceRecordVO::getWorkOrderNumber));
                for (Map.Entry<String, List<AttendanceRecordVO>> workOrderEntry : workOrderRecordMap.entrySet()) {
                    String workOrderNumber = workOrderEntry.getKey();
                    WorkOrderEntity workOrder = calDurationMap.getWorkOrderMap().get(workOrderNumber);
                    if (workOrder == null) {
                        continue;
                    }
                    MaterialEntity materialEntity = calDurationMap.getMaterialCodeMap().get(workOrder.getMaterialCode());
                    ValuationConfigEntity config = calDurationMap.getWorkOrderConfigMap().get(workOrderNumber);
                    if (config == null) {
                        continue;
                    }
                    // 总工时
                    BigDecimal durationH = workOrderEntry.getValue().stream().map(AttendanceRecordVO::getDurationH).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                    // 转换成小时
                    MetricsValuationCalEntity valuationCal = MetricsValuationCalEntity.builder()
                            .workOrderNumber(workOrderNumber)
                            .username(username)
                            .valuationConfigId(config.getId())
                            .craftProcedureId(config.getCraftProcedureId())
                            .materialCode(config.getMaterialCode())
                            .materialName(Optional.ofNullable(materialEntity).map(MaterialEntity::getName).orElse(null))
                            .calDurationH(durationH)
                            .valuation(durationH.multiply(config.getUnitPrice()))
                            .state(ValuationCalStateEnum.CREATED.getCode())
                            .recordDate(recordDate)
                            .workCenterId(workOrder.getWorkCenterId())
                            .workCenterName(workOrder.getWorkCenterName())
                            .lineId(workOrder.getLineId())
                            .lineName(workOrder.getLineName())
                            .deviceName(workOrder.getDevicesNameStr())
                            .teamName(workOrder.getTeamsNameStr())
                            .build();
                    MetricsValuationCalEntity oldCal = calDurationMap.getExistsCalRecordMap().get(buildUserConfigRecordKey(recordDate, username, workOrderNumber, config.getId()));
                    // 去重(已有已审核) + 更新(已有未审核) + 全新
                    if (oldCal != null) {
                        if (ValuationCalStateEnum.AUDITED.getCode().equals(oldCal.getState())) {
                            continue;
                        } else {
                            valuationCal.setId(oldCal.getId());
                        }
                    }
                    valuationCalEntities.add(valuationCal);
                }
            }
        }

        if (CollUtil.isNotEmpty(valuationCalEntities)) {
            metricsValuationCalService.saveOrUpdateBatch(valuationCalEntities);
            log.info("工资计算-计时：时间[ {} ], 记录: {}", recordDates, valuationCalEntities);
        }
    }

    /**
     * 工单号 ： 工资配置 Map
     */
    private CalWorkOrderMap buildCalWorkOrderMap(Set<String> workOrderNumberSet, Collection<Date> recordDates, ValuationConfigMethodEnum valuationConfigMethodEnum) {
        // 工单的
        List<WorkOrderEntity> allWorkOrders = workOrderService.lambdaQuery().in(WorkOrderEntity::getWorkOrderNumber, workOrderNumberSet).list();
        workOrderService.showWorkOrdersExtraName(allWorkOrders, ShowExtraNameDTO.builder().team(true).device(true).build());
        Map<String, WorkOrderEntity> allWorkOrderMap = allWorkOrders.stream().collect(Collectors.toMap(WorkOrderEntity::getWorkOrderNumber, Function.identity()));

        // 物料的
        Set<String> allMaterialCodes = allWorkOrders.stream().map(WorkOrderEntity::getMaterialCode).collect(Collectors.toSet());
        List<MaterialEntity> materials = materialService.lambdaQuery().in(MaterialEntity::getCode, allMaterialCodes).list();
        Map<String, MaterialEntity> materialCodeMap = materials.stream().collect(Collectors.toMap(MaterialEntity::getCode, Function.identity()));

        // 今日所有存在的, 方便去重. 理论上来说:同一个时间段, 每个用户同一个工单只能存在一条计算结果
        List<MetricsValuationCalEntity> existsCals = metricsValuationCalService.lambdaQuery().in(MetricsValuationCalEntity::getRecordDate, recordDates).list();
        // 已存在的计算Map
        Map<String, MetricsValuationCalEntity> existsCalRecordMap = new HashMap<>(16);
        for (MetricsValuationCalEntity existsCal : existsCals) {
            existsCalRecordMap.put(buildUserConfigRecordKey(existsCal.getRecordDate(), existsCal.getUsername(), existsCal.getWorkOrderNumber(), existsCal.getValuationConfigId()), existsCal);
        }

        // 1. 计价类型为物料的
        Map<String, ValuationConfigEntity> r1 = buildWorkOrderConfigMap(allWorkOrders, allMaterialCodes, valuationConfigMethodEnum);

        // 2. 计价类型为工序
        // 物料匹配过，但没有配对的工单，需要工序匹配
        Map<String, ValuationConfigEntity> r2 = Maps.newHashMap();
        Collection<String> lastWorkOrderNumbers = CollUtil.subtract(allWorkOrderMap.keySet(), r1.keySet());
        // 由于工时的没有对应工位（或工序的记录）, 这里需要从中找出一个可以满足配置项的工序即可
        if (CollUtil.isNotEmpty(lastWorkOrderNumbers)) {
            List<WorkOrderProcedureRelationEntity> allProcedureRelations = workOrderProcedureRelationService.lambdaQuery()
                    .in(WorkOrderProcedureRelationEntity::getWorkOrderNumber, lastWorkOrderNumbers).list();
            if (CollUtil.isNotEmpty(allProcedureRelations)) {
                List<Integer> allCraftProcedureIds = allProcedureRelations.stream().map(WorkOrderProcedureRelationEntity::getCraftProcedureId).collect(Collectors.toList());
                Map<String, List<Integer>> workOrderNumberCraftProcedureMap = allProcedureRelations.stream()
                        .collect(Collectors.groupingBy(
                                WorkOrderProcedureRelationEntity::getWorkOrderNumber,
                                Collectors.mapping(WorkOrderProcedureRelationEntity::getCraftProcedureId, Collectors.toList()))
                        );
                List<ValuationConfigEntity> craftProcedureConfig = valuationConfigService.lambdaQuery()
                        .eq(ValuationConfigEntity::getState, ValuationConfigStateEnum.USED.getCode())
                        .eq(ValuationConfigEntity::getValuationMethod, valuationConfigMethodEnum)
                        .eq(ValuationConfigEntity::getValuationType, ValuationConfigTypeEnum.PROCEDURE.getCode())
                        .in(ValuationConfigEntity::getCraftProcedureId, allCraftProcedureIds)
                        .list();
                Map<Integer, ValuationConfigEntity> craftProcedureConfigMap = craftProcedureConfig.stream().collect(Collectors.toMap(ValuationConfigEntity::getCraftProcedureId, Function.identity()));
                r2 = workOrderNumberCraftProcedureMap.entrySet().stream().collect(HashMap::new, (m, entry) -> {
                    String workOrderNumber = entry.getKey();
                    WorkOrderEntity workOrder = allWorkOrderMap.get(workOrderNumber);
                    // 当前工单对应的工艺工序列表
                    List<Integer> craftProcedureIds = entry.getValue();
                    if (CollUtil.isEmpty(craftProcedureIds)) {
                        return;
                    }
                    for (Integer craftProcedureId : craftProcedureIds) {
                        // 工资配置
                        ValuationConfigEntity config = craftProcedureConfigMap.get(craftProcedureId);
                        // 工资配置的编码 为空 | 等于该工单对应的编码， 则匹配
                        if (config != null && (StringUtils.isEmpty(config.getMaterialCode()) || config.getMaterialCode().equals(workOrder.getMaterialCode()))) {
                            m.put(workOrderNumber, config);
                            break;
                        }
                    }
                }, HashMap::putAll);
            }
        }
        // 3. 合并
        Map<String, ValuationConfigEntity> workOrderConfigMap = Stream.of(r1.entrySet(), r2.entrySet())
                .flatMap(Collection::stream).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (v1, v2) -> v1));

        return CalWorkOrderMap.builder()
                .workOrderMap(allWorkOrderMap)
                .materialCodeMap(materialCodeMap)
                .existsCalRecordMap(existsCalRecordMap)
                .workOrderConfigMap(workOrderConfigMap)
                .build();
    }

    private HashMap<String, ValuationConfigEntity> buildWorkOrderConfigMap(List<WorkOrderEntity> allWorkOrders,
                                                                           Set<String> allMaterialCodes,
                                                                           ValuationConfigMethodEnum valuationConfigMethodEnum) {
        List<ValuationConfigEntity> materialConfig = valuationConfigService.lambdaQuery()
                .eq(ValuationConfigEntity::getState, ValuationConfigStateEnum.USED.getCode())
                .eq(ValuationConfigEntity::getValuationMethod, valuationConfigMethodEnum.getCode())
                .eq(ValuationConfigEntity::getValuationType, ValuationConfigTypeEnum.MATERIAL.getCode())
                .in(ValuationConfigEntity::getMaterialCode, allMaterialCodes)
                .list();
        Map<String, ValuationConfigEntity> materialConfigMap = materialConfig.stream().collect(Collectors.toMap(ValuationConfigEntity::getMaterialCode, Function.identity()));
        return allWorkOrders.stream().collect(HashMap::new, (m, v) -> {
            ValuationConfigEntity config = materialConfigMap.get(v.getMaterialCode());
            if (config != null) {
                m.put(v.getWorkOrderNumber(), config);
            }
        }, HashMap::putAll);
    }


    @Override
    public void countTask(Collection<Date> recordDates) {
        if (CollUtil.isEmpty(recordDates)) {
            return;
        }
        // 获取总的时间区间, 先将记录时间（无时分秒）转换成首班格式的时间
        List<Date> sortDates = recordDates.stream().sorted().collect(Collectors.toList());
        Date firstDay = sortDates.get(0);
        Date lastDay = sortDates.get(sortDates.size() - 1);
        // 获取
        Date beginTime = dictService.getBeginTimeByReportDate(firstDay);
        Date endTime = DateUtil.addDate(dictService.getBeginTimeByReportDate(lastDay), 1);

        // 获取用户扫码过站记录
        List<ProductFlowCodeRecordEntity> allReports = productFlowCodeRecordService.lambdaQuery()
//                .eq(ProductFlowCodeRecordEntity::getIsReport, true)
                .ge(ProductFlowCodeRecordEntity::getReportTime, beginTime)
                .lt(ProductFlowCodeRecordEntity::getReportTime, endTime)
                .isNotNull(ProductFlowCodeRecordEntity::getReportBy)
                .isNotNull(ProductFlowCodeRecordEntity::getRelationNumber)
                .list();
        boolean emptyCodeReport = CollUtil.isEmpty(allReports);
        // 获取用户报工记录
        List<ReportLineEntity> allReportLines = reportLineService.lambdaQuery()
                .in(ReportLineEntity::getReportDate, recordDates)
                .isNotNull(ReportLineEntity::getOperator)
                .list();
        boolean emptyReportLine = CollUtil.isEmpty(allReportLines);
        // 最终的工资
        List<MetricsValuationCalEntity> valuationCalEntities = new ArrayList<>();
        if (emptyCodeReport) {
            log.info("工资计算-计件：时间[ {} ], 无过站记录", recordDates);
        } else {
            // 工单号-工艺工序id : 工资配置 Map
            CalWorkOrderProcedureMap calCountMap = buildCalWorkOrderProcedureMap(allReports, recordDates);

            // 2.1 先按日期 分组， 避免重复计算
            Map<Date, List<ProductFlowCodeRecordEntity>> recordDateReportsMap = allReports.stream().collect(Collectors.groupingBy(e -> dictService.getRecordDate(e.getReportTime())));
            for (Map.Entry<Date, List<ProductFlowCodeRecordEntity>> recordDateEntry : recordDateReportsMap.entrySet()) {
                Date recordDate = recordDateEntry.getKey();
                // 2.2 按 用户名 分组
                Map<String, List<ProductFlowCodeRecordEntity>> usernameRecordsMap = recordDateEntry.getValue().stream().collect(Collectors.groupingBy(ProductFlowCodeRecordEntity::getReportBy));
                for (Map.Entry<String, List<ProductFlowCodeRecordEntity>> usernameEntry : usernameRecordsMap.entrySet()) {
                    String username = usernameEntry.getKey();
                    // 2.3 按工单号 分组
                    Map<String, List<ProductFlowCodeRecordEntity>> workOrderNumberRecordsMap = usernameEntry.getValue().stream().collect(Collectors.groupingBy(ProductFlowCodeRecordEntity::getRelationNumber));
                    for (Map.Entry<String, List<ProductFlowCodeRecordEntity>> workOrderNumberEntry : workOrderNumberRecordsMap.entrySet()) {
                        String workOrderNumber = workOrderNumberEntry.getKey();
                        // 2.4 按工序
                        Map<Integer, List<ProductFlowCodeRecordEntity>> craftProcedureRecordsMap = workOrderNumberEntry.getValue().stream()
                                // 处理工艺工序id为空的情况, 设置为一个不存在的值(-1)
                                .peek(e -> e.setProdureId(e.getProdureId() == null ? -1 : e.getProdureId()))
                                .collect(Collectors.groupingBy(ProductFlowCodeRecordEntity::getProdureId));
                        for (Map.Entry<Integer, List<ProductFlowCodeRecordEntity>> craftProcedureEntry : craftProcedureRecordsMap.entrySet()) {
                            Integer craftProcedureId = craftProcedureEntry.getKey();
                            WorkOrderEntity workOrder = calCountMap.getWorkOrderMap().get(workOrderNumber);
                            ValuationConfigEntity config = calCountMap.selectCountConfig(workOrderNumber, craftProcedureId);
                            if (config == null || workOrder == null) {
                                continue;
                            }
                            MaterialEntity materialEntity = calCountMap.getMaterialCodeMap().get(config.getMaterialCode());
                            // 计件
                            BigDecimal calCount = BigDecimal.valueOf(
                                    craftProcedureEntry.getValue().size()
                            );
                            MetricsValuationCalEntity valuationCal = MetricsValuationCalEntity.builder()
                                    .username(username)
                                    .valuationConfigId(config.getId())
                                    .craftProcedureId(config.getCraftProcedureId())
                                    .workOrderNumber(workOrderNumber)
                                    .materialCode(config.getMaterialCode())
                                    .materialName(Optional.ofNullable(materialEntity).map(MaterialEntity::getName).orElse(null))
                                    .calCount(calCount)
                                    .valuation(calCount.multiply(config.getUnitPrice()))
                                    .state(ValuationCalStateEnum.CREATED.getCode())
                                    .recordDate(recordDate)
                                    .workCenterId(workOrder.getWorkCenterId())
                                    .workCenterName(workOrder.getWorkCenterName())
                                    .lineId(workOrder.getLineId())
                                    .lineName(workOrder.getLineName())
                                    .deviceName(workOrder.getDevicesNameStr())
                                    .teamName(workOrder.getTeamsNameStr())
                                    .build();
                            MetricsValuationCalEntity oldCal = calCountMap.getExistsCalRecordMap().get(buildUserConfigRecordKey(recordDate, username, workOrderNumber, config.getId()));
                            // 去重(已有已审核) + 更新(已有未审核) + 全新
                            if (oldCal != null) {
                                if (ValuationCalStateEnum.AUDITED.getCode().equals(oldCal.getState())) {
                                    continue;
                                } else {
                                    valuationCal.setId(oldCal.getId());
                                }
                            }
                            valuationCalEntities.add(valuationCal);

                        }
                    }
                }
            }
        }
        if (emptyReportLine) {
            log.info("工资计算-计件：时间[ {} ], 无报工记录", recordDates);
        } else {
            // 工单号 ： 工资配置 Map
            Set<String> workOrderNumberSet = allReportLines.stream().map(ReportLineEntity::getWorkOrder).collect(Collectors.toSet());
            CalWorkOrderMap calCount2Map = buildCalWorkOrderMap(workOrderNumberSet, recordDates, ValuationConfigMethodEnum.COUNT);
            // 3.1 按日期分组
            Map<Date, List<ReportLineEntity>> recordDateReportLinesMap = allReportLines.stream().collect(Collectors.groupingBy(ReportLineEntity::getReportDate));
            for (Map.Entry<Date, List<ReportLineEntity>> recordDateEntry : recordDateReportLinesMap.entrySet()) {
                Date recordDate = recordDateEntry.getKey();
                // 3.2 按 用户名 分组
                Map<String, List<ReportLineEntity>> usernameRecordsMap = recordDateEntry.getValue().stream().collect(Collectors.groupingBy(ReportLineEntity::getOperator));
                for (Map.Entry<String, List<ReportLineEntity>> usernameEntry : usernameRecordsMap.entrySet()) {
                    Map<String, List<ReportLineEntity>> workOrderNumberRecordsMap = usernameEntry.getValue().stream().collect(Collectors.groupingBy(ReportLineEntity::getWorkOrder));
                    // 这里的用户可能有多个，每个人都要有一条数据。需要用',' 分隔
                    String usernames = usernameEntry.getKey();
                    String[] usernameArr = usernames.split(Constant.SEP);
                    for (String username : usernameArr) {
                        // 3.3 按工单号 分组
                        for (Map.Entry<String, List<ReportLineEntity>> workOrderNumberEntry : workOrderNumberRecordsMap.entrySet()) {
                            String workOrderNumber = workOrderNumberEntry.getKey();
                            WorkOrderEntity workOrder = calCount2Map.getWorkOrderMap().get(workOrderNumber);
                            if (Objects.isNull(workOrder)) {
                                continue;
                            }
                            MaterialEntity materialEntity = calCount2Map.getMaterialCodeMap().get(workOrder.getMaterialCode());
                            ValuationConfigEntity config = calCount2Map.getWorkOrderConfigMap().get(workOrderNumber);
                            if (config == null) {
                                continue;
                            }
                            BigDecimal calCount = BigDecimal.valueOf(
                                    workOrderNumberEntry.getValue().stream().map(ReportLineEntity::getFinishCount).reduce(Double::sum).orElse(0d)
                            );
                            MetricsValuationCalEntity valuationCal = MetricsValuationCalEntity.builder()
                                    .workOrderNumber(workOrderNumber)
                                    .username(username)
                                    .valuationConfigId(config.getId())
                                    .craftProcedureId(config.getCraftProcedureId())
                                    .materialCode(config.getMaterialCode())
                                    .materialName(Optional.ofNullable(materialEntity).map(MaterialEntity::getName).orElse(null))
                                    .calCount(calCount)
                                    .valuation(calCount.multiply(config.getUnitPrice()))
                                    .state(ValuationCalStateEnum.CREATED.getCode())
                                    .recordDate(recordDate)
                                    .workCenterId(workOrder.getWorkCenterId())
                                    .workCenterName(workOrder.getWorkCenterName())
                                    .lineId(workOrder.getLineId())
                                    .lineName(workOrder.getLineName())
                                    .deviceName(workOrder.getDevicesNameStr())
                                    .teamName(workOrder.getTeamsNameStr())
                                    .build();
                            MetricsValuationCalEntity oldCal = calCount2Map.getExistsCalRecordMap().get(buildUserConfigRecordKey(recordDate, username, workOrderNumber, config.getId()));
                            // 去重(已有已审核) + 更新(已有未审核) + 全新
                            if (oldCal != null) {
                                if (ValuationCalStateEnum.AUDITED.getCode().equals(oldCal.getState())) {
                                    continue;
                                } else {
                                    valuationCal.setId(oldCal.getId());
                                }
                            }
                            valuationCalEntities.add(valuationCal);
                        }
                    }
                }
            }
        }
        List<MetricsValuationCalEntity> results = valuationCalEntities;
        if (CollUtil.isNotEmpty(valuationCalEntities)) {
            // 按照唯一键分组, 组内计算的数据求和再合并成一辆
            Map<String, List<MetricsValuationCalEntity>> uniGroup = valuationCalEntities.stream()
                    .collect(Collectors.groupingBy(e -> buildUserConfigRecordKey(e.getRecordDate(), e.getUsername(), e.getWorkOrderNumber(), e.getValuationConfigId())));
            results = uniGroup.values().stream().map(record -> {
                // calCount的数据
                BigDecimal innerCalCount = record.stream().map(MetricsValuationCalEntity::getCalCount).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(null);
                MetricsValuationCalEntity r = BeanUtil.copyProperties(record.get(0), MetricsValuationCalEntity.class);
                r.setCalCount(innerCalCount);
                return r;
            })
                    .sorted(Comparator.comparing(MetricsValuationCalEntity::getRecordDate).thenComparing(MetricsValuationCalEntity::getUsername))
                    .collect(Collectors.toList());
            metricsValuationCalService.saveOrUpdateBatch(results);
        }
        log.info("工资计算-计件：时间[ {} ], 记录: {}", recordDates, results);
    }

    /**
     * 工单号-工艺工序id : 工资配置 Map
     */
    private CalWorkOrderProcedureMap buildCalWorkOrderProcedureMap(List<ProductFlowCodeRecordEntity> allReports, Collection<Date> recordDates) {
        // 按照工单划分
        List<String> allWorkOrderNumbers = allReports.stream().map(ProductFlowCodeRecordEntity::getRelationNumber).distinct().collect(Collectors.toList());
        // 所有工单
        List<WorkOrderEntity> allWorkOrders = workOrderService.lambdaQuery().in(WorkOrderEntity::getWorkOrderNumber, allWorkOrderNumbers).list();
        workOrderService.showWorkOrdersExtraName(allWorkOrders, ShowExtraNameDTO.builder().team(true).device(true).build());
        Map<String, WorkOrderEntity> allWorkOrderMap = allWorkOrders.stream().collect(Collectors.toMap(WorkOrderEntity::getWorkOrderNumber, Function.identity()));
        // 所有物料编码
        Set<String> allMaterialCodes = allWorkOrders.stream().map(WorkOrderEntity::getMaterialCode).collect(Collectors.toSet());
        List<MaterialEntity> materials = materialService.lambdaQuery().in(MaterialEntity::getCode, allMaterialCodes).list();
        Map<String, MaterialEntity> materialCodeMap = materials.stream().collect(Collectors.toMap(MaterialEntity::getCode, Function.identity()));
        // 今日所有存在的, 方便去重. 理论上来说:同一个时间段, 每个用户同一个工资配置(物料)只能存在一条计算结果
        List<MetricsValuationCalEntity> existsCals = metricsValuationCalService.lambdaQuery().in(MetricsValuationCalEntity::getRecordDate, recordDates).list();
        // 已存在的计算Map
        HashMap<String, MetricsValuationCalEntity> existsCalRecordMap = new HashMap<>(16);
        for (MetricsValuationCalEntity existsCal : existsCals) {
            existsCalRecordMap.put(buildUserConfigRecordKey(existsCal.getRecordDate(), existsCal.getUsername(), existsCal.getWorkOrderNumber(), existsCal.getValuationConfigId()), existsCal);
        }
        // 计算配置的映射
        // 1. 计价类型为物料的
        Map<String, ValuationConfigEntity> r1 = buildWorkOrderConfigMap(allWorkOrders, allMaterialCodes, ValuationConfigMethodEnum.COUNT);
        // 2. 计划类型为工序
        Collection<String> lastWorkOrderNumbers = CollUtil.subtract(allWorkOrderMap.keySet(), r1.keySet());
        Map<Integer, ValuationConfigEntity> r2 = Maps.newHashMap();
        if (CollUtil.isNotEmpty(lastWorkOrderNumbers)) {
            // 过滤物料已经匹配的
            List<ProductFlowCodeRecordEntity> procedureReports = allReports.stream().filter(e -> lastWorkOrderNumbers.contains(e.getRelationNumber())).collect(Collectors.toList());
            Set<Integer> allCraftProcedureIds = procedureReports.stream().map(ProductFlowCodeRecordEntity::getProdureId).collect(Collectors.toSet());
            List<ValuationConfigEntity> craftProcedureConfig = valuationConfigService.lambdaQuery()
                    .eq(ValuationConfigEntity::getState, ValuationConfigStateEnum.USED.getCode())
                    .eq(ValuationConfigEntity::getValuationMethod, ValuationConfigMethodEnum.COUNT.getCode())
                    .eq(ValuationConfigEntity::getValuationType, ValuationConfigTypeEnum.PROCEDURE.getCode())
                    .in(ValuationConfigEntity::getCraftProcedureId, allCraftProcedureIds)
                    .list();
            Map<Integer, ValuationConfigEntity> craftProcedureIdConfigMap = craftProcedureConfig.stream().collect(Collectors.toMap(ValuationConfigEntity::getCraftProcedureId, Function.identity()));
            // 构造 craftProduceId : config Map
            r2 = procedureReports.stream().collect(HashMap::new, (m, e) -> {
                Integer craftProduceId = e.getProdureId();
                ValuationConfigEntity config = craftProcedureIdConfigMap.get(craftProduceId);
                if (config == null) {
                    return;
                }
                m.put(craftProduceId, config);
            }, HashMap::putAll);
        }

        return CalWorkOrderProcedureMap.builder()
                .workOrderMap(allWorkOrderMap)
                .materialCodeMap(materialCodeMap)
                .existsCalRecordMap(existsCalRecordMap)
                .workOrderConfigMap(r1)
                .craftProcedureIdConfigMap(r2)
                .build();
    }

    private String buildUserConfigRecordKey(Date recordDate, String username, String workOrderNumber, Integer configId) {
        return recordDate + Constant.CROSSBAR + username + Constant.CROSSBAR + workOrderNumber + Constant.CROSSBAR + configId;
    }


    @Override
    public void audit(List<ValuationCalAuditDTO> dtoList) {
        if (CollUtil.isEmpty(dtoList)) {
            return;
        }
        List<Integer> ids = dtoList.stream().map(ValuationCalAuditDTO::getId).collect(Collectors.toList());
        List<MetricsValuationCalEntity> calEntities = metricsValuationCalService.listByIds(ids);
        if (CollUtil.isEmpty(calEntities)) {
            return;
        }
        Set<Integer> configIdSet = calEntities.stream().map(MetricsValuationCalEntity::getValuationConfigId).collect(Collectors.toSet());
        Map<Integer, ValuationConfigEntity> configIdMap = valuationConfigService.listByIds(configIdSet).stream().collect(Collectors.toMap(ValuationConfigEntity::getId, Function.identity()));
        Map<Integer, ValuationCalAuditDTO> dtoIdMap = dtoList.stream().collect(Collectors.toMap(ValuationCalAuditDTO::getId, Function.identity()));
        Date now = new Date();
        for (MetricsValuationCalEntity cal : calEntities) {
            if (ValuationCalStateEnum.AUDITED.getCode().equals(cal.getState())) {
                throw new ResponseException(String.format("用户[%s]-物料[%s]-日期[%s]的工资已审核", cal.getUsername(), cal.getMaterialName(), cal.getRecordDate()));
            }
            ValuationCalAuditDTO dto = dtoIdMap.get(cal.getId());
            if (dto == null) {
                continue;
            }
            ValuationConfigEntity config = configIdMap.get(cal.getValuationConfigId());
            if (config == null) {
                continue;
            }
            // 看计价方式 - 计时
            if (ValuationConfigMethodEnum.DURATION.getCode().equals(config.getValuationMethod())) {
                BigDecimal auditDurationH = dto.getAuditDurationH();
                if (auditDurationH == null) {
                    throw new ResponseException(String.format("用户[%s]-物料[%s]-日期[%s]的没有审核时长", cal.getUsername(), cal.getMaterialName(), cal.getRecordDate()));
                }
                cal.setAuditDurationH(auditDurationH);
                cal.setValuation(auditDurationH.multiply(config.getUnitPrice()));
            } else if (ValuationConfigMethodEnum.COUNT.getCode().equals(config.getValuationMethod())) {
                BigDecimal auditCount = dto.getAuditCount();
                if (auditCount == null) {
                    throw new ResponseException(String.format("用户[%s]-物料[%s]-日期[%s]的没有审核件数", cal.getUsername(), cal.getMaterialName(), cal.getRecordDate()));
                }
                cal.setAuditCount(auditCount);
                cal.setValuation(auditCount.multiply(config.getUnitPrice()));
            } else {
                continue;
            }
            cal.setAuditor(userAuthenService.getUsername());
            cal.setAuditTime(now);
            cal.setState(ValuationCalStateEnum.AUDITED.getCode());
        }
        metricsValuationCalService.saveOrUpdateBatch(calEntities);
    }

    @Override
    public Page<ValuationCalVO> getPage(ValuationCalSelectDTO dto) {
        LambdaQueryWrapper<MetricsValuationCalEntity> wrapper = Wrappers.lambdaQuery(MetricsValuationCalEntity.class);
        if (CollUtil.isNotEmpty(dto.getValuationTypes()) || CollUtil.isNotEmpty(dto.getValuationMethods())) {
            List<ValuationConfigEntity> configs = valuationConfigService.lambdaQuery()
                    .in(CollUtil.isNotEmpty(dto.getValuationTypes()), ValuationConfigEntity::getValuationType, dto.getValuationTypes())
                    .in(CollUtil.isNotEmpty(dto.getValuationMethods()), ValuationConfigEntity::getValuationMethod, dto.getValuationMethods())
                    .list();
            if (CollUtil.isNotEmpty(configs)) {
                wrapper.in(MetricsValuationCalEntity::getValuationConfigId, configs.stream().map(ValuationConfigEntity::getId).collect(Collectors.toList()));
            } else {
                return new Page<>();
            }
        }
        if (StringUtils.isNotEmpty(dto.getMaterialCode()) || StringUtils.isNotEmpty(dto.getMaterialName()) || StringUtils.isNotEmpty(dto.getMaterialStandard())) {
            List<MaterialEntity> materials = materialService.lambdaQuery()
                    .like(dto.getMaterialName() != null, MaterialEntity::getName, dto.getMaterialName())
                    .like(dto.getMaterialCode() != null, MaterialEntity::getCode, dto.getMaterialCode())
                    .like(dto.getMaterialStandard() != null, MaterialEntity::getStandard, dto.getMaterialStandard())
                    .list();
            if (CollUtil.isEmpty(materials)) {
                return new Page<>();
            } else {
                wrapper.in(MetricsValuationCalEntity::getMaterialCode, materials.stream().map(MaterialEntity::getCode).collect(Collectors.toList()));
            }
        }
        // 工单相关
        if (StringUtils.isNotEmpty(dto.getWorkCenterIds()) || StringUtils.isNotEmpty(dto.getLineIds())
                || StringUtils.isNotEmpty(dto.getTeamIds()) || StringUtils.isNotEmpty(dto.getDeviceIds())
                || StringUtils.isNotEmpty(dto.getWorkOrderNumber())) {
            LambdaQueryWrapper<WorkOrderEntity> workOrderWrapper = new LambdaQueryWrapper<>();
            workOrderWrapper.like(StringUtils.isNotEmpty(dto.getWorkOrderNumber()), WorkOrderEntity::getWorkOrderNumber, dto.getWorkOrderNumber());
            workOrderService.buildWrapper(workOrderWrapper, WorkOrderWrapperDTO.builder()
                    .workCenterIds(dto.getWorkCenterIds())
                    .lineIds(dto.getLineIds())
                    .teamIds(dto.getTeamIds())
                    .deviceIds(dto.getDeviceIds())
                    .build()
            );
            List<WorkOrderEntity> workOrders = workOrderService.list(workOrderWrapper);
            if (CollectionUtils.isEmpty(workOrders)) {
                return new Page<>();
            }
            wrapper.in(MetricsValuationCalEntity::getWorkOrderNumber, workOrders.stream().map(WorkOrderEntity::getWorkOrderNumber).collect(Collectors.toSet()));
        }
        // 工艺工序
        if (CollUtil.isNotEmpty(dto.getCraftIds()) || CollUtil.isNotEmpty(dto.getProcedureIds())) {
            List<CraftProcedureEntity> craftProcedures = craftProcedureService.lambdaQuery()
                    .in(CollUtil.isNotEmpty(dto.getCraftIds()), CraftProcedureEntity::getCraftId, dto.getCraftIds())
                    .in(CollUtil.isNotEmpty(dto.getProcedureIds()), CraftProcedureEntity::getProcedureId, dto.getProcedureIds())
                    .list();
            if (CollectionUtils.isEmpty(craftProcedures)) {
                return new Page<>();
            }
            wrapper.in(MetricsValuationCalEntity::getCraftProcedureId, craftProcedures.stream().map(CraftProcedureEntity::getId).collect(Collectors.toSet()));
        }
        if (StringUtils.isNotEmpty(dto.getUsernameNick())) {
            List<String> usernames = sysUserService.lambdaQuery().like(SysUserEntity::getNickname, dto.getUsernameNick()).list()
                    .stream().map(SysUserEntity::getUsername).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(dto.getUsernames())) {
                // 取交集
                usernames.retainAll(dto.getUsernames());
            }
            if (CollectionUtils.isEmpty(usernames)) {
                return new Page<>();
            }
            dto.setUsernames(usernames);
        }
        wrapper.in(CollUtil.isNotEmpty(dto.getUsernames()), MetricsValuationCalEntity::getUsername, dto.getUsernames());
        wrapper.in(CollUtil.isNotEmpty(dto.getAuditors()), MetricsValuationCalEntity::getAuditor, dto.getAuditors());
        wrapper.in(CollUtil.isNotEmpty(dto.getStates()), MetricsValuationCalEntity::getState, dto.getStates());
        wrapper.ge(StringUtils.isNotEmpty(dto.getStartRecordDate()), MetricsValuationCalEntity::getRecordDate, dto.getStartRecordDate());
        wrapper.le(StringUtils.isNotEmpty(dto.getEndRecordDate()), MetricsValuationCalEntity::getRecordDate, dto.getEndRecordDate());
        wrapper.orderByDesc(MetricsValuationCalEntity::getId);
        Page<MetricsValuationCalEntity> page = metricsValuationCalService.page(dto.sortPage(), wrapper);
        Page<ValuationCalVO> result = JacksonUtil.convertPage(page, ValuationCalVO.class);
        showName(result.getRecords());
        return result;
    }

    private void showName(List<ValuationCalVO> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        // 物料的
        List<String> materialCodes = records.stream().map(ValuationCalVO::getMaterialCode).collect(Collectors.toList());
        Map<String, MaterialEntity> materialCodeMap = CollUtil.isEmpty(materialCodes) ?
                Maps.newHashMap() :
                materialService.lambdaQuery().in(MaterialEntity::getCode, materialCodes).list()
                        .stream().collect(Collectors.toMap(MaterialEntity::getCode, Function.identity()));

        // 用户的
        Map<String, String> userNameNickMap = sysUserService.getUserNameNickMap(
                Stream.of(
                        records.stream().map(ValuationCalVO::getUsername).collect(Collectors.toList()),
                        records.stream().map(ValuationCalVO::getAuditor).collect(Collectors.toList()),
                        records.stream().map(ValuationCalVO::getCreateBy).collect(Collectors.toList()),
                        records.stream().map(ValuationCalVO::getUpdateBy).collect(Collectors.toList())
                ).flatMap(Collection::stream).distinct().collect(Collectors.toList())
        );
        // 工单的
        List<String> workOrderNumbers = records.stream().map(ValuationCalVO::getWorkOrderNumber).collect(Collectors.toList());
        List<WorkOrderEntity> workOrders = workOrderService.lambdaQuery().in(WorkOrderEntity::getWorkOrderNumber, workOrderNumbers).list();
        workOrderService.showWorkOrdersExtraName(workOrders, ShowExtraNameDTO.builder().team(true).device(true).build());
        Map<String, WorkOrderEntity> workOrderMap = workOrders.stream().collect(Collectors.toMap(WorkOrderEntity::getWorkOrderNumber, Function.identity()));
        // 配置的
        Set<Integer> configIdSet = records.stream().map(ValuationCalVO::getValuationConfigId).collect(Collectors.toSet());
        List<ValuationConfigEntity> configs = valuationConfigService.listByIds(configIdSet);
        Map<Integer, ValuationConfigEntity> configIdMap = configs.stream().collect(Collectors.toMap(ValuationConfigEntity::getId, Function.identity()));
        // 工序的
        List<Integer> craftProcedureIds = configs.stream().map(ValuationConfigEntity::getCraftProcedureId).collect(Collectors.toList());
        List<CraftProcedureEntity> craftProcedures = CollUtil.isEmpty(craftProcedureIds) ? Collections.emptyList() : craftProcedureService.listByIds(craftProcedureIds);
        Map<Integer, CraftProcedureEntity> craftProcedureMap = craftProcedures.stream().collect(Collectors.toMap(CraftProcedureEntity::getId, Function.identity()));
        // 工艺的
        List<Integer> craftIds = craftProcedures.stream().map(CraftProcedureEntity::getCraftId).collect(Collectors.toList());
        List<CraftEntity> crafts = CollUtil.isEmpty(craftIds) ? Collections.emptyList() : craftService.listByIds(craftIds);
        Map<Integer, CraftEntity> craftMap = crafts.stream().collect(Collectors.toMap(CraftEntity::getCraftId, Function.identity()));
        // 产线
        Set<Integer> lineIds = workOrders.stream().map(WorkOrderEntity::getLineId).filter(Objects::nonNull).collect(Collectors.toSet());
        List<ProductionLineEntity> lines = CollUtil.isEmpty(lineIds) ? Collections.emptyList() : productionLineService.listByIds(lineIds);
        // 车间
        Set<Integer> gIds = lines.stream().map(ProductionLineEntity::getGid).filter(Objects::nonNull).collect(Collectors.toSet());
        List<GridEntity> grids = CollUtil.isEmpty(gIds) ? Collections.emptyList() : gridService.listByIds(gIds);
        Map<Integer, GridEntity> gridMap = grids.stream().collect(Collectors.toMap(GridEntity::getGid, Function.identity()));
        HashMap<Integer, GridEntity> lineIdGridNameMap = lines.stream().collect(HashMap::new, (m, v) -> {
            GridEntity grid = gridMap.get(v.getGid());
            if (grid != null) {
                m.put(v.getProductionLineId(), grid);
            }
        }, HashMap::putAll);

        for (ValuationCalVO record : records) {
            record.setUserNickname(userNameNickMap.get(record.getUsername()));
            record.setAuditorName(userNameNickMap.get(record.getAuditor()));
            record.setCreateByName(userNameNickMap.get(record.getCreateBy()));
            record.setUpdateByName(userNameNickMap.get(record.getUpdateBy()));
            // 配置
            ValuationConfigEntity config = configIdMap.get(record.getValuationConfigId());
            if (config != null) {
                MaterialEntity materialEntity = materialCodeMap.get(config.getMaterialCode());
                if (materialEntity != null) {
                    record.setMaterialStandard(materialEntity.getStandard());
                }
                record.setUnitPrice(config.getUnitPrice());
                record.setValuationMethod(config.getValuationMethod());
                record.setValuationType(config.getValuationType());
                record.setValuationMethodName(Optional.ofNullable(ValuationConfigMethodEnum.fromCode(config.getValuationMethod())).map(ValuationConfigMethodEnum::getName).orElse(null));
                record.setValuationTypeName(Optional.ofNullable(ValuationConfigTypeEnum.fromCode(config.getValuationType())).map(ValuationConfigTypeEnum::getName).orElse(null));
                record.setCustomizeName(config.getCustomizeName());
                // 工艺工序
                Integer craftProcedureId = config.getCraftProcedureId();
                CraftProcedureEntity craftProcedure = craftProcedureMap.get(craftProcedureId);
                if (craftProcedure != null) {
                    record.setCraftProcedureId(craftProcedureId);
                    record.setProcedureName(craftProcedure.getProcedureName());
                    record.setProcedureId(craftProcedure.getProcedureId());
                    CraftEntity craft = craftMap.get(craftProcedure.getCraftId());
                    if (craft != null) {
                        record.setCraftName(craft.getName());
                        record.setCraftId(craft.getCraftId());
                    }
                }

            }
            // 工单 -> 工作中心, 产线, 设备
            WorkOrderEntity workOrder = workOrderMap.get(record.getWorkOrderNumber());
            if (workOrder != null) {
                record.setWorkCenterId(workOrder.getWorkCenterId());
                record.setWorkCenterName(workOrder.getWorkCenterName());
                record.setDeviceId(workOrder.getDevicesIdStr());
                record.setDeviceName(workOrder.getDevicesNameStr());
                record.setLineId(workOrder.getLineId());
                record.setLineName(workOrder.getLineName());
                record.setTeamId(workOrder.getTeamsIdStr());
                record.setTeamName(workOrder.getTeamsNameStr());
                GridEntity grid = lineIdGridNameMap.get(workOrder.getLineId());
                if (grid != null) {
                    record.setGid(grid.getGid());
                    record.setGname(grid.getGname());
                }

            }

            record.setStateName(Optional.ofNullable(ValuationCalStateEnum.fromCode(record.getState())).map(ValuationCalStateEnum::getName).orElse(null));
        }

    }

    @Override
    public Page<ValuationReportUserVO> reportUserPage(ValuationCalReportSelectDTO dto) {
        int current = dto.getCurrent() == null ? 1 : dto.getCurrent();
        int size = dto.getSize() == null ? Integer.MAX_VALUE : dto.getSize();
        Page<ValuationReportUserVO> page = new Page<>(current, size);
        if (queryMaterialAndCheck(dto)) {
            return page;
        }
        metricsValuationCalMapper.reportUserPage(page, dto);

        // showName
        List<ValuationReportUserVO> records = page.getRecords();
        List<String> usernames = records.stream().map(ValuationReportUserVO::getUsername).collect(Collectors.toList());
        Map<String, String> userNameNickMap = sysUserService.getUserNameNickMap(usernames);
        records.forEach(r -> r.setUserNickname(userNameNickMap.get(r.getUsername())));
        return page;
    }

    private boolean queryMaterialAndCheck(ValuationCalReportSelectDTO dto) {
        if (StringUtils.isNotEmpty(dto.getMaterialCode()) || StringUtils.isNotEmpty(dto.getMaterialName())) {
            List<MaterialEntity> materials = materialService.lambdaQuery()
                    .like(dto.getMaterialName() != null, MaterialEntity::getName, dto.getMaterialName())
                    .like(dto.getMaterialCode() != null, MaterialEntity::getCode, dto.getMaterialCode())
                    .list();
            List<String> materialCodes = materials.stream().map(MaterialEntity::getCode).collect(Collectors.toList());
            if (CollUtil.isEmpty(materialCodes)) {
                return true;
            }
            // 合并
            dto.setMaterialCodes(
                    Stream.of(materialCodes, dto.getMaterialCodes()).filter(Objects::nonNull).flatMap(Collection::stream).distinct().collect(Collectors.toList())
            );
        }
        if (StringUtils.isNotEmpty(dto.getUsernameNick())) {
            List<String> usernames = sysUserService.lambdaQuery().like(SysUserEntity::getNickname, dto.getUsernameNick()).list()
                    .stream().map(SysUserEntity::getUsername).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(dto.getUsernames())) {
                // 取交集
                usernames.retainAll(dto.getUsernames());
            }
            if (CollectionUtils.isEmpty(usernames)) {
                return true;
            }
            dto.setUsernames(usernames);
        }
        if (StringUtils.isNotEmpty(dto.getWorkCenterIds())) {
            List<Integer> workCenterIds = Arrays.stream(dto.getWorkCenterIds().split(Constants.SEP)).map(Integer::valueOf).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(dto.getWorkCenterIdList())) {
                // 取交集
                workCenterIds.retainAll(dto.getWorkCenterIdList());
            }
            if (CollectionUtils.isEmpty(workCenterIds)) {
                return true;
            }
            dto.setWorkCenterIdList(workCenterIds);
        }
        if (StringUtils.isNotEmpty(dto.getLineIds())) {
            List<Integer> lineIds = Arrays.stream(dto.getLineIds().split(Constants.SEP)).map(Integer::valueOf).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(dto.getLineIdList())) {
                // 取交集
                lineIds.retainAll(dto.getLineIdList());
            }
            if (CollectionUtils.isEmpty(lineIds)) {
                return true;
            }
            dto.setLineIdList(lineIds);
        }
        return false;
    }

    @Override
    public Page<ValuationReportWorkCenterVO> reportWorkCenterPage(ValuationCalReportSelectDTO dto) {
        int current = dto.getCurrent() == null ? 1 : dto.getCurrent();
        int size = dto.getSize() == null ? Integer.MAX_VALUE : dto.getSize();
        Page<ValuationReportWorkCenterVO> page = new Page<>(current, size);
        if (queryMaterialAndCheck(dto)) {
            return page;
        }
        metricsValuationCalMapper.reportWorkCenterPage(page, dto);
        List<ValuationReportWorkCenterVO> records = page.getRecords();
        Set<Integer> workCenterIds = records.stream().map(ValuationReportWorkCenterVO::getWorkCenterId).collect(Collectors.toSet());
        if (CollUtil.isNotEmpty(workCenterIds)) {
            Map<Integer, WorkCenterEntity> workCenterMap = workCenterService.listByIds(workCenterIds)
                    .stream().collect(Collectors.toMap(WorkCenterEntity::getId, Function.identity()));
            records.forEach(e -> {
                WorkCenterEntity workCenter = workCenterMap.get(e.getWorkCenterId());
                if (workCenter != null) {
                    e.setWorkCenterName(workCenter.getName());
                }
            });
        }

        return page;
    }

    @Override
    public void manualReportAdd(ValuationManualReportAddDTO dto) {
        ValuationConfigEntity config = valuationConfigService.getById(dto.getValuationConfigId());
        if(config == null) {
            throw new ResponseException("找不到该工资配置");
        }
        WorkOrderEntity workOrder = workOrderService.getSimpleWorkOrderByNumber(dto.getWorkOrderNumber());
        if(workOrder == null) {
            throw new ResponseException("找不到工单号：" + dto.getWorkOrderNumber());
        }
        workOrderService.showWorkOrdersExtraName(Collections.singletonList(workOrder), ShowExtraNameDTO.builder().team(true).device(true).material(true).build());
        for (String username : dto.getUsernames()) {
            MetricsValuationCalEntity valuationCal = MetricsValuationCalEntity.builder()
                    .workOrderNumber(dto.getWorkOrderNumber())
                    .materialCode(workOrder.getMaterialCode())
                    .materialName(workOrder.getMaterialName())
                    .workCenterId(workOrder.getWorkCenterId())
                    .workCenterName(workOrder.getWorkCenterName())
                    .lineId(workOrder.getLineId())
                    .lineName(workOrder.getLineName())
                    .deviceName(workOrder.getDevicesNameStr())
                    .teamName(workOrder.getTeamsNameStr())
                    .calDurationH(dto.getDuration())
                    .valuation(dto.getDuration().multiply(config.getUnitPrice()))
                    .recordDate(dto.getRecordDate())
                    .valuationConfigId(dto.getValuationConfigId())
                    .username(username)
                    .createBy(userAuthenService.getUsername())
                    .createTime(new Date())
                    .build();
            metricsValuationCalService.save(valuationCal);
        }

    }

    @Override
    public void manualReportEdit(ValuationManualReportEditDTO dto) {
        MetricsValuationCalEntity valuationCal = metricsValuationCalService.getById(dto.getId());
        if(valuationCal == null) {
            throw new ResponseException("找不到工资记录：" + dto.getId());
        }
        ValuationConfigEntity config = valuationConfigService.getById(valuationCal.getValuationConfigId());
        if(config == null) {
            throw new ResponseException("找不到该工资配置");
        }
        if(!ValuationConfigTypeEnum.MANUAL.getCode().equals(config.getValuationType())) {
            throw new ResponseException("该记录不是手动上报的");
        }
        if(ValuationCalStateEnum.AUDITED.getCode().equals(valuationCal.getState())) {
            throw new ResponseException("已审批记录无法修改");
        }
        valuationCal.setCalDurationH(dto.getDuration());
        valuationCal.setUpdateBy(userAuthenService.getUsername());
        valuationCal.setUpdateTime(new Date());
        metricsValuationCalService.updateById(valuationCal);
    }
}
