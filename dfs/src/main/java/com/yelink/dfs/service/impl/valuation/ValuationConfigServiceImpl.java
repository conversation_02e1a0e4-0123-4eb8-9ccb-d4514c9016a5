package com.yelink.dfs.service.impl.valuation;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import com.yelink.dfs.common.UserAuthenService;
import com.yelink.dfs.constant.valuation.ValuationCalStateEnum;
import com.yelink.dfs.constant.valuation.ValuationConfigMethodEnum;
import com.yelink.dfs.constant.valuation.ValuationConfigStateEnum;
import com.yelink.dfs.constant.valuation.ValuationConfigTypeEnum;
import com.yelink.dfs.entity.product.CraftEntity;
import com.yelink.dfs.entity.product.CraftProcedureEntity;
import com.yelink.dfs.entity.product.MaterialEntity;
import com.yelink.dfs.entity.target.metrics.MetricsValuationCalEntity;
import com.yelink.dfs.entity.valuation.ValuationConfigEntity;
import com.yelink.dfs.entity.valuation.dto.ValuationConfigInsertDTO;
import com.yelink.dfs.entity.valuation.dto.ValuationConfigSelectDTO;
import com.yelink.dfs.entity.valuation.dto.ValuationConfigUpdateDTO;
import com.yelink.dfs.entity.valuation.vo.ValuationConfigVO;
import com.yelink.dfs.mapper.valuation.ValuationConfigMapper;
import com.yelink.dfs.service.product.CraftProcedureService;
import com.yelink.dfs.service.product.CraftService;
import com.yelink.dfs.service.product.MaterialService;
import com.yelink.dfs.service.target.metrics.MetricsValuationCalService;
import com.yelink.dfs.service.user.SysUserService;
import com.yelink.dfs.service.valuation.ValuationConfigService;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.entity.common.BatchChangeStateDTO;
import com.yelink.dfscommon.utils.JacksonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ValuationConfigServiceImpl extends ServiceImpl<ValuationConfigMapper, ValuationConfigEntity> implements ValuationConfigService {

    @Resource
    private UserAuthenService userAuthenService;
    @Resource
    private SysUserService sysUserService;
    @Resource
    private MaterialService materialService;
    @Resource
    private MetricsValuationCalService metricsValuationCalService;
    @Resource
    private CraftProcedureService craftProcedureService;
    @Resource
    private CraftService craftService;


    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void add(ValuationConfigInsertDTO dto) {
        ValuationConfigEntity config = parseDto(dto);
        config.setState(ValuationConfigStateEnum.USED.getCode());
        config.setCreateBy(userAuthenService.getUsername());
        save(config);
        try {
            checkConfig(config);
        }catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ValuationConfigUpdateDTO dto) {
        ValuationConfigEntity config = getById(dto.getId());
        if(config == null) {
            throw new ResponseException("找不到该配置");
        }
        if(ValuationConfigStateEnum.fromCode(dto.getState()) == null) {
            throw new ResponseException("状态错误");
        }
        if(ValuationConfigStateEnum.USED.getCode().equals(config.getState()) && ValuationConfigStateEnum.FAIL.getCode().equals(dto.getState())) {
            boolean exists = metricsValuationCalService.existTransactional(
                    Wrappers.lambdaQuery(MetricsValuationCalEntity.class)
                            .eq(MetricsValuationCalEntity::getValuationConfigId, dto.getId())
                            .eq(MetricsValuationCalEntity::getState, ValuationCalStateEnum.CREATED.getCode())
            );
            if(exists) {
                throw new ResponseException("存在未审核的工作计算，无法变更为失效");
            }
        }
        config.setState(dto.getState());
        config.setRemark(dto.getRemark());
        config.setUnitPrice(dto.getUnitPrice());
        config.setCustomizeName(dto.getCustomizeName());
        config.setExtendFieldOne(dto.getExtendFieldOne());
        config.setExtendFieldTwo(dto.getExtendFieldTwo());
        config.setExtendFieldThree(dto.getExtendFieldThree());
        config.setUpdateBy(userAuthenService.getUsername());
        config.setUpdateTime(new Date());
        updateById(config);
        // 更新后再次校验
        try {
            checkConfig(config);
        }catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            throw e;
        }
    }

    @Override
    public boolean checkExistNoAudit(ValuationConfigUpdateDTO dto) {
        return metricsValuationCalService.existTransactional(
                Wrappers.lambdaQuery(MetricsValuationCalEntity.class)
                        .eq(MetricsValuationCalEntity::getValuationConfigId, dto.getId())
                        .eq(MetricsValuationCalEntity::getState, ValuationCalStateEnum.CREATED.getCode())
        );
    }

    private ValuationConfigEntity parseDto(ValuationConfigInsertDTO dto) {
        ValuationConfigEntity config = BeanUtil.copyProperties(dto, ValuationConfigEntity.class);
        if(config == null) {
            throw new ResponseException("入参异常");
        }

        ValuationConfigTypeEnum valuationConfigTypeEnum = ValuationConfigTypeEnum.fromCode(config.getValuationType());
        if(valuationConfigTypeEnum == null) {
            throw new ResponseException("计价类型错误");
        }
        // 物料
        if(!StringUtils.isEmpty(config.getMaterialCode())) {
            MaterialEntity material = materialService.getSimpleMaterialByCode(config.getMaterialCode());
            if(material == null) {
                throw new ResponseException("不存在该物料");
            }
        }
        // 工艺工序 -> 反带出物料信息
        if(valuationConfigTypeEnum == ValuationConfigTypeEnum.PROCEDURE) {
            CraftProcedureEntity craftProcedure = craftProcedureService.getById(config.getCraftProcedureId());
            if(craftProcedure == null) {
                throw new ResponseException("不存在该工艺工序");
            }
            CraftEntity craft = craftService.getById(craftProcedure.getCraftId());
            config.setMaterialCode(craft.getMaterialCode());
        }
        // 手动时 -> 计划方式设置为计时
        if(valuationConfigTypeEnum == ValuationConfigTypeEnum.MANUAL) {
            if(StringUtils.isEmpty(config.getCustomizeName())) {
                throw new ResponseException("手动时需填写自定义名称");
            }
            config.setValuationMethod(ValuationConfigMethodEnum.DURATION.getCode());
            config.setMaterialCode(null);
            config.setCraftProcedureId(null);
        }
        if(ValuationConfigMethodEnum.fromCode(config.getValuationMethod()) == null) {
            throw new ResponseException("计价方式错误");
        }
        return config;
    }

    private void checkConfig(ValuationConfigEntity config) {
        // 非手动的
        if(!ValuationConfigTypeEnum.MANUAL.getCode().equals(config.getValuationType())) {
            // 不允许物料相同
            if(config.getMaterialCode() != null) {
                List<ValuationConfigEntity> duplicateMaterialList = lambdaQuery()
                        .eq(ValuationConfigEntity::getMaterialCode, config.getMaterialCode())
                        .eq(ValuationConfigEntity::getState, ValuationConfigStateEnum.USED.getCode())
                        .notIn(ValuationConfigEntity::getValuationType, ValuationConfigTypeEnum.MANUAL.getCode())
                        .list();
                Map<String, List<String>> typeMaterialSetMap = duplicateMaterialList.stream().collect(Collectors.groupingBy(
                                ValuationConfigEntity::getValuationType,
                                Collectors.mapping(ValuationConfigEntity::getMaterialCode, Collectors.toList())
                        )
                );
                // 条件1. 计价类型=物料时, 内部不允许有相同的
                if(ValuationConfigTypeEnum.MATERIAL.getCode().equals(config.getValuationType())) {
                    List<String> materialTypeMaterials = typeMaterialSetMap.get(config.getValuationType());
                    if(CollUtil.isNotEmpty(materialTypeMaterials) && materialTypeMaterials.size() > 1) {
                        throw new ResponseException("存在计价类型为[物料]的重复物料");
                    }
                }
                // 条件2. 将不同计价类型分组, 同一个物料最多只能出现在一个组中
                if(typeMaterialSetMap.keySet().size() > 1) {
                    throw new ResponseException("不同计价类型分组, 同一个物料最多只能出现在一个组中");
                }
            }
            // 判断工序是否有重复项
            if(ValuationConfigTypeEnum.PROCEDURE.getCode().equals(config.getValuationType())) {
                long duplicate = lambdaQuery()
                        .eq(ValuationConfigEntity::getValuationType, ValuationConfigTypeEnum.PROCEDURE.getCode())
                        .eq(ValuationConfigEntity::getState, ValuationConfigStateEnum.USED.getCode())
                        // 工艺工序id
                        .eq(ValuationConfigEntity::getCraftProcedureId, config.getCraftProcedureId())
                        .count();
                if(duplicate > 1) {
                    throw new ResponseException("已存在相同的工序配置");
                }
            }
        }else {
            // 手动时
            if(ValuationConfigTypeEnum.MANUAL.getCode().equals(config.getValuationType())) {
                long duplicate = lambdaQuery()
                        .eq(ValuationConfigEntity::getValuationType, ValuationConfigTypeEnum.MANUAL.getCode())
                        .eq(ValuationConfigEntity::getState, ValuationConfigStateEnum.USED.getCode())
                        // 自定义名称
                        .eq(ValuationConfigEntity::getCustomizeName, config.getCustomizeName())
                        .count();
                if(duplicate > 1) {
                    throw new ResponseException("已存在相同的手动配置");
                }
            }
        }
    }

    @Override
    public void delete(Integer id) {
        ValuationConfigEntity config = getById(id);
        if(config == null) {
            throw new ResponseException("找不到该配置");
        }
        if(ValuationConfigStateEnum.USED.getCode().equals(config.getState())) {
            throw new ResponseException("当前处于生效状态");
        }
        boolean exists = metricsValuationCalService.existTransactional(
                Wrappers.lambdaQuery(MetricsValuationCalEntity.class)
                        .eq(MetricsValuationCalEntity::getValuationConfigId, id)
        );
        if(exists) {
            throw new ResponseException("该配置已被应用，无法删除");
        }
        removeById(id);
    }

    @Override
    public ValuationConfigVO detail(Integer id) {
        ValuationConfigEntity config = getById(id);
        if(config == null) {
            return null;
        }
        ValuationConfigVO vo = JacksonUtil.convertObject(config, ValuationConfigVO.class);
        showName(Collections.singletonList(vo));
        return vo;
    }

    @Override
    public Page<ValuationConfigVO> getPage(ValuationConfigSelectDTO dto) {
        LambdaQueryWrapper<ValuationConfigEntity> wrapper = Wrappers.lambdaQuery(ValuationConfigEntity.class);
        if(StringUtils.isNotEmpty(dto.getMaterialCode()) || StringUtils.isNotEmpty(dto.getMaterialName()) || StringUtils.isNotEmpty(dto.getMaterialStandard())) {
            List<MaterialEntity> materials = materialService.lambdaQuery()
                    .like(dto.getMaterialCode() != null, MaterialEntity::getCode, dto.getMaterialCode())
                    .like(dto.getMaterialName() != null, MaterialEntity::getName, dto.getMaterialName())
                    .like(dto.getMaterialStandard() != null, MaterialEntity::getStandard, dto.getMaterialStandard())
                    .list();
            if(CollUtil.isEmpty(materials)) {
                return new Page<>();
            }else {
                wrapper.in(ValuationConfigEntity::getMaterialCode, materials.stream().map(MaterialEntity::getCode).collect(Collectors.toList()));
            }
        }
        // 工艺工序
        if(CollUtil.isNotEmpty(dto.getCraftIds()) || CollUtil.isNotEmpty(dto.getProcedureIds())) {
            List<CraftProcedureEntity> craftProcedures = craftProcedureService.lambdaQuery()
                    .in(CollUtil.isNotEmpty(dto.getCraftIds()), CraftProcedureEntity::getCraftId, dto.getCraftIds())
                    .in(CollUtil.isNotEmpty(dto.getProcedureIds()), CraftProcedureEntity::getProcedureId, dto.getProcedureIds())
                    .list();
            if (CollectionUtils.isEmpty(craftProcedures)) {
                return new Page<>();
            }
            wrapper.in(ValuationConfigEntity::getCraftProcedureId, craftProcedures.stream().map(CraftProcedureEntity::getId).collect(Collectors.toSet()));
        }
        wrapper.in(CollUtil.isNotEmpty(dto.getCreateBys()), ValuationConfigEntity::getCreateBy, dto.getCreateBys());
        wrapper.in(CollUtil.isNotEmpty(dto.getValuationMethods()), ValuationConfigEntity::getValuationMethod, dto.getValuationMethods());
        wrapper.in(CollUtil.isNotEmpty(dto.getValuationTypes()), ValuationConfigEntity::getValuationType, dto.getValuationTypes());
        wrapper.in(CollUtil.isNotEmpty(dto.getStates()), ValuationConfigEntity::getState, dto.getStates());
        wrapper.orderByDesc(ValuationConfigEntity::getId);
        Page<ValuationConfigEntity> page = this.page(dto.sortPage(), wrapper);
        Page<ValuationConfigVO> result = JacksonUtil.convertPage(page, ValuationConfigVO.class);
        showName(result.getRecords());
        return result;
    }
    private void showName(List<ValuationConfigVO> records) {
        if(CollUtil.isEmpty(records)) {
            return;
        }
        // 物料的
        Set<String> materialCodes = records.stream().map(ValuationConfigVO::getMaterialCode).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<String, MaterialEntity> materialCodeMap = CollUtil.isEmpty(materialCodes)? Maps.newHashMap():
                materialService.lambdaQuery().in(MaterialEntity::getCode, materialCodes).list().stream().collect(Collectors.toMap(MaterialEntity::getCode, Function.identity()));

        // 用户的
        Map<String, String> userNameNickMap = sysUserService.getUserNameNickMap(
                Stream.of(
                        records.stream().map(ValuationConfigVO::getCreateBy).collect(Collectors.toList()),
                        records.stream().map(ValuationConfigVO::getUpdateBy).collect(Collectors.toList())
                ).flatMap(Collection::stream).distinct().collect(Collectors.toList())
        );
        // 工序的
        Set<Integer> craftProcedureIds = records.stream().map(ValuationConfigVO::getCraftProcedureId).collect(Collectors.toSet());
        List<CraftProcedureEntity> craftProcedures = CollUtil.isEmpty(craftProcedureIds)? Collections.emptyList() : craftProcedureService.listByIds(craftProcedureIds);
        Map<Integer, CraftProcedureEntity> craftProcedureMap = craftProcedures.stream().collect(Collectors.toMap(CraftProcedureEntity::getId, Function.identity()));
        // 工艺的
        Set<Integer> craftIds = craftProcedures.stream().map(CraftProcedureEntity::getCraftId).collect(Collectors.toSet());
        List<CraftEntity> crafts = CollUtil.isEmpty(craftIds)? Collections.emptyList() : craftService.listByIds(craftIds);
        Map<Integer, CraftEntity> craftMap = crafts.stream().collect(Collectors.toMap(CraftEntity::getCraftId, Function.identity()));

        for(ValuationConfigVO record: records) {
            MaterialEntity material = materialCodeMap.get(record.getMaterialCode());
            if(material != null) {
                record.setMaterialName(material.getName());
                record.setMaterialStandard(material.getStandard());
            }
            CraftProcedureEntity craftProcedure = craftProcedureMap.get(record.getCraftProcedureId());
            if(craftProcedure != null) {
                record.setProcedureName(craftProcedure.getProcedureName());
                record.setProcedureId(craftProcedure.getProcedureId());
                CraftEntity craft = craftMap.get(craftProcedure.getCraftId());
                if(craft != null) {
                    record.setCraftId(craft.getCraftId());
                    record.setCraftCode(craft.getCraftCode());
                    record.setCraftName(craft.getName());
                }
            }

            record.setCreateByName(userNameNickMap.get(record.getCreateBy()));
            record.setUpdateByName(userNameNickMap.get(record.getUpdateBy()));
            record.setValuationMethodName(Optional.ofNullable(ValuationConfigMethodEnum.fromCode(record.getValuationMethod())).map(ValuationConfigMethodEnum::getName).orElse(null));
            record.setValuationTypeName(Optional.ofNullable(ValuationConfigTypeEnum.fromCode(record.getValuationType())).map(ValuationConfigTypeEnum::getName).orElse(null));
            record.setStateName(Optional.ofNullable(ValuationConfigStateEnum.fromCode(record.getState())).map(ValuationConfigStateEnum::getName).orElse(null));
        }
    }

    @Override
    public Boolean batchUpdate(BatchChangeStateDTO batchApprovalDTO, String username) {
        List<ValuationConfigEntity> list = this.lambdaQuery().in(ValuationConfigEntity::getId, batchApprovalDTO.getIds()).list();
        // 改状态时，要求状态都是一样的才能改
        long stateCount = list.stream().map(ValuationConfigEntity::getState).distinct().count();
        if (stateCount > 1) {
            throw new ResponseException(RespCodeEnum.MUST_BE_IN_THE_SAME_STATE_TO_MODIFY);
        }
        if(ValuationConfigStateEnum.USED.getCode().equals(list.get(0).getState()) && ValuationConfigStateEnum.FAIL.getCode().equals(batchApprovalDTO.getState())) {
            boolean exists = metricsValuationCalService.existTransactional(
                    Wrappers.lambdaQuery(MetricsValuationCalEntity.class)
                            .in(MetricsValuationCalEntity::getValuationConfigId, batchApprovalDTO.getIds())
                            .eq(MetricsValuationCalEntity::getState, ValuationCalStateEnum.CREATED.getCode())
            );
            if(exists) {
                throw new ResponseException("存在未审核的工作计算，无法变更为失效");
            }
        }
        return this.lambdaUpdate().in(ValuationConfigEntity::getId, batchApprovalDTO.getIds())
                .set(ValuationConfigEntity::getState, batchApprovalDTO.getState())
                .set(ValuationConfigEntity::getUpdateTime, new Date())
                .set(ValuationConfigEntity::getUpdateBy, username)
                .update();
    }

    @Override
    public void batchDelete(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        Long count = this.lambdaQuery().in(ValuationConfigEntity::getId, ids)
                .eq(ValuationConfigEntity::getState, ValuationConfigStateEnum.USED.getCode())
                .count();
        if(count > 0) {
            throw new ResponseException("有生效的配置，无法删除");
        }
        boolean exists = metricsValuationCalService.existTransactional(
                Wrappers.lambdaQuery(MetricsValuationCalEntity.class)
                        .in(MetricsValuationCalEntity::getValuationConfigId, ids)
        );
        if(exists) {
            throw new ResponseException("有配置已被应用，无法删除");
        }
        this.removeByIds(ids);
    }

}
