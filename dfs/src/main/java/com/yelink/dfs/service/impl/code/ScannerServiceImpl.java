package com.yelink.dfs.service.impl.code;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.OperationType;
import com.yelink.dfs.constant.RedisKeyPrefix;
import com.yelink.dfs.constant.code.CodeReportAttributeEnum;
import com.yelink.dfs.constant.code.MaintainTypeEnum;
import com.yelink.dfs.constant.code.ProductFlowCodeMaintainStateEnum;
import com.yelink.dfs.constant.code.ProductFlowCodeQualityStateEnum;
import com.yelink.dfs.constant.code.ProductFlowCodeStateEnum;
import com.yelink.dfs.constant.code.ScannerQualityStateEnum;
import com.yelink.dfs.constant.model.FacilitiesCountTypeEnum;
import com.yelink.dfs.constant.product.CraftProcedureInspectMethodEnum;
import com.yelink.dfs.constant.product.DataTypeEnum;
import com.yelink.dfs.constant.product.InspectComparatorEnum;
import com.yelink.dfs.constant.product.InspectDefectJudgeEnum;
import com.yelink.dfs.constant.product.InspectTriggerConditionEnum;
import com.yelink.dfs.constant.product.MaterialInspectMethodEnum;
import com.yelink.dfs.entity.code.CodeReportEntity;
import com.yelink.dfs.entity.code.MaterialReplaceEntity;
import com.yelink.dfs.entity.code.ProductFlowCodeEntity;
import com.yelink.dfs.entity.code.ProductFlowCodeRecordEntity;
import com.yelink.dfs.entity.code.ProductFlowCodeRecordExtendEntity;
import com.yelink.dfs.entity.code.dto.CodeRecordInformationDTO;
import com.yelink.dfs.entity.code.dto.DetectionSubmitDTO;
import com.yelink.dfs.entity.code.dto.MaintainSubmitDTO;
import com.yelink.dfs.entity.code.dto.ScannerReportDTO;
import com.yelink.dfs.entity.code.dto.ScannerRespDTO;
import com.yelink.dfs.entity.common.config.dto.MaintainLocationMachineConfigDTO;
import com.yelink.dfs.entity.defect.DefectDefineEntity;
import com.yelink.dfs.entity.device.DeviceEntity;
import com.yelink.dfs.entity.device.dto.IndicatorEntityDTO;
import com.yelink.dfs.entity.feed.FeedRecordEntity;
import com.yelink.dfs.entity.maintain.dto.MaintainRecordDTO;
import com.yelink.dfs.entity.model.FacilitiesEntity;
import com.yelink.dfs.entity.order.WorkOrderBasicUnitRelationEntity;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.entity.order.WorkOrderProcedureRelationEntity;
import com.yelink.dfs.entity.order.dto.OperationOrderDTO;
import com.yelink.dfs.entity.product.CraftEntity;
import com.yelink.dfs.entity.product.CraftProcedureEntity;
import com.yelink.dfs.entity.product.MaterialEntity;
import com.yelink.dfs.entity.product.ProcedureControllerConfigEntity;
import com.yelink.dfs.entity.product.ProcedureInspectRecordBaseUnitRelationEntity;
import com.yelink.dfs.entity.product.ProcedureInspectRecordEntity;
import com.yelink.dfs.entity.product.dto.AddRecordListDTO;
import com.yelink.dfs.entity.product.dto.CheckInspectSchemeDTO;
import com.yelink.dfs.entity.reporter.dto.ReporterRecordDTO;
import com.yelink.dfscommon.dto.dfs.ReporterRecordVO;
import com.yelink.dfs.entity.target.TargetModelEntity;
import com.yelink.dfs.entity.user.SysUserEntity;
import com.yelink.dfs.mapper.code.CodeRelevanceMapper;
import com.yelink.dfs.mapper.defect.DefectDefineMapper;
import com.yelink.dfs.mapper.target.TargetModelMapper;
import com.yelink.dfs.open.v1.aksk.ams.ExtProductOrderInterface;
import com.yelink.dfs.open.v1.locationMachine.dto.FidNewestResultDTO;
import com.yelink.dfs.service.code.CodeLogService;
import com.yelink.dfs.service.code.CodeRelevanceService;
import com.yelink.dfs.service.code.CodeReportService;
import com.yelink.dfs.service.code.CodeTargetRecordService;
import com.yelink.dfs.service.code.MaterialReplaceService;
import com.yelink.dfs.service.code.ProductFlowCodeRecordExtendService;
import com.yelink.dfs.service.code.ProductFlowCodeRecordService;
import com.yelink.dfs.service.code.ProductFlowCodeService;
import com.yelink.dfs.service.code.ScannerService;
import com.yelink.dfs.service.common.CommonService;
import com.yelink.dfs.service.common.DictService;
import com.yelink.dfs.service.common.OperationLogService;
import com.yelink.dfs.service.common.config.BusinessConfigService;
import com.yelink.dfs.service.device.DeviceService;
import com.yelink.dfs.service.feed.FeedRecordService;
import com.yelink.dfs.service.impl.common.WorkPropertise;
import com.yelink.dfs.service.maintain.MaintainRecordService;
import com.yelink.dfs.service.manufacture.ProductionLineService;
import com.yelink.dfs.service.model.FacilitiesService;
import com.yelink.dfs.service.model.WorkCenterService;
import com.yelink.dfs.service.order.IsolationService;
import com.yelink.dfs.service.order.OrderWorkOrderService;
import com.yelink.dfs.service.order.RecordWorkOrderUnqualifiedService;
import com.yelink.dfs.service.order.WorkOrderBasicUnitRelationService;
import com.yelink.dfs.service.order.WorkOrderProcedureInspectResultService;
import com.yelink.dfs.service.order.WorkOrderProcedureRelationService;
import com.yelink.dfs.service.order.WorkOrderService;
import com.yelink.dfs.service.product.CraftProcedureService;
import com.yelink.dfs.service.product.CraftService;
import com.yelink.dfs.service.product.MaterialInspectMethodService;
import com.yelink.dfs.service.product.MaterialService;
import com.yelink.dfs.service.product.ProcedureControllerConfigService;
import com.yelink.dfs.service.product.ProcedureInspectionConfigService;
import com.yelink.dfs.service.target.record.ReportLineService;
import com.yelink.dfs.service.user.SysUserService;
import com.yelink.dfscommon.api.assignment.AssignmentInterface;
import com.yelink.dfscommon.constant.Constants;
import com.yelink.dfscommon.constant.OrderNumTypeEnum;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.constant.dfs.code.CodeRelationTypeEnum;
import com.yelink.dfscommon.constant.dfs.code.FeedOperationTypeEnum;
import com.yelink.dfscommon.constant.dfs.code.FeedStateEnum;
import com.yelink.dfscommon.constant.dfs.code.ProductFlowCodeRecordStateEnum;
import com.yelink.dfscommon.constant.dfs.code.ProductFlowCodeTypeEnum;
import com.yelink.dfscommon.constant.dfs.config.ConfigConstant;
import com.yelink.dfscommon.constant.dfs.config.ConfigValueConstant;
import com.yelink.dfscommon.constant.dfs.order.WorkOrderStateEnum;
import com.yelink.dfscommon.dto.ams.open.order.ProductOrderDetailDTO;
import com.yelink.dfscommon.dto.common.config.FullPathCodeDTO;
import com.yelink.dfscommon.dto.dfs.DefectDefineDTO;
import com.yelink.dfscommon.dto.dfs.DefectRecordDTO;
import com.yelink.dfscommon.dto.dfs.FeedRecordDTO;
import com.yelink.dfscommon.dto.dfs.MaintainBatchDTO;
import com.yelink.dfscommon.dto.dfs.ProductFlowCodeAddFeedRecordDTO;
import com.yelink.dfscommon.dto.dfs.ReportDTO;
import com.yelink.dfscommon.entity.ams.ProductOrderEntity;
import com.yelink.dfscommon.entity.ams.ProductOrderReportRemarkEntity;
import com.yelink.dfscommon.entity.dfs.OperationLogEntity;
import com.yelink.dfscommon.entity.dfs.ProductFlowCodeResultEntity;
import com.yelink.dfscommon.entity.dfs.WorkCenterEntity;
import com.yelink.dfscommon.entity.dfs.productFlowCode.AddFlowCodeBatchDTO;
import com.yelink.dfscommon.entity.dfs.productFlowCode.AddFlowCodeDTO;
import com.yelink.dfscommon.entity.dfs.productFlowCode.AddMaterialCodeDTO;
import com.yelink.dfscommon.entity.dfs.productFlowCode.CodeRelevanceEntity;
import com.yelink.dfscommon.entity.dfs.productFlowCode.CodeTargetRecordEntity;
import com.yelink.dfscommon.entity.dfs.productFlowCode.GeneralSubmitDTO;
import com.yelink.dfscommon.entity.dfs.productFlowCode.ProcedureInspectionConfigDefectEntity;
import com.yelink.dfscommon.entity.dfs.productFlowCode.ProcedureInspectionConfigEntity;
import com.yelink.dfscommon.utils.DateUtil;
import com.yelink.dfscommon.utils.JacksonUtil;
import com.yelink.dfscommon.utils.MathUtil;
import com.yelink.dfscommon.utils.WrapperUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeSet;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Service
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ScannerServiceImpl implements ScannerService {
    private final static String AGING_DURATION = "老化时长";
    @Resource
    private FacilitiesService facilitiesService;
    @Resource
    private ProductFlowCodeRecordService productFlowCodeRecordService;
    @Resource
    private ProductionLineService productionLineService;
    @Resource
    private WorkOrderService workOrderService;
    @Resource
    private MaterialService materialService;
    @Resource
    private FeedRecordService feedRecordService;
    @Resource
    private CommonService commonService;
    @Resource
    private DeviceService deviceService;
    @Resource
    private CodeTargetRecordService codeTargetRecordService;
    @Resource
    private ProductFlowCodeService productFlowCodeService;
    @Resource
    private CraftService craftService;
    @Resource
    private CraftProcedureService craftProcedureService;
    @Resource
    private ProcedureControllerConfigService procedureControllerConfigService;
    @Resource
    private ProcedureInspectionConfigService procedureInspectionConfigService;
    @Resource
    private MaterialReplaceService materialReplaceService;
    @Resource
    private MaintainRecordService maintainRecordService;
    @Resource
    private DictService dictService;
    @Resource
    private CodeRelevanceMapper codeRelevanceMapper;
    @Resource
    private TargetModelMapper targetModelMapper;
    @Resource
    private RecordWorkOrderUnqualifiedService recordWorkOrderUnqualifiedService;
    @Resource
    private OperationLogService operationLogService;
    @Resource
    private SysUserService userService;
    @Resource
    private OrderWorkOrderService orderWorkOrderService;
    @Resource
    private ExtProductOrderInterface extProductOrderInterface;
    @Resource
    private BusinessConfigService businessConfigService;
    @Resource
    private WorkOrderProcedureRelationService workOrderProcedureRelationService;
    @Resource
    private WorkCenterService workCenterService;
    @Resource
    private WorkOrderBasicUnitRelationService workOrderBasicUnitRelationService;
    @Resource
    private WorkPropertise workPropertise;
    @Resource
    private AssignmentInterface assignmentInterface;
    @Resource
    private IsolationService isolationService;
    @Resource
    private CodeRelevanceService codeRelevanceService;
    @Resource
    private CodeReportService codeReportService;
    @Resource
    private ReportLineService reportLineService;
    @Resource
    private MaterialInspectMethodService materialInspectMethodService;
    @Resource
    private DefectDefineMapper defectDefineMapper;
    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private WorkOrderProcedureInspectResultService procedureInspectResultService;
    @Resource
    private ProductFlowCodeRecordExtendService codeRecordExtendService;
    @Resource
    private CodeLogService codeLogService;

    /**
     * 添加过站记录--一般工位机
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ProductFlowCodeResultEntity addCodeRecordGeneral(AddFlowCodeDTO addFlowCodeDTO) {
        try {
            //反向添加流水码
            ProductFlowCodeEntity productFlowCodeEntity = productFlowCodeService.getByProductFlowCode(addFlowCodeDTO.getProductFlowCode());
            if (productFlowCodeEntity == null) {
                if (addFlowCodeDTO.getAutoAddCode() != null && addFlowCodeDTO.getAutoAddCode() && StringUtils.isNotBlank(addFlowCodeDTO.getWorkOrderNumber())) {
                    this.addProductFlowCode(addFlowCodeDTO.getProductFlowCode(), addFlowCodeDTO.getWorkOrderNumber(),null, addFlowCodeDTO.getUserName());
                } else {
                    throw new ResponseException("流水码" + addFlowCodeDTO.getProductFlowCode() + "无法解析");
                }
            }
            //工艺工序为空逻辑
            ProductFlowCodeRecordEntity productFlowCodeRecordEntity;
            if (addFlowCodeDTO.getCraftProcedureId() == null) {
                productFlowCodeRecordEntity = this.addCodeRecordNoCraftProcedure(addFlowCodeDTO);
            } else if (Boolean.FALSE.equals(addFlowCodeDTO.getIsCheck())) {
                // 添加过站记录--无检查
                productFlowCodeRecordEntity = this.addCodeRecordNoCheck(addFlowCodeDTO);
            } else {
                productFlowCodeRecordEntity = this.addCodeRecord(addFlowCodeDTO);
            }
            // 添加条码日志
            codeLogService.add(productFlowCodeEntity, productFlowCodeRecordEntity, "扫码过站", "扫码过站");
            return ProductFlowCodeResultEntity.builder()
                    .workOrderNumber(productFlowCodeRecordEntity.getRelationNumber())
                    .productFlowCode(productFlowCodeRecordEntity.getProductFlowCode())
                    .codeRecordId(productFlowCodeRecordEntity.getId())
                    .type(ProductFlowCodeTypeEnum.getNameByCode(productFlowCodeRecordEntity.getType()))
                    .craftProcedureId(productFlowCodeRecordEntity.getProdureId())
                    .build();
        } catch (Exception e) {
            //如果报错将条码缓存信息删除
            redisTemplate.delete(RedisKeyPrefix.SCANNER_PRODUCT_FLOW_CODE + addFlowCodeDTO.getProductFlowCode());
            throw e;
        }
    }
    /**
     * 添加过站记录和并提交追溯信息
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ProductFlowCodeResultEntity addRecordAndSubmit(GeneralSubmitDTO generalSubmitDTO) {
        //默认自动添加条码
        AddFlowCodeDTO addFlowCodeDTO = AddFlowCodeDTO.builder()
                .productFlowCode(generalSubmitDTO.getProductFlowCode())
                .fid(generalSubmitDTO.getFid())
                .needFid(generalSubmitDTO.getFid()!=null)
                .userName(generalSubmitDTO.getUserName())
                .state(generalSubmitDTO.getState()==null?ProductFlowCodeRecordStateEnum.COMPLETE.getCode():generalSubmitDTO.getState())
                .workOrderNumber(generalSubmitDTO.getWorkOrderNumber())
                .craftProcedureId(generalSubmitDTO.getCraftProcedureId())
                .isCheck(generalSubmitDTO.getIsCheck())
                .autoAddCode(false)
                .isEndTime(true).build();
        if(addFlowCodeDTO.getCraftProcedureId()==null) {
            List<CraftProcedureEntity> craftProcedureEntityList = craftProcedureService.getCraftProcedureEntitiesByMaterialFid(addFlowCodeDTO.getFid(), addFlowCodeDTO.getWorkOrderNumber());
            if (!CollectionUtils.isEmpty(craftProcedureEntityList)) {
                addFlowCodeDTO.setCraftProcedureId(craftProcedureEntityList.get(0).getId());
            }
        }
        //开了校验且没有工艺工序的报错
        if(addFlowCodeDTO.getIsCheck()&&addFlowCodeDTO.getCraftProcedureId()==null){
            throw new ResponseException("无对应工艺工序");
        }
        ProductFlowCodeResultEntity productFlowCodeResultEntity = this.addCodeRecordGeneral(addFlowCodeDTO);
        //添加指标
        generalSubmitDTO.setCodeRecordId(productFlowCodeResultEntity.getCodeRecordId());
        CraftProcedureEntity craftProcedureEntity = craftProcedureService.getById(productFlowCodeResultEntity.getCraftProcedureId());
        if (craftProcedureEntity != null) {
            productFlowCodeResultEntity.setCraftProcedureName(craftProcedureEntity.getProcedureName());
            productFlowCodeResultEntity.setCraftProcedureAlias(craftProcedureEntity.getAlias());
        }
        this.generalSubmit(generalSubmitDTO);
        return productFlowCodeResultEntity;
    }
    /**
     * 添加过站记录--无工艺工序
     */
    @Transactional(rollbackFor = Exception.class)
    public ProductFlowCodeRecordEntity addCodeRecordNoCraftProcedure(AddFlowCodeDTO addFlowCodeDTO) {
        //1、获取扫码记录相关信息
        CodeRecordInformationDTO codeRecordInformationDTO = getRecordInformation(addFlowCodeDTO);
        //工单加工位如果有对应工艺工序则填上
        Integer fid = codeRecordInformationDTO.getFacilitiesEntity() == null ? null : codeRecordInformationDTO.getFacilitiesEntity().getFid();
        if (fid != null) {
            String workOrderNumber = codeRecordInformationDTO.getWorkOrderEntity() == null ? null : codeRecordInformationDTO.getWorkOrderEntity().getWorkOrderNumber();
            List<CraftProcedureEntity> craftProcedureEntityList = craftProcedureService.getCraftProcedureEntitiesByMaterialFid(fid, workOrderNumber);
            if (!CollectionUtils.isEmpty(craftProcedureEntityList)) {
                codeRecordInformationDTO.setCraftProcedureEntity(craftProcedureEntityList.get(0));
            }
        }
        //2、添加过站记录
        return saveRecord(addFlowCodeDTO, codeRecordInformationDTO);
    }


    /**
     * 添加过站记录--无检查
     */
    @Transactional(rollbackFor = Exception.class)
    public ProductFlowCodeRecordEntity addCodeRecordNoCheck(AddFlowCodeDTO addFlowCodeDTO) {
        //1、获取扫码记录相关信息
        CodeRecordInformationDTO codeRecordInformationDTO = getRecordInformation(addFlowCodeDTO);
        //2、添加过站记录
        return saveRecord(addFlowCodeDTO, codeRecordInformationDTO);
    }



    /**
     * 添加过站记录（标准方法）
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ProductFlowCodeRecordEntity addCodeRecord(AddFlowCodeDTO addFlowCodeDTO) {
        //1、获取扫码记录相关信息
        CodeRecordInformationDTO codeRecordInformationDTO = getRecordInformation(addFlowCodeDTO);
        CraftProcedureEntity craftProcedureEntity = codeRecordInformationDTO.getCraftProcedureEntity();
        //2、拿到关联条码
        Set<String> relevanceCodes = productFlowCodeRecordService.getRelevanceCodes(addFlowCodeDTO.getProductFlowCode());
        //3、判断维修质检,返回该工序后的所有工序
        Set<Integer> lastProcedures = checkMaintainAndQuality(relevanceCodes, craftProcedureEntity);
        //6、维修后将后续所有工序记录失效
        if (!CollectionUtils.isEmpty(lastProcedures)) {
            productFlowCodeRecordService.lambdaUpdate().in(ProductFlowCodeRecordEntity::getProductFlowCode, relevanceCodes)
                    .in(ProductFlowCodeRecordEntity::getProdureId, lastProcedures)
                    .set(ProductFlowCodeRecordEntity::getState, ProductFlowCodeRecordStateEnum.DISABLE.getCode())
                    .update();
        }
        //4、工序控制校验
        checkProcedureController(codeRecordInformationDTO, relevanceCodes, addFlowCodeDTO);
        //5、校验条码质检状态
        if (codeRecordInformationDTO.getFacilitiesEntity() != null && codeRecordInformationDTO.getFacilitiesEntity().getCodeStateCheck()) {
            Integer qualityState = codeRecordInformationDTO.getProductFlowCodeEntity().getQualityState();
            if (qualityState != null && ProductFlowCodeQualityStateEnum.FALSE.getCode() == qualityState) {
                throw new ResponseException("条码状态为不合格，无法通过" + codeRecordInformationDTO.getFacilitiesEntity().getFname() + "工位");
            }
        }
        //7、添加过站记录
        return saveRecord(addFlowCodeDTO, codeRecordInformationDTO);

    }

    /**
     * 包装工位机-提交
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addProductFlowCodePackaging(String productFlowCode, String finishedProductCode, Integer fid, Integer craftProcedureId, String userName) {
        //如果成平码无记录则需要反向添加
        ProductFlowCodeEntity productFlowCodeEntity = productFlowCodeService.getByProductFlowCode(finishedProductCode);
        if (productFlowCodeEntity == null) {
            productFlowCodeEntity = productFlowCodeService.getByProductFlowCode(productFlowCode);
            productFlowCodeEntity.setId(null);
            productFlowCodeEntity.setType(ProductFlowCodeTypeEnum.FINISHED_PRODUCT_CODE.getCode());
            productFlowCodeEntity.setProductFlowCode(finishedProductCode);
            productFlowCodeEntity.setCreateBy(userName);
            productFlowCodeEntity.setCreateTime(new Date());
            productFlowCodeService.save(productFlowCodeEntity);
            // 添加日志
            codeLogService.addCodeLog(Stream.of(productFlowCodeEntity).collect(Collectors.toList()), OperationType.ADD);
        }
        CodeRelevanceEntity codeRelevanceEntity = codeRelevanceService.lambdaQuery().eq(CodeRelevanceEntity::getCode, finishedProductCode)
                .eq(CodeRelevanceEntity::getRelevanceCode, productFlowCode).last("limit 1").one();
        if (codeRelevanceEntity == null) {
            //添加关联表
            codeRelevanceEntity = CodeRelevanceEntity.builder()
                    .code(finishedProductCode).relevanceCode(productFlowCode).build();
            productFlowCodeService.relevanceCode(codeRelevanceEntity);
        }
        //添加成品码记录ls
        AddFlowCodeDTO addFlowCodeFinish = AddFlowCodeDTO.builder().productFlowCode(finishedProductCode).fid(fid).userName(userName)
                .state(ProductFlowCodeRecordStateEnum.DISABLE.getCode()).craftProcedureId(craftProcedureId).isEndTime(true).build();
        ProductFlowCodeRecordEntity productFlowCodeRecordEntity = this.addCodeRecordNoCheck(addFlowCodeFinish);
        // 添加条码日志
        codeLogService.add(productFlowCodeEntity, productFlowCodeRecordEntity, "条码关联，关联后的条码为：" + finishedProductCode, "条码关联");
    }

    /**
     * 获取过站记录需要的基本信息，并且做了基础数据合法性校验以及内容缓存
     *
     * @param addFlowCodeDTO 基本信息需要参数
     * @return CodeRecordInformationDTO
     */
    private CodeRecordInformationDTO getRecordInformation(AddFlowCodeDTO addFlowCodeDTO) {
        //1、校验码的合法性
        ProductFlowCodeEntity productFlowCodeEntity = productFlowCodeService.getByProductFlowCode(addFlowCodeDTO.getProductFlowCode());
        if (productFlowCodeEntity == null) {
            throw new ResponseException("流水码" + addFlowCodeDTO.getProductFlowCode() + "无法解析");
        }
        //2、校验工位信息
        FacilitiesEntity facilitiesEntity = null;
        Integer lineId = addFlowCodeDTO.getLineId();
        if (addFlowCodeDTO.getNeedFid()) {
            String facilitiesEntityKey = RedisKeyPrefix.SCANNER_FACILITIES + addFlowCodeDTO.getFid();
            Object object = redisTemplate.opsForValue().get(facilitiesEntityKey);
            if (object != null) {
                facilitiesEntity = JSONObject.parseObject((String) object, FacilitiesEntity.class);
            } else {
                facilitiesEntity = facilitiesService.getById(addFlowCodeDTO.getFid());
            }
            if (facilitiesEntity == null) {
                throw new ResponseException("找不到对应" + addFlowCodeDTO.getFid() + "工位信息");
            }
            redisTemplate.opsForValue().set(facilitiesEntityKey, JSONObject.toJSONString(facilitiesEntity), workPropertise.getScannerRecordTime(), TimeUnit.MINUTES);
            lineId = facilitiesEntity.getProductionLineId();
        }

        //3、校验工艺工序信息
        CraftProcedureEntity craftProcedureEntity;
        String craftProcedureEntityKey = RedisKeyPrefix.SCANNER_CRAFT_PROCEDURE + addFlowCodeDTO.getCraftProcedureId();
        Object craftProcedureObject = redisTemplate.opsForValue().get(craftProcedureEntityKey);
        if (craftProcedureObject != null) {
            craftProcedureEntity = JSONObject.parseObject((String) craftProcedureObject, CraftProcedureEntity.class);
        } else {
            craftProcedureEntity = craftProcedureService.getById(addFlowCodeDTO.getCraftProcedureId());
        }
        if (addFlowCodeDTO.getCraftProcedureId() != null && craftProcedureEntity == null) {
            throw new ResponseException("找不到" + addFlowCodeDTO.getCraftProcedureId() + "对应工艺工序信息");
        }
        if (craftProcedureEntity != null) {
            redisTemplate.opsForValue().set(craftProcedureEntityKey, JSONObject.toJSONString(craftProcedureEntity), workPropertise.getScannerRecordTime(), TimeUnit.MINUTES);
        }
        //获取工序控制信息
        ProcedureControllerConfigEntity procedureControllerConfigEntity = null;
        if (craftProcedureEntity != null) {
            String procedureControllerConfigKey = RedisKeyPrefix.SCANNER_PROCEDURE_CONTROLLER_CONFIG + craftProcedureEntity.getId();
            Object procedureControllerConfigObject = redisTemplate.opsForValue().get(procedureControllerConfigKey);
            if (procedureControllerConfigObject != null) {
                procedureControllerConfigEntity = JSONObject.parseObject((String) procedureControllerConfigObject, ProcedureControllerConfigEntity.class);
            } else {
                procedureControllerConfigEntity = procedureControllerConfigService.getDetail(craftProcedureEntity.getId());
            }
            if (procedureControllerConfigEntity != null) {
                redisTemplate.opsForValue().set(procedureControllerConfigKey, JSONObject.toJSONString(procedureControllerConfigEntity), workPropertise.getScannerRecordTime(), TimeUnit.MINUTES);
            }
        }
        //通过码获取到对应的生产工单、如果是订单流水码则通过获取订单信息然后找到符合条件的工单信息，否则直接通过单号获取工单信息
        Boolean productionCheck = procedureControllerConfigEntity != null&&procedureControllerConfigEntity.getProcedureControllerCheck()&& procedureControllerConfigEntity.getProductionCheck();
        WorkOrderEntity workOrderEntity = getWorkOrderByCode(productFlowCodeEntity.getRelationType(),productionCheck,
                lineId, productFlowCodeEntity.getRelationNumber(), addFlowCodeDTO.getWorkOrderNumber(),facilitiesEntity==null?null:facilitiesEntity.getFid());
        if (workOrderEntity == null) {
            throw new ResponseException("条码对应工单不存在");
        }
        if (StringUtils.isNotBlank(addFlowCodeDTO.getWorkOrderNumber()) && !workOrderEntity.getWorkOrderNumber().equals(addFlowCodeDTO.getWorkOrderNumber())) {
            throw new ResponseException("条码对应工单与所选工单不一致");
        }
        //判断工序合法
        if (craftProcedureEntity != null) {
            if (workOrderEntity.getCraftId() == null || !workOrderEntity.getCraftId().equals(craftProcedureEntity.getCraftId())) {
                throw new ResponseException("此产品不包含该工序");
            }
        }
        return CodeRecordInformationDTO.builder()
                .productFlowCodeEntity(productFlowCodeEntity)
                .craftProcedureEntity(craftProcedureEntity)
                .facilitiesEntity(facilitiesEntity)
                .procedureControllerConfigEntity(procedureControllerConfigEntity)
                .workOrderEntity(workOrderEntity)
                .remark(addFlowCodeDTO.getRemark()).build();
    }

    /**
     * 工序控制校验
     *
     * @param codeRecordInformationDTO 基础信息
     * @param relevanceCodes           关联条码
     * @param addFlowCodeDTO
     */
    @Override
    public void checkProcedureController(CodeRecordInformationDTO codeRecordInformationDTO, Set<String> relevanceCodes, AddFlowCodeDTO addFlowCodeDTO) {
        // 失败的过站记录不进行校验
        if (ProductFlowCodeRecordStateEnum.FAIL.getCode().equals(addFlowCodeDTO.getState())) {
            return;
        }
        CraftProcedureEntity craftProcedureEntity = codeRecordInformationDTO.getCraftProcedureEntity();
        ProcedureControllerConfigEntity procedureControllerConfigEntity = codeRecordInformationDTO.getProcedureControllerConfigEntity();
        WorkOrderEntity workOrderEntity = codeRecordInformationDTO.getWorkOrderEntity();
        if (procedureControllerConfigEntity == null) {
            return;
        }
        //3、工序控制合法性校验
        if (!procedureControllerConfigEntity.getProcedureControllerCheck()) {
            return;
        }
        //投产检查：开启则只允许扫码投产状态的工单
        if (procedureControllerConfigEntity.getProductionCheck()) {
            if (workOrderEntity!=null&&!WorkOrderStateEnum.INVESTMENT.getCode().equals(workOrderEntity.getState())) {
                throw new ResponseException(workOrderEntity.getWorkOrderNumber() + "工单状态无法扫码");
            }
        }
        //跳站检查
        checkJump(relevanceCodes, craftProcedureEntity);
        //重复检查
        repetition(craftProcedureEntity, procedureControllerConfigEntity, relevanceCodes);
        //最大失败次数检查
        if (procedureControllerConfigEntity.getMaxFailCount() != null && ProductFlowCodeRecordStateEnum.FAIL.getCode().equals(addFlowCodeDTO.getState())) {
            checkMaxFailCount(craftProcedureEntity.getId(), procedureControllerConfigEntity.getMaxFailCount(), relevanceCodes);
        }
        //工序互检
        if (procedureControllerConfigEntity.getLastValueCheck()) {
            lastValueCheck(craftProcedureEntity, relevanceCodes);
        }
        //签名确认
        if (procedureControllerConfigEntity.getSignatureCheck()) {
            if (StringUtils.isBlank(addFlowCodeDTO.getSignatureUrl())) {
                throw new ResponseException("未进行签名确认");
            }
        }
        //投入数检查
        if(procedureControllerConfigEntity.getInputCheck()){
           WorkOrderEntity workOrderEntityInput = workOrderService.getSimpleWorkOrderByNumber(workOrderEntity.getWorkOrderNumber());
           if(workOrderEntityInput.getInputTotal()>=workOrderEntityInput.getPlanQuantity()){
               throw new ResponseException("投入数大于计划数");
           }
        }
        //最大流转时间检查
        if (procedureControllerConfigEntity.getProductionTimeoutThresholdConfiguration() != null
                && procedureControllerConfigEntity.getProductionTimeoutThresholdConfiguration() > 0
                &&StringUtils.isNotBlank(craftProcedureEntity.getSupProcedureId())) {
                ProductFlowCodeRecordEntity productFlowCodeRecordEntity = productFlowCodeRecordService.lambdaQuery()
                        .in(ProductFlowCodeRecordEntity::getProductFlowCode, relevanceCodes)
                        .eq(ProductFlowCodeRecordEntity::getState, ProductFlowCodeRecordStateEnum.COMPLETE.getCode())
                        .eq(ProductFlowCodeRecordEntity::getProdureId, craftProcedureEntity.getSupProcedureId())
                        .orderByDesc(ProductFlowCodeRecordEntity::getReportTime)
                        .orderByDesc(ProductFlowCodeRecordEntity::getId).last("limit 1").one();
                if (productFlowCodeRecordEntity != null) {
                    Double betweenTime = DateUtil.getDifferenceMin(productFlowCodeRecordEntity.getReportTime(), new Date(), 2);
                    if (procedureControllerConfigEntity.getProductionTimeoutThresholdConfiguration() < betweenTime) {
                        throw new ResponseException("超过最大流转时长:" + betweenTime + "min");
                    }
                }
            }
        // 校验工单的制造单元和所属工位一致
        FullPathCodeDTO dto = FullPathCodeDTO.builder()
                .fullPathCode(ConfigConstant.MULTIPLE_LINE_PRODUCTION).build();
        Map<String, String> valueMap = businessConfigService.getValueMap(dto);
        Boolean checkLineAndFac = JacksonUtil.parseObject(valueMap.get(ConfigValueConstant.CHECK_LINE_AND_FAC), Boolean.class);
        if (Boolean.TRUE.equals(checkLineAndFac)&&workOrderEntity!=null) {
            if (codeRecordInformationDTO.getFacilitiesEntity() != null &&
                    !codeRecordInformationDTO.getFacilitiesEntity().getProductionLineId().equals(workOrderEntity.getLineId())) {
                throw new ResponseException("工单的制造单元和过站选择工位的制造单元不一致");
            }
        }
    }

    /**
     * 获取基础过站记录信息
     *
     * @param codeRecordInformationDTO 过站记录需要数据
     * @return ProductFlowCodeRecordEntity
     */
    private ProductFlowCodeRecordEntity getCodeCodeRecord(CodeRecordInformationDTO codeRecordInformationDTO, AddFlowCodeDTO addFlowCodeDTO) {
        CraftProcedureEntity craftProcedureEntity = codeRecordInformationDTO.getCraftProcedureEntity();
        ProductFlowCodeEntity productFlowCodeEntity = codeRecordInformationDTO.getProductFlowCodeEntity();
        FacilitiesEntity facilitiesEntity = codeRecordInformationDTO.getFacilitiesEntity();
        WorkOrderEntity workOrderEntity = codeRecordInformationDTO.getWorkOrderEntity();
        Date date = new Date();
        //4、添加扫码记录
        ProductFlowCodeRecordEntity productFlowCodeRecordEntity = ProductFlowCodeRecordEntity.builder()
                .productFlowCode(productFlowCodeEntity.getProductFlowCode())
                .relationNumber(workOrderEntity.getWorkOrderNumber())
                .type(productFlowCodeEntity.getType())
                .produreId(craftProcedureEntity == null ? null : craftProcedureEntity.getId())
                .produreName(craftProcedureEntity == null ? null : craftProcedureEntity.getProcedureName())
                .materialName(productFlowCodeEntity.getMaterialName())
                .materialCode(productFlowCodeEntity.getMaterialCode())
                .reportTime(date)
                .startTime(date)
                .productOrderNumber(workOrderEntity.getProductOrderNumber())
                .reportAttribute(CodeReportAttributeEnum.MANUAL_REPORT.getCode())
                .reportBy(addFlowCodeDTO.getUserName())
                .state(addFlowCodeDTO.getState())
                .version(productFlowCodeEntity.getVersion())
                .reportSignatureUrl(addFlowCodeDTO.getSignatureUrl())
                .remark(addFlowCodeDTO.getRemark())
                .build();
        if (addFlowCodeDTO.getDeviceId() != null) {
            productFlowCodeRecordEntity.setDeviceId(addFlowCodeDTO.getDeviceId());
        }
        if (addFlowCodeDTO.getNeedFid()) {
            if(facilitiesEntity!=null) {
                // 有工位信息
                productFlowCodeRecordEntity.setLineName(productionLineService.getById(facilitiesEntity.getProductionLineId()).getName());
                productFlowCodeRecordEntity.setIsReport(FacilitiesCountTypeEnum.reportCode.contains(facilitiesEntity.getIsCheck()));
                productFlowCodeRecordEntity.setIsInput(facilitiesEntity.getIsInput());
                productFlowCodeRecordEntity.setLineId(facilitiesEntity.getProductionLineId());
                productFlowCodeRecordEntity.setFacId(facilitiesEntity.getFid());
                productFlowCodeRecordEntity.setFacName(facilitiesEntity.getFname());
            }
            //5、拿到所有设备id和指标名,逗号隔开
//            getDeviceIdAndTargetNames(productFlowCodeRecordEntity);
        } else {
            productFlowCodeRecordEntity.setIsReport(addFlowCodeDTO.getIsReport()!=null&&addFlowCodeDTO.getIsReport());
            productFlowCodeRecordEntity.setIsInput(addFlowCodeDTO.getIsInput()!=null&&addFlowCodeDTO.getIsInput());
            productFlowCodeRecordEntity.setLineId(addFlowCodeDTO.getLineId());
        }
        return productFlowCodeRecordEntity;
    }

    /**
     * 保存过站记录
     *
     * @param addFlowCodeDTO           请求信息
     * @param codeRecordInformationDTO 基础信息
     * @return ProductFlowCodeRecordEntity
     */
    private ProductFlowCodeRecordEntity saveRecord(AddFlowCodeDTO addFlowCodeDTO, CodeRecordInformationDTO codeRecordInformationDTO) {
        ProductFlowCodeRecordEntity productFlowCodeRecordEntity = this.getCodeCodeRecord(codeRecordInformationDTO, addFlowCodeDTO);
        productFlowCodeRecordService.save(productFlowCodeRecordEntity);
        if (Boolean.TRUE.equals(addFlowCodeDTO.getSupplementaryReport())) {
            // 保存扩展信息
            ProductFlowCodeRecordExtendEntity codeRecordExtendEntity = ProductFlowCodeRecordExtendEntity.builder()
                    .codeRecordId(productFlowCodeRecordEntity.getId()).supplementaryReport(true).build();
            codeRecordExtendService.save(codeRecordExtendEntity);
        }
        //6、刷新投入产出时间
        Date date = new Date();
        productFlowCodeService.refreshReportTime(codeRecordInformationDTO.getProductFlowCodeEntity(), date);
        //7、补全该工位上一次扫码的结束时间
//        if (addFlowCodeDTO.getIsEndTime() != null && addFlowCodeDTO.getIsEndTime()) {
//            codeAsyncService.complementEndTime(addFlowCodeDTO.getFid(), date);
//        }
        if( 1 == productFlowCodeRecordEntity.getState()&&productFlowCodeRecordEntity.getIsReport() &&productFlowCodeRecordEntity.getIsInput()){
            //同时报工投入数和完成数
            productFlowCodeRecordService.refreshCodeInputAndReport(productFlowCodeRecordEntity);
        }else if (productFlowCodeRecordEntity.getIsReport() && 1 == productFlowCodeRecordEntity.getState()) {
            //完成数报工逻辑
            productFlowCodeRecordService.refreshCodeAndReport(productFlowCodeRecordEntity);
        }else if (productFlowCodeRecordEntity.getIsInput() && 1 == productFlowCodeRecordEntity.getState()) {
            //投入数报工逻辑
            productFlowCodeRecordService.refreshCodeAndInput(productFlowCodeRecordEntity);
        }
        return productFlowCodeRecordEntity;
    }


    @Override
    public ProductFlowCodeRecordEntity addCodeRecordMaintainAndRefreshReport(String barCode, Integer fid, String username, Integer maintianType, Integer backCraftProcedureId, Integer craftProcedureId, String workOrderNumberTemp, Integer teamId) {
        ProductFlowCodeRecordEntity productFlowCodeRecordEntity = addCodeRecordMaintain(barCode,fid,username,maintianType,backCraftProcedureId,craftProcedureId,workOrderNumberTemp,teamId);
        try {
            CodeReportEntity codeReportEntity = codeReportService.lambdaQuery().eq(CodeReportEntity::getProductFlowCode,barCode).last("limit 1").one();
            if(codeReportEntity!=null) {
                // 刷新工单报工
                productFlowCodeRecordService.reportLineMca(productFlowCodeRecordEntity.getRelationNumber(), codeReportEntity.getReportTime()==null?new Date():codeReportEntity.getReportTime());
            }
            //刷新工单不良数
            productFlowCodeRecordService.unqualifiedLine(productFlowCodeRecordEntity.getRelationNumber(),  codeReportEntity.getUnqualifiedTime()==null?new Date():codeReportEntity.getUnqualifiedTime());
        }catch (Exception e){
            log.info("刷新工单报工、刷新工单不良数错误",e);
        }
        return productFlowCodeRecordEntity;
    }


    /**
     * 扫码重复检查
     *
     * @param craftProcedureEntity            工艺工序
     * @param procedureControllerConfigEntity 工艺工序控制
     * @param relevanceCodes                  关联条码
     */
    private void repetition(CraftProcedureEntity craftProcedureEntity, ProcedureControllerConfigEntity procedureControllerConfigEntity, Set<String> relevanceCodes) {
        List<ProductFlowCodeRecordEntity> productFlowCodeRecordEntityList = productFlowCodeRecordService.lambdaQuery()
                .in(ProductFlowCodeRecordEntity::getProductFlowCode, relevanceCodes)
                .eq(ProductFlowCodeRecordEntity::getProdureId, craftProcedureEntity.getId())
                .eq(ProductFlowCodeRecordEntity::getState, ProductFlowCodeRecordStateEnum.COMPLETE.getCode()).list();
        if (CollectionUtils.isEmpty(productFlowCodeRecordEntityList)) {
            return;
        }
        //如果开启重复交验
        if (procedureControllerConfigEntity.getReformCheck()) {
            //找到下一个该做的工序
            CraftProcedureEntity craftProcedureEntityNext = getNextCraftProcedureNoScanner(craftProcedureEntity, relevanceCodes);
            if (craftProcedureEntityNext != null) {
                throw new ResponseException("请到" + craftProcedureEntityNext.getProcedureName() + "工序进行操作，当前工序请不要重复操作");
            }
            throw new ResponseException("请不要重复扫码！");
        }
        //如果是关键工序
        if (procedureControllerConfigEntity.getJumpStationCheck()) {
            LambdaQueryWrapper<ProductFlowCodeRecordEntity> lambdaQueryWrapper2 = new LambdaQueryWrapper<>();
            lambdaQueryWrapper2.in(ProductFlowCodeRecordEntity::getProductFlowCode, relevanceCodes)
                    .orderByDesc(ProductFlowCodeRecordEntity::getReportTime)
                    .orderByDesc(ProductFlowCodeRecordEntity::getId)
                    .eq(ProductFlowCodeRecordEntity::getState, ProductFlowCodeRecordStateEnum.COMPLETE.getCode()).last("limit 1");
            ProductFlowCodeRecordEntity productFlowCodeRecordEntity = productFlowCodeRecordService.getOne(lambdaQueryWrapper2);
            if (!craftProcedureEntity.getId().equals(productFlowCodeRecordEntity.getProdureId())) {
                //找到下一个该做的工序
                CraftProcedureEntity craftProcedureEntityNext = getNextCraftProcedureNoScanner(craftProcedureEntity, relevanceCodes);
                if (craftProcedureEntityNext != null) {
                    CraftProcedureEntity entity = craftProcedureService.lambdaQuery().eq(CraftProcedureEntity::getId, productFlowCodeRecordEntity.getProdureId()).one();
                    throw new ResponseException("上一扫码工序为" + entity.getProcedureName() + "，请到" +  craftProcedureEntityNext.getProcedureName() + "工序进行操作");
                }
            }
        }
    }

    /**
     * 根据条码拿到工单
     *
     * @param relationType            条码关联单据类型
     * @param productionCheck 是否做投产检查
     * @param lineId          产线id
     * @param relationNumber  单号
     * @return WorkOrderEntity
     */
    private WorkOrderEntity getWorkOrderByCode(Integer relationType, Boolean productionCheck, Integer lineId, String relationNumber, String workOrderNumber,Integer fid) {
        WorkOrderEntity workOrderEntity = null;
        if (CodeRelationTypeEnum.PRODUCT_ORDER.getCode().equals(relationType)) {
            if(StringUtils.isNotBlank(workOrderNumber)){
                return workOrderService.getSimpleWorkOrderByNumber(workOrderNumber);
            }
            //如果是订单流水码需要根据订单号查询到工单
            if (productionCheck) {
                workOrderEntity = orderWorkOrderService.getWorkOrderByOrderIdAndLineId(relationNumber, lineId,
                        OrderNumTypeEnum.PRODUCT_ORDER.getTypeCode(), WorkOrderStateEnum.INVESTMENT.getCode(), workOrderNumber,fid);
            } else {
                workOrderEntity = orderWorkOrderService.getWorkOrderByOrderIdAndLineId(relationNumber, lineId,
                        OrderNumTypeEnum.PRODUCT_ORDER.getTypeCode(), null, workOrderNumber,fid);
            }

            if (workOrderEntity != null) {
                workOrderEntity.setProductOrderNumber(relationNumber);
            }
            return workOrderEntity;
        } else {
            String workOrderEntityKey = RedisKeyPrefix.SCANNER_WORK_ORDER + relationNumber;
            Object workOrderEntityObject = redisTemplate.opsForValue().get(workOrderEntityKey);
            if (workOrderEntityObject != null) {
                workOrderEntity = JSONObject.parseObject((String) workOrderEntityObject, WorkOrderEntity.class);
            } else {
                workOrderEntity = workOrderService.getSimpleWorkOrderByNumber(relationNumber);
            }
            if (workOrderEntity != null) {
                redisTemplate.opsForValue().set(workOrderEntityKey, JSONObject.toJSONString(workOrderEntity), workPropertise.getScannerRecordTime(), TimeUnit.MINUTES);
            }
            return workOrderEntity;
        }
    }

    /**
     * 最大失败次数检查
     *
     * @param craftProdureId 工艺工序id
     * @param maxFailCount   最大失败次数
     * @param relevanceCodes 关联条码
     */
    private void checkMaxFailCount(Integer craftProdureId, Integer maxFailCount, Set<String> relevanceCodes) {
        LambdaQueryWrapper<ProductFlowCodeRecordEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(ProductFlowCodeRecordEntity::getProductFlowCode, relevanceCodes)
                .eq(ProductFlowCodeRecordEntity::getProdureId, craftProdureId)
                .eq(ProductFlowCodeRecordEntity::getState, ProductFlowCodeRecordStateEnum.FAIL.getCode());
        Long failCount = productFlowCodeRecordService.count(lambdaQueryWrapper);
        if (failCount >= maxFailCount) {
            throw new ResponseException("超过最大失败次数" + maxFailCount);
        }
    }

    /**
     * @param craftProcedureEntity
     * @param relevanceCodes
     */
    private void lastValueCheck(CraftProcedureEntity craftProcedureEntity, Set<String> relevanceCodes) {
        if (craftProcedureEntity.getSupProcedureId() == null) {
            return;
        }
        LambdaQueryWrapper<ProductFlowCodeRecordEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(ProductFlowCodeRecordEntity::getProductFlowCode, relevanceCodes)
                .eq(ProductFlowCodeRecordEntity::getState, ProductFlowCodeRecordStateEnum.COMPLETE.getCode())
                .eq(ProductFlowCodeRecordEntity::getProdureId, craftProcedureEntity.getSupProcedureId())
                .orderByDesc(ProductFlowCodeRecordEntity::getReportTime)
                .orderByDesc(ProductFlowCodeRecordEntity::getId).last("limit 1");
        ProductFlowCodeRecordEntity productFlowCodeRecordEntity = productFlowCodeRecordService.getOne(lambdaQueryWrapper);
        if (productFlowCodeRecordEntity == null) {
            return;
        }
        LambdaQueryWrapper<CodeTargetRecordEntity> codeTargetQueryWrapper = new LambdaQueryWrapper<>();
        codeTargetQueryWrapper.eq(CodeTargetRecordEntity::getCodeRecordId, productFlowCodeRecordEntity.getId())
                .eq(CodeTargetRecordEntity::getValueCheck, true).eq(CodeTargetRecordEntity::getResult, false);
        Long count = codeTargetRecordService.count(codeTargetQueryWrapper);
        if (count > 0) {
            CraftProcedureEntity supCraftProcedureEntity = craftProcedureService.getById(craftProcedureEntity.getSupProcedureId());
            throw new ResponseException("工序:" + supCraftProcedureEntity.getProcedureName() + "指标检测未通过");
        }
    }

    /**
     * 检查扫码合法
     *
     * @param addFlowCodeDTO 请求信息
     */
    @Override
    public ProductFlowCodeResultEntity checkAddCodeRecord(AddFlowCodeDTO addFlowCodeDTO) {
        //1、获取扫码记录相关信息
        CodeRecordInformationDTO codeRecordInformationDTO = getRecordInformation(addFlowCodeDTO);
        CraftProcedureEntity craftProcedureEntity = codeRecordInformationDTO.getCraftProcedureEntity();
        //2、拿到关联条码
        Set<String> relevanceCodes = new HashSet<>();
        if (CollectionUtils.isEmpty(addFlowCodeDTO.getProductFlowCodes())) {
            relevanceCodes = productFlowCodeRecordService.getRelevanceCodes(addFlowCodeDTO.getProductFlowCode());
        } else {
            for (String productFlowCode : addFlowCodeDTO.getProductFlowCodes()) {
                relevanceCodes.addAll(productFlowCodeRecordService.getRelevanceCodes(productFlowCode));
            }
        }
        //3、判断维修质检,返回该工序后的所有工序
        Set<Integer> lastProcedures = checkMaintainAndQuality(relevanceCodes, craftProcedureEntity);
        //4、维修后将后续所有工序记录失效
        if (!CollectionUtils.isEmpty(lastProcedures)) {
            productFlowCodeRecordService.lambdaUpdate().in(ProductFlowCodeRecordEntity::getProductFlowCode, relevanceCodes)
                    .in(ProductFlowCodeRecordEntity::getProdureId, lastProcedures)
                    .set(ProductFlowCodeRecordEntity::getState, ProductFlowCodeRecordStateEnum.DISABLE.getCode())
                    .update();
        }
        //5、工序控制校验
        checkProcedureController(codeRecordInformationDTO, relevanceCodes, addFlowCodeDTO);
        //6、校验条码质检状态
        if (codeRecordInformationDTO.getFacilitiesEntity() != null && codeRecordInformationDTO.getFacilitiesEntity().getCodeStateCheck()) {
            Integer qualityState = codeRecordInformationDTO.getProductFlowCodeEntity().getQualityState();
            if (qualityState != null && ProductFlowCodeQualityStateEnum.FALSE.getCode() == qualityState) {
                throw new ResponseException("条码状态为不合格，无法通过" + codeRecordInformationDTO.getFacilitiesEntity().getFname() + "工位");
            }
        }
        WorkOrderEntity workOrderEntity=codeRecordInformationDTO.getWorkOrderEntity();
        ProductFlowCodeEntity productFlowCodeEntity = codeRecordInformationDTO.getProductFlowCodeEntity();
        //返回条码信息
        return ProductFlowCodeResultEntity.builder()
                .workOrderNumber(workOrderEntity==null?null:workOrderEntity.getWorkOrderNumber())
                .productFlowCode(productFlowCodeEntity==null?null:productFlowCodeEntity.getProductFlowCode())
                .type(ProductFlowCodeTypeEnum.getNameByCode(productFlowCodeEntity==null?null:productFlowCodeEntity.getType()))
                .craftProcedureId(craftProcedureEntity==null?null:craftProcedureEntity.getId())
                .build();
    }
    /**
     * 流水码绑定工单
     *
     * @param productFlowCode 条码号
     * @param workOrderNumber 工单号
     * @param materialCode 物料编号
     * @param userName        用户名
     * @return ProductFlowCodeEntity
     */
    private ProductFlowCodeEntity addProductFlowCode(String productFlowCode, String workOrderNumber,String materialCode ,String userName) {
        WorkOrderEntity workOrderEntity = workOrderService.getSimpleWorkOrderByNumber(workOrderNumber);
        if (workOrderEntity == null) {
            throw new ResponseException("无" + workOrderNumber + "对应工单");
        }
        if(StringUtils.isBlank(materialCode)){
            materialCode=workOrderEntity.getMaterialCode();
        }
        MaterialEntity materialEntity = materialService.getSimpleMaterialByCode(materialCode);
        ProductFlowCodeEntity productFlowCodeEntity = ProductFlowCodeEntity.builder()
                .productFlowCode(productFlowCode).type(ProductFlowCodeTypeEnum.PRODUCT_FLOW_CODE.getCode()).relationType(CodeRelationTypeEnum.WORK_ORDER.getCode())
                .relationNumber(workOrderNumber).materialCode(materialCode).materialName(materialEntity == null ? null : materialEntity.getName())
                .materialType(materialEntity==null?null:materialEntity.getType())
                .skuId(workOrderEntity.getSkuId()).createBy(userName).createTime(new Date()).state(ProductFlowCodeStateEnum.NO_PRINT.getCode()).build();
        productFlowCodeService.save(productFlowCodeEntity);
        // 添加日志
        codeLogService.addCodeLog(Stream.of(productFlowCodeEntity).collect(Collectors.toList()), OperationType.ADD);
        return productFlowCodeEntity;
    }


    @Override
    public ProductFlowCodeEntity addProductFlowCode(String productFlowCode, String workOrderNumber,String materialCode, Integer type, String userName) {
        WorkOrderEntity workOrderEntity = workOrderService.getSimpleWorkOrderByNumber(workOrderNumber);
        if (workOrderEntity == null) {
            throw new ResponseException("无" + workOrderNumber + "对应工单");
        }
        if(StringUtils.isBlank(materialCode)){
            materialCode=workOrderEntity.getMaterialCode();
        }
        MaterialEntity materialEntity = materialService.getSimpleMaterialByCode(materialCode);
        ProductFlowCodeEntity productFlowCodeEntity = ProductFlowCodeEntity.builder()
                .productFlowCode(productFlowCode).type(type).relationType(ProductFlowCodeTypeEnum.getRelationTypeByCode(type)).materialType(materialEntity==null?null:materialEntity.getType())
                .relationNumber(workOrderNumber).materialCode(workOrderEntity.getMaterialCode()).materialName(materialEntity == null ? null : materialEntity.getName())
                .skuId(workOrderEntity.getSkuId()).createBy(userName).createTime(new Date()).state(ProductFlowCodeStateEnum.NO_PRINT.getCode()).build();
        productFlowCodeService.save(productFlowCodeEntity);
        // 添加日志
        codeLogService.addCodeLog(Stream.of(productFlowCodeEntity).collect(Collectors.toList()), OperationType.ADD);
        return productFlowCodeEntity;
    }

    /**
     * 绑定流水码并且添加扫码记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProductFlowCodeResultEntity buildingAndAdd(String code, String workOrderNumber, Integer fid, Integer craftProcedureId, String userName) {
        LambdaQueryWrapper<ProductFlowCodeEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProductFlowCodeEntity::getProductFlowCode, code).eq(ProductFlowCodeEntity::getType, ProductFlowCodeTypeEnum.PRODUCT_FLOW_CODE.getCode());
        long count = productFlowCodeService.count(lambdaQueryWrapper);
        if (count > 0) {
            throw new ResponseException("单品码已绑定工单，请检查");
        }
        //反向添加
        ProductFlowCodeEntity productFlowCodeEntity = addProductFlowCode(code, workOrderNumber,null, userName);
        AddFlowCodeDTO addFlowCodeDTO = AddFlowCodeDTO.builder().productFlowCode(productFlowCodeEntity.getProductFlowCode()).fid(fid).userName(userName)
                .state(ProductFlowCodeRecordStateEnum.COMPLETE.getCode()).craftProcedureId(craftProcedureId).isEndTime(true).build();
        //增加记录
        return this.addCodeRecordGeneral(addFlowCodeDTO);

    }

    /**
     * 获取工位下的所有设备以及指标名称
     *
     * @param productFlowCodeRecordEntity 过站记录
     */
    private void getDeviceIdAndTargetNames(ProductFlowCodeRecordEntity productFlowCodeRecordEntity) {
        //拿到工位设备列表
        List<DeviceEntity> deviceEntityList = deviceService.lambdaQuery()
                .eq(DeviceEntity::getFid, productFlowCodeRecordEntity.getFacId())
                .list();
        productFlowCodeRecordEntity.setDeviceId(StringUtils.join(deviceEntityList.stream().map(DeviceEntity::getDeviceId).collect(Collectors.toList()), Constant.SEP));
        String targetName = null;
        if (!CollectionUtils.isEmpty(deviceEntityList)) {
            //拿到设备指标集合
            List<Integer> deviceModelIds = deviceEntityList.stream().map(DeviceEntity::getModelId).collect(Collectors.toList());
            LambdaQueryWrapper<TargetModelEntity> targetModelWrapper = new LambdaQueryWrapper<>();
            targetModelWrapper.in(TargetModelEntity::getModelId, deviceModelIds);
            List<TargetModelEntity> targetModelEntityList = targetModelMapper.selectList(targetModelWrapper);
            targetName = StringUtils.join(targetModelEntityList.stream().map(TargetModelEntity::getTargetName).distinct().collect(Collectors.toList()), Constant.SEP);
        }
        productFlowCodeRecordEntity.setTargetName(targetName);
    }


    /**
     * 维修工位机-扫码
     */
    @Override
    public Map<String, Object> scannerMaintain(String productFlowCode, Integer fid) {
        //拿到条码信息
        ProductFlowCodeEntity productFlowCodeEntity = productFlowCodeService.getByProductFlowCode(productFlowCode);
        if (productFlowCodeEntity == null) {
            throw new ResponseException("流水码" + productFlowCode + "无法解析");
        }
        List<WorkOrderEntity> workOrderEntityList = new ArrayList<>();
        //如果是订单流水码需要根据订单号查询到工单
        if (productFlowCodeEntity.getType() == ProductFlowCodeTypeEnum.ORDER_PRODUCT_CODE.getCode()) {
            FacilitiesEntity facilitiesEntity = facilitiesService.getById(fid);
            Integer lineId = facilitiesEntity == null ? null : facilitiesEntity.getProductionLineId();
            //根据配置拿到工单列表
            FullPathCodeDTO dto = FullPathCodeDTO.builder()
                    .fullPathCode(ConfigConstant.ORDER_WORK_ORDER_SELECT).build();
            MaintainLocationMachineConfigDTO config = businessConfigService.getValueDto(dto, MaintainLocationMachineConfigDTO.class);

            if(config==null||ConfigValueConstant.SELECT_BY_FID.equals(config.getOrderWorkOrderSelect())){
               //根据工位匹配
                WorkOrderEntity workOrderEntity = orderWorkOrderService.getWorkOrderByOrderIdAndLineId(productFlowCodeEntity.getRelationNumber(), lineId, OrderNumTypeEnum.PRODUCT_ORDER.getTypeCode(), null, null,fid);
               if(workOrderEntity!=null) {
                   workOrderEntityList.add(workOrderEntity);
               }
            }else if(ConfigValueConstant.SELECT_BY_UNQUALIFIED_RECORD.equals(config.getOrderWorkOrderSelect())){
                //根据最近质检记录选择
                ProductFlowCodeRecordEntity productFlowCodeRecordEntity=productFlowCodeRecordService.lambdaQuery()
                        .eq(ProductFlowCodeRecordEntity::getProductFlowCode,productFlowCode)
                        .eq(ProductFlowCodeRecordEntity::getIsUnqualified,true)
                        .orderByDesc(ProductFlowCodeRecordEntity::getId)
                        .last("limit 1").one();
                if(productFlowCodeRecordEntity==null){
                    throw new ResponseException("找不到最新质检记录");
                }
                WorkOrderEntity workOrderEntity = workOrderService.getSimpleWorkOrderByNumber(productFlowCodeRecordEntity.getRelationNumber());
                if(workOrderEntity!=null) {
                    workOrderEntityList.add(workOrderEntity);
                }
            }else if(ConfigValueConstant.SELECT_ALL.equals(config.getOrderWorkOrderSelect())){
                //订单下所有工单
                workOrderEntityList=workOrderService.lambdaQuery().eq(WorkOrderEntity::getProductOrderNumber,productFlowCodeEntity.getRelationNumber()).list();
            }
        } else {
            WorkOrderEntity workOrderEntity = workOrderService.getSimpleWorkOrderByNumber(productFlowCodeEntity.getRelationNumber());
            workOrderEntityList.add(workOrderEntity);
        }
        if (CollectionUtils.isEmpty(workOrderEntityList)) {
            throw new ResponseException("无对应生产工单");
        }
        // 工单不包含创建、关闭、取消
        List<Integer> states = Arrays.asList(WorkOrderStateEnum.CREATED.getCode(), WorkOrderStateEnum.CLOSED.getCode(), WorkOrderStateEnum.CANCELED.getCode());
        List<WorkOrderEntity> workOrderEntities = workOrderEntityList.stream().filter(o -> o.getLineId() != null && !states.contains(o.getState())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(workOrderEntities)) {
            throw new ResponseException("无对应生产工单");
        }

        Map<String, Object> resultMap = new HashMap<>(2);
        resultMap.put("workOrderEntityList", workOrderEntities);
        resultMap.put("productFlowCode", productFlowCode);
        resultMap.put("type", ProductFlowCodeTypeEnum.getNameByCode(productFlowCodeEntity.getType()));
        return resultMap;
    }


    /**
     * 维修工位机添加扫码记录
     *
     * @param productFlowCode      条码号
     * @param fid                  工位id
     * @param userName             用户名
     * @param maintainType         维修类型
     * @param backCraftProcedureId 返回工艺工序
     * @return ProductFlowCodeRecordEntity
     */
    @Override
    public ProductFlowCodeRecordEntity addCodeRecordMaintain(String productFlowCode, Integer fid, String userName, Integer maintainType, Integer backCraftProcedureId, Integer craftProcedureId, String workOrderNumberTemp,Integer teamId) {
        //1、获取扫码记录相关信息
        AddFlowCodeDTO addFlowCodeDTO = AddFlowCodeDTO.builder()
                .productFlowCode(productFlowCode)
                .fid(fid)
                .userName(userName)
                .craftProcedureId(craftProcedureId)
                .state(ProductFlowCodeRecordStateEnum.COMPLETE.getCode())
                .isEndTime(true)
                .workOrderNumber(workOrderNumberTemp).build();
        CodeRecordInformationDTO codeRecordInformationDTO = getRecordInformation(addFlowCodeDTO);
        ProductFlowCodeEntity productFlowCodeEntity = codeRecordInformationDTO.getProductFlowCodeEntity();
        //5、添加过站记录
        ProductFlowCodeRecordEntity productFlowCodeRecordEntity = saveRecord(addFlowCodeDTO, codeRecordInformationDTO);
        if(MaintainTypeEnum.REPAIR.getCode().equals(maintainType)) {
            //如果是返回产线取消不良
            productFlowCodeRecordService.cancelCodeUnqualified(productFlowCodeRecordEntity);
        }else if (MaintainTypeEnum.SCRAP.getCode().equals(maintainType)) {
            //如果是报废的话扫码记录报工资格全部转为失效
            productFlowCodeRecordService.refreshCodeUnqualified(productFlowCodeRecordEntity);
        } else if (MaintainTypeEnum.SCRAP_REUSE.getCode().equals(maintainType)) {
            //复用的话版本号加一
            productFlowCodeRecordService.refreshCodeUnqualified(productFlowCodeRecordEntity);
            productFlowCodeEntity.setVersion(productFlowCodeEntity.getVersion() + 1);
        } else if (MaintainTypeEnum.FINISHED.getCode().equals(maintainType)) {
            //成品完成数加一，不良数减掉
            productFlowCodeRecordService.refreshCodeReport(productFlowCodeRecordEntity);
        } else if (MaintainTypeEnum.REPAIR.getCode().equals(maintainType)) {
            //冻结
        }
        //补全维修信息
        productFlowCodeRecordEntity.setIsMaintain(true);
        productFlowCodeRecordEntity.setMaintainType(maintainType);
        productFlowCodeRecordEntity.setBackCraftProcedureId(backCraftProcedureId);
        productFlowCodeRecordEntity.setIsUnqualified(MaintainTypeEnum.SCRAP.getCode().equals(maintainType) || MaintainTypeEnum.SCRAP_REUSE.getCode().equals(maintainType));
        productFlowCodeRecordEntity.setFirstUnqualified(productFlowCodeRecordEntity.getIsUnqualified());
        productFlowCodeRecordEntity.setIsReport(MaintainTypeEnum.FINISHED.getCode().equals(maintainType));
        productFlowCodeRecordService.updateById(productFlowCodeRecordEntity);

        //修改条码维修状态
        if (MaintainTypeEnum.SCRAP.getCode().equals(maintainType)) {
            productFlowCodeEntity.setMaintainState(ProductFlowCodeMaintainStateEnum.FALSE.getCode());
            productFlowCodeEntity.setQualityState(ProductFlowCodeQualityStateEnum.FALSE.getCode());
        } else {
            productFlowCodeEntity.setMaintainState(ProductFlowCodeMaintainStateEnum.TRUE.getCode());
            productFlowCodeEntity.setQualityState(ProductFlowCodeQualityStateEnum.TRUE.getCode());
        }
        productFlowCodeService.updateById(productFlowCodeEntity);
        redisTemplate.opsForValue().set(RedisKeyPrefix.SCANNER_PRODUCT_FLOW_CODE + productFlowCode, JSONObject.toJSONString(productFlowCodeEntity), 5, TimeUnit.MINUTES);
        // 添加条码日志
        codeLogService.add(productFlowCodeEntity, productFlowCodeRecordEntity, "维修", "维修");
        return productFlowCodeRecordEntity;
    }


    /**
     * 质检工位机添加扫码记录 - 无工艺工序
     *
     * @param addFlowCodeDTO
     * @return ProductFlowCodeRecordEntity
     */
    @Override
    public ProductFlowCodeResultEntity addCodeRecordQuality(AddFlowCodeDTO addFlowCodeDTO) {
        //2、添加过站记录
        ProductFlowCodeResultEntity productFlowCodeResultEntity = this.addCodeRecordGeneral(addFlowCodeDTO);
        ProductFlowCodeRecordEntity productFlowCodeRecordEntity = productFlowCodeRecordService.getById(productFlowCodeResultEntity.getCodeRecordId());
        productFlowCodeRecordEntity.setIsQuality(true);
        productFlowCodeRecordEntity.setFirstUnqualified(addFlowCodeDTO.getIsUnqualified());
        productFlowCodeRecordEntity.setIsUnqualified(addFlowCodeDTO.getIsUnqualified());
        productFlowCodeRecordService.updateById(productFlowCodeRecordEntity);

        ProductFlowCodeEntity productFlowCodeEntity = productFlowCodeService.getByProductFlowCode(addFlowCodeDTO.getProductFlowCode());


        if (addFlowCodeDTO.getIsUnqualified()) {
            //刷掉产出数
            productFlowCodeRecordService.refreshCodeUnqualifiedAndReport(productFlowCodeRecordEntity);
            productFlowCodeEntity.setQualityState(ProductFlowCodeQualityStateEnum.FALSE.getCode());
        } else {
            productFlowCodeRecordService.cancelCodeUnqualifiedAndReport(productFlowCodeRecordEntity);
            productFlowCodeEntity.setQualityState(ProductFlowCodeQualityStateEnum.TRUE.getCode());
        }
        //刷掉条码检查状态
        productFlowCodeEntity.setMaintainState(null);
        productFlowCodeService.updateById(productFlowCodeEntity);
        redisTemplate.opsForValue().set(RedisKeyPrefix.SCANNER_PRODUCT_FLOW_CODE + productFlowCodeEntity.getProductFlowCode(), JSONObject.toJSONString(productFlowCodeEntity), workPropertise.getScannerRecordTime(), TimeUnit.MINUTES);
        // 添加条码日志
        codeLogService.add(productFlowCodeEntity, productFlowCodeRecordEntity, "质检", "质检");
        return ProductFlowCodeResultEntity.builder()
                .workOrderNumber(productFlowCodeRecordEntity.getRelationNumber())
                .productFlowCode(productFlowCodeRecordEntity.getProductFlowCode())
                .codeRecordId(productFlowCodeRecordEntity.getId())
                .type(ProductFlowCodeTypeEnum.getNameByCode(productFlowCodeRecordEntity.getType()))
                .reportTime(productFlowCodeRecordEntity.getReportTime())
                .build();
    }

    /**
     * 批量添加质检过站记录
     *
     * @param addFlowCodeBatchDTO
     * @return ProductFlowCodeRecordEntity
     */
    @Override
    public void addCodeRecordQualityBatch(AddFlowCodeBatchDTO addFlowCodeBatchDTO) {
        if (CollectionUtils.isEmpty(addFlowCodeBatchDTO.getProductFlowCodeList())) {
            throw new ResponseException("条码号不能为空");
        }
        //1、反向添加流水码
        AddFlowCodeDTO addFlowCodeDTO = new AddFlowCodeDTO();
        BeanUtils.copyProperties(addFlowCodeBatchDTO, addFlowCodeDTO);
        List<ProductFlowCodeEntity> productFlowCodeEntityList = new ArrayList<>();
        String firstProductFlowCode = addFlowCodeBatchDTO.getProductFlowCodeList().get(0);
        addFlowCodeDTO.setProductFlowCode(firstProductFlowCode);
        ProductFlowCodeEntity productFlowCodeEntityOne = productFlowCodeService.getByProductFlowCode(firstProductFlowCode);
        if (productFlowCodeEntityOne == null) {
            productFlowCodeEntityOne = this.addProductFlowCode(addFlowCodeDTO.getProductFlowCode(), addFlowCodeDTO.getWorkOrderNumber(), null, addFlowCodeDTO.getUserName());
            productFlowCodeEntityOne.setQualityState(addFlowCodeDTO.getIsUnqualified() ? ProductFlowCodeQualityStateEnum.FALSE.getCode() : ProductFlowCodeQualityStateEnum.TRUE.getCode());
            productFlowCodeService.updateById(productFlowCodeEntityOne);
        }
        for (int i = 1; i < addFlowCodeBatchDTO.getProductFlowCodeList().size(); i++) {
            ProductFlowCodeEntity productFlowCodeEntityTemp = new ProductFlowCodeEntity();
            BeanUtils.copyProperties(productFlowCodeEntityOne, productFlowCodeEntityTemp);
            String productFlowCode = addFlowCodeBatchDTO.getProductFlowCodeList().get(i);
            productFlowCodeEntityTemp.setProductFlowCode(productFlowCode);
            productFlowCodeEntityTemp.setId(null);
            productFlowCodeEntityList.add(productFlowCodeEntityTemp);
        }
        productFlowCodeService.synProductFlowCode(productFlowCodeEntityList, addFlowCodeBatchDTO.getUserName());
        //2、单品码报工
        CodeRecordInformationDTO codeRecordInformationDTO = getRecordInformation(addFlowCodeDTO);
        if (addFlowCodeDTO.getIsUnqualified()) {
            CodeReportEntity codeReportTempEntity = codeReportService.lambdaQuery().eq(CodeReportEntity::getProductFlowCode, productFlowCodeEntityOne.getProductFlowCode())
                    .eq(CodeReportEntity::getRelationNumber,productFlowCodeEntityOne.getRelationNumber()).one();
            CodeReportEntity codeReportOneEntity = CodeReportEntity.builder()
                    .relationNumber(productFlowCodeEntityOne.getRelationNumber())
                    .productFlowCode(productFlowCodeEntityOne.getProductFlowCode())
                    .isReport(false)
                    .isUnqualified(true)
                    .unqualifiedBy(productFlowCodeEntityOne.getCreateBy())
                    .unqualifiedTime(productFlowCodeEntityOne.getCreateTime())
                    .isFirstUnqualified(true)
                    .firstUnqualifiedTime(productFlowCodeEntityOne.getCreateTime())
                    .lineId(codeRecordInformationDTO.getFacilitiesEntity() == null ? null : codeRecordInformationDTO.getFacilitiesEntity().getProductionLineId())
                    .version(1)
                    .createTime(new Date())
                    .build();
            if (codeReportTempEntity != null) {
                codeReportOneEntity.setId(codeReportTempEntity.getId());
            }
            codeReportService.saveOrUpdate(codeReportOneEntity);
            List<CodeReportEntity> codeReportEntities = new ArrayList<>();
            for (ProductFlowCodeEntity productFlowCodeEntity : productFlowCodeEntityList) {
                CodeReportEntity codeReportEntity = CodeReportEntity.builder()
                        .relationNumber(productFlowCodeEntity.getRelationNumber())
                        .productFlowCode(productFlowCodeEntity.getProductFlowCode())
                        .isReport(false)
                        .isUnqualified(true)
                        .unqualifiedBy(productFlowCodeEntity.getCreateBy())
                        .unqualifiedTime(productFlowCodeEntity.getCreateTime())
                        .isFirstUnqualified(true)
                        .firstUnqualifiedTime(productFlowCodeEntity.getCreateTime())
                        .lineId(codeRecordInformationDTO.getFacilitiesEntity() == null ? null : codeRecordInformationDTO.getFacilitiesEntity().getProductionLineId())
                        .version(1)
                        .createTime(new Date())
                        .build();
                codeReportEntities.add(codeReportEntity);
            }
            codeReportService.saveBatch(codeReportEntities);
            productFlowCodeRecordService.unqualifiedLine(addFlowCodeBatchDTO.getWorkOrderNumber(), productFlowCodeEntityOne.getCreateTime());
            productFlowCodeRecordService.reportLineMca(addFlowCodeBatchDTO.getWorkOrderNumber(), productFlowCodeEntityOne.getCreateTime());
        }
    }

    /**
     * 关键工序检查
     *
     * @param relevanceCodes       关联条码
     * @param craftProcedureEntity 工艺工序
     */
    @Override
    public void checkJump(Set<String> relevanceCodes, CraftProcedureEntity craftProcedureEntity) {
        if (StringUtils.isBlank(craftProcedureEntity.getSupProcedureId())) {
            return;
        }
        //拿到工艺下所有该工序之前的关键工序
        Object object = redisTemplate.opsForValue().get(RedisKeyPrefix.SCANNER_KEY_PROCEDURE + craftProcedureEntity.getId());
        Set<Integer> keyProcedureIds;
        if (object != null) {
            keyProcedureIds = JSONObject.parseObject((String) object, Set.class);
        } else {
            keyProcedureIds = getKeyProcedures(craftProcedureEntity, new HashMap<>());
        }
        if (CollectionUtils.isEmpty(keyProcedureIds)) {
            return;
        } else {
            redisTemplate.opsForValue().set(RedisKeyPrefix.SCANNER_KEY_PROCEDURE + craftProcedureEntity.getId(), JSONObject.toJSONString(keyProcedureIds), workPropertise.getScannerRecordTime(), TimeUnit.MINUTES);
        }
        //判断是否所有关键工序都已扫过
        //未通过关键工序列表
        List<String> keyProcedureStringList = new ArrayList<>();
        for (Integer keyProcedureId : keyProcedureIds) {
            LambdaQueryWrapper<ProductFlowCodeRecordEntity> lambdaQueryWrapperTemp = new LambdaQueryWrapper<>();
            lambdaQueryWrapperTemp.eq(ProductFlowCodeRecordEntity::getProdureId, keyProcedureId)
                    .in(ProductFlowCodeRecordEntity::getProductFlowCode, relevanceCodes)
                    .orderByDesc(ProductFlowCodeRecordEntity::getReportTime)
                    .orderByDesc(ProductFlowCodeRecordEntity::getId)
                    .eq(ProductFlowCodeRecordEntity::getState, ProductFlowCodeRecordStateEnum.COMPLETE.getCode()).last("limit 1");
            ProductFlowCodeRecordEntity productFlowCodeRecordEntityemp = productFlowCodeRecordService.getOne(lambdaQueryWrapperTemp);
            if (productFlowCodeRecordEntityemp == null) {
                CraftProcedureEntity keyCraftProcedureEntity = craftProcedureService.getById(keyProcedureId);
                if (keyCraftProcedureEntity == null) {
                    throw new ResponseException("无对应工艺工序:" + keyProcedureId);
                }
                keyProcedureStringList.add(keyCraftProcedureEntity.getProcedureName());
            }
        }
        if(!CollectionUtils.isEmpty(keyProcedureStringList)) {
            keyProcedureStringList = keyProcedureStringList.stream().sorted(Collections.reverseOrder()).collect(Collectors.toList());
            throw new ResponseException("请先通过关键工序:" + keyProcedureStringList.stream().collect(Collectors.joining(Constant.SEP)));
        }
    }

    /**
     * 判断维修质检,返回该工序后的所有工序
     *
     * @param relevanceCodes       关联条码
     * @param craftProcedureEntity 工艺工序
     */
    public Set<Integer> checkMaintainAndQuality(Set<String> relevanceCodes, CraftProcedureEntity craftProcedureEntity) {
        //拿到最新一次为完成的扫码记录
        LambdaQueryWrapper<ProductFlowCodeRecordEntity> lambdaQueryWrapper2 = new LambdaQueryWrapper<>();
        lambdaQueryWrapper2.in(ProductFlowCodeRecordEntity::getProductFlowCode, relevanceCodes)
                .eq(ProductFlowCodeRecordEntity::getState, ProductFlowCodeRecordStateEnum.COMPLETE.getCode())
                .orderByDesc(ProductFlowCodeRecordEntity::getReportTime)
                .orderByDesc(ProductFlowCodeRecordEntity::getId).last("limit 1");
        ProductFlowCodeRecordEntity productFlowCodeRecordEntity = productFlowCodeRecordService.getOne(lambdaQueryWrapper2);
        if (productFlowCodeRecordEntity == null) {
            return null;
        }
        //如果上一次为维修扫码或者无工序质检
        if (MaintainTypeEnum.REPAIR.getCode().equals(productFlowCodeRecordEntity.getMaintainType())
                || (productFlowCodeRecordEntity.getIsQuality() && productFlowCodeRecordEntity.getProdureId() == null)) {
            if (productFlowCodeRecordEntity.getBackCraftProcedureId() != null && !craftProcedureEntity.getId().equals(productFlowCodeRecordEntity.getBackCraftProcedureId())) {
                CraftProcedureEntity craftProcedureEntityBak = craftProcedureService.getById(productFlowCodeRecordEntity.getBackCraftProcedureId());
                throw new ResponseException("指定返回工序为：" + craftProcedureEntityBak.getProcedureName());
            }
            //拿到工序后的所有工序
            Object object = redisTemplate.opsForValue().get(RedisKeyPrefix.SCANNER_LAST_PROCEDURES + craftProcedureEntity.getId());
            Set<Integer> result;
            if (object != null) {
                result = JSON.parseObject((String) object, Set.class);
            } else {
                List<CraftProcedureEntity> craftProcedureEntityList = craftProcedureService.getProcedureList(craftProcedureEntity.getCraftId());
                result = this.getLastProcedures(craftProcedureEntity, craftProcedureEntityList, new HashMap<>());
            }
            if (result != null) {
                redisTemplate.opsForValue().set(RedisKeyPrefix.SCANNER_LAST_PROCEDURES + craftProcedureEntity.getId(), JSON.toJSONString(result), workPropertise.getScannerRecordTime(), TimeUnit.MINUTES);
            }
            return result;
        }
        if (MaintainTypeEnum.FINISHED.getCode().equals(productFlowCodeRecordEntity.getMaintainType())) {
            throw new ResponseException("该产品已完成，请勿扫码");
        }
        if (MaintainTypeEnum.SCRAP.getCode().equals(productFlowCodeRecordEntity.getMaintainType())) {
            throw new ResponseException("该产品已报废，请勿扫码");
        }
        if (MaintainTypeEnum.FREEZE.getCode().equals(productFlowCodeRecordEntity.getMaintainType())) {
            throw new ResponseException("该产品已冻结，请勿扫码");
        }
        return null;
    }


    /**
     * 获取下一个未扫码工序
     *
     * @param craftProcedureEntity 工艺工序
     * @param relevanceCodes       关联条码
     * @return CraftProcedureEntity
     */
    private CraftProcedureEntity getNextCraftProcedureNoScanner(CraftProcedureEntity craftProcedureEntity, Set<String> relevanceCodes) {
        LambdaQueryWrapper<ProductFlowCodeRecordEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(ProductFlowCodeRecordEntity::getProductFlowCode, relevanceCodes).eq(ProductFlowCodeRecordEntity::getState, ProductFlowCodeRecordStateEnum.COMPLETE.getCode());
        List<ProductFlowCodeRecordEntity> recordList = productFlowCodeRecordService.list(lambdaQueryWrapper);
        List<CraftProcedureEntity> list = new ArrayList<>();
        list.add(craftProcedureEntity);
        return getNextCraftProcedureNoScanner(list, recordList.stream().map(ProductFlowCodeRecordEntity::getProdureId).collect(Collectors.toList()));
    }

    /**
     * 获取下一个未扫码工序(递归方法)
     *
     * @param list 工艺工序集合
     * @return CraftProcedureEntity
     */
    private CraftProcedureEntity getNextCraftProcedureNoScanner(List<CraftProcedureEntity> list, List<Integer> recordList) {
        for (CraftProcedureEntity craftProcedureEntity : list) {
            LambdaQueryWrapper<CraftProcedureEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(CraftProcedureEntity::getSupProcedureId, craftProcedureEntity.getId());
            List<CraftProcedureEntity> listNext = craftProcedureService.list(lambdaQueryWrapper);
            if (CollectionUtils.isEmpty(listNext)) {
                continue;
            }
            for (CraftProcedureEntity craftProcedureEntityNext : listNext) {
                if (!recordList.contains(craftProcedureEntityNext.getId())) {
                    return craftProcedureEntityNext;
                }
            }
            return getNextCraftProcedureNoScanner(listNext, recordList);
        }
        return null;
    }

    /**
     * 获取工序的上一级关键工序(递归)
     *
     * @param craftProcedureEntity 工艺工序
     * @return 关键工序
     */
    @Override
    public Set<Integer> getKeyProcedures(CraftProcedureEntity craftProcedureEntity, Map<Integer, Integer> map) {
        Set<Integer> resultList = new HashSet<>();
        if (StringUtils.isBlank(craftProcedureEntity.getSupProcedureId())) {
            return resultList;
        }
        String[] supProcedures = craftProcedureEntity.getSupProcedureId().split(Constant.SEP);
        for (String supProcedureId : supProcedures) {
            if (map.containsKey(Integer.valueOf(supProcedureId))) {
                continue;
            } else {
                map.put(Integer.valueOf(supProcedureId), Integer.valueOf(supProcedureId));
            }
            ProcedureControllerConfigEntity procedureControllerConfigEntity = procedureControllerConfigService.getDetail(Integer.valueOf(supProcedureId));
            if (procedureControllerConfigEntity != null && procedureControllerConfigEntity.getJumpStationCheck()) {
                resultList.add(Integer.valueOf(supProcedureId));
            }
            CraftProcedureEntity craftProcedureEntityTemp = craftProcedureService.getById(Integer.valueOf(supProcedureId));
            if(craftProcedureEntityTemp==null){
                throw new ResponseException("找不到"+supProcedureId+"对应工艺工序");
            }
            resultList.addAll(getKeyProcedures(craftProcedureEntityTemp, map));
        }
        return resultList;
    }

    /**
     * 获取工艺工序后的所有工艺工序（包括自己）
     *
     * @param craftProcedureEntity 工艺工序
     * @return 工艺工序后的所有工艺工序
     */
    @Override
    public Set<Integer> getLastProcedures(CraftProcedureEntity craftProcedureEntity, List<CraftProcedureEntity> list, Map<Integer, Integer> map) {
        Set<Integer> result = new HashSet<>();
        result.add(craftProcedureEntity.getId());
        for (CraftProcedureEntity craftProcedureEntityTemp : list) {
            if (map.containsKey(craftProcedureEntityTemp.getId())) {
                continue;
            }
            if (StringUtils.isNotBlank(craftProcedureEntityTemp.getSupProcedureId()) && Arrays.asList(craftProcedureEntityTemp.getSupProcedureId().split(Constant.SEP)).contains(craftProcedureEntity.getId().toString())) {
                map.put(craftProcedureEntityTemp.getId(), craftProcedureEntityTemp.getId());
                result.addAll(getLastProcedures(craftProcedureEntityTemp, list, map));
            }
        }
        return result;
    }

    /**
     * 一般工位机-提交
     *
     * @param generalSubmitDTO 提交信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void generalSubmit(GeneralSubmitDTO generalSubmitDTO) {
        //拿到对应扫码记录
        ProductFlowCodeRecordEntity productFlowCodeRecordEntity = productFlowCodeRecordService.getById(generalSubmitDTO.getCodeRecordId());
        if (productFlowCodeRecordEntity == null) {
            throw new ResponseException("请先扫码后再提交");
        }
        ProductFlowCodeEntity productFlowCodeEntity = productFlowCodeService.getByProductFlowCode(productFlowCodeRecordEntity.getProductFlowCode());
        // 物料检查
        //拿到工艺工序控制配置信息
        if (Objects.nonNull(productFlowCodeRecordEntity.getProdureId())) {
            ProcedureControllerConfigEntity procedureControllerConfigEntity = procedureControllerConfigService.getDetail(productFlowCodeRecordEntity.getProdureId());
            boolean materialCheck = procedureControllerConfigEntity != null && procedureControllerConfigEntity.getMaterialCheck();
            feedRecordService.scannerCheckMaterial(materialCheck, generalSubmitDTO.getCheckType(), generalSubmitDTO.getMaterialCodes(), productFlowCodeRecordEntity.getRelationNumber(), productFlowCodeRecordEntity.getProdureId());
        }
        //添加上料记录
        addFeedRecord(generalSubmitDTO.getMaterialCodes(),generalSubmitDTO.getMaterialCodeList(), productFlowCodeRecordEntity);
        //添加指标记录
        submitTargetValue(productFlowCodeRecordEntity, generalSubmitDTO.getProcedureInspectionConfigEntityList());
        //添加质检记录
        if (generalSubmitDTO.getDefectRecordDTO() != null) {
            generalSubmitDTO.getDefectRecordDTO().setBarCode(productFlowCodeRecordEntity.getProductFlowCode());
            generalSubmitDTO.getDefectRecordDTO().setWorkOrder(productFlowCodeRecordEntity.getRelationNumber());
            generalSubmitDTO.getDefectRecordDTO().setCodeRecordId(productFlowCodeRecordEntity.getId());
            generalSubmitDTO.getDefectRecordDTO().setFid(productFlowCodeRecordEntity.getFacId());
            recordWorkOrderUnqualifiedService.saveRecord(generalSubmitDTO.getDefectRecordDTO(), generalSubmitDTO.getUserName());
        }
        // 添加条码日志
        codeLogService.add(productFlowCodeEntity, productFlowCodeRecordEntity, "上料或检验项提交", "上料或检验项提交");
    }

    /**
     * 维修工位机-提交
     *
     * @param maintainSubmitDTO 提交信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void maintainSubmit(MaintainSubmitDTO maintainSubmitDTO) {
        //添加扫码记录
        ProductFlowCodeRecordEntity productFlowCodeRecordEntity = addCodeRecordMaintainAndRefreshReport(maintainSubmitDTO.getProductFlowCode(), maintainSubmitDTO.getFid(), maintainSubmitDTO.getUserName(), maintainSubmitDTO.getMaintainType(), maintainSubmitDTO.getBackCraftProcedureId(), maintainSubmitDTO.getCraftProcedureId(), maintainSubmitDTO.getWorkOrderNumber(), maintainSubmitDTO.getTeamId());
         //添加上料记录
        List<FeedRecordEntity> feedRecordEntityList = maintainSubmitDTO.getFeedRecordEntityList();
        List<FeedRecordEntity> oldFeedRecordList = new ArrayList<>();
        String reportBy = productFlowCodeRecordEntity.getReportBy();
        Integer codeRecordId = productFlowCodeRecordEntity.getId();
        if (!CollectionUtils.isEmpty(feedRecordEntityList)) {
            List<MaterialReplaceEntity> materialReplaceEntityList = new ArrayList<>();
            Date date = new Date();
            for (FeedRecordEntity feedRecordEntity : feedRecordEntityList) {
                feedRecordEntity.setWorkOrderNum(productFlowCodeRecordEntity.getRelationNumber());
                feedRecordEntity.setLineId(productFlowCodeRecordEntity.getLineId());
                feedRecordEntity.setFacId(productFlowCodeRecordEntity.getFacId());
                feedRecordEntity.setFeedTime(date);
                feedRecordEntity.setFeedBy(reportBy);
                feedRecordEntity.setCodeRecordId(codeRecordId);
                feedRecordEntity.setOperationType(FeedOperationTypeEnum.FEED.getCode());
                feedRecordEntity.setMaterialNum(0.0);

                if (feedRecordEntity.getFeedRecordId() != null) {
                    // 替换
                    FeedRecordEntity oldFeedRecord = feedRecordService.getById(feedRecordEntity.getFeedRecordId());
                    if (oldFeedRecord == null) {
                        continue;
                    }
                    // 失效旧记录
                    oldFeedRecord.setState(FeedStateEnum.DISABLE.getCode());
                    oldFeedRecordList.add(oldFeedRecord);
                    // 替换后处理
                    feedRecordEntity.setOperationType(FeedOperationTypeEnum.REPLACE_FEED.getCode());
                    feedRecordEntity.setRelateFeedRecordId(oldFeedRecord.getFeedRecordId());
                    feedRecordEntity.setFeedRecordId(null);
                    // 物料替换
                    materialReplace(productFlowCodeRecordEntity, materialReplaceEntityList, oldFeedRecord, feedRecordEntity);
                }
            }
            materialReplaceService.saveBatch(materialReplaceEntityList);
            feedRecordService.saveOrUpdateBatch(feedRecordEntityList);
            feedRecordService.updateBatchById(oldFeedRecordList);
        }
        //解绑上料记录
        if (!CollectionUtils.isEmpty(maintainSubmitDTO.getDeleteFeedRecordEntityList())) {
            for(FeedRecordEntity feedRecordEntity:maintainSubmitDTO.getDeleteFeedRecordEntityList()) {
                feedRecordService.unbind(feedRecordEntity.getFeedRecordId(), codeRecordId, reportBy);
            }
        }
        // 添加维修记录
        if (!CollectionUtils.isEmpty(maintainSubmitDTO.getMaintainDefineDTOList())) {
            MaintainRecordDTO maintainRecordDTO = MaintainRecordDTO.builder()
                    .barCode(productFlowCodeRecordEntity.getProductFlowCode())
                    .defines(maintainSubmitDTO.getMaintainDefineDTOList())
                    .fid(productFlowCodeRecordEntity.getFacId())
                    .quantity(1)
                    .workOrder(productFlowCodeRecordEntity.getRelationNumber())
                    .codeRecordId(codeRecordId)
                    .maintianType(maintainSubmitDTO.getMaintainType())
                    .build();
            if(MaintainTypeEnum.REPAIR.getCode().equals(maintainSubmitDTO.getMaintainType())){
                maintainRecordDTO.setBackCraftProcedureId(maintainSubmitDTO.getBackCraftProcedureId());
            }
            maintainRecordService.saveRecord(maintainRecordDTO, reportBy);
            recordWorkOrderUnqualifiedService.updateRecord(maintainSubmitDTO.getProductFlowCode(), maintainSubmitDTO.getMaintainDefineDTOList());
        }
        //添加流水码关联
        if (StringUtils.isNotBlank(maintainSubmitDTO.getReplaceCode())) {
            CodeRelevanceEntity codeRelevanceEntity = CodeRelevanceEntity.builder()
                    .code(productFlowCodeRecordEntity.getProductFlowCode())
                    .relevanceCode(maintainSubmitDTO.getReplaceCode())
                    .build();
            codeRelevanceMapper.insert(codeRelevanceEntity);
        }
    }

    /**
     * 物料替换
     * @param productFlowCodeRecordEntity
     * @param materialReplaceEntityList
     * @param oldFeedRecord
     * @param newFeedRecord
     */
    private void materialReplace(ProductFlowCodeRecordEntity productFlowCodeRecordEntity, List<MaterialReplaceEntity> materialReplaceEntityList,FeedRecordEntity oldFeedRecord,FeedRecordEntity newFeedRecord) {
        MaterialReplaceEntity materialReplaceEntity = new MaterialReplaceEntity();
        materialReplaceEntity.setCodeRecordId(productFlowCodeRecordEntity.getId());
        materialReplaceEntity.setReplaceMaterialCode(newFeedRecord.getMaterialCode());
        materialReplaceEntity.setReplaceMaterialName(newFeedRecord.getMaterialName());
        materialReplaceEntity.setMaterialName(oldFeedRecord.getMaterialName());
        materialReplaceEntity.setMaterialCode(oldFeedRecord.getMaterialCode());
        materialReplaceEntity.setReplaceTime(new Date());
        materialReplaceEntity.setReplaceBy(productFlowCodeRecordEntity.getReportBy());
        materialReplaceEntityList.add(materialReplaceEntity);
    }

    /**
     * 获取维修可返回工序
     *
     * @param productFlowCode 单品码
     * @param workOrderNumber 工单号
     * @return 工序列表
     */
    @Override
    public List<CraftProcedureEntity> getMaintainBackProcedures(String productFlowCode, String workOrderNumber) {
        WorkOrderEntity workOrderEntity = workOrderService.getSimpleWorkOrderByNumber(workOrderNumber);
        if (workOrderEntity == null) {
            throw new ResponseException("无" + workOrderNumber + "对应工单");
        }
        // 工单可以选择指定工艺,而非最新
        CraftEntity craftEntity = craftService.getById(workOrderEntity.getCraftId());
        if (Objects.isNull(craftEntity)) {
            log.error("工单没有关联工艺,{}", workOrderEntity.getWorkOrderNumber());
            return new ArrayList<>();
        }
        //拿到关联条码
        Set<String> relevanceCodes = productFlowCodeRecordService.getRelevanceCodes(productFlowCode);
        List<CraftProcedureEntity> craftProcedureEntities = craftProcedureService.getProcedureList(craftEntity.getCraftId());
        //工序控制关闭的话返回全部工序
        if (!craftEntity.getProcedureControllerCheck()) {
            for (CraftProcedureEntity craftProcedureEntity : craftProcedureEntities) {
                LambdaQueryWrapper<ProductFlowCodeRecordEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                lambdaQueryWrapper.in(ProductFlowCodeRecordEntity::getProductFlowCode, relevanceCodes)
                        .eq(ProductFlowCodeRecordEntity::getProdureId, craftProcedureEntity.getId());
                long count = productFlowCodeRecordService.count(lambdaQueryWrapper);
                craftProcedureEntity.setWhetherImport(count > 0);
            }
            return craftProcedureEntities;
        }
        List<CraftProcedureEntity> craftProcedureEntitiesResult = new ArrayList<>();
        //判断工序是否做过
        for (CraftProcedureEntity craftProcedureEntity : craftProcedureEntities) {
            LambdaQueryWrapper<ProductFlowCodeRecordEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.in(ProductFlowCodeRecordEntity::getProductFlowCode, relevanceCodes)
                    .eq(ProductFlowCodeRecordEntity::getProdureId, craftProcedureEntity.getId());
            long count = productFlowCodeRecordService.count(lambdaQueryWrapper);
            craftProcedureEntity.setWhetherImport(count > 0);
            //拿到工艺工序控制配置信息
            ProcedureControllerConfigEntity procedureControllerConfigEntity = procedureControllerConfigService.getDetail(craftProcedureEntity.getId());
            //如果关键工序且无扫码记录，后续工序就不应该展示了
            if (count <= 0 && (procedureControllerConfigEntity == null ? false : procedureControllerConfigEntity.getJumpStationCheck())) {
                break;
            }
            craftProcedureEntitiesResult.add(craftProcedureEntity);
        }
        return craftProcedureEntitiesResult;
    }

    /**
     * 添加上料记录
     *
     * @param materialCodes
     * @param productFlowCodeRecordEntity
     */
    private void addFeedRecord(String materialCodes, List<AddMaterialCodeDTO> materialCodeList, ProductFlowCodeRecordEntity productFlowCodeRecordEntity) {
        if (StringUtils.isBlank(materialCodes) && CollectionUtils.isEmpty(materialCodeList)) {
            return;
        }
        if (StringUtils.isNotBlank(materialCodes) && CollectionUtils.isEmpty(materialCodeList)) {
            materialCodeList = Arrays.stream(materialCodes.split(Constant.CN_DUN_HAO)).map(AddMaterialCodeDTO::new).collect(Collectors.toList());
        }
        //添加上料记录
        List<FeedRecordEntity> batchSaves = new ArrayList<>();
        Date date = new Date();
        for (AddMaterialCodeDTO materialCodeDto : materialCodeList) {
            if (StringUtils.isNotBlank(materialCodeDto.getReverseAdditionMaterialCode())) {
                //反向添加物料码
                ProductFlowCodeEntity productFlowCodeMaterialEntity = productFlowCodeService.getByProductFlowCode(materialCodeDto.getMaterialCode());
                if (productFlowCodeMaterialEntity == null) {
                    addProductFlowCode(materialCodeDto.getMaterialCode(), productFlowCodeRecordEntity.getRelationNumber(),
                            materialCodeDto.getReverseAdditionMaterialCode(), productFlowCodeRecordEntity.getReportBy());
                }
            }
            FeedRecordEntity feedRecordEntity = FeedRecordEntity.builder().lineId(productFlowCodeRecordEntity.getLineId())
                    .facId(productFlowCodeRecordEntity.getFacId())
                    .workOrderNum(productFlowCodeRecordEntity.getRelationNumber())
                    .feedTime(date)
                    .feedBy(productFlowCodeRecordEntity.getReportBy())
                    .codeRecordId(productFlowCodeRecordEntity.getId())
                    .materialNum(1.0).build();
            feedRecordService.buildFeedRecordMaterialProp(feedRecordEntity, materialCodeDto.getMaterialCode());
            batchSaves.add(feedRecordEntity);
            //添加关联表
            CodeRelevanceEntity codeRelevanceEntity = CodeRelevanceEntity.builder()
                    .code(productFlowCodeRecordEntity.getProductFlowCode()).relevanceCode(feedRecordEntity.getMaterialCode()).build();
            productFlowCodeService.relevanceCode(codeRelevanceEntity);
        }
        feedRecordService.saveBatch(batchSaves);
    }

    @Override
    public void addFeedRecord(ProductFlowCodeAddFeedRecordDTO addFeedRecordDTO) {
        if (CollectionUtils.isEmpty(addFeedRecordDTO.getFeedRecords()) || CollectionUtils.isEmpty(addFeedRecordDTO.getCodeRecordIds())) {
            return;
        }
        FacilitiesEntity fac = facilitiesService.getById(addFeedRecordDTO.getFacId());

//        Set<String> materialBatches = addFeedRecordDTO.getFeedRecords().stream().map(FeedRecordDTO::getMaterialBatch).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
//        Map<String, BarCodeEntity> batchEntityMap = new HashMap<>(materialBatches.size());
//        if (!CollectionUtils.isEmpty(materialBatches)) {
//            batchEntityMap = barCodeService.lambdaQuery()
//                    .select(BarCodeEntity::getBarCode, BarCodeEntity::getSupplier)
//                    .in(BarCodeEntity::getBarCode, materialBatches)
//                    .list().stream().collect(Collectors.toMap(BarCodeEntity::getBarCode, v -> v, (n, o) -> o));
//        }

        Map<String, MaterialEntity> codeMaterialMap = new HashMap<>(16);
        //添加上料记录
        List<FeedRecordEntity> batchSaves = new ArrayList<>();
        for (Integer codeRecordId : addFeedRecordDTO.getCodeRecordIds()) {
            ProductFlowCodeRecordEntity productFlowCodeRecordEntity = productFlowCodeRecordService.getById(codeRecordId);
            if(productFlowCodeRecordEntity==null){
                continue;
            }
            for (FeedRecordDTO feedRecord : addFeedRecordDTO.getFeedRecords()) {
                MaterialEntity material = codeMaterialMap.get(feedRecord.getMaterialCode());
                if (Objects.isNull(material)) {
                    material = materialService.getSimpleMaterialByCode(feedRecord.getMaterialCode());
                    codeMaterialMap.put(feedRecord.getMaterialCode(), material);
                }
                FeedRecordEntity feedRecordEntity = FeedRecordEntity.builder()
                        .lineId(fac.getProductionLineId())
                        .facId(addFeedRecordDTO.getFacId())
                        .workOrderNum(addFeedRecordDTO.getWorkOrderNumber())
                        .feedTime(Objects.isNull(feedRecord.getFeedTime()) ? new Date() : feedRecord.getFeedTime())
                        .feedBy(addFeedRecordDTO.getCreateBy())
                        .codeRecordId(codeRecordId)
                        .materialNum(1.0)
                        .materialCode(feedRecord.getMaterialCode())
                        .relevanceMaterialCode(feedRecord.getMaterialCode())
                        .materialName(Objects.nonNull(material) ? material.getName() : null)
                        .batchNumber(feedRecord.getMaterialBatch())
//                        .supplierName(StringUtils.isBlank(feedRecord.getMaterialBatch()) ? null : batchEntityMap.getOrDefault(feedRecord.getMaterialBatch(), new BarCodeEntity()).getSupplier())
                        .build();
                batchSaves.add(feedRecordEntity);
                //添加关联表
                CodeRelevanceEntity  codeRelevanceEntity = CodeRelevanceEntity.builder()
                        .code(productFlowCodeRecordEntity.getProductFlowCode()).relevanceCode(feedRecordEntity.getMaterialCode()).build();
                productFlowCodeService.relevanceCode(codeRelevanceEntity);
            }
        }
        feedRecordService.saveBatch(batchSaves);
    }


    /**
     * 保存工位指标
     *
     * @param productFlowCodeRecordEntity         过站记录
     * @param procedureInspectionConfigEntityList 检测信息
     */
    private void submitTargetValue(ProductFlowCodeRecordEntity productFlowCodeRecordEntity, List<ProcedureInspectionConfigEntity> procedureInspectionConfigEntityList) {
        if (CollectionUtils.isEmpty(procedureInspectionConfigEntityList)) {
            return;
        }
        ProductFlowCodeEntity productFlowCodeEntity=productFlowCodeService.getByProductFlowCode(productFlowCodeRecordEntity.getProductFlowCode());
        List<CodeTargetRecordEntity> batchSaves = new ArrayList<>();
        for (ProcedureInspectionConfigEntity procedureInspectionConfigEntity : procedureInspectionConfigEntityList) {
            CodeTargetRecordEntity codeTargetRecordEntity = CodeTargetRecordEntity.builder()
                    .id(null)
                    .codeRecordId(productFlowCodeRecordEntity.getId())
                    .code(productFlowCodeRecordEntity.getProductFlowCode())
                    .targetEname(procedureInspectionConfigEntity.getInspectionCode())
                    .targetName(procedureInspectionConfigEntity.getInspectionName())
                    .procedureInspectionConfigId(procedureInspectionConfigEntity.getId())
                    .value(procedureInspectionConfigEntity.getValue())
                    .result(procedureInspectionConfigEntity.getResult())
                    .upperLimit(procedureInspectionConfigEntity.getUpperLimit())
                    .downLimit(procedureInspectionConfigEntity.getDownLimit())
                    .inspectionInstrument(procedureInspectionConfigEntity.getInspectionInstrument())
                    .reportBy(productFlowCodeRecordEntity.getReportBy())
                    .reportTime(new Date())
                    .valueCheck(procedureInspectionConfigEntity.getValueCheck())
                    .unit(procedureInspectionConfigEntity.getUnit())
                    .build();
            batchSaves.add(codeTargetRecordEntity);
            //mongodb插入对应的标识属性
            codeTargetRecordService.addCodeTarget(productFlowCodeRecordEntity.getProductFlowCode(),codeTargetRecordEntity.getTargetEname(),codeTargetRecordEntity.getValue(),productFlowCodeEntity.getMaterialType(),productFlowCodeEntity.getMaterialCode(),productFlowCodeEntity.getMaterialName(),codeTargetRecordEntity.getReportBy(),Boolean.FALSE.equals(codeTargetRecordEntity.getResult())?"不合格":"合格",procedureInspectionConfigEntity.getUrl());
           // 添加相关不良记录
            addDefectRecord(productFlowCodeRecordEntity, procedureInspectionConfigEntity);
        }
        codeTargetRecordService.saveBatch(batchSaves);
        // 如果关联工序检验项，需要添加到工序检验记录表里
        saveProcedureInspectRecord(productFlowCodeRecordEntity, batchSaves);
    }

    /**
     * 如果关联工序检验项，需要添加到工序检验记录表里
     */
    private void saveProcedureInspectRecord(ProductFlowCodeRecordEntity productFlowCodeRecordEntity, List<CodeTargetRecordEntity> batchSaves) {
        List<ProcedureInspectRecordEntity> recordEntities = new ArrayList<>();
        WorkOrderEntity workOrderEntity = workOrderService.getSimpleWorkOrderByNumber(productFlowCodeRecordEntity.getRelationNumber());
        ProcedureInspectRecordBaseUnitRelationEntity build = null;
        if (Objects.nonNull(workOrderEntity)) {
            // 找到工单关联的多个生产基本单元
            List<WorkOrderBasicUnitRelationEntity> relationEntities = workOrderBasicUnitRelationService.getByWorkOrderNumber(productFlowCodeRecordEntity.getRelationNumber());
            for (WorkOrderBasicUnitRelationEntity relationEntity : relationEntities) {
                build = ProcedureInspectRecordBaseUnitRelationEntity.builder()
                        .workCenterType(relationEntity.getWorkCenterType())
                        .productionBasicUnitId(relationEntity.getProductionBasicUnitId())
                        .productionBasicUnitName(relationEntity.getProductionBasicUnitName())
                        .build();
            }
        }
        for (CodeTargetRecordEntity entity : batchSaves) {
            ProcedureInspectRecordEntity recordEntity = ProcedureInspectRecordEntity.builder()
                    .recordCode(String.valueOf(entity.getCodeRecordId()))
                    .flowCode(productFlowCodeRecordEntity.getProductFlowCode())
                    .workOrderNumber(productFlowCodeRecordEntity.getRelationNumber())
//                    .productionBaseUnitName(productionBasicUnitName)
                    .craftProcedureId(productFlowCodeRecordEntity.getProdureId())
                    .inspectCode(entity.getTargetEname())
                    .sampleNumber(1)
                    .inspectValue(entity.getValue())
                    .materialCode(productFlowCodeRecordEntity.getMaterialCode())
                    .reporter(productFlowCodeRecordEntity.getReportBy())
                    .reportTime(new Date())
                    .build();
            recordEntities.add(recordEntity);
        }
        // 结果记录，如果记录已存在则刷新该条记录
        procedureInspectResultService.saveOrUpdateRecords(recordEntities, productFlowCodeRecordEntity.getReportBy(), productFlowCodeRecordEntity.getRelationNumber(), productFlowCodeRecordEntity.getProductFlowCode(), build);
    }

    /**
     * 添加相关不良记录
     * @param productFlowCodeRecordEntity
     * @param procedureInspectionConfigEntity
     */
    private void addDefectRecord(ProductFlowCodeRecordEntity productFlowCodeRecordEntity, ProcedureInspectionConfigEntity procedureInspectionConfigEntity) {
        // 未关联不良或检验项合格时直接返回
        if (Boolean.FALSE.equals(procedureInspectionConfigEntity.getIsDefect()) || Boolean.TRUE.equals(procedureInspectionConfigEntity.getResult())) {
            return;
        }
        if (InspectDefectJudgeEnum.BOOLEAN_JUDGE.getCode().equals(procedureInspectionConfigEntity.getDefectJudgeType())) {
            DefectRecordDTO defectRecordDTO = getDefectRecordDTO(productFlowCodeRecordEntity, procedureInspectionConfigEntity.getDefectId());
            recordWorkOrderUnqualifiedService.saveRecord(defectRecordDTO, productFlowCodeRecordEntity.getReportBy());
        } else if (InspectDefectJudgeEnum.RANGE_JUDGE.getCode().equals(procedureInspectionConfigEntity.getDefectJudgeType())) {
            List<ProcedureInspectionConfigDefectEntity> defectEntities = procedureInspectionConfigEntity.getDefectEntities();
            if (CollectionUtils.isEmpty(defectEntities) || !DataTypeEnum.NUMERICAL_TYPE.getFieldEname().equals(procedureInspectionConfigEntity.getDataType())) {
                return;
            }
            for (ProcedureInspectionConfigDefectEntity defectEntity : defectEntities) {
                boolean number = MathUtil.isNumberBy(procedureInspectionConfigEntity.getValue());
                if (!number) {
                    continue;
                }
                double value = Double.parseDouble(procedureInspectionConfigEntity.getValue());
                String upperLimit = defectEntity.getUpperLimit();
                String downLimit = defectEntity.getDownLimit();
                // 上下限都为空，直接返回
                if (StringUtils.isBlank(upperLimit) && StringUtils.isBlank(downLimit)) {
                    continue;
                }
                boolean upperResult, downResult;
                if (StringUtils.isBlank(upperLimit)) {
                    upperResult = true;
                } else {
                    upperResult = value <= Double.parseDouble(upperLimit);
                }
                if (StringUtils.isBlank(downLimit)) {
                    downResult = true;
                } else {
                    downResult = value >= Double.parseDouble(downLimit);
                }
                if (Boolean.FALSE.equals(upperResult) || Boolean.FALSE.equals(downResult)) {
                    continue;
                }
                DefectRecordDTO defectRecordDTO = getDefectRecordDTO(productFlowCodeRecordEntity, defectEntity.getDefectId());
                recordWorkOrderUnqualifiedService.saveRecord(defectRecordDTO, productFlowCodeRecordEntity.getReportBy());
            }
        }
    }

    /**
     * 构造不良记录实体
     * @param productFlowCodeRecordEntity
     * @param defectId
     * @return
     */
    private DefectRecordDTO getDefectRecordDTO(ProductFlowCodeRecordEntity productFlowCodeRecordEntity, Integer defectId) {
        DefectDefineEntity defectDefineEntity = defectDefineMapper.selectById(defectId);
        DefectDefineDTO defectDefineDTO = new DefectDefineDTO();
        BeanUtils.copyProperties(defectDefineEntity, defectDefineDTO);
        List<DefectDefineDTO> defectDefineDTOS = new ArrayList<>();
        defectDefineDTOS.add(defectDefineDTO);
        DefectRecordDTO defectRecordDTO = new DefectRecordDTO();
        defectRecordDTO.setDefines(defectDefineDTOS);
        defectRecordDTO.setBarCode(productFlowCodeRecordEntity.getProductFlowCode());
        defectRecordDTO.setWorkOrder(productFlowCodeRecordEntity.getRelationNumber());
        defectRecordDTO.setCodeRecordId(productFlowCodeRecordEntity.getId());
        defectRecordDTO.setFid(productFlowCodeRecordEntity.getFacId());
        defectRecordDTO.setQuantity(1);
        return defectRecordDTO;
    }


    /**
     * 获取工艺工序对应检测项
     *
     * @param craftProcedureId 工艺工序
     * @return 检测项列表
     */
    @Override
    public List<ProcedureInspectionConfigEntity> listProcedureInspection(Integer craftProcedureId, Integer fid) {
        List<ProcedureInspectionConfigEntity> procedureInspectionConfigEntityList = procedureInspectionConfigService.getProcedureInspectionConfigEntities(craftProcedureId);
        if (CollectionUtils.isEmpty(procedureInspectionConfigEntityList)||fid==null) {
            return procedureInspectionConfigEntityList;
        }
        //拿到工位设备指标
        LambdaQueryWrapper<DeviceEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(DeviceEntity::getFid, fid);
        lambdaQueryWrapper.last("limit 1");
        List<DeviceEntity> deviceEntityList = deviceService.list(lambdaQueryWrapper);
        if (CollectionUtils.isEmpty(deviceEntityList)) {
            return procedureInspectionConfigEntityList;
        }
        for (ProcedureInspectionConfigEntity procedureInspectionConfigEntity : procedureInspectionConfigEntityList) {
            IndicatorEntityDTO indicatorEntityDTO = null;
            for (DeviceEntity deviceEntity : deviceEntityList) {
                //如果多个设备都拿到该指标，拿第一个
                indicatorEntityDTO = commonService.getLatestTargetValue(deviceEntity.getDeviceId(), procedureInspectionConfigEntity.getInspectionCode());
                if (indicatorEntityDTO != null) {
                    break;
                }
            }
            if (indicatorEntityDTO != null) {
                procedureInspectionConfigEntity.setValue(indicatorEntityDTO.getDataVal());
                procedureInspectionConfigEntity.setCreateTime(DateUtil.parse(indicatorEntityDTO.getTime(), DateUtil.DATETIME_FORMAT));
            }

            //无值的话补上默认值
            if (StringUtils.isBlank(procedureInspectionConfigEntity.getValue())) {
                procedureInspectionConfigEntity.setValue(procedureInspectionConfigEntity.getDefaultValue());
            }

            if (StringUtils.isNotBlank(procedureInspectionConfigEntity.getValue())) {
                Boolean checkResult = checkItem(procedureInspectionConfigEntity);
                procedureInspectionConfigEntity.setResult(checkResult);
            }
        }
        return procedureInspectionConfigEntityList;
    }


    /**
     * 计算各个指标项的结果，符合或者不符合
     *
     * @param procedureInspectionConfigEntity 结果值
     */
    @Override
    public boolean checkItem(ProcedureInspectionConfigEntity procedureInspectionConfigEntity) {
        if (StringUtils.isBlank(procedureInspectionConfigEntity.getValue())) {
            return false;
        }
        //字符类型
        if (DataTypeEnum.CHARACTER_TYPE.getFieldEname().equals(procedureInspectionConfigEntity.getDataType())) {
            if (StringUtils.isBlank(procedureInspectionConfigEntity.getStandardValue())) {
                return false;
            }
            if (InspectComparatorEnum.CHAR_EQUAL.getCode().equals(procedureInspectionConfigEntity.getComparator())) {
                return procedureInspectionConfigEntity.getStandardValue().equals(procedureInspectionConfigEntity.getValue());
            }
            if (InspectComparatorEnum.NOT_EQUAL.getCode().equals(procedureInspectionConfigEntity.getComparator())) {
                return !procedureInspectionConfigEntity.getStandardValue().equals(procedureInspectionConfigEntity.getValue());
            }
            String[] standardValues = procedureInspectionConfigEntity.getStandardValue().split(Constant.SEP);
            String[] values = procedureInspectionConfigEntity.getValue().split(Constant.SEP);
            if (Arrays.asList(standardValues).containsAll(Arrays.asList(values)) && InspectComparatorEnum.IN.getCode().equals(procedureInspectionConfigEntity.getComparator())) {
                return true;
            }
            return !Arrays.asList(standardValues).containsAll(Arrays.asList(values)) && InspectComparatorEnum.NOT_IN.getCode().equals(procedureInspectionConfigEntity.getComparator());
            //boolean类型
        } else if (DataTypeEnum.BOOLEAN_TYPE.getFieldEname().equals(procedureInspectionConfigEntity.getDataType())) {
            if (StringUtils.isBlank(procedureInspectionConfigEntity.getStandardValue())) {
                return false;
            }
            String[] standardValues = procedureInspectionConfigEntity.getStandardValue().split(Constant.SEP);
            return Arrays.asList(standardValues).contains(procedureInspectionConfigEntity.getValue());
        } else if (DataTypeEnum.NUMERICAL_TYPE.getFieldEname().equals(procedureInspectionConfigEntity.getDataType())) {
            //数字类型
            // 判断是否是数字
            boolean number = MathUtil.isNumberBy(procedureInspectionConfigEntity.getValue());
            if (!number) {
                return false;
            }
            // 填写的检测值
            double itemValue = Double.parseDouble(procedureInspectionConfigEntity.getValue());
            double referenceValue = Double.parseDouble(procedureInspectionConfigEntity.getStandardValue());
            // 如果是大于
            if (InspectComparatorEnum.BIG.getCode().equals(procedureInspectionConfigEntity.getComparator())) {
                if (procedureInspectionConfigEntity.getDownLimit() == null) {
                    return false;
                }
                return itemValue > Double.parseDouble(procedureInspectionConfigEntity.getDownLimit());
            }
            // 如果是大于等于
            if (InspectComparatorEnum.BIG_EQUAL.getCode().equals(procedureInspectionConfigEntity.getComparator())) {
                if (procedureInspectionConfigEntity.getDownLimit() == null) {
                    return false;
                }
                return itemValue >= Double.parseDouble(procedureInspectionConfigEntity.getDownLimit());
            }
            // 如果是小于
            if (InspectComparatorEnum.SMALL.getCode().equals(procedureInspectionConfigEntity.getComparator())) {
                if (procedureInspectionConfigEntity.getUpperLimit() == null) {
                    return false;
                }
                return itemValue < Double.parseDouble(procedureInspectionConfigEntity.getUpperLimit());
            }
            // 如果是小于等于
            if (InspectComparatorEnum.SMALL_EQUAL.getCode().equals(procedureInspectionConfigEntity.getComparator())) {
                if (procedureInspectionConfigEntity.getUpperLimit() == null) {
                    return false;
                }
                return itemValue <= Double.parseDouble(procedureInspectionConfigEntity.getUpperLimit());
            }
            // 如果相等
            if (InspectComparatorEnum.DATA_CHAR_EQUAL.getCode().equals(procedureInspectionConfigEntity.getComparator())) {
                return itemValue == referenceValue;
            }
            // 如果在之间
            if (InspectComparatorEnum.DOUBLE_SYMBOL.getCode().equals(procedureInspectionConfigEntity.getComparator())) {
                boolean upper = MathUtil.isNumberBy(procedureInspectionConfigEntity.getUpperLimit());
                boolean lower = MathUtil.isNumberBy(procedureInspectionConfigEntity.getDownLimit());
                // 判断是否是数字
                if (upper && lower) {
                    double upperLimitValue = Double.parseDouble(procedureInspectionConfigEntity.getUpperLimit());
                    double downLimitValue = Double.parseDouble(procedureInspectionConfigEntity.getDownLimit());
                    return upperLimitValue >= itemValue && downLimitValue <= itemValue;
                }
                return Double.compare(itemValue, referenceValue) != 0;
            }
            // 如果在范围外
            if (InspectComparatorEnum.BETWEEN.getCode().equals(procedureInspectionConfigEntity.getComparator())) {
                boolean upper = MathUtil.isNumberBy(procedureInspectionConfigEntity.getUpperLimit());
                boolean lower = MathUtil.isNumberBy(procedureInspectionConfigEntity.getDownLimit());
                // 判断是否是数字
                if (upper && lower) {
                    double upperLimitValue = Double.parseDouble(procedureInspectionConfigEntity.getUpperLimit());
                    double downLimitValue = Double.parseDouble(procedureInspectionConfigEntity.getDownLimit());
                    return upperLimitValue < itemValue || downLimitValue > itemValue;
                }
                return false;
            }
            return false;
        }
        return false;
    }


    /**
     * 获取老化工位机的老化列表--当天完成的以及所有未完成的，顺序是达到时间未完成、未完成、完成
     *
     * @param fid 工位id
     * @return 记录列表
     */
    @Override
    public List<ProductFlowCodeRecordEntity> getOldList(Integer fid) {
        Date startDate = dictService.getRecordDate(new Date());
        LambdaQueryWrapper<ProductFlowCodeRecordEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProductFlowCodeRecordEntity::getFacId, fid).ne(ProductFlowCodeRecordEntity::getState, ProductFlowCodeRecordStateEnum.DISABLE.getCode()).isNotNull(ProductFlowCodeRecordEntity::getStartTime).
                and(o -> o.isNull(ProductFlowCodeRecordEntity::getEndTime).or(
                        e -> e.isNotNull(ProductFlowCodeRecordEntity::getEndTime).ge(ProductFlowCodeRecordEntity::getEndTime, startDate)
                ));
        List<ProductFlowCodeRecordEntity> list = productFlowCodeRecordService.list(lambdaQueryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return list;
        }
        Date date = new Date();
        for (ProductFlowCodeRecordEntity productFlowCodeRecordEntity : list) {
            productFlowCodeRecordEntity.setOldTime(DateUtil.getTimeDifference(productFlowCodeRecordEntity.getStartTime(), productFlowCodeRecordEntity.getEndTime() != null ? productFlowCodeRecordEntity.getEndTime() : date));
            //拿到老化标准时长
            Double standard = procedureInspectionConfigService.getStandard(fid, productFlowCodeRecordEntity.getRelationNumber(), AGING_DURATION);
            if (standard == null) {
                continue;
            }
            productFlowCodeRecordEntity.setStandard(DateUtil.getTimeDifference(standard));
            productFlowCodeRecordEntity.setIsOld(productFlowCodeRecordEntity.getStandard().compareTo(productFlowCodeRecordEntity.getOldTime()) <= 0);
            productFlowCodeRecordEntity.setSort(productFlowCodeRecordEntity.getStandard().compareTo(productFlowCodeRecordEntity.getOldTime()));
        }
        //排序
        list.sort(Comparator.comparing((ProductFlowCodeRecordEntity::getState)).thenComparing(ProductFlowCodeRecordEntity::getSort, Comparator.nullsFirst(Integer::compareTo)));
        return list;
    }

    /**
     * 提交老化记录
     *
     * @param id   记录id
     * @param date 时间
     */
    @Override
    public void submitOldRecord(Integer id, Date date) {
        LambdaUpdateWrapper<ProductFlowCodeRecordEntity> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(ProductFlowCodeRecordEntity::getId, id).set(ProductFlowCodeRecordEntity::getEndTime, date)
                .set(ProductFlowCodeRecordEntity::getState, ProductFlowCodeRecordStateEnum.COMPLETE.getCode());
        productFlowCodeRecordService.update(lambdaUpdateWrapper);
    }

    /**
     * 获取工单最近绑定10条流水码
     *
     * @param workOrderNumber 工单号
     * @return 流水码列表
     */
    @Override
    public List<ProductFlowCodeEntity> listProductFlowCodeByWorkOrder(String workOrderNumber) {
        LambdaQueryWrapper<ProductFlowCodeEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProductFlowCodeEntity::getRelationNumber, workOrderNumber)
                .orderByDesc(ProductFlowCodeEntity::getCreateTime)
                .orderByDesc(ProductFlowCodeEntity::getId)
                .last("limit 10");
        return productFlowCodeService.list(lambdaQueryWrapper);
    }

    /**
     * 撤销单品码绑定记录
     *
     * @param productFlowCode 条码号
     * @param fid             工位id
     * @param userName        用户名
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void repealBuildingCode(String productFlowCode, Integer fid, String userName) {
        LambdaQueryWrapper<ProductFlowCodeRecordEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProductFlowCodeRecordEntity::getProductFlowCode, productFlowCode)
                .ne(ProductFlowCodeRecordEntity::getFacId, fid);
        long count = productFlowCodeRecordService.count(lambdaQueryWrapper);
        if (count > 0) {
            throw new ResponseException("单品码已有后续过站记录不可撤销");
        }
        LambdaQueryWrapper<ProductFlowCodeEntity> lambdaQueryWrapper1 = new LambdaQueryWrapper<>();
        lambdaQueryWrapper1.eq(ProductFlowCodeEntity::getProductFlowCode, productFlowCode);
        ProductFlowCodeEntity productFlowCodeEntity = productFlowCodeService.getOne(lambdaQueryWrapper1);
        if (productFlowCodeEntity == null) {
            throw new ResponseException("无对应记录");
        }
        //删除流水码
        productFlowCodeService.removeById(productFlowCodeEntity.getId());
        //删除扫码记录
        LambdaUpdateWrapper<ProductFlowCodeRecordEntity> productFlowCodeRecordUpdateWrapper = new LambdaUpdateWrapper<>();
        productFlowCodeRecordUpdateWrapper.eq(ProductFlowCodeRecordEntity::getProductFlowCode, productFlowCode)
                .eq(ProductFlowCodeRecordEntity::getType, ProductFlowCodeTypeEnum.PRODUCT_FLOW_CODE.getCode());
        productFlowCodeRecordService.remove(productFlowCodeRecordUpdateWrapper);
        String nickname = null;
        if (StringUtils.isNotBlank(userName)) {
            SysUserEntity entity = userService.selectByUsername(userName);
            nickname = entity == null ? null : entity.getNickname();
        }
        //刷新报工
        CodeReportEntity codeReportEntity = codeReportService.lambdaQuery().eq(CodeReportEntity::getProductFlowCode,productFlowCode).last("limit 1").one();
        if(codeReportEntity!=null) {
            codeReportService.removeById(codeReportEntity.getId());
            productFlowCodeRecordService.reportLineMca(productFlowCodeEntity.getRelationNumber(), codeReportEntity.getReportTime()==null?new Date():codeReportEntity.getReportTime());
            //刷新投入数
            productFlowCodeRecordService.inputLine(productFlowCodeEntity.getRelationNumber(),  codeReportEntity.getInputTime()==null?new Date():codeReportEntity.getInputTime());

        }
        //加日志
        operationLogService.manualInsert(OperationLogEntity.builder()
                .module("流水码")
                .username(userName)
                .nickname(nickname)
                .type(OperationType.DELETE)
                .des("撤销了" + productFlowCode + "流水码绑定记录")
                .createTime(new Date())
                .build());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void confirmAndSubmit(List<DetectionSubmitDTO> submits, String username) {
        // 检验数据
        for (DetectionSubmitDTO submit : submits) {
            List<IndicatorEntityDTO> targetLatestValues = submit.getTargetLatestValues();
            for (IndicatorEntityDTO targetLatestValue : targetLatestValues) {
                if (StringUtils.isBlank(targetLatestValue.getDataVal())) {
                    log.error("指标值存在为空的情况,无法提交保存, targetName:{}", targetLatestValue.getTargetName());
                    throw new ResponseException("指标值存在为空的情况,无法提交保存");
                }
            }
        }
        Date nowDate = new Date();
        for (DetectionSubmitDTO submitDTO : submits) {
            // 指标数据
            List<IndicatorEntityDTO> targetLatestValues = submitDTO.getTargetLatestValues();
            Integer craftProcedureId = submitDTO.getCraftProcedureId();
            // 生产流水码
            String productFlowCode = submitDTO.getProductFlowCode();
            Integer fid = submitDTO.getFid();
            AddFlowCodeDTO addFlowCodeDTO = AddFlowCodeDTO.builder().productFlowCode(productFlowCode).fid(fid).userName(username)
                    .state(ProductFlowCodeRecordStateEnum.COMPLETE.getCode()).craftProcedureId(craftProcedureId).isEndTime(true).build();
            // 添加扫码记录
            ProductFlowCodeResultEntity stringObjectMap = this.addCodeRecordGeneral(addFlowCodeDTO);
            List<CodeTargetRecordEntity> batchSaves = new ArrayList<>();
            if (!CollectionUtils.isEmpty(targetLatestValues)) {
                for (IndicatorEntityDTO targetValue : targetLatestValues) {
                    CodeTargetRecordEntity codeTargetRecordEntity = CodeTargetRecordEntity.builder()
                            .codeRecordId(stringObjectMap.getCodeRecordId())
                            .targetName(targetValue.getTargetCname())
                            .targetEname(targetValue.getTargetName())
                            .value(targetValue.getDataVal())
                            .unit(targetValue.getUnit())
                            .reportBy(username)
                            .reportTime(nowDate)
                            .build();
                    batchSaves.add(codeTargetRecordEntity);
                }
                // 批量保存指标数据
                codeTargetRecordService.saveBatch(batchSaves);
            }
        }
    }


    /**
     * 批量添加条码记录
     *
     * @param addRecordListDTO 添加条码列表
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addCodeRecordList(AddRecordListDTO addRecordListDTO) {
        if (CollectionUtils.isEmpty(addRecordListDTO.getCodeList())) {
            throw new ResponseException("条码列表不能为空");
        }
        for (String code : addRecordListDTO.getCodeList()) {
            AddFlowCodeDTO addFlowCodeDTO = AddFlowCodeDTO.builder()
                    .productFlowCode(code)
                    .craftProcedureId(addRecordListDTO.getCraftProcedureId())
                    .fid(addRecordListDTO.getFid())
                    .isEndTime(true)
                    .state(ProductFlowCodeRecordStateEnum.COMPLETE.getCode())
                    .userName(addRecordListDTO.getUserName())
                    .supplementaryReport(addRecordListDTO.getSupplementaryReport())
                    .build();
            this.addCodeRecordGeneral(addFlowCodeDTO);
        }
    }


    /**
     * 批量返修 联用人工组装线定制逻辑  - 工单返工
     *
     * @param maintainBatchDTO
     * @return
     */
    @Override
    public Integer maintainBatch(MaintainBatchDTO maintainBatchDTO) {
        //返修流程
        CraftProcedureEntity craftProcedureEntity = craftProcedureService.getById(maintainBatchDTO.getCraftProcedureId());
        List<CraftProcedureEntity> craftProcedureEntityList = craftProcedureService.getProcedureList(craftProcedureEntity.getCraftId());
        int successCount = 0;
        Set<Integer> lastProcedures = this.getLastProcedures(craftProcedureEntity, craftProcedureEntityList, new HashMap<>());

        for (String productFlowCode : maintainBatchDTO.getFlowCodeList()) {
            Long count = productFlowCodeRecordService.lambdaQuery()
                    .eq(ProductFlowCodeRecordEntity::getProductFlowCode, productFlowCode)
                    .in(ProductFlowCodeRecordEntity::getProdureId, lastProcedures)
                    .eq(ProductFlowCodeRecordEntity::getState, ProductFlowCodeRecordStateEnum.COMPLETE.getCode())
                    .count();
            if (count > 0) {
                //增加维修返工记录
                addCodeRecordMaintain(productFlowCode, maintainBatchDTO.getFid(), maintainBatchDTO.getUserName(), MaintainTypeEnum.REPAIR.getCode(), maintainBatchDTO.getCraftProcedureId(), null, maintainBatchDTO.getWorkOrderNumber(),null);
                successCount += 1;
            }
        }
        try {
            // 刷新工单报工
            productFlowCodeRecordService.reportLineMca(maintainBatchDTO.getWorkOrderNumber(), null);
            //刷新工单不良数
            productFlowCodeRecordService.unqualifiedLine(maintainBatchDTO.getWorkOrderNumber(), null);
        }catch (Exception e){
            log.info("刷新工单报工、刷新工单不良数错误",e);
        }
        return successCount;
    }


    @Override
    public ScannerRespDTO scanner(String productFlowCode, Integer procedureId, String userName) {
        // 查询扫码报工配置
        FullPathCodeDTO dto = FullPathCodeDTO.builder()
                .fullPathCode(ConfigConstant.SCANNER_REPORT_CONFIG).build();
        Map<String, String> valueMap = businessConfigService.getValueMap(dto);
        List<Integer> codeTypes = JSON.parseArray(valueMap.get(ConfigValueConstant.CODE_TYPE), Integer.class);
        if (CollectionUtils.isEmpty(codeTypes)) {
            throw new ResponseException("未配置扫码报工配置的扫码类型");
        }
        // 查询流水码
        ProductFlowCodeEntity productFlowCodeEntity = productFlowCodeService.getByProductFlowCode(productFlowCode);
        if (productFlowCodeEntity == null) {
            throw new ResponseException("流水码" + productFlowCode + "无法解析");
        }
        Integer code = productFlowCodeEntity.getType();
        if (!codeTypes.contains(code)) {
            throw new ResponseException("不支持" + ProductFlowCodeTypeEnum.getNameByCode(code));
        }
        // 查询工单状态配置
        FullPathCodeDTO dto2 = FullPathCodeDTO.builder()
                .fullPathCode(ConfigConstant.WORK_ORDER_STATE_CONFIG).build();
        Map<String, String> valueMap2 = businessConfigService.getValueMap(dto2);
        Boolean autoInvestment = JSON.parseObject(valueMap2.get(ConfigValueConstant.AUTO_INVESTMENT), Boolean.class);
        if (ProductFlowCodeTypeEnum.PRODUCT_FLOW_CODE.getCode() == code) {
            // 查询工单信息
            LambdaQueryWrapper<WorkOrderEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(WorkOrderEntity::getWorkOrderNumber, productFlowCodeEntity.getRelationNumber());
            // 有配权限才进行数据隔离
            Boolean hasPermission = isolationService.dataIsolation(userName, wrapper);
            if (!hasPermission) {
                throw new ResponseException(RespCodeEnum.NO_WORK_CENTER_PERMISSION);
            }
            WorkOrderEntity workOrderEntity = workOrderService.getOne(wrapper);
            if (workOrderEntity == null) {
                throw new ResponseException("条码对应工单不存在或无该工单权限");
            }
            if (StringUtils.isBlank(workOrderEntity.getProductOrderNumber())) {
                throw new ResponseException("工单没绑定生产订单");
            }
            WorkCenterEntity workCenterEntity = workCenterService.getById(workOrderEntity.getWorkCenterId());
            // 查询工作中心type
            workOrderEntity.setWorkCenterType(workCenterEntity == null ? null : workCenterEntity.getType());
            LambdaQueryWrapper<WorkOrderProcedureRelationEntity> relationQueryWrapper = new LambdaQueryWrapper<>();
            relationQueryWrapper.eq(WorkOrderProcedureRelationEntity::getWorkOrderId, workOrderEntity.getWorkOrderId())
                    .eq(procedureId != null, WorkOrderProcedureRelationEntity::getProcedureId, procedureId)
                    .orderByAsc(WorkOrderProcedureRelationEntity::getCraftProcedureId)
                    .orderByAsc(WorkOrderProcedureRelationEntity::getId)
                    .last("limit 1");
            WorkOrderProcedureRelationEntity workOrderProcedureRelationEntity = workOrderProcedureRelationService.getOne(relationQueryWrapper);
            if (workOrderProcedureRelationEntity == null) {
                throw new ResponseException(RespCodeEnum.WORK_ORDER_BIND_PROCESS_NOT_FOUND);
            }
            workOrderEntity.setCraftProcedureId(workOrderProcedureRelationEntity.getCraftProcedureId());
            return ScannerRespDTO.builder().workOrderEntity(workOrderEntity).autoInvestment(autoInvestment)
                    .workOrderEntities(Stream.of(workOrderEntity).collect(Collectors.toList()))
                    .build();
        } else if (ProductFlowCodeTypeEnum.ORDER_PRODUCT_CODE.getCode() == code) {
            // 查询订单信息
            ProductOrderEntity orderEntity = extProductOrderInterface.selectProductOrderByNumber(ProductOrderDetailDTO.builder().productOrderNumber(productFlowCodeEntity.getRelationNumber()).build());
            if (orderEntity == null) {
                throw new ResponseException("条码对应生产订单不存在");
            }
            // 订单模式
            List<CraftProcedureEntity> craftProcedureEntities = getCraftProcedureListByProductOrderNumber(orderEntity.getProductOrderNumber());
            ScannerRespDTO respDTO = ScannerRespDTO.builder().craftProcedureEntities(craftProcedureEntities).autoInvestment(autoInvestment).build();
            if (procedureId == null) {
                return respDTO;
            }
            // 工序模式，根据procedureId过滤出符合条件的工序列表，获取工序列表下所有非空的工单列表，进行扁平化处理合并成一个List列表，再根据工单ID进行去重
            List<WorkOrderEntity> collect = craftProcedureEntities
                    .stream().filter(o -> procedureId.equals(o.getProcedureId()))
                    .map(CraftProcedureEntity::getWorkOrderEntities)
                    .filter(w -> !CollectionUtils.isEmpty(w))
                    .flatMap(Collection::stream)
                    .collect(Collectors.collectingAndThen(Collectors.toCollection(() ->
                            new TreeSet<>(Comparator.comparing(WorkOrderEntity::getWorkOrderId))), ArrayList::new));
            if (CollectionUtils.isEmpty(collect)) {
                throw new ResponseException("找不到对应的生产订单信息");
            }
            respDTO.setWorkOrderEntities(collect);
            return respDTO;
        } else {
            throw new ResponseException("不支持" + ProductFlowCodeTypeEnum.getNameByCode(code));
        }
    }

    /**
     * 通过生产订单号查询工艺工序列表
     */
    private List<CraftProcedureEntity> getCraftProcedureListByProductOrderNumber(String productOrderNumber) {
        // 获取生产订单关联的生产工单
        List<WorkOrderEntity> workOrders = orderWorkOrderService.listWorkOrderByOrderNumber(productOrderNumber);
        if (CollectionUtils.isEmpty(workOrders)) {
            return new ArrayList<>();
        }
        // 取第一个工单关联的工艺
        Integer craftId = workOrders.get(0).getCraftId();
        if (Objects.isNull(craftId)) {
            return new ArrayList<>();
        }
        // 获取工艺关联的工序列表
        List<CraftProcedureEntity> craftProcedureEntities = craftProcedureService.getProcedureList(craftId);
        // 查询工序关联的所有工单
        List<Integer> craftProcedureIds = craftProcedureEntities.stream().map(CraftProcedureEntity::getId).collect(Collectors.toList());
        List<WorkOrderProcedureRelationEntity> relationList = workOrderProcedureRelationService.lambdaQuery().in(WorkOrderProcedureRelationEntity::getCraftProcedureId, craftProcedureIds).list();

        Map<Integer, List<WorkOrderProcedureRelationEntity>> craftProcedureIdRelationsMap = relationList.stream().collect(Collectors.groupingBy(WorkOrderProcedureRelationEntity::getCraftProcedureId));
        // 查询所有工单列表
        List<Integer> workOrderIds = relationList.stream().map(WorkOrderProcedureRelationEntity::getWorkOrderId).collect(Collectors.toList());
        List<WorkOrderEntity> allWorkOrderEntity = workOrderService.lambdaQuery().in(WorkOrderEntity::getWorkOrderId, workOrderIds).list();

        //生产工单的报工记录查询
        ReporterRecordDTO reporterRecordSelectDTO = ReporterRecordDTO.builder().productOrderNumber(productOrderNumber).build();
        Page<ReporterRecordVO> pageData = reportLineService.getReporterRecord(reporterRecordSelectDTO);
        // 按工单分组
        Map<Integer, List<ReporterRecordVO>> workOrderIdReportRecordsMap = pageData.getRecords().stream().collect(Collectors.groupingBy(ReporterRecordVO::getWorkOrderId));

        for (CraftProcedureEntity craftProcedureEntity : craftProcedureEntities) {
            List<WorkOrderProcedureRelationEntity> tempRelationList = craftProcedureIdRelationsMap.get(craftProcedureEntity.getId());
            if (CollectionUtils.isEmpty(tempRelationList)) {
                // 该工艺工序没有绑定工单
                continue;
            }
            Set<Integer> tempWorkOrderIds = tempRelationList.stream().map(WorkOrderProcedureRelationEntity::getWorkOrderId).collect(Collectors.toSet());
            List<WorkOrderEntity> tempWorkOrders = allWorkOrderEntity.stream()
                    .filter(workOrderEntity -> tempWorkOrderIds.contains(workOrderEntity.getWorkOrderId()) && StringUtils.isNotBlank(workOrderEntity.getProductOrderNumber())
                            && workOrderEntity.getProductOrderNumber().contains(productOrderNumber))
                    .collect(Collectors.toList());
            double finishSum = tempWorkOrders.stream().filter(o -> o.getFinishCount() != null).mapToDouble(WorkOrderEntity::getFinishCount).sum();
            double plannedWorkingHourSum = tempWorkOrders.stream().filter(o -> o.getPlannedWorkingHours() != null).mapToDouble(WorkOrderEntity::getPlannedWorkingHours).sum();
            double actualWorkingHourSum = tempWorkOrders.stream().filter(o -> o.getActualWorkingHours() != null).mapToDouble(WorkOrderEntity::getActualWorkingHours).sum();
            double planQuantitySum = tempWorkOrders.stream().filter(o -> o.getPlanQuantity() != null).mapToDouble(WorkOrderEntity::getPlanQuantity).sum();
            //不合格数
            double unqualifiedSum = tempWorkOrders.stream().filter(o -> o.getUnqualified() != null).mapToDouble(WorkOrderEntity::getUnqualified).sum();
            //不良描述、报工照片
            String defectDesc = null;
            List<String> urls = new ArrayList<>();
            for (WorkOrderEntity workOrder : tempWorkOrders) {
                List<ReporterRecordVO> reportRecords = workOrderIdReportRecordsMap.getOrDefault(workOrder.getWorkOrderId(), new ArrayList<>());
                //不良描述、报工照片
                defectDesc = reportRecords.stream().map(ReporterRecordVO::getDefectDesc).filter(StringUtils::isNotBlank).collect(Collectors.joining(Constants.SEP));
                for (ReporterRecordVO reportRecord : reportRecords) {
                    urls.addAll(Arrays.asList(reportRecord.getPicUrls()));
                }
            }
            craftProcedureEntity.setUnqualifiedSum(unqualifiedSum);
            craftProcedureEntity.setDefectDesc(defectDesc);
            craftProcedureEntity.setPicUrls(urls);
            craftProcedureEntity.setFinishCountSum(finishSum);
            craftProcedureEntity.setActualWorkingHourSum(actualWorkingHourSum);
            craftProcedureEntity.setPlannedWorkingHourSum(plannedWorkingHourSum);
            craftProcedureEntity.setPlanQuantitySum(planQuantitySum);
            for (WorkOrderEntity workOrderEntity : tempWorkOrders) {
                workOrderEntity.setStateName(WorkOrderStateEnum.getNameByCode(workOrderEntity.getState()));
                // 如果开启作业工单特性 设置工单下的作业工单列表
                if (workPropertise.getIsOpenOperationOrder()) {
                    List<OperationOrderDTO> operationOrder = JacksonUtil.getResponseArray(assignmentInterface.getOperationOrder(workOrderEntity.getWorkOrderNumber()), OperationOrderDTO.class);
                    workOrderEntity.setOperationOrderDTOS(operationOrder);
                }
                workOrderEntity.setCraftProcedureId(craftProcedureEntity.getId());
                WorkCenterEntity workCenterEntity = workCenterService.getById(workOrderEntity.getWorkCenterId());
                // 查询工作中心type
                workOrderEntity.setWorkCenterType(workCenterEntity == null ? null : workCenterEntity.getType());
            }
            craftProcedureEntity.setWorkOrderEntities(tempWorkOrders);
            List<ProductOrderReportRemarkEntity> list = extProductOrderInterface.getReportRemarkListByProductOrderNumber(ProductOrderDetailDTO.builder().productOrderNumber(productOrderNumber).build());
            craftProcedureEntity.setProductOrderReportRemarkEntities(list);
        }
        return craftProcedureEntities;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void scannerReport(ScannerReportDTO dto) {
        WorkOrderEntity reqEntity = dto.getWorkOrderEntity();
        if (reqEntity == null) {
            throw new ResponseException("工单不能为空");
        }
        FullPathCodeDTO configDto = FullPathCodeDTO.builder()
                .fullPathCode(ConfigConstant.WORK_ORDER_STATE_CONFIG).build();
        Map<String, String> valueMap = businessConfigService.getValueMap(configDto);
        Boolean autoInvestment = JSON.parseObject(valueMap.get(ConfigValueConstant.AUTO_INVESTMENT), Boolean.class);
        WorkOrderEntity workOrderEntity = workOrderService.getById(reqEntity.getWorkOrderId());
        if (Objects.isNull(workOrderEntity)) {
            throw new ResponseException(RespCodeEnum.WORK_ORDER_ID_SELECT_FAIL.fmtDes(reqEntity.getWorkOrderId()));
        }
        // 报工时，读取产品检验控制配置，工艺工序的检验方案控制配置（检验对应检验类型的送检单，合格才能通过）
        CheckInspectSchemeDTO schemeDTO = CheckInspectSchemeDTO.builder()
                .workOrderEntity(workOrderEntity)
                .materialInspectMethodCode(MaterialInspectMethodEnum.PRODUCT_ORDER_BY_ONE_OF_WORK_ORDERS_REPORT.getCode())
                .craftProcedureInspectMethodCode(CraftProcedureInspectMethodEnum.WORK_ORDER_REPORT.getCode())
                .inspectTriggerCodes(Arrays.asList(InspectTriggerConditionEnum.CHECK_BY_EVERY_REPORT.getCode(), InspectTriggerConditionEnum.CHECK_BY_FIRST_REPORT.getCode(), InspectTriggerConditionEnum.SHIFT_CHANGE_CHECK_BY_REPORT.getCode(), InspectTriggerConditionEnum.CROSS_NEW_DATE_CHECK_BY_REPORT.getCode()))
                .abnormalBehavior(Constant.WORK_ORDER_REPORT)
                .build();
        materialInspectMethodService.checkInspectSchemeController(schemeDTO);

        Integer state = workOrderEntity.getState();
        String workOrderNumber = workOrderEntity.getWorkOrderNumber();
        // 自动投产，开启配置并且工单状态不是投产
        if (!WorkOrderStateEnum.INVESTMENT.getCode().equals(state)) {
            if (Boolean.TRUE.equals(autoInvestment)) {
                if (!WorkOrderStateEnum.RELEASED.getCode().equals(state) && !WorkOrderStateEnum.HANG_UP.getCode().equals(state)) {
                    throw new ResponseException("工单状态为" + WorkOrderStateEnum.getNameByCode(state) + "，不能自动投产");
                }
                // 设置生产基本单元
                if (dto.getIsSetProductionUnit() != null && dto.getIsSetProductionUnit()) {
                    workOrderService.updateLineById(reqEntity, dto.getUserName());
                }
                productionLineService.reportAction(ReportDTO.builder().workOrder(workOrderNumber).action(Constant.START).build());
            } else {
                throw new ResponseException("工单不是投产状态，不能报工");
            }
        }
        AddFlowCodeDTO addFlowCodeDTO = AddFlowCodeDTO.builder()
                .productFlowCode(dto.getProductFlowCode())
                .userName(dto.getUserName())
                .state(ProductFlowCodeRecordStateEnum.COMPLETE.getCode())
                .craftProcedureId(reqEntity.getCraftProcedureId())
                .workOrderNumber(workOrderNumber)
                .autoAddCode(false)
                .isEndTime(true)
                .needFid(false)
                .isInput(false)
                .lineId(workOrderEntity.getLineId())
                .build();
        if (ScannerQualityStateEnum.QUALIFIED.getCode().equals(dto.getQualityState())) {
            // 添加扫码记录并报工
            addFlowCodeDTO.setIsReport(true);
            addFlowCodeDTO.setIsUnqualified(false);
            this.addCodeRecord(addFlowCodeDTO);
        } else {
            addFlowCodeDTO.setIsReport(false);
            addFlowCodeDTO.setIsUnqualified(true);
            this.addCodeRecordQuality(addFlowCodeDTO);
        }

        Boolean autoFinished = JSON.parseObject(valueMap.get(ConfigValueConstant.AUTO_FINISHED), Boolean.class);
        if (Boolean.FALSE.equals(autoFinished)) {
            return;
        }
        // 自动完成，开启配置并且计划数等于完成数
        Double finishCount = productFlowCodeRecordService.getReportCount(workOrderNumber, Constant.REPORT_LINE);
        if (workOrderEntity.getPlanQuantity().equals(finishCount)) {
            productionLineService.reportAction(ReportDTO.builder().workOrder(workOrderNumber).action(Constant.END).build());
        }
    }

    /**
     * 获取老化检验工位机的老化列表
     *
     * @param productFlowCode
     * @param isAutoCommit
     * @return
     */
    @Override
    public Map<String, Object> listOldCheckRecord(String productFlowCode, Boolean isAutoCommit) {
        Map<String, Object> resultMap = new HashMap<>();
        Date date = new Date();
        LambdaQueryWrapper<ProductFlowCodeRecordEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProductFlowCodeRecordEntity::getState, ProductFlowCodeRecordStateEnum.CREAT.getCode()).eq(ProductFlowCodeRecordEntity::getProductFlowCode, productFlowCode).isNotNull(ProductFlowCodeRecordEntity::getStartTime).isNull(ProductFlowCodeRecordEntity::getEndTime);
        List<ProductFlowCodeRecordEntity> list = productFlowCodeRecordService.list(lambdaQueryWrapper);
        for (ProductFlowCodeRecordEntity productFlowCodeRecordEntity : list) {
            productFlowCodeRecordEntity.setOldTime(DateUtil.getTimeDifference(productFlowCodeRecordEntity.getStartTime(), productFlowCodeRecordEntity.getEndTime() != null ? productFlowCodeRecordEntity.getEndTime() : new Date()));
            //拿到老化标准时长
            Double standard = procedureInspectionConfigService.getStandard(productFlowCodeRecordEntity.getFacId(), productFlowCodeRecordEntity.getRelationNumber(), AGING_DURATION);
            if (standard == null) {
                continue;
            }
            productFlowCodeRecordEntity.setStandard(DateUtil.getTimeDifference(standard));
            Boolean isOld = productFlowCodeRecordEntity.getStandard().compareTo(productFlowCodeRecordEntity.getOldTime()) <= 0;
            productFlowCodeRecordEntity.setIsOld(isOld);
            //如果达到老化时间并且设置了自动提交
            if (isOld && isAutoCommit && productFlowCodeRecordEntity.getEndTime() == null) {
                submitOldRecord(productFlowCodeRecordEntity.getId(), date);
                productFlowCodeRecordEntity.setEndTime(date);
                productFlowCodeRecordEntity.setState(ProductFlowCodeRecordStateEnum.COMPLETE.getCode());
            }
        }
        //拿到工单信息
        ProductFlowCodeEntity productFlowCodeEntity = productFlowCodeService.getByProductFlowCode(productFlowCode);
        resultMap.put("workOrderNumber", productFlowCodeEntity.getRelationNumber());
        resultMap.put("productFlowCode", productFlowCode);
        resultMap.put("oldList", list);
        return resultMap;
    }

    /**
     * 老化检验工位机-提交
     *
     * @param generalSubmitDTO
     */
    @Override
    public void oldCheckSubmit(GeneralSubmitDTO generalSubmitDTO, String userName) {
        //默认自动添加条码
        AddFlowCodeDTO addFlowCodeDTO = AddFlowCodeDTO.builder()
                .productFlowCode(generalSubmitDTO.getProductFlowCode())
                .fid(generalSubmitDTO.getFid())
                .userName(userName)
                .state(ProductFlowCodeRecordStateEnum.COMPLETE.getCode())
                .workOrderNumber(generalSubmitDTO.getWorkOrderNumber())
                .craftProcedureId(generalSubmitDTO.getCraftProcedureId())
                .isCheck(generalSubmitDTO.getIsCheck())
                .autoAddCode(true)
                .deviceId(generalSubmitDTO.getDeviceId())
                .isEndTime(true).build();
        //生成扫码记录
        ProductFlowCodeRecordEntity codeRecordEntity = addCodeRecord(addFlowCodeDTO);
        //提交相关信息
        generalSubmitDTO.setCodeRecordId(codeRecordEntity.getId());
        generalSubmit(generalSubmitDTO);
    }

    @Override
    public String getWorkOrderNumber(AddFlowCodeDTO addFlowCodeDTO) {
        //反向添加流水码
        ProductFlowCodeEntity productFlowCodeEntity = productFlowCodeService.getByProductFlowCode(addFlowCodeDTO.getProductFlowCode());
        if (productFlowCodeEntity == null) {
            if (addFlowCodeDTO.getAutoAddCode() != null && addFlowCodeDTO.getAutoAddCode() && StringUtils.isNotBlank(addFlowCodeDTO.getWorkOrderNumber())) {
                productFlowCodeEntity = this.addProductFlowCode(addFlowCodeDTO.getProductFlowCode(), addFlowCodeDTO.getWorkOrderNumber(),null, addFlowCodeDTO.getUserName());
            } else {
                throw new ResponseException("流水码" + addFlowCodeDTO.getProductFlowCode() + "无法解析");
            }
        }
        WorkOrderEntity workOrderEntity = getWorkOrderByCode(productFlowCodeEntity.getRelationType(),false,
                null, productFlowCodeEntity.getRelationNumber(), addFlowCodeDTO.getWorkOrderNumber(),addFlowCodeDTO.getFid());
        if (workOrderEntity == null) {
            throw new ResponseException("条码对应工单不存在");
        }
        if (StringUtils.isNotBlank(addFlowCodeDTO.getWorkOrderNumber()) && !workOrderEntity.getWorkOrderNumber().equals(addFlowCodeDTO.getWorkOrderNumber())) {
            throw new ResponseException("条码对应工单与所选工单不一致");
        }
        return workOrderEntity.getWorkOrderNumber();
    }


    /**
     * 获取扫码工单信息
     *
     * @param fid
     * @param workOrderNumber
     * @param craftProcedureId
     * @return
     */
    @Override
    public FidNewestResultDTO getWorkOrderInformation(Integer fid, String workOrderNumber, Integer craftProcedureId) {
        FacilitiesEntity facilitiesEntity = facilitiesService.getById(fid);
        if (facilitiesEntity == null) {
            return null;
        }
        Integer productionLineId = facilitiesEntity.getProductionLineId();
        WorkOrderEntity workOrderEntity;
        if (StringUtils.isNotBlank(workOrderNumber)) {
            workOrderEntity = workOrderService.getSimpleWorkOrderByNumber(workOrderNumber);
        } else {
            LambdaQueryWrapper<WorkOrderEntity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(WorkOrderEntity::getState, WorkOrderStateEnum.INVESTMENT.getCode())
                    .eq(WorkOrderEntity::getLineId, productionLineId)
                    .orderByDesc(WorkOrderEntity::getStateChangeTime)
                    .orderByDesc(WorkOrderEntity::getWorkOrderId)
                    .last("limit 1");
            workOrderEntity = workOrderService.getOne(queryWrapper);
        }
        if (workOrderEntity == null) {
            return null;
        }
        workOrderEntity.setMaterialFields(materialService.getEntityByCodeAndSkuId(workOrderEntity.getMaterialCode(), workOrderEntity.getSkuId()));

        FidNewestResultDTO fidNewestResultDTO = new FidNewestResultDTO();
        if (workOrderEntity != null) {
            fidNewestResultDTO.setPlanQuantity(workOrderEntity.getPlanQuantity());
            fidNewestResultDTO.setInputTotal(workOrderEntity.getInputTotal());
            fidNewestResultDTO.setFinishCount(workOrderEntity.getFinishCount());
            fidNewestResultDTO.setMaterialCode(workOrderEntity.getMaterialCode());
            fidNewestResultDTO.setMaterialName(workOrderEntity.getMaterialFields().getName());
            fidNewestResultDTO.setStandard(workOrderEntity.getMaterialFields().getStandard());
            fidNewestResultDTO.setWorkOrderNumber(workOrderEntity.getWorkOrderNumber());
            fidNewestResultDTO.setWorkOrderName(workOrderEntity.getWorkOrderName());
            fidNewestResultDTO.setFcode(facilitiesEntity.getFcode());
            fidNewestResultDTO.setFname(facilitiesEntity.getFname());
            fidNewestResultDTO.setLineCode(workOrderEntity.getLineCode());
            fidNewestResultDTO.setLineName(workOrderEntity.getLineName());
            fidNewestResultDTO.setProductOrderNumber(workOrderEntity.getProductOrderNumber());
            fidNewestResultDTO.setSaleOrderNumber(workOrderEntity.getSaleOrderNumber());

            //工序信息
            //拿到工单该工序过站数
            LambdaQueryWrapper<ProductFlowCodeRecordEntity> productFlowCodeRecordWrapper = new LambdaQueryWrapper<>();
            productFlowCodeRecordWrapper.eq(ProductFlowCodeRecordEntity::getRelationNumber, workOrderNumber);
            productFlowCodeRecordWrapper.eq(ProductFlowCodeRecordEntity::getFacId, fid);
            //不传工序找工位过站数
            WrapperUtil.eq(productFlowCodeRecordWrapper, ProductFlowCodeRecordEntity::getProdureId, craftProcedureId);
            productFlowCodeRecordWrapper.eq(ProductFlowCodeRecordEntity::getState, ProductFlowCodeRecordStateEnum.COMPLETE.getCode());
            Long count = productFlowCodeRecordService.count(productFlowCodeRecordWrapper);
            fidNewestResultDTO.setPassCount(count.intValue());
            //维修数量
            LambdaQueryWrapper<ProductFlowCodeRecordEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(ProductFlowCodeRecordEntity::getRelationNumber, workOrderNumber).eq(ProductFlowCodeRecordEntity::getFacId, fid)
                    .eq(ProductFlowCodeRecordEntity::getIsMaintain, true);
            Long maintainCount = productFlowCodeRecordService.count(lambdaQueryWrapper);
            fidNewestResultDTO.setMaintainCount(maintainCount.intValue());

            CraftProcedureEntity craftProcedureEntity = craftProcedureService.getById(craftProcedureId);
            if (craftProcedureEntity != null) {
                fidNewestResultDTO.setProcedureName(craftProcedureEntity.getProcedureName());
                fidNewestResultDTO.setAlias(craftProcedureEntity.getAlias());
            }
        }
        return fidNewestResultDTO;
    }

}
