package com.yelink.dfs.service.impl.common.config;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yelink.dfs.common.UserAuthenService;
import com.yelink.dfs.mapper.common.config.OrderPushDownRecordMapper;
import com.yelink.dfs.provider.constant.KafkaMessageTypeEnum;
import com.yelink.dfs.service.common.config.OrderPushDownConfigService;
import com.yelink.dfs.service.common.config.OrderPushDownRecordService;
import com.yelink.dfs.service.user.SysUserService;
import com.yelink.dfscommon.constant.Constants;
import com.yelink.dfscommon.constant.dfs.DfsEventTypeEnum;
import com.yelink.dfscommon.dto.pushdown.item.PushDownRecordDeleteDTO;
import com.yelink.dfscommon.dto.pushdown.item.PushDownRecordSelectDTO;
import com.yelink.dfscommon.entity.dfs.OrderPushDownConfigEntity;
import com.yelink.dfscommon.entity.dfs.OrderPushDownRecordEntity;
import com.yelink.dfscommon.provider.MessagePushToKafkaService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class OrderPushDownRecordServiceImpl extends ServiceImpl<OrderPushDownRecordMapper, OrderPushDownRecordEntity> implements OrderPushDownRecordService {
    @Resource
    private SysUserService userService;
    @Resource
    private UserAuthenService authService;
    @Resource
    @Lazy
    private OrderPushDownConfigService orderPushDownConfigService;
    @Resource
    private MessagePushToKafkaService messagePushToKafkaService;

    @Override
    public Page<OrderPushDownRecordEntity> getRecordList(PushDownRecordSelectDTO selectDTO) {
        LambdaQueryWrapper<OrderPushDownRecordEntity> w = Wrappers.lambdaQuery(OrderPushDownRecordEntity.class);
        Page<OrderPushDownRecordEntity> page = this.page(selectDTO.buildPage(), conditionQuery(selectDTO, w));
        showName(page.getRecords());
        return page;
    }

    /**
     * 条件查询
     */
    private static LambdaQueryWrapper<OrderPushDownRecordEntity> conditionQuery(PushDownRecordSelectDTO selectDTO, LambdaQueryWrapper<OrderPushDownRecordEntity> lambda) {
        lambda
                .eq(OrderPushDownRecordEntity::getSourceOrderType, selectDTO.getSourceOrderType())
                .eq(StringUtils.isNotBlank(selectDTO.getSourceOrderNumber()), OrderPushDownRecordEntity::getSourceOrderNumber, selectDTO.getSourceOrderNumber())
                .eq(StringUtils.isNotBlank(selectDTO.getSourceOrderBatch()), OrderPushDownRecordEntity::getSourceOrderBatch, selectDTO.getSourceOrderBatch())
                .eq(StringUtils.isNotBlank(selectDTO.getMaterialCode()), OrderPushDownRecordEntity::getMaterialCode, selectDTO.getMaterialCode())
                .eq(StringUtils.isNotBlank(selectDTO.getTargetOrderType()), OrderPushDownRecordEntity::getTargetOrderType, selectDTO.getTargetOrderType())
                .eq(selectDTO.getSourceSkuId() != null, OrderPushDownRecordEntity::getSourceSkuId, selectDTO.getSourceSkuId())
                .like(StringUtils.isNotBlank(selectDTO.getTargetOrderNumber()), OrderPushDownRecordEntity::getTargetOrderNumber, selectDTO.getTargetOrderNumber())
                .in(CollectionUtils.isNotEmpty(selectDTO.getTargetOrderNumbers()), OrderPushDownRecordEntity::getTargetOrderNumber, selectDTO.getTargetOrderNumbers())
                .in(CollectionUtils.isNotEmpty(selectDTO.getSourceOrderNumbers()), OrderPushDownRecordEntity::getSourceOrderNumber, selectDTO.getSourceOrderNumbers())
                .in(Objects.nonNull(selectDTO.getSourceOrderMaterialId()), OrderPushDownRecordEntity::getSourceOrderMaterialId, selectDTO.getSourceOrderMaterialId())
                .in(CollectionUtils.isNotEmpty(selectDTO.getSourceOrderMaterialIds()), OrderPushDownRecordEntity::getSourceOrderMaterialId, selectDTO.getSourceOrderMaterialIds());
        // 根据目标单据类型和时间排序
        lambda.orderByAsc(OrderPushDownRecordEntity::getTargetOrderType)
                .orderByDesc(OrderPushDownRecordEntity::getRecordDate)
                .orderByDesc(OrderPushDownRecordEntity::getId);
        return lambda;
    }

    @Override
    public void batchAddRecord(List<OrderPushDownRecordEntity> entities) {
        if (CollectionUtils.isEmpty(entities)) {
            return;
        }
        Date date = new Date();
        String username = StringUtils.isBlank(authService.getUsername()) ? entities.get(0).getCreateBy() : authService.getUsername();
        for (OrderPushDownRecordEntity entity : entities) {
            entity.setCreateBy(username);
            entity.setRecordDate(date);
        }
        this.saveBatch(entities);
        // 推送消息
        messagePushToKafkaService.pushNewMessage(entities, Constants.KAFKA_VALUE_CHAIN_TOPIC, DfsEventTypeEnum.ORDER_PUSH_DOWN_RECORD_MESSAGE_ADD);
    }

    @Override
    public List<OrderPushDownRecordEntity> getSourceRecordList(PushDownRecordSelectDTO selectDTO) {
        QueryWrapper<OrderPushDownRecordEntity> qw = new QueryWrapper<>();
        // 根据pushDownCode字段去重，只获取源单据相关字段
        qw.select("distinct push_down_code", "source_order_batch", "push_down_quantity", "source_order_type", "source_order_number", "source_order_material_id", "material_code", "is_abnormal", "abnormal_remark");
        LambdaQueryWrapper<OrderPushDownRecordEntity> lambda = qw.lambda();
        LambdaQueryWrapper<OrderPushDownRecordEntity> w = conditionQuery(selectDTO, lambda);
        Page<OrderPushDownRecordEntity> page = this.page(selectDTO.buildPage(), w);
        List<OrderPushDownRecordEntity> records = page.getRecords();
        return records;
    }

    @Override
    public void deleteRecord(PushDownRecordDeleteDTO deleteDTO) {
        List<OrderPushDownRecordEntity> entities = this.lambdaQuery().eq(OrderPushDownRecordEntity::getTargetOrderType, deleteDTO.getTargetOrderType())
                .eq(OrderPushDownRecordEntity::getTargetOrderNumber, deleteDTO.getTargetOrderNumber())
                .eq(StringUtils.isNotBlank(deleteDTO.getTargetOrderMaterialCode()), OrderPushDownRecordEntity::getMaterialCode, deleteDTO.getTargetOrderMaterialCode())
                .eq(Objects.nonNull(deleteDTO.getTargetOrderMaterialId()), OrderPushDownRecordEntity::getTargetOrderMaterialId, deleteDTO.getTargetOrderMaterialId())
                .eq(StringUtils.isNotBlank(deleteDTO.getTargetOrderBatch()), OrderPushDownRecordEntity::getTargetOrderBatch, deleteDTO.getTargetOrderBatch())
                .list();
        this.removeByIds(entities);
        // 推送消息
        messagePushToKafkaService.pushNewMessage(entities, Constants.KAFKA_VALUE_CHAIN_TOPIC, DfsEventTypeEnum.ORDER_PUSH_DOWN_RECORD_MESSAGE_DELETE);
    }

    @Override
    public void batchUpdateRecord(List<OrderPushDownRecordEntity> entities) {
        if (CollectionUtils.isEmpty(entities)) {
            return;
        }
        for (OrderPushDownRecordEntity recordEntity : entities) {
            this.lambdaUpdate()
                    .eq(OrderPushDownRecordEntity::getTargetOrderType, recordEntity.getTargetOrderType())
                    .eq(OrderPushDownRecordEntity::getTargetOrderNumber, recordEntity.getTargetOrderNumber())
                    .eq(StringUtils.isNotBlank(recordEntity.getTargetOrderMaterialCode()), OrderPushDownRecordEntity::getTargetOrderMaterialCode, recordEntity.getTargetOrderMaterialCode())
                    .eq(Objects.nonNull(recordEntity.getTargetOrderMaterialId()), OrderPushDownRecordEntity::getTargetOrderMaterialId, recordEntity.getTargetOrderMaterialId())
                    .eq(StringUtils.isNotBlank(recordEntity.getTargetOrderBatch()), OrderPushDownRecordEntity::getTargetOrderBatch, recordEntity.getTargetOrderBatch())
                    .set(StringUtils.isNotBlank(recordEntity.getAbnormalRemark()), OrderPushDownRecordEntity::getIsAbnormal, true)
                    .set(StringUtils.isNotBlank(recordEntity.getAbnormalRemark()), OrderPushDownRecordEntity::getAbnormalRemark, recordEntity.getAbnormalRemark())
                    .update();
        }
        // 推送消息
        messagePushToKafkaService.pushNewMessage(entities, Constants.KAFKA_VALUE_CHAIN_TOPIC, DfsEventTypeEnum.ORDER_PUSH_DOWN_RECORD_MESSAGE_UPDATE);
    }

    /**
     * 展示名称
     */
    @Override
    public void showName(List<OrderPushDownRecordEntity> result) {
        if (CollectionUtils.isEmpty(result)) {
            return;
        }
        List<String> usernames = result.stream().map(OrderPushDownRecordEntity::getCreateBy)
                .filter(Objects::nonNull).distinct()
                .collect(Collectors.toList());
        Map<String, String> userNameNickMap = userService.getUserNameNickMap(usernames);
        // 查询目标单据类型名称
        List<String> targetOrderTypes = result.stream().map(OrderPushDownRecordEntity::getTargetOrderType)
                .filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<String, String> targetOrderTypeMap = orderPushDownConfigService.lambdaQuery()
                .in(OrderPushDownConfigEntity::getCode, targetOrderTypes).list()
                .stream().collect(Collectors.toMap(OrderPushDownConfigEntity::getCode, OrderPushDownConfigEntity::getName, (v1, v2) -> v1));
        result.forEach(e -> {
            e.setCreateByName(userNameNickMap.get(e.getCreateBy()));
            e.setTargetOrderTypeName(targetOrderTypeMap.get(e.getTargetOrderType()));
        });
    }
}

