package com.yelink.inner.listen.manager.impl.approve;


import com.yelink.dfs.constant.ValueChainKafkaMessageConstants;
import com.yelink.dfs.entity.approve.config.ApproveRecordEntity;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.open.v1.workOrder.dto.WorkOrderDetailDTO;
import com.yelink.dfs.service.order.WorkOrderService;
import com.yelink.dfscommon.constant.approve.config.ApprovalStatusEnum;
import com.yelink.dfscommon.constant.approve.config.ApproveModuleEnum;
import com.yelink.dfscommon.constant.dfs.order.WorkOrderStateEnum;
import com.yelink.inner.listen.manager.ApproveMessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Slf4j
@Service(ValueChainKafkaMessageConstants.WORK_ORDER_MSG_SERVICE)
public class WorkOrderMsgServiceImpl implements ApproveMessageService {

    @Resource
    private WorkOrderService workOrderService;

    @Async
    @Override
    public void dealApprove(ApproveRecordEntity recordEntity) {
        // 生效审批
        if (ApproveModuleEnum.WORK_ORDER.getReleasedCode().equals(recordEntity.getFullPathCode())) {
            WorkOrderDetailDTO detailDTO = new WorkOrderDetailDTO(recordEntity.getOrderNumber());
            WorkOrderEntity entity = workOrderService.getWorkOrderById(detailDTO);
            if (entity == null) {
                log.error("未找到对应的工单，工单号：{}", recordEntity.getOrderNumber());
                return;
            }
            if (!WorkOrderStateEnum.CREATED.getCode().equals(entity.getState()) || ApprovalStatusEnum.TO_BE_APPROVAL.getCode() != entity.getApprovalStatus()) {
                log.error("工单审批错误，不是创建待审批，工单号：{}，状态：{}，审批状态：{}", entity.getWorkOrderNumber(),
                        WorkOrderStateEnum.getNameByCode(entity.getState()), ApprovalStatusEnum.getNameByCode(entity.getApprovalStatus()));
                return;
            }
            Integer approvalStatus = recordEntity.getApprovalStatus();
            // 已通过
            boolean toApproved = ApprovalStatusEnum.APPROVED.getWechatCode().equals(approvalStatus);
            // 已驳回
            boolean toReject = ApprovalStatusEnum.REJECTED.getWechatCode().equals(approvalStatus);
            if (toApproved || toReject) {
                entity.setApprovalStatus(ApprovalStatusEnum.getCodeByWechatCode(approvalStatus));
                entity.setApprovalSuggestion(recordEntity.getApprovalSuggestion());
                entity.setActualApprover(recordEntity.getApprover());
                entity.setApprovalTime(recordEntity.getApprovalTime());
                // 单据审批更新
                workOrderService.orderApprove(entity, toApproved, false, null);
            }
        }
    }

}
