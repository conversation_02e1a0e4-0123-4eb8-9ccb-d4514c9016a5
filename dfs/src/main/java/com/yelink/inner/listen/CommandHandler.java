package com.yelink.inner.listen;

import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.inner.data.access.SensorIotSetInfo;
import com.yelink.inner.service.CommandStrategyContext;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @description: 下发命令数据监听
 * @author: shuang
 * @time: 2020/8/11
 */
@Slf4j
@Component
@AllArgsConstructor
public class CommandHandler {

    private CommandStrategyContext commandStrategyContext;

    /**
     * 下发命令
     *
     * @param sensorIotSetInfo
     */
    public ResponseData commandMessage(SensorIotSetInfo sensorIotSetInfo) {
        log.info("Command message is " + sensorIotSetInfo.toString());
        log.info("下发数据到达");
        //命令下发需要直接设置到终端
        return commandStrategyContext.sendCommand(sensorIotSetInfo);
    }
}
