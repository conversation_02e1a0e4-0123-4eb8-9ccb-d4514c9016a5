package com.yelink.inner.listen;

import com.alibaba.fastjson.JSON;
import com.yelink.dfs.controller.statement.DailyExportThreadManager;
import com.yelink.dfs.entity.statement.dto.CommRemindMessageDTO;
import com.yelink.dfs.service.common.config.BusinessConfigValueService;
import com.yelink.dfscommon.constant.dfs.config.ConfigConstant;
import com.yelink.inner.service.KafkaDeviceStatusHandler;
import com.yelink.inner.service.KafkaStrategyContext;
import com.yelink.inner.service.KafkaWebSocketPublisher;
import com.yelink.notice.service.SocketTransferService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.StopWatch;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description: kafka监听
 * @Author: zhengfu
 * @Date: 2020/10/14
 */
@Slf4j
@Component
@AllArgsConstructor
public class KafkaConsumer {

    private KafkaStrategyContext kafkaStrategyContext;
    private KafkaWebSocketPublisher kafkaWebSocketPublisher;
    private SocketTransferService socketTransferService;
    private DailyExportThreadManager dailyExportThreadManager;
    private KafkaDeviceStatusHandler kafkaDeviceStatusHandler;
    private BusinessConfigValueService businessConfigValueService;

    /**
     * 消费监听
     * iot如监听到有优先级的topic接收消息，会停止property_post_exchange,event_post_exchange的消息发送
     */
    @KafkaListener(topics = "#{'${kafka.topics}'.split(',')}")
    public void onMessage1(List<ConsumerRecord<?, ?>> record) {
        log.info("本次批量处理kafka消息数：{}", record.size());
        kafkaStrategyContext.converterData(record);
    }

    /**
     * 高优先级topic
     */
    @KafkaListener(topics = "high_priority_post")
    public void onMessageHigh(List<ConsumerRecord<?, ?>> record) {
        log.info("high_priority_post-本次批量处理kafka消息数：{}", record.size());
        kafkaStrategyContext.converterData(record);
    }

    /**
     * 中优先级topic
     */
    @KafkaListener(topics = "middle_priority_post")
    public void onMessageMiddle(List<ConsumerRecord<?, ?>> record) {
        log.info("middle_priority_post-本次批量处理kafka消息数：{}", record.size());
        StopWatch watch = StopWatch.createStarted();
        kafkaStrategyContext.converterData(record);
        log.info("批量处理kafka消息{}条用时：{}ms", record.size(), watch.getTime());
    }

    /**
     * 低优先级topic
     */
    @KafkaListener(topics = "low_priority_post")
    public void onMessageLow(List<ConsumerRecord<?, ?>> record) {
        log.info("low_priority_post-本次批量处理kafka消息数：{}", record.size());
        kafkaStrategyContext.converterData(record);
    }

    /**
     * 告警topic
     */
    @KafkaListener(topics = "alarm_priority_post")
    public void onMessageAlarm(List<ConsumerRecord<?, ?>> record) {
        log.info("alarm_priority_post-本次批量处理kafka消息数：{}", record.size());
        kafkaStrategyContext.converterData(record);
    }

    /**
     * websocket监听
     *
     * @see com.yelink.dfscommon.config.KafkaTopic#
     */
    @KafkaListener(topics = "#{kafkaTopic.getWebsocket()}", groupId = "#{'${kafka.websocket.consumer}'}")
    public void websocket(List<ConsumerRecord<?, ?>> record) {
        log.info("本次批量处理kafka消息数：{}", record.size());
        for (ConsumerRecord<?, ?> consumerRecord : record) {
            // 消费的哪个topic、partition的消息,打印出消息内容
            log.info("kafka websocket消息：" + consumerRecord.topic() + "-" + consumerRecord.partition() + "-" + consumerRecord.value());
            kafkaWebSocketPublisher.sendWebsocket(consumerRecord);
        }
    }

    /**
     * 转发服务websocket监听
     *
     * @see com.yelink.dfscommon.config.KafkaTopic#
     */
    @KafkaListener(topics = "#{kafkaTopic.getTransferSocket()}", groupId = "#{'${kafka.websocket.consumer}'}")
    public void transferSocket(List<ConsumerRecord<?, ?>> record) {
        log.info("本次批量处理kafka消息数：{}", record.size());
        for (ConsumerRecord<?, ?> consumerRecord : record) {
            // 消费的哪个topic、partition的消息,打印出消息内容
            log.info("kafka transfer websocket消息：" + consumerRecord.topic() + "-" + consumerRecord.partition() + "-" + consumerRecord.value());
            socketTransferService.sendWebsocket(consumerRecord.topic(), (String) consumerRecord.value());
        }
    }

    /**
     * 每个机器都会被监听消费, 用于处理双机环境下 无法保证某段逻辑一定会被执行 的此类问题
     *
     * @see com.yelink.dfscommon.config.KafkaTopic#
     */
    @KafkaListener(topics = "#{kafkaTopic.getForwardRemindMessage()}", groupId = "#{'${kafka.websocket.consumer}'}")
    public void forwardRemindMessage(List<ConsumerRecord<?, ?>> record) {
        log.info("本次批量处理kafka消息数：{}", record.size());
        for (ConsumerRecord<?, ?> consumerRecord : record) {
            // 消费的哪个topic、partition的消息,打印出消息内容
            log.info("kafka forward remind message消息：" + consumerRecord.topic() + "-" + consumerRecord.partition() + "-" + consumerRecord.value());

            CommRemindMessageDTO remindMassageDTO = JSON.parseObject((String) consumerRecord.value(), CommRemindMessageDTO.class);
            if (CommRemindMessageDTO.RemindMessageType.STOP_DAILY_EXPORT.equals(remindMassageDTO.getType())) {
                // stopDailyExport
                dailyExportThreadManager.stopExport(remindMassageDTO.getMessage());
            } else if (CommRemindMessageDTO.RemindMessageType.STOP_LINE_EXPORT.equals(remindMassageDTO.getType())) {
                // stopLineExport
                dailyExportThreadManager.lineExportStop();
            }
        }
    }

    /**
     * 该消息仅当设备状态改变的时候才会推送，而非定时推送的
     */
    @KafkaListener(topics = "deviceStatus")
    public void deviceStatusMessage(List<ConsumerRecord<?, ?>> record) {
        log.info("本次设备状态批量处理kafka消息数：{}", record.size());
        for (ConsumerRecord<?, ?> consumerRecord : record) {
            log.info("kafka deviceStatus消息：" + consumerRecord.topic() + "-" + consumerRecord.partition() + "-" + consumerRecord.value());
            kafkaDeviceStatusHandler.dealRecord(consumerRecord);
        }
    }

    /**
     * 消费监听 精制变更用户
     * 精制联调
     */
    @KafkaListener(topics = "jingzhi_user")
    public void jzUserMessage(List<ConsumerRecord<?, ?>> records) {
        log.info("监听精制变更用户kafka消息：{}", records);
        boolean syncUsers = Boolean.parseBoolean(businessConfigValueService.getValueByRedis(ConfigConstant.USER_SYNC_CONFIG_SYNC_FROM_JZ_USER_ENABLE));
        if (syncUsers) {
            kafkaStrategyContext.converterJzUser(records);
        }
    }

    /**
     * 消费监听 精制变更角色
     *
     * @param records
     */
    @KafkaListener(topics = "jingzhi_role")
    public void jzRoleMessage(List<ConsumerRecord<?, ?>> records) {
        log.info("监听精制变更角色kafka消息：{}", records);
        boolean syncRoles = Boolean.parseBoolean(businessConfigValueService.getValueByRedis(ConfigConstant.USER_SYNC_CONFIG_SYNC_FROM_JZ_ROLE_ENABLE));
        if (syncRoles) {
            kafkaStrategyContext.converterJzRole(records);
        }
    }

    /**
     * 消费监听 精制变更用户-角色绑定信息
     *
     * @param records
     */
    @KafkaListener(topics = "jingzhi_user_role")
    public void jzMappingUserRoleMessage(List<ConsumerRecord<?, ?>> records) {
        log.info("监听精制变更用户-角色绑定关系kafka消息：{}", records);
        kafkaStrategyContext.converterJzUserRoleMapping(records);
    }

    /**
     * 消费监听 组织变更程序
     *
     * @param records
     */
    @KafkaListener(topics = "jingzhi_app_role")
    public void jzMappingRoleAppMessage(List<ConsumerRecord<?, ?>> records) {
        log.info("监听组织变更小程序kafka消息：{}", records);
        kafkaStrategyContext.jzMappingRoleAppMessage(records);
    }

    /**
     * 消费监听 精制变更小程序-角色绑定信息
     *
     * @param records
     */
    @KafkaListener(topics = "jingzhi_role_app")
    public void jzMappingAppRoleMessage(List<ConsumerRecord<?, ?>> records) {
        log.info("监听精制变更小程序-角色绑定关系kafka消息：{}", records);
        kafkaStrategyContext.jzMappingAppRoleMessage(records);
    }

    /**
     * ECM变更小程序触发权限同步
     *
     * @param records
     */
    @KafkaListener(topics = "sync_cloud_app")
    public void syncOrganizationInfoMessage(List<ConsumerRecord<?, ?>> records) {
        log.info("监听精制变更小程序权限kafka消息：{}", records);
        kafkaStrategyContext.converterSyncOrganizationInfo(records);
    }

    /**
     * 仓库消息监听
     *
     * @param records
     */
    @KafkaListener(topics = "event_warehouse_state")
    public void warehouseInfoMessage(List<ConsumerRecord<?, ?>> records) {
        log.info("监听仓库kafka消息：{}", records);
        kafkaStrategyContext.converterWarehouseInfo(records);
    }

    /**
     * 注册任务消息监听
     *
     * @param records
     */
    @KafkaListener(topics = "event_task")
    public void taskMessage(List<ConsumerRecord<?, ?>> records) {
        log.info("任务管理kafka消息：{}", records);
        kafkaStrategyContext.converterTaskData(records);
    }

    /**
     * 价值链状态消息监听
     * @param records
     */
    @KafkaListener(topics = {"#{kafkaTopic.getEventValueChainState()}"})
    public void forwardMessage(List<ConsumerRecord<?, ?>> records){
        log.info("本次批量处理kafka消息数：{}", records.size());
        kafkaStrategyContext.forwardMessage(records);
    }

    /**
     * 价值链消息监听
     * @param records
     */
    @KafkaListener(topics = {"#{kafkaTopic.getEventValueChain()}"})
    public void valueChainMessage(List<ConsumerRecord<?, ?>> records){
        log.info("本次批量处理kafka消息数：{}", records.size());
        kafkaStrategyContext.valueChainMessage(records);
    }

}
