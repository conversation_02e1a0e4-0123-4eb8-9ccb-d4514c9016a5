//package com.yelink.inner.listen.manager.impl;
//
//
//import com.yelink.dfs.constant.ValueChainKafkaMessageConstants;
//import com.yelink.dfs.entity.stock.DeliveryApplicationEntity;
//import com.yelink.dfs.provider.constant.KafkaMessageTypeEnum;
//import com.yelink.dfs.pushdown.writeback.WriteBackSaleOrder2DeliveryApplication;
//import com.yelink.dfs.pushdown.writeback.WriteBackWorkOrder2WorkMaterialList;
//import com.yelink.dfscommon.entity.DfsStandardMessageEntity;
//import com.yelink.dfscommon.entity.dfs.WorkOrderMaterialListEntity;
//import com.yelink.dfscommon.utils.JacksonUtil;
//import com.yelink.inner.listen.manager.ForwardMessageService;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.scheduling.annotation.Async;
//import org.springframework.stereotype.Service;
//
//import javax.annotation.Resource;
//
///**
// * <AUTHOR>
// */
//@Slf4j
//@Service(ValueChainKafkaMessageConstants.WORK_ORDER_MATERIAL_LIST_MSG_SERVICE)
//public class WorkOrderMaterialListMsgServiceImpl implements ForwardMessageService {
//
//    @Resource
//    private WriteBackWorkOrder2WorkMaterialList writeBackWorkOrder2WorkMaterialList;
//
//    @Async
//    @Override
//    public void dealMessage(DfsStandardMessageEntity dfsKafkaMessageEntity) {
//        String messageType = dfsKafkaMessageEntity.getMessageType();
//        if (KafkaMessageTypeEnum.WORK_ORDER_MATERIAL_LIST_UPDATE_MESSAGE.getTypeCode().equals(messageType)) {
//            WorkOrderMaterialListEntity e = JacksonUtil.convertObject(dfsKafkaMessageEntity.getChangeAfterContent(), WorkOrderMaterialListEntity.class);
//            writeBackWorkOrder2WorkMaterialList.dealWriteBack(e);
//        }
//    }
//
//}
