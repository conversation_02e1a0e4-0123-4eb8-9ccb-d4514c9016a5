package com.yelink.inner.listen.manager.impl;


import com.yelink.dfs.constant.ValueChainKafkaMessageConstants;
import com.yelink.dfs.entity.approve.config.ApproveRecordEntity;
import com.yelink.dfscommon.constant.dfs.DfsEventTypeEnum;
import com.yelink.dfscommon.entity.DfsStandardMessageEntity;
import com.yelink.dfscommon.entity.dfs.OrderPushDownRecordEntity;
import com.yelink.dfscommon.utils.JacksonUtil;
import com.yelink.inner.listen.manager.ApproveMessageService;
import com.yelink.inner.listen.manager.OrderPushDownMessageService;
import com.yelink.inner.listen.manager.ValueChainMessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Service(ValueChainKafkaMessageConstants.ORDER_PUSH_DOWN_RECORD_MSG_SERVICE)
public class PushDownMsgServiceImpl implements ValueChainMessageService {

    @Resource
    private Map<String, OrderPushDownMessageService> strategyMap;

    @Async
    @Override
    public void dealMessage(DfsStandardMessageEntity dfsKafkaMessageEntity) {
        String messageType = dfsKafkaMessageEntity.getMessageType();
        // 单据下推记录消息
        if (DfsEventTypeEnum.ORDER_PUSH_DOWN_RECORD_MESSAGE_ADD.getTypeCode().equals(messageType) ||
                DfsEventTypeEnum.ORDER_PUSH_DOWN_RECORD_MESSAGE_DELETE.getTypeCode().equals(messageType)) {
            List<OrderPushDownRecordEntity> recordEntities = JacksonUtil.convertArray(dfsKafkaMessageEntity.getChangeAfterContent(), OrderPushDownRecordEntity.class);
            OrderPushDownRecordEntity recordEntity = recordEntities.get(0);
            OrderPushDownMessageService messageService = strategyMap.get(recordEntity.getSourceOrderType() + "MsgService");
            if (Objects.isNull(messageService)) {
                return;
            }
            messageService.deal(recordEntities);
        }
    }

}
