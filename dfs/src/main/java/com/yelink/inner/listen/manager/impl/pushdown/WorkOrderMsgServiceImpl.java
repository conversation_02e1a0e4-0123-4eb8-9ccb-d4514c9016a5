package com.yelink.inner.listen.manager.impl.pushdown;


import com.yelink.dfs.constant.ValueChainKafkaMessageConstants;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.service.common.config.OrderPushDownIdentifierService;
import com.yelink.dfs.service.common.config.OrderPushDownRecordService;
import com.yelink.dfs.service.order.WorkOrderService;
import com.yelink.dfscommon.constant.OrderNumTypeEnum;
import com.yelink.dfscommon.constant.dfs.pushdown.PushDownIdentifierFullPathEnum;
import com.yelink.dfscommon.constant.dfs.pushdown.PushDownIdentifierStateEnum;
import com.yelink.dfscommon.entity.dfs.OrderPushDownIdentifierEntity;
import com.yelink.dfscommon.entity.dfs.OrderPushDownRecordEntity;
import com.yelink.dfscommon.utils.WrapperUtil;
import com.yelink.inner.listen.manager.OrderPushDownMessageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service(ValueChainKafkaMessageConstants.WORK_ORDER_MSG_SERVICE)
public class WorkOrderMsgServiceImpl implements OrderPushDownMessageService {

    @Resource
    private WorkOrderService workOrderService;
    @Resource
    private OrderPushDownIdentifierService pushDownIdentifierService;
    @Resource
    private OrderPushDownRecordService orderPushDownRecordService;

    @Async
    @Override
    public void deal(List<OrderPushDownRecordEntity> recordEntities) {
        if (CollectionUtils.isEmpty(recordEntities)) {
            return;
        }

        try {
            // 1. 按工单号和目标单据类型分组传入的下推记录
            Map<String, Map<String, List<OrderPushDownRecordEntity>>> recordsByWorkOrderAndTargetType = recordEntities.stream()
                    .filter(record -> Objects.nonNull(record.getSourceOrderNumber()) && Objects.nonNull(record.getTargetOrderType()))
                    .collect(Collectors.groupingBy(
                            OrderPushDownRecordEntity::getSourceOrderNumber,
                            Collectors.groupingBy(OrderPushDownRecordEntity::getTargetOrderType)
                    ));

            if (recordsByWorkOrderAndTargetType.isEmpty()) {
                log.warn("未找到有效的工单号和目标单据类型，跳过处理");
                return;
            }

            // 2. 查询涉及的工单信息
            List<String> workOrderNumbers = new ArrayList<>(recordsByWorkOrderAndTargetType.keySet());
            List<WorkOrderEntity> workOrderEntities = workOrderService.lambdaQuery()
                    .in(WorkOrderEntity::getWorkOrderNumber, workOrderNumbers).list();
            if (CollectionUtils.isEmpty(workOrderEntities)) {
                log.warn("未找到对应的工单信息，工单号: {}", workOrderNumbers);
                return;
            }

            // 3. 为每个工单的每种目标单据类型计算下推状态并更新标识
            List<OrderPushDownIdentifierEntity> identifierEntities = new ArrayList<>();
            for (WorkOrderEntity workOrder : workOrderEntities) {
                Map<String, List<OrderPushDownRecordEntity>> targetTypeRecords = recordsByWorkOrderAndTargetType.get(workOrder.getWorkOrderNumber());
                if (targetTypeRecords == null) {
                    continue;
                }

                // 为每种目标单据类型分别处理
                for (Map.Entry<String, List<OrderPushDownRecordEntity>> entry : targetTypeRecords.entrySet()) {
                    String targetOrderType = entry.getKey();

                    // 查询该工单对该目标单据类型的所有下推记录
                    List<OrderPushDownRecordEntity> allTypeRecords = orderPushDownRecordService.lambdaQuery()
                            .eq(OrderPushDownRecordEntity::getSourceOrderNumber, workOrder.getWorkOrderNumber())
                            .eq(OrderPushDownRecordEntity::getSourceOrderType, OrderNumTypeEnum.WORK_ORDER.getTypeCode())
                            .eq(OrderPushDownRecordEntity::getTargetOrderType, targetOrderType)
                            .list();

                    // 计算该目标单据类型的下推状态
                    String pushDownState = calculatePushDownState(workOrder, allTypeRecords);

                    // 获取对应的全路径编码
                    String valueFullPathCode = PushDownIdentifierFullPathEnum.getFullPathCode(targetOrderType, pushDownState);

                    // 创建或更新下推标识
                    OrderPushDownIdentifierEntity identifierEntity = OrderPushDownIdentifierEntity.builder()
                            .orderType(OrderNumTypeEnum.WORK_ORDER.getTypeCode())
                            .orderMaterialId(workOrder.getWorkOrderId().toString())
                            .targetOrderType(targetOrderType)
                            .state(pushDownState)
                            .valueFullPathCode(valueFullPathCode)
                            .build();

                    identifierEntities.add(identifierEntity);
                }
            }

            // 4. 批量保存或更新下推标识
            if (CollectionUtils.isNotEmpty(identifierEntities)) {
                updatePushDownIdentifiers(identifierEntities);
            }

        } catch (Exception e) {
            log.error("处理工单下推标识时发生异常", e);
        }
    }

    /**
     * 计算工单的下推状态
     *
     * @param workOrder 工单信息
     * @param pushDownRecords 下推记录列表
     * @return 下推状态
     */
    private String calculatePushDownState(WorkOrderEntity workOrder, List<OrderPushDownRecordEntity> pushDownRecords) {
        if (CollectionUtils.isEmpty(pushDownRecords)) {
            return PushDownIdentifierStateEnum.NO_PUSH_DOWN.getCode();
        }

        // 过滤异常记录，计算有效下推数量
        List<OrderPushDownRecordEntity> validRecords = pushDownRecords.stream()
                .filter(record -> !Boolean.TRUE.equals(record.getIsAbnormal()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(validRecords)) {
            return PushDownIdentifierStateEnum.NO_PUSH_DOWN.getCode();
        }

        // 去重并计算下推数量总和
        List<OrderPushDownRecordEntity> distinctRecords = validRecords.stream()
                .filter(WrapperUtil.distinctByKey(OrderPushDownRecordEntity::getPushDownCode))
                .collect(Collectors.toList());

        double totalPushDownQuantity = distinctRecords.stream()
                .mapToDouble(OrderPushDownRecordEntity::getPushDownQuantity)
                .sum();

        double planQuantity = workOrder.getPlanQuantity() != null ? workOrder.getPlanQuantity() : 0.0;

        // 判断下推状态
        if (totalPushDownQuantity == 0) {
            return PushDownIdentifierStateEnum.NO_PUSH_DOWN.getCode();
        } else if (totalPushDownQuantity < planQuantity) {
            return PushDownIdentifierStateEnum.PART_PUSH_DOWN.getCode();
        } else {
            return PushDownIdentifierStateEnum.ALL_PUSH_DOWN.getCode();
        }
    }

    /**
     * 更新下推标识
     *
     * @param identifierEntities 下推标识实体列表
     */
    private void updatePushDownIdentifiers(List<OrderPushDownIdentifierEntity> identifierEntities) {
        for (OrderPushDownIdentifierEntity entity : identifierEntities) {
            // 查询是否已存在相同的标识记录（包括目标单据类型）
            OrderPushDownIdentifierEntity existingEntity = pushDownIdentifierService.lambdaQuery()
                    .eq(OrderPushDownIdentifierEntity::getOrderType, entity.getOrderType())
                    .eq(OrderPushDownIdentifierEntity::getOrderMaterialId, entity.getOrderMaterialId())
                    .eq(OrderPushDownIdentifierEntity::getTargetOrderType, entity.getTargetOrderType())
                    .one();

            if (existingEntity != null) {
                // 更新现有记录
                existingEntity.setState(entity.getState());
                existingEntity.setValueFullPathCode(entity.getValueFullPathCode());
                pushDownIdentifierService.updateById(existingEntity);
                log.debug("更新下推标识: 工单ID={}, 目标单据类型={}, 状态={}, 全路径编码={}",
                        entity.getOrderMaterialId(), entity.getTargetOrderType(), entity.getState(), entity.getValueFullPathCode());
            } else {
                // 新增记录
                pushDownIdentifierService.save(entity);
                log.debug("新增下推标识: 工单ID={}, 目标单据类型={}, 状态={}, 全路径编码={}",
                        entity.getOrderMaterialId(), entity.getTargetOrderType(), entity.getState(), entity.getValueFullPathCode());
            }
        }
    }
}
