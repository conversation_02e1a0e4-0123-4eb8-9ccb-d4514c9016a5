package com.yelink.inner.listen.manager.impl.pushdown;


import com.yelink.dfs.constant.ValueChainKafkaMessageConstants;
import com.yelink.dfs.entity.approve.config.ApproveRecordEntity;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.open.v1.workOrder.dto.WorkOrderDetailDTO;
import com.yelink.dfs.service.common.config.BusinessConfigService;
import com.yelink.dfs.service.common.config.BusinessConfigValueService;
import com.yelink.dfs.service.common.config.OrderPushDownIdentifierService;
import com.yelink.dfs.service.order.WorkOrderService;
import com.yelink.dfscommon.constant.approve.config.ApprovalStatusEnum;
import com.yelink.dfscommon.constant.approve.config.ApproveModuleEnum;
import com.yelink.dfscommon.constant.dfs.order.WorkOrderStateEnum;
import com.yelink.dfscommon.entity.dfs.OrderPushDownRecordEntity;
import com.yelink.inner.listen.manager.ApproveMessageService;
import com.yelink.inner.listen.manager.OrderPushDownMessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Service(ValueChainKafkaMessageConstants.WORK_ORDER_MSG_SERVICE)
public class WorkOrderMsgServiceImpl implements OrderPushDownMessageService {

    @Resource
    private WorkOrderService workOrderService;
    @Resource
    private OrderPushDownIdentifierService pushDownIdentifierService;
    @Resource
    private BusinessConfigValueService businessConfigValueService;

    @Async
    @Override
    public void deal(List<OrderPushDownRecordEntity> recordEntities) {
        //



    }
}
