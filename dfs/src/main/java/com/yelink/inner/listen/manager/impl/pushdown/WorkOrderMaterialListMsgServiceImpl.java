package com.yelink.inner.listen.manager.impl.pushdown;//package com.yelink.inner.listen.manager.impl;


import com.yelink.dfs.constant.ValueChainKafkaMessageConstants;
import com.yelink.dfs.pushdown.writeback.WriteBackWorkOrder2WorkMaterialList;
import com.yelink.dfs.service.order.WorkOrderMaterialListService;
import com.yelink.dfscommon.entity.dfs.OrderPushDownRecordEntity;
import com.yelink.inner.listen.manager.OrderPushDownMessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Service(ValueChainKafkaMessageConstants.WORK_ORDER_MATERIAL_LIST_MSG_SERVICE)
public class WorkOrderMaterialListMsgServiceImpl implements OrderPushDownMessageService {

    @Resource
    private WorkOrderMaterialListService workOrderMaterialListService;


    @Override
    public void deal(List<OrderPushDownRecordEntity> recordEntities) {

    }
}
