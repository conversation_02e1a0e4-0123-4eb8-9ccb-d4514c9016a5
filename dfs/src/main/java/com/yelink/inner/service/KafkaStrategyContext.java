package com.yelink.inner.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.yelink.dfs.config.KafkaDeviceTopic;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.user.RoleEnum;
import com.yelink.dfs.entity.sensor.SensorProductEntity;
import com.yelink.dfs.entity.user.SysPermissionEntity;
import com.yelink.dfs.entity.user.SysRoleBindPermissionEntity;
import com.yelink.dfs.entity.user.SysRoleEntity;
import com.yelink.dfs.entity.user.SysUserEntity;
import com.yelink.dfs.entity.user.SysUserRoleEntity;
import com.yelink.dfs.mapper.user.enums.EnabledEnum;
import com.yelink.dfs.open.v1.aksk.dto.OpenApiDTO;
import com.yelink.dfs.open.v1.permission.dto.RolePermissionBindDTO;
import com.yelink.dfs.service.app.AppOnlineService;
import com.yelink.dfs.service.open.OpenApiConfigService;
import com.yelink.dfs.service.sensor.SensorProductService;
import com.yelink.dfs.service.user.SysPermissionService;
import com.yelink.dfs.service.user.SysRolePermissionService;
import com.yelink.dfs.service.user.SysRoleService;
import com.yelink.dfs.service.user.SysUserRoleService;
import com.yelink.dfs.service.user.SysUserService;
import com.yelink.dfs.utils.ExtApiUtil;
import com.yelink.dfscommon.constant.OpenApiEnum;
import com.yelink.dfscommon.entity.DfsStandardMessageEntity;
import com.yelink.dfscommon.entity.dfs.app.AppOnlineEntity;
import com.yelink.dfscommon.entity.sync.dto.JzRoleAppBindDTO;
import com.yelink.dfscommon.entity.sync.dto.JzRoleVO;
import com.yelink.dfscommon.utils.JacksonUtil;
import com.yelink.inner.constant.SensorServiceNameConstant;
import com.yelink.inner.data.access.JsonObjectEntity;
import com.yelink.inner.entity.dto.JzAppBindRoleDTO;
import com.yelink.inner.entity.dto.JzMsgResponseDTO;
import com.yelink.inner.entity.dto.JzRoleBindAppDTO;
import com.yelink.inner.entity.dto.JzRoleDTO;
import com.yelink.inner.entity.dto.JzSyncRoleDTO;
import com.yelink.inner.entity.dto.JzUseRoleDTO;
import com.yelink.inner.entity.dto.JzUserDTO;
import com.yelink.inner.listen.manager.ForwardMessageService;
import com.yelink.inner.listen.manager.ValueChainMessageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @description: kafka分发策略
 * @author: shuang
 * @time: 2020/8/12
 */
@Slf4j
@Component
public class KafkaStrategyContext {
    private static final String STATUS = "status";
    private static final String ALARM = "alarm";
    private static final String ADD = "ADD";
    private static final String UPDATE = "UPDATE";
    private static final String DELETE = "DELETE";
    private static final String NONE_PRODUCT_KEY = "none";
    @Autowired
    private KafkaDeviceTopic kafkaDeviceTopic;

    /**
     * 使用线程安全的ConcurrentHashMap存储所有实现CommandService接口的Bean
     * key: 方法名
     * value：实现SensorDataByKafkaService接口Bean
     */
    @Resource
    private Map<String, SensorDataByKafkaService> converterMap;
    @Resource
    private Map<String, SensorBatchDataByKafkaService> bathConverterMap;
    @Resource
    private Map<String, OrderDataByKafkaService> orderDataConverterMap;
    @Resource
    private Map<String, ForwardMessageService> strategyMap;
    @Resource
    private Map<String, ValueChainMessageService> valueChainMap;
    @Resource
    private SysRoleService sysRoleService;
    @Resource
    private AppOnlineService appOnlineService;
    @Resource
    private SysUserRoleService userRoleService;
    @Resource
    private SysUserService userService;
    @Resource
    private SensorProductService sensorProductService;
    @Resource
    private SysPermissionService sysPermissionService;
    @Resource
    private SysRolePermissionService rolePermissionService;
    @Resource
    private TaskDataByKafkaService taskDataByKafkaService;
    @Resource
    private OpenApiConfigService openApiConfigService;

    /**
     * 根据topic分发到对应的处理方法
     *
     * @param records
     */
    public void converterData(List<ConsumerRecord<?, ?>> records) {
        try {
            Map<String, String> maps = kafkaDeviceTopic.getBeanNameByProductKey();

            List<JsonObjectEntity> msgList = new ArrayList<>();

            for (ConsumerRecord<?, ?> record : records) {
                // 消费的哪个topic、partition的消息,打印出消息内容
                log.info("kafka消息：" + record.topic() + "-" + record.partition() + "-" + record.value());
                JsonObjectEntity jsonObjectEntity = JSONObject.parseObject((String) record.value(), JsonObjectEntity.class);
                jsonObjectEntity.setTimestamp(record.timestamp());
                msgList.add(jsonObjectEntity);
            }
            //按productKey分组
            Map<String, List<JsonObjectEntity>> collect = msgList.stream().collect(Collectors.groupingBy(x -> Optional.ofNullable(x.getProductKey()).orElse(NONE_PRODUCT_KEY)));

            for (Map.Entry<String, List<JsonObjectEntity>> entry : collect.entrySet()) {
                String beanName = maps.get(entry.getKey());
                if (StringUtils.isBlank(beanName)) {
                    log.info("productKey:{}的消息类型未配置，设置为通用类型", entry.getKey());
                    updateSensorProduct(entry.getKey());
                    beanName = SensorServiceNameConstant.COMMON_COLLECTOR;
                }

                SensorBatchDataByKafkaService batchDealer = bathConverterMap.get(beanName);
                SensorDataByKafkaService dealer = converterMap.get(beanName);
                //判断是否批量处理
                if (batchDealer == null && dealer == null) {
                    log.info("kafka消息未找到对应的处理方法");
                    continue;
                }

                //按dataType分组
                Map<Optional<String>, List<JsonObjectEntity>> groupByDataType =
                        entry.getValue().stream().collect(Collectors.groupingBy(x -> Optional.ofNullable(x.getDataType())));

                for (Map.Entry<Optional<String>, List<JsonObjectEntity>> optionalListEntry : groupByDataType.entrySet()) {
                    String dataType = optionalListEntry.getKey().orElse(null);

                    if (dataType != null) {
                        if (dataType.contains(ALARM)) {
                            batchConverterAlarm(optionalListEntry.getValue(), dealer, batchDealer);
                        } else if (!dataType.contains(STATUS)) {
                            batchConverterData(optionalListEntry.getValue(), dealer, batchDealer);
                        } else {
                            //状态上报
                            log.info("状态上报消息暂不处理");
                        }
                    } else {
                        batchConverterData(optionalListEntry.getValue(), dealer, batchDealer);
                    }
                }
            }
        } catch (Exception e) {
            log.error("kafka分发处理过程中发生异常", e);
        }
    }

    private void updateSensorProduct(String key) {
        try {
            SensorProductEntity one = sensorProductService.lambdaQuery()
                    .eq(SensorProductEntity::getSensorType, SensorServiceNameConstant.COMMON_COLLECTOR).one();
            StringBuilder sb = new StringBuilder();
            if (StringUtils.isNotBlank(one.getProductKey())) {
                sb.append(one.getProductKey()).append(",");
            }
            sb.append(key);
            one.setProductKey(sb.toString());
            sensorProductService.updateById(one);
            kafkaDeviceTopic.run();
        } catch (Exception e) {
            log.error("", e);
        }
    }

    private void batchConverterData(List<JsonObjectEntity> value, SensorDataByKafkaService dealer, SensorBatchDataByKafkaService batchDealer) {
        try {
            if (batchDealer != null) {
                batchDealer.converterData(value);
            } else {
                for (JsonObjectEntity jsonObjectEntity : value) {
                    dealer.converterData(jsonObjectEntity);
                }
            }
        } catch (Exception e) {
            log.error("kafka处理过程中发生异常", e);
        }

    }

    private void batchConverterAlarm(List<JsonObjectEntity> value, SensorDataByKafkaService dealer, SensorBatchDataByKafkaService batchDealer) {
        try {
            if (batchDealer != null) {
                batchDealer.converterAlarm(value);
            } else {
                for (JsonObjectEntity jsonObjectEntity : value) {
                    dealer.converterAlarm(jsonObjectEntity);
                }
            }
        } catch (Exception e) {
            log.error("kafka处理过程中发生异常", e);
        }
    }


    public void converterJzUser(List<ConsumerRecord<?, ?>> records) {
        try {
            for (ConsumerRecord<?, ?> record : records) {
                JzMsgResponseDTO jzResponseDTO = JSONObject.parseObject((String) record.value(), JzMsgResponseDTO.class);
                // 密钥，如果不为空，则不处理精制发送的消息，避免和精制的套娃
                if (StringUtils.isNotBlank(jzResponseDTO.getAccessKeyId())) {
                    continue;
                }
                JSONObject parse = JSONObject.parseObject(JSON.toJSONString(jzResponseDTO.getData()));
                if (parse == null) {
                    continue;
                }
                JzUserDTO jzUser = parse.toJavaObject(JzUserDTO.class);
                String operation = jzResponseDTO.getOperation();
                switch (operation) {
                    case ADD:
                        SysUserEntity oldUser = userService.lambdaQuery().eq(SysUserEntity::getUsername, jzUser.getUsername()).one();
                        if (oldUser == null) {
                            SysUserEntity build = SysUserEntity.builder()
                                    .username(jzUser.getUsername())
                                    .nickname(jzUser.getFirstName())
                                    .password(Constant.BLANK)
                                    .departmentName(Constant.BLANK)
                                    .imUserName(jzUser.getImUserName())
                                    .keycloakId(jzUser.getId())
                                    .creatByJz(true)
                                    .compId(1)
                                    .mobile(jzUser.getPhone())
                                    .build();
                            build.setCreateTime(new Date());
                            build.setUpdateTime(new Date());
                            userService.save(build);
                            break;
                        } else {
                            //如果dfs存在该用户，接收到创建用户消息，则把用户改为启用状态
                            userService.lambdaUpdate().
                                    eq(SysUserEntity::getUsername, oldUser.getUsername())
                                    .set(SysUserEntity::getEnabled, EnabledEnum.ENABLE.getCode())
                                    .set(SysUserEntity::getNickname, jzUser.getFirstName())
                                    .set(SysUserEntity::getImUserName, jzUser.getImUserName())
                                    .set(SysUserEntity::getKeycloakId, jzUser.getId())
                                    .set(SysUserEntity::getMobile, jzUser.getPhone())
                                    .update();
                        }
                        break;
                    case UPDATE: {
                        String username = jzUser.getUsername();
                        SysUserEntity sysUserEntity = userService.selectByUsername(username);
                        if (sysUserEntity == null) {
                            break;
                        }
//                        if (!sysUserEntity.getNickname().equals(jzUser.getFirstName())) {
                        sysUserEntity.setMobile(jzUser.getPhone());
                        sysUserEntity.setNickname(jzUser.getFirstName());
                        // 更新用户
                        userService.updateById(sysUserEntity);
                        // 禁用/启用用户， 并对应解除/恢复精制角色用户绑定关系
                        String disOrEnable = jzUser.getEnabled() ? EnabledEnum.ENABLE.getCode() : EnabledEnum.DISABLE.getCode();
                        userService.updateDisableById(disOrEnable, String.valueOf(sysUserEntity.getId()), null);
                        break;
//                        }
//                        break;
                    }
                    case DELETE: {
                        String username = jzUser.getUsername();
//                        SysUserEntity sysUserEntity = userService.selectByUsername(username);
                        // dfs软删除用户，禁用用户并解除精制角色用户绑定关系
                        userService.deleteUser(username);
//                        userService.updateDisableById(EnabledEnum.DISABLE.getCode(), String.valueOf(sysUserEntity.getId()), null);
                        break;
                    }
                    default:
                        break;
                }
            }
        } catch (Exception e) {
            log.error("kafka处理过程中发生异常", e);
        }
    }

    public void converterJzUserRoleMapping(List<ConsumerRecord<?, ?>> records) {
        try {
            for (ConsumerRecord<?, ?> record : records) {
                JzMsgResponseDTO jzResponseDTO = JSONObject.parseObject((String) record.value(), JzMsgResponseDTO.class);
                Object date = jzResponseDTO.getData();
                if (date == null) {
                    continue;
                }
                JSONArray objects = JSONObject.parseArray(JSON.toJSONString(jzResponseDTO.getData()));
                List<JzUseRoleDTO> jzUseRoleDTOS = objects.toJavaList(JzUseRoleDTO.class);
                for (JzUseRoleDTO jzUseRoleDTO : jzUseRoleDTOS) {
                    List<JzSyncRoleDTO> jzSyncRoleDTOS = jzUseRoleDTO.getRoles();
                    if (CollectionUtils.isEmpty(jzSyncRoleDTOS)) {
                        continue;
                    }
                    List<String> roleNames = jzSyncRoleDTOS.stream().map(JzSyncRoleDTO::getRoleName).collect(Collectors.toList());
                    String username = jzUseRoleDTO.getUserName();
                    SysUserEntity sysUserEntity = userService.selectByUsername(username);
                    if (sysUserEntity == null || CollectionUtils.isEmpty(roleNames)) {
                        continue;
                    }
                    Integer userId = sysUserEntity.getId();
                    // 如果为admin、yelinkoncall的内置用户，需要过滤内置绑定角色，防止业务流程出现问题
                    if (sysUserEntity.getUsername().equals(Constant.ADMIN)) {
                        roleNames.add(RoleEnum.ADMIN.getName());
                    } else if (sysUserEntity.getUsername().equals(Constant.YELINK_ONCALL)) {
                        roleNames.add(RoleEnum.SYSTEM_ADMIN.getName());
                    }
                    // 删除未被禁用的旧角色映射
                    userRoleService.lambdaUpdate().eq(SysUserRoleEntity::getUserId, userId).eq(SysUserRoleEntity::getEnabled, EnabledEnum.ENABLE.getCode()).remove();
                    List<SysUserRoleEntity> sysUserBindRoleEntities = new ArrayList<>();
                    // 绑定新的角色映射
                    List<String> distinctUserNames = roleNames.stream().distinct().collect(Collectors.toList());
                    for (String roleName : distinctUserNames) {
                        SysRoleEntity sysRoleEntity = sysRoleService.lambdaQuery().eq(SysRoleEntity::getName, roleName).one();
                        if (sysRoleEntity == null) {
                            continue;
                        }

                        Long count = userRoleService.lambdaQuery().eq(SysUserRoleEntity::getUserId, userId)
                                .eq(SysUserRoleEntity::getRoleId, sysRoleEntity.getId()).count();
                        if (count == 0) {
                            SysUserRoleEntity build = SysUserRoleEntity.builder()
                                    .userId(userId).roleId(sysRoleEntity.getId()).build();
                            sysUserBindRoleEntities.add(build);
                        }
                    }
                    // 批量绑定用户角色关系
                    if (!CollectionUtils.isEmpty(sysUserBindRoleEntities)) {
                        userRoleService.saveBatch(sysUserBindRoleEntities);
                    }
                }
            }
        } catch (Exception e) {
            log.error("kafka处理过程中发生异常", e);
        }
    }

    public void converterJzRole(List<ConsumerRecord<?, ?>> records) {
        try {
            for (ConsumerRecord<?, ?> record : records) {
                JzMsgResponseDTO jzResponseDTO = JSONObject.parseObject((String) record.value(), JzMsgResponseDTO.class);
                JSONObject parse = JSONObject.parseObject(JSON.toJSONString(jzResponseDTO.getData()));
                if (parse == null) {
                    continue;
                }
                String operation = jzResponseDTO.getOperation();
                JzRoleDTO jzRole = parse.toJavaObject(JzRoleDTO.class);
                SysRoleEntity sysRoleEntity = sysRoleService.lambdaQuery().eq(SysRoleEntity::getName, jzRole.getRoleName()).one();
                switch (operation) {
                    case ADD:
                        // 新增角色
                        if (sysRoleEntity != null) {
                            break;
                        }
                        SysRoleEntity roleEntity = SysRoleEntity.builder()
                                .name(jzRole.getRoleName())
                                .keycloakId(jzRole.getRoleId())
                                .build();
                        sysRoleService.save(roleEntity);
                        break;
//                    case UPDATE: {
//                        // 修改角色
//                        if (sysRoleEntity != null) {
//                            sysRoleEntity.setName(jzRole.getRoleName());
//                            sysRoleMapper.updateById(sysRoleEntity);
//                        }
//                        break;
//                    }
                    case DELETE: {
                        // 删除角色
                        if (sysRoleEntity != null) {
                            sysRoleService.removeById(sysRoleEntity.getId());
                            // 删除用户角色绑定关系
                            userRoleService.lambdaUpdate().eq(SysUserRoleEntity::getRoleId, sysRoleEntity.getId()).remove();
                        }
                        break;
                    }
                    default:
                        break;
                }
            }
        } catch (Exception e) {
            log.error("kafka处理过程中发生异常", e);
        }
    }

    public void converterSyncOrganizationInfo(List<ConsumerRecord<?, ?>> records) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }
        try {
            sysPermissionService.judgeAppButtonPermission();
            sysPermissionService.updateAppPermission();
        } catch (Exception e) {
            log.error("", e);
        }
    }

    public void converterWarehouseInfo(List<ConsumerRecord<?, ?>> records) {
        log.info("本次批量处理kafka消息数：{}", records.size());
        for (ConsumerRecord<?, ?> consumerRecord : records) {
            // 消费的哪个topic、partition的消息,打印出消息内容
            log.info("kafka forward remind message消息：" + consumerRecord.topic() + "-" + consumerRecord.partition() + "-" + consumerRecord.value());
            try {
                DfsStandardMessageEntity kafkaCommandEntity = JacksonUtil.parseObject(consumerRecord.value().toString(), DfsStandardMessageEntity.class);
                //处理kafka消息
                OrderDataByKafkaService orderKafkaService = orderDataConverterMap.get("warehouse_" + kafkaCommandEntity.getMessageModel() + "WriteBackService");
                if (orderKafkaService != null) {
                    orderKafkaService.converterData(kafkaCommandEntity);
                    continue;
                }
                log.info("未找到该消息类型的具体处理实现类，消息内容：{}", consumerRecord.value());
            } catch (Exception e) {
                log.error("消息处理错误，消息：{}", consumerRecord.value(), e);
            }
        }
    }

    public void converterTaskData(List<ConsumerRecord<?, ?>> records) {
        log.info("本次批量处理kafka消息数：{}", records.size());
        for (ConsumerRecord<?, ?> consumerRecord : records) {
            // 消费的哪个topic、partition的消息,打印出消息内容
            log.info("任务消息：" + consumerRecord.topic() + "-" + consumerRecord.partition() + "-" + consumerRecord.value());
            try {
                DfsStandardMessageEntity kafkaCommandEntity = JacksonUtil.parseObject(consumerRecord.value().toString(), DfsStandardMessageEntity.class);
                //处理kafka消息
                taskDataByKafkaService.converterData(kafkaCommandEntity);
            } catch (Exception e) {
                log.error("消息处理错误，消息：{}", consumerRecord.value(), e);
            }
        }
    }

    public void forwardMessage(List<ConsumerRecord<?, ?>> records) {
        for (ConsumerRecord<?, ?> consumerRecord : records) {
            log.info("接收到kafka消息：主题 - {} ；分区 - {}", consumerRecord.topic(), consumerRecord.partition());
            log.debug("数据 - {}", consumerRecord.value());
            //消息处理
            try {
                DfsStandardMessageEntity kafkaCommandEntity = JacksonUtil.parseObject(consumerRecord.value().toString(), DfsStandardMessageEntity.class);
                //处理kafka消息
                ForwardMessageService forwardMessageService = strategyMap.get(kafkaCommandEntity.getMessageModel() + "MsgService");
                if (forwardMessageService != null) {
                    forwardMessageService.dealMessage(kafkaCommandEntity);
                    continue;
                }
                log.debug("未找到该消息类型的具体处理实现类，消息内容：{}", consumerRecord.value());
            } catch (Exception e) {
                log.error("消息处理错误，消息：{}", consumerRecord.value(), e);
            }
        }
    }

    public void jzMappingRoleAppMessage(List<ConsumerRecord<?, ?>> records) {
        try {
            for (ConsumerRecord<?, ?> record : records) {
                JzMsgResponseDTO jzResponseDTO = JSONObject.parseObject((String) record.value(), JzMsgResponseDTO.class);
                Object date = jzResponseDTO.getData();
                if (date == null) {
                    continue;
                }
                JSONObject objects = JSONObject.parseObject(JSON.toJSONString(jzResponseDTO.getData()));
                if (objects == null) {
                    continue;
                }
                JzAppBindRoleDTO jzAppBindRoleDTO = objects.toJavaObject(JzAppBindRoleDTO.class);
                String operation = jzResponseDTO.getOperation();
                AppOnlineEntity appOnlineEntity = appOnlineService.lambdaQuery().eq(AppOnlineEntity::getApplicationId, jzAppBindRoleDTO.getApplicationId()).one();
                // 非注册列表中的在线小程序直接返回
                if (Objects.isNull(appOnlineEntity)) {
                    continue;
                }
                switch (operation) {
                    case ADD:
                        // 组织添加在线小程序，需要将dfs角色-在线小程序权限同步给精制
                        List<Integer> bindRoleIds = rolePermissionService.lambdaQuery().eq(SysRoleBindPermissionEntity::getPermissionId, appOnlineEntity.getPermissionId())
                                .list().stream()
                                .map(SysRoleBindPermissionEntity::getRoleId).distinct().collect(Collectors.toList());
                        for (Integer bindRoleId : bindRoleIds) {
                            sysPermissionService.noticeJingZhiRolebindApp(bindRoleId);
                        }
                        try {
                            // tips: dfs只对已绑定该在线小程序的角色进行同步，未绑定该在线小程序的角色不进行同步。保险处理：读取所有未绑定的角色，通知精制解绑
                            // 获取精制端该小程序绑定的角色列表，过滤dfs未绑定的角色，通知精制解绑
                            HashMap<String, String> param = new HashMap<>();
                            param.put("applicationId", appOnlineEntity.getApplicationId());
                            OpenApiDTO openApiDTO = OpenApiDTO.builder()
                                    .sendReqParam(param)
                                    .modelCode(OpenApiEnum.GET_ROLES_BY_APPLICATION_ID.getModelCode())
                                    .interfaceCode(OpenApiEnum.GET_ROLES_BY_APPLICATION_ID.getInterfaceCode())
                                    .build();
                            List<JzRoleVO> jzRoleVOS = openApiConfigService.callOpenUrlToGetArray(openApiDTO, JzRoleVO.class);
                            if (CollectionUtils.isNotEmpty(jzRoleVOS)) {
                                noticeJingZhiUnbindRole(bindRoleIds, jzRoleVOS, jzAppBindRoleDTO);
                            }
                        } catch (Exception e) {
                            log.error("dfs角色-在线小程序关系同步到精制平台失败！");
                            return;
                        }
                        log.info("dfs角色-在线小程序的关系已同步到精制平台");
                        break;
                    case UPDATE: {
                        // 以下都是增量数据
                        List<SysRoleEntity> bindRoleEntities = CollectionUtils.isNotEmpty(jzAppBindRoleDTO.getBindRoleNames()) ? sysRoleService.lambdaQuery().in(SysRoleEntity::getName, jzAppBindRoleDTO.getBindRoleNames()).list() : new ArrayList<>();
                        List<SysRoleEntity> unbindRoleEntities = CollectionUtils.isNotEmpty(jzAppBindRoleDTO.getUnbindRoleNames()) ? sysRoleService.lambdaQuery().in(SysRoleEntity::getName, jzAppBindRoleDTO.getUnbindRoleNames()).list() : new ArrayList<>();
                        // 绑定角色权限
                        bindRolePermission(bindRoleEntities, appOnlineEntity);
                        // 解绑角色权限
                        unbindRolePermission(unbindRoleEntities, appOnlineEntity);
                        log.info("精制角色-在线小程序关系绑定成功!在线小程序为：{}", appOnlineEntity.getApplicationId());
                    }
                    case DELETE: {
                        // 组织删除小程序不进行操作
//                        // 以下都是增量数据
//                        List<SysRoleEntity> unbindRoleEntities = CollectionUtils.isEmpty(jzAppBindRoleDTO.getUnbindRoleNames()) ? sysRoleService.lambdaQuery().in(SysRoleEntity::getName, jzAppBindRoleDTO.getUnbindRoleNames()).list() : new ArrayList<>();
//                        // 解绑角色权限
//                        unbindRolePermission(unbindRoleEntities, appOnlineEntity);
                        break;
                    }
                    default:
                        break;
                }
            }
        } catch (Exception e) {
            log.error("kafka处理过程中发生异常", e);
        }
    }

    /**
     * 解绑精制角色-在线小程序关系
     */
    private void noticeJingZhiUnbindRole(List<Integer> bindRoleIds, List<JzRoleVO> jzRoleVOS, JzAppBindRoleDTO jzAppBindRoleDTO) {
        List<String> jingzhiRoleNames = jzRoleVOS.stream().map(JzRoleVO::getName).collect(Collectors.toList());
        List<String> unbindRoleNames = sysRoleService.lambdaQuery()
                .notIn(SysRoleEntity::getId, bindRoleIds)
                .in(SysRoleEntity::getName, jingzhiRoleNames)
                .list().stream()
                .map(SysRoleEntity::getName).collect(Collectors.toList());
        // 2、通知精制解绑
        if (CollectionUtils.isNotEmpty(unbindRoleNames)) {
            for (String unbindRoleName : unbindRoleNames) {
                JzRoleAppBindDTO unbindDTO = JzRoleAppBindDTO.builder().applicationIds(Collections.singletonList(jzAppBindRoleDTO.getApplicationId())).roleName(unbindRoleName).build();
                OpenApiDTO unbindApi = OpenApiDTO.builder()
                        .sendReqObject(unbindDTO)
                        .modelCode(OpenApiEnum.UNBIND_ROLE_APP.getModelCode())
                        .interfaceCode(OpenApiEnum.UNBIND_ROLE_APP.getInterfaceCode())
                        .token(ExtApiUtil.getTokenBasedOnContext())
                        .build();
                openApiConfigService.callOpenUrl(unbindApi);
            }
        }
    }

    private void unbindRolePermission(List<SysRoleEntity> unbindRoleEntities, AppOnlineEntity appOnlineEntity) {
        if (CollectionUtils.isEmpty(unbindRoleEntities)) {
            return;
        }
        for (SysRoleEntity roleEntity : unbindRoleEntities) {
            // 获取菜单权限下的按钮权限
            List<String> permissionIds = sysPermissionService.lambdaQuery().eq(SysPermissionEntity::getParentId, appOnlineEntity.getPermissionId()).list().stream().map(SysPermissionEntity::getId).collect(Collectors.toList());
            permissionIds.add(appOnlineEntity.getPermissionId());
            sysPermissionService.unbindRolePermission(RolePermissionBindDTO.builder().roleName(roleEntity.getName()).permissionIds(permissionIds).build());
        }
    }

    /**
     * 绑定角色权限
     */
    private void bindRolePermission(List<SysRoleEntity> bindRoleEntities, AppOnlineEntity appOnlineEntity) {
        if (CollectionUtils.isEmpty(bindRoleEntities)) {
            return;
        }
        for (SysRoleEntity roleEntity : bindRoleEntities) {
            // 获取菜单权限下的按钮权限
            List<String> permissionIds = sysPermissionService.lambdaQuery().eq(SysPermissionEntity::getParentId, appOnlineEntity.getPermissionId()).list().stream().map(SysPermissionEntity::getId).collect(Collectors.toList());
            permissionIds.add(appOnlineEntity.getPermissionId());
            sysPermissionService.bindRolePermission(RolePermissionBindDTO.builder().roleName(roleEntity.getName()).permissionIds(permissionIds).build());
        }
    }


    public void jzMappingAppRoleMessage(List<ConsumerRecord<?, ?>> records) {
        try {
            for (ConsumerRecord<?, ?> record : records) {
                JzMsgResponseDTO jzResponseDTO = JSONObject.parseObject((String) record.value(), JzMsgResponseDTO.class);
                // 密钥，如果不为空，则不处理精制发送的消息，避免和精制的套娃
                if (StringUtils.isNotBlank(jzResponseDTO.getAccessKeyId())) {
                    continue;
                }
                Object date = jzResponseDTO.getData();
                if (date == null) {
                    continue;
                }
                JSONObject objects = JSONObject.parseObject(JSON.toJSONString(jzResponseDTO.getData()));
                if (objects == null) {
                    continue;
                }
                JzRoleBindAppDTO jzAppBindRoleDTO = objects.toJavaObject(JzRoleBindAppDTO.class);
                String operation = jzResponseDTO.getOperation();
                switch (operation) {
                    case UPDATE: {
                        SysRoleEntity role = sysRoleService.lambdaQuery().eq(SysRoleEntity::getName, jzAppBindRoleDTO.getRoleName()).last("limit 1").one();
                        if (role == null) {
                            return;
                        }
                        // 绑定角色权限
                        if (CollectionUtils.isNotEmpty(jzAppBindRoleDTO.getBindApplicationIds())) {
                            List<String> bindPermissionIds = appOnlineService.lambdaQuery().in(AppOnlineEntity::getApplicationId, jzAppBindRoleDTO.getBindApplicationIds()).list().stream().map(AppOnlineEntity::getPermissionId).collect(Collectors.toList());
                            // 获取菜单权限下的按钮权限
                            List<String> permissionIds = sysPermissionService.lambdaQuery().in(SysPermissionEntity::getParentId, bindPermissionIds).list().stream().map(SysPermissionEntity::getId).collect(Collectors.toList());
                            permissionIds.addAll(bindPermissionIds);
                            sysPermissionService.bindRolePermission(RolePermissionBindDTO.builder().roleName(role.getName()).permissionIds(permissionIds).build());
                        }
                        // 解绑角色权限
                        if (CollectionUtils.isNotEmpty(jzAppBindRoleDTO.getUnbindApplicationIds())) {
                            List<String> unbindPermissionIds = appOnlineService.lambdaQuery().in(AppOnlineEntity::getApplicationId, jzAppBindRoleDTO.getUnbindApplicationIds()).list().stream().map(AppOnlineEntity::getPermissionId).collect(Collectors.toList());
                            // 获取菜单权限下的按钮权限
                            List<String> permissionIds = sysPermissionService.lambdaQuery().in(SysPermissionEntity::getParentId, unbindPermissionIds).list().stream().map(SysPermissionEntity::getId).collect(Collectors.toList());
                            permissionIds.addAll(unbindPermissionIds);
                            sysPermissionService.unbindRolePermission(RolePermissionBindDTO.builder().roleName(role.getName()).permissionIds(permissionIds).build());
                        }
                    }
                    default:
                        break;
                }
            }
        } catch (Exception e) {
            log.error("kafka处理过程中发生异常", e);
        }
    }

    public void valueChainMessage(List<ConsumerRecord<?, ?>> records) {
        for (ConsumerRecord<?, ?> consumerRecord : records) {
            log.info("接收到kafka消息：主题 - {} ；分区 - {}", consumerRecord.topic(), consumerRecord.partition());
            log.debug("数据 - {}", consumerRecord.value());
            //消息处理
            try {
                DfsStandardMessageEntity kafkaCommandEntity = JacksonUtil.parseObject(consumerRecord.value().toString(), DfsStandardMessageEntity.class);
                //处理kafka消息
                ValueChainMessageService messageService = valueChainMap.get(kafkaCommandEntity.getMessageModel() + "MsgService");
                if (messageService != null) {
                    messageService.dealMessage(kafkaCommandEntity);
                    continue;
                }
                log.debug("未找到该消息类型的具体处理实现类，消息内容：{}", consumerRecord.value());
            } catch (Exception e) {
                log.error("消息处理错误，消息：{}", consumerRecord.value(), e);
            }
        }

    }
}









