package com.yelink.inner.service.impl.kafka;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.RedisKeyPrefix;
import com.yelink.dfs.constant.alarm.AlarmConstant;
import com.yelink.dfs.constant.alarm.AlarmDealStateEnum;
import com.yelink.dfs.constant.alarm.AlarmRecoveryTypeEnum;
import com.yelink.dfs.constant.alarm.AlarmTypesEnum;
import com.yelink.dfs.constant.sensor.SensorParamEnum;
import com.yelink.dfs.entity.alarm.AlarmDefinitionEntity;
import com.yelink.dfs.entity.alarm.AlarmEntity;
import com.yelink.dfs.entity.code.ProductFlowCodeEntity;
import com.yelink.dfs.entity.defect.DefectDefineEntity;
import com.yelink.dfs.entity.device.DeviceEntity;
import com.yelink.dfs.entity.device.DeviceSensorEntity;
import com.yelink.dfs.entity.target.TargetModelEntity;
import com.yelink.dfs.mapper.target.TargetModelMapper;
import com.yelink.dfs.service.alarm.AlarmDefinitionService;
import com.yelink.dfs.service.alarm.AlarmNotificationService;
import com.yelink.dfs.service.alarm.AlarmService;
import com.yelink.dfs.service.code.ProductFlowCodeService;
import com.yelink.dfs.service.common.CommonService;
import com.yelink.dfs.service.common.DictService;
import com.yelink.dfs.service.common.RedisService;
import com.yelink.dfs.service.defect.DefectSchemeService;
import com.yelink.dfs.service.device.DeviceSensorService;
import com.yelink.dfs.service.device.DeviceService;
import com.yelink.dfs.service.impl.common.WorkPropertise;
import com.yelink.dfs.service.order.RecordWorkOrderUnqualifiedService;
import com.yelink.dfs.service.target.TargetModelService;
import com.yelink.dfs.service.work.calendar.WorkCalendarService;
import com.yelink.dfscommon.constant.ModelEnum;
import com.yelink.dfscommon.dto.dfs.DefectDefineDTO;
import com.yelink.dfscommon.dto.dfs.DefectRecordDTO;
import com.yelink.dfscommon.utils.CommonUtils;
import com.yelink.dfscommon.utils.ParserSensorDataUtil;
import com.yelink.inner.data.access.JsonObjectEntity;
import com.yelink.inner.data.access.SensorValueTemplate;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.core.RedisOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.SessionCallback;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.data.redis.support.atomic.RedisAtomicInteger;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * kafka公共方法
 * @Date 2021/5/24 19:59
 */
@Slf4j
@AllArgsConstructor
@Service
public class KafkaCommonMethod {
    public static final String RUN = "1.0";
    public static final String STOP = "0.0";
    private RedisService redisService;
    private DeviceSensorService deviceSensorService;
    private RedisTemplate redisTemplate;
    private WorkPropertise workPropertise;
    private AlarmService alarmService;
    private AlarmNotificationService alarmNotificationService;
    private DeviceService deviceService;
    private AlarmDefinitionService alarmDefinitionService;
    private DictService dictService;
    private CommonService commonService;
    private TargetModelMapper targetModelMapper;
    private WorkCalendarService workCalendarService;
    private DefectSchemeService schemeService;
    private RecordWorkOrderUnqualifiedService unqualifiedService;
    private ProductFlowCodeService productFlowCodeService;
    private static final String SENSOR_STATE_PREFIX = ModelEnum.SENSOR.name().toUpperCase() + RedisKeyPrefix._STATE_;
    private TargetModelService targetModelService;


    public String getSensorValue(JsonObjectEntity jsonDataEntity) {
        JSONObject params = jsonDataEntity.getParams();
        List<SensorValueTemplate> sensorValueList = new ArrayList<>();
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            String key = entry.getKey();
            String value = String.valueOf(entry.getValue());
            SensorParamEnum sensorParamEnum = SensorParamEnum.getByEname(key);
            if (sensorParamEnum != null) {
                sensorValueList.add(new SensorValueTemplate(sensorParamEnum, value));
            }
        }
        return JSONObject.toJSONString(sensorValueList);
    }

    @Async
    public void pushRedisQueue(JsonObjectEntity jsonDataEntity, String sensorValue) {
        // 获取采集器绑定的生产设备（可能为一对多）
        String eui = jsonDataEntity.getDevName();
        List<Integer> list = deviceSensorService.getDeviceIdsBySensor(eui);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        List<SensorValueTemplate> sensorValueList = JSONArray.parseArray(sensorValue, SensorValueTemplate.class);
        List<String> collect = sensorValueList.stream().map(SensorValueTemplate::getEname).collect(Collectors.toList());
        List<Map.Entry<String, String>> entryList = new ArrayList<>();
        for (Integer deviceId : list) {
            Integer modelId = deviceService.getModelByDeviceId(deviceId);

            //获取需要实时记录的指标名
            List<String> constantTargetList = targetModelMapper.getConstantTargetList(modelId);

            if (CollectionUtils.isEmpty(constantTargetList)) {
                // 对每一条绑定的数据推送到不同的队列中
                // 存储到Redis，key为eui + deviceId
                String key = RedisKeyPrefix.DEVICE_SENSOR_VALUE + eui + "_" + deviceId;
                entryList.add(new AbstractMap.SimpleEntry<>(key, sensorValue));
                continue;
            }
            for (String target : constantTargetList) {
                //判断是否如果包含实时记录指标
                if (collect.contains(target)) {
                    DeviceEntity deviceEntity = deviceService.getById(deviceId);
                    commonService.dealTargetData(sensorValueList, deviceEntity, jsonDataEntity.getDate());
                    break;
                }
            }
        }
        if (CollectionUtils.isEmpty(entryList)) {
            return;
        }
        redisTemplate.executePipelined(new SessionCallback() {
            @Override
            public Object execute(RedisOperations operations) throws DataAccessException {
                for (Map.Entry<String, String> entry : entryList) {
                    redisTemplate.opsForValue().set(entry.getKey(), entry.getValue());
                }
                return null;
            }
        });
    }

    /**
     * 同一个设备的指标可能不会同时采集，通过推送到不同的队列生成指标
     *
     * @param jsonDataEntity
     * @param sensorValue
     * @param infix          一般为指标名
     */
    public void pushRedisQueueByInfix(JsonObjectEntity jsonDataEntity, String sensorValue, String infix) {
        List<DeviceSensorEntity> list = deviceSensorService.getDeviceSensorEntity(jsonDataEntity.getDevName());
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        // 存储到Redis，key为类型标识 + eui，只取一条数据
        String key = RedisKeyPrefix.DEVICE_SENSOR_VALUE + "_" + infix + "_" + list.get(0).getEui();
        redisService.pushData(key, sensorValue);
    }

    /**
     * 扬子江处理状态
     *
     * @param
     * @return
     */
    public Integer dealState(String eui, String sensorValue) {
        //此处传感器状态与设备状态值code一致
        Integer state = getState(eui, sensorValue);
        updateDeviceStateByEui(eui, state);
        return state;
    }

    private Integer getState(String eui, String sensorValue) {
        ValueOperations valueOperations = redisTemplate.opsForValue();
        List<SensorValueTemplate> sensorValueTemplates = JSON.parseArray(sensorValue, SensorValueTemplate.class);
        for (SensorValueTemplate template : sensorValueTemplates) {
            if (SensorParamEnum.RUNNING_STATE.getEname().equals(template.getEname())) {
                if (template.getValue().equals(Constant.YES)) {
                    valueOperations.set(SENSOR_STATE_PREFIX + eui, workPropertise.getSensorRunning(), workPropertise.getMaxCounterInterval(), TimeUnit.MILLISECONDS);
                    return workPropertise.getSensorRunning();
                }
            }
            if (SensorParamEnum.PAUSE_STATE.getEname().equals(template.getEname())) {
                if (template.getValue().equals(Constant.YES)) {
                    valueOperations.set(SENSOR_STATE_PREFIX + eui, workPropertise.getSensorPause(), workPropertise.getMaxCounterInterval(), TimeUnit.MILLISECONDS);
                    return workPropertise.getSensorPause();
                }
            }
            if (SensorParamEnum.DEVICE_STATE.getEname().equals(template.getEname())) {
                String value = template.getValue();
                int state = Integer.parseInt(value);
                valueOperations.set(SENSOR_STATE_PREFIX + eui, state, workPropertise.getMaxCounterInterval(), TimeUnit.MILLISECONDS);
                return state;
            }
            // 可能存在三种状态都为0的状态，如果存在，则都归为停止状态
            /*if (SensorParamEnum.STOP_STATE.getEname().equals(template.getEname())) {
                valueOperations.set(SENSOR_STATE_PREFIX + eui, workPropertise.getSensorStop(), workPropertise.getMaxCounterInterval(), TimeUnit.MILLISECONDS);
                return workPropertise.getSensorStop();
            }*/
        }
        return workPropertise.getSensorStop();
    }

    /**
     * 通过eui查询所绑定的设备，更新设备状态
     *
     * @param eui
     * @param state
     */
    public void updateDeviceStateByEui(String eui, Integer state) {
        LambdaQueryWrapper<DeviceSensorEntity> qw = new LambdaQueryWrapper<>();
        qw.eq(DeviceSensorEntity::getEui, eui);
        List<DeviceSensorEntity> list = deviceSensorService.list(qw);
        Date date = new Date();
        //修改所有绑定的设备的状态
        for (DeviceSensorEntity entity : list) {
            DeviceEntity deviceEntity = deviceService.getById(entity.getDeviceId());
            //如果状态有变更需要记录变更时间
            if (state.equals(deviceEntity.getState())) {
                deviceService.updateById(DeviceEntity.builder().deviceId(entity.getDeviceId()).state(state).build());
            } else {
                deviceService.updateById(DeviceEntity.builder().deviceId(entity.getDeviceId()).state(state).stateUpdateTime(date).build());
            }
        }
    }

    /**
     * LCM处理状态
     *
     * @param eui
     * @param sensorValue
     */
    public void dealLcmState(String eui, String sensorValue) {
        ValueOperations valueOperations = redisTemplate.opsForValue();
        List<SensorValueTemplate> sensorValueTemplates = JSON.parseArray(sensorValue, SensorValueTemplate.class);
        for (SensorValueTemplate template : sensorValueTemplates) {
            if (SensorParamEnum.DEVICE_STATE.getEname().equals(template.getEname())) {
                if (template.getValue().equals(RUN)) {
                    valueOperations.set(SENSOR_STATE_PREFIX + eui, workPropertise.getSensorRunning(), workPropertise.getMaxCounterInterval(), TimeUnit.MILLISECONDS);
                } else if (template.getValue().equals(STOP)) {
                    valueOperations.set(SENSOR_STATE_PREFIX + eui, workPropertise.getSensorStop(), workPropertise.getMaxCounterInterval(), TimeUnit.MILLISECONDS);
                }
            }
        }
    }

    /**
     * 处理奥德485采集器状态
     *
     * @param eui         运行状态（0自动状态1自动运行2停止中3手动状态)
     *                    只有自动运行是在工作，其他三种设备状态都属于停止
     * @param sensorValue
     */
    public void dealAodeCollectorState(String eui, String sensorValue) {
        ValueOperations valueOperations = redisTemplate.opsForValue();
        List<SensorValueTemplate> sensorValueTemplates = JSON.parseArray(sensorValue, SensorValueTemplate.class);
        Integer state = workPropertise.getSensorStop();
        for (SensorValueTemplate template : sensorValueTemplates) {
            if (SensorParamEnum.DEVICE_STATE.getEname().equals(template.getEname())) {
                if (template.getValue().equals(Constant.AUTOMATIC_OPERATION)) {
                    state = workPropertise.getSensorRunning();
                    valueOperations.set(SENSOR_STATE_PREFIX + eui, workPropertise.getSensorRunning(), workPropertise.getMaxCounterInterval(), TimeUnit.MILLISECONDS);
                } else if (template.getValue().equals(Constant.AUTOMATIC_STATUS)
                        || template.getValue().equals(Constant.STOPPING)
                        || template.getValue().equals(Constant.MANUAL_STATUS)) {
                    state = workPropertise.getSensorStop();
                    valueOperations.set(SENSOR_STATE_PREFIX + eui, workPropertise.getSensorStop(), workPropertise.getMaxCounterInterval(), TimeUnit.MILLISECONDS);
                }
            }
        }
        updateDeviceStateByEui(eui, state);
    }

    /**
     * 旧的的告警处理方法，如果能保证一个采集器对应一个个设备的话可以采用该方法
     *
     * @param jsonDataEntity
     * <AUTHOR>
     */
    public void occurAlarm(JsonObjectEntity jsonDataEntity) {
        JSONObject params = jsonDataEntity.getParams();
        Date date = jsonDataEntity.getDate() == null ? new Date() : jsonDataEntity.getDate();

        Map<String, String> abnormalInformationMap = filterAbnormalInformation(params);

        for (Map.Entry<String, Object> stringObjectEntry : params.entrySet()) {
            String devName = jsonDataEntity.getDevName();
            //1.查询是否有此定义的告警描述
            AlarmDefinitionEntity alarmDefinitionEntity = alarmDefinitionService.getOneByAlarmDefinitionCode(stringObjectEntry.getKey());
            if (alarmDefinitionEntity == null) {
                continue;
            }
            if (Double.parseDouble(stringObjectEntry.getValue().toString()) == Constant.KAFKA_COMMON_DEVICE_ALARM_OCCUR) {
                //iot上的告警数据
                //告警上报处理
                //找到告警定义详情
                String alarmDefinitionName = alarmDefinitionEntity.getAlarmDefinitionName();
                String abnormalInformation = abnormalInformationMap.get(stringObjectEntry.getKey());
                alarmDefinitionEntity.setAbnormalInformation(abnormalInformation);
                // 获取采集器eui
                String sensorEui = jsonDataEntity.getDevName();
                // 通过采集器eui获取绑定的生产设备
                LambdaQueryWrapper<DeviceSensorEntity> deviceSensorWrapper = new LambdaQueryWrapper<>();
                deviceSensorWrapper.eq(DeviceSensorEntity::getEui, sensorEui);
                List<DeviceSensorEntity> list = deviceSensorService.list(deviceSensorWrapper);
                for (DeviceSensorEntity deviceSensorEntity : list) {
                    DeviceEntity deviceEntity = deviceService.getById(deviceSensorEntity.getDeviceId());
                    if (deviceEntity == null) {
                        continue;
                    }
                    //判断是否在工作时间段内，不在时间段内不产生告警
                    boolean inWorkTime = workCalendarService.inWorkTime(deviceEntity.getDeviceId(), ModelEnum.DEVICE.getType(), date);
                    if (!inWorkTime) {
                        return;
                    }
                    //处理正常告警
                    //聚合码(如果iot有上报异常消息，则取异常消息生成聚合码)
                    String elfHash = StringUtils.isNotBlank(abnormalInformation) ? abnormalInformation : alarmDefinitionEntity.getAlarmDefinitionCode();
                    String aggregateCode = Constant.DEVICE_ALARM + deviceEntity.getDeviceId() + ParserSensorDataUtil.ELFHash(elfHash);
                    //插入一个key到redis中（处理告警上报的周期）
                    String redisKey = CommonUtils.getAlarmKey(aggregateCode, Constant.TEMP);
                    //需要先判断缓存中是否已经有这个数据
                    Integer num = (Integer) redisTemplate.opsForValue().get(redisKey);
                    if (num == null) {
                        redisTemplate.opsForValue().set(redisKey, 1);
                        num = 1;
                    }
                    //告警周期
                    Integer alarmCycle = dictService.getAlarmCycleByCode(AlarmTypesEnum.FAULT_ALARM.getCode(), Constant.ALARM_CYCLE_SETTING);
                    if (num < alarmCycle) {
                        //给临时告警累加次数
                        RedisAtomicInteger redisAtomicInteger = new RedisAtomicInteger(redisKey, redisTemplate.getConnectionFactory());
                        redisAtomicInteger.getAndIncrement();
                    } else {
                        //上报告警周期大于3时则真是上报告警
                        deviceEntity.setAlarmDesc(alarmDefinitionName);
                        alarmService.dealDeviceAutoReportAlarm(deviceEntity, null, null, sensorEui, alarmDefinitionEntity);
                        redisTemplate.delete(redisKey);
                    }

                }
            } else {
                //处理恢复告警
                dealRecoveryAlarm(devName, alarmDefinitionEntity);
            }
        }
    }

    /**
     * 过滤出带_content后缀的字段，组成map<alarmId,异常信息>
     *
     * @param params
     * @return
     */
    private Map<String, String> filterAbnormalInformation(JSONObject params) {
        Map<String, String> abnormalMap = new HashMap<>(16);
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            if (entry.getKey().contains(AlarmConstant.ALARM_CONTENT_SUFFIX)) {
                abnormalMap.put(entry.getKey().replace(AlarmConstant.ALARM_CONTENT_SUFFIX, ""), String.valueOf(entry.getValue()));
                params.remove(entry.getKey());
            }
        }
        return abnormalMap;
    }

    /**
     * 处理恢复告警
     *
     * @param devName
     * @param alarmDefinitionEntity
     */
    private void dealRecoveryAlarm(String devName, AlarmDefinitionEntity alarmDefinitionEntity) {
        Date now = new Date();
        // 通过采集器eui获取绑定的生产设备
        List<DeviceSensorEntity> list = deviceSensorService.getDeviceSensorEntity(devName);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (DeviceSensorEntity deviceSensorEntity : list) {
            DeviceEntity deviceEntity = deviceService.getById(deviceSensorEntity.getDeviceId());
            if (deviceEntity == null) {
                continue;
            }
            //找出redis中的key  看是否存在 存在则有告警并使其对应的value自增1  不存在则没有告警
            //聚合码
            String aggregateCode = Constant.DEVICE_ALARM + deviceEntity.getDeviceId() + ParserSensorDataUtil.ELFHash(alarmDefinitionEntity.getAlarmDefinitionCode());
            String alarmKey = CommonUtils.getAlarmKey(aggregateCode, Constant.REAL);
            Object o = redisTemplate.opsForValue().get(alarmKey);
            if (o == null) {
                continue;
            }
            //恢复告警周期
            Integer alarmCycle = dictService.getAlarmCycleByCode(AlarmTypesEnum.RECOVERY_ALARM.getCode(), Constant.ALARM_CYCLE_SETTING);
            if ((int) o < alarmCycle) {
                RedisAtomicInteger redisAtomicInteger = new RedisAtomicInteger(alarmKey, Objects.requireNonNull(redisTemplate.getConnectionFactory()));
                redisAtomicInteger.getAndIncrement();
            } else {
                //通过告警聚合码找出对应的告警信息
                AlarmEntity alarmEntity = alarmService.getDetail(aggregateCode);
                if (alarmEntity != null) {
                    alarmEntity.setAlarmType(AlarmTypesEnum.RECOVERY_ALARM.getCode());
                    alarmEntity.setAlarmUpdateTime(now);
                    alarmEntity.setAlarmRecoveryTime(now);
                    alarmEntity.setAlarmRecoveryType(AlarmRecoveryTypeEnum.AUTOMATIC_RECOVERY.getCode());
                    alarmEntity.setDealState(AlarmDealStateEnum.DEALED_ALARM.getStateCode());
                    alarmService.updateById(alarmEntity);
                    //删除缓存信息
                    redisTemplate.delete(alarmKey);
                    //通知处理人
                    alarmNotificationService.pushNotice(alarmEntity);
                }
            }
        }
    }


    /**
     * Lcm点胶机状态处理
     *
     * @param eui
     * @param sensorValue
     */
    public void dealDispensingMachineState(String eui, String sensorValue) {
        ValueOperations valueOperations = redisTemplate.opsForValue();
        List<SensorValueTemplate> sensorValueTemplates = JSON.parseArray(sensorValue, SensorValueTemplate.class);
        for (SensorValueTemplate sensorValueTemplate : sensorValueTemplates) {
            if (SensorParamEnum.STOP_STATUS.getEname().equals(sensorValueTemplate.getEname())) {
                if (sensorValueTemplate.getValue().equals(Constant.YES)) {
                    valueOperations.set(SENSOR_STATE_PREFIX + eui, workPropertise.getSensorStop(), workPropertise.getMaxCounterInterval(), TimeUnit.MILLISECONDS);
                    break;
                }
            }
            //三种状态 只要有一个为启动状态则认为设备为启动状态
            if (SensorParamEnum.LEFT_STARTUP_STATUS.getEname().equals(sensorValueTemplate.getEname())
                    || SensorParamEnum.RIGHT_STARTUP_STATUS.getEname().equals(sensorValueTemplate.getEname())
                    || SensorParamEnum.AUTO_STARTUP_STATUS.getEname().equals(sensorValueTemplate.getEname())) {
                if (sensorValueTemplate.getValue().equals(Constant.YES)) {
                    valueOperations.set(SENSOR_STATE_PREFIX + eui, workPropertise.getSensorRunning(), workPropertise.getMaxCounterInterval(), TimeUnit.MILLISECONDS);
                    break;
                }
            }

        }
    }

    /**
     * 添加工位质检记录
     */
    public void addUnqualifiedRecord(Integer fid, String barCode, Integer quantity) {
        //拿到工位质检方案
        List<DefectDefineDTO> defectDefineDTOS = new ArrayList<>();
        Map<String, List<DefectDefineEntity>> map = schemeService.getSchemeByFid(fid);
        if (CollectionUtils.isEmpty(map)) {
            log.error("工位{}无质检方案", fid);
            return;
        }
        for (String key : map.keySet()) {
            List<DefectDefineEntity> temp = map.get(key);
            for (DefectDefineEntity defectDefineEntity : temp) {
                DefectDefineDTO defectDefineDTO = DefectDefineDTO.builder()
                        .defectId(defectDefineEntity.getDefectId())
                        .defectName(defectDefineEntity.getDefectName())
                        .defectType(defectDefineEntity.getDefectType())
                        .schemeId(defectDefineEntity.getSchemeId())
                        .build();
                defectDefineDTOS.add(defectDefineDTO);
            }
        }
        ProductFlowCodeEntity productFlowCodeEntity = productFlowCodeService.getByProductFlowCode(barCode);
        if (productFlowCodeEntity == null) {
            log.error("{}条码无效", barCode);
            return;
        }
        DefectRecordDTO dto = DefectRecordDTO.builder()
                .barCode(barCode)
                .fid(fid)
                .defines(defectDefineDTOS)
                .quantity(quantity)
                .remark("工位自动上报")
                .workOrder(productFlowCodeEntity.getRelationNumber())
                .build();
        //添加工位不良检测记录
        unqualifiedService.saveRecord(dto, null);
    }

    public String getSensorValueByTargetModel(JsonObjectEntity jsonDataEntity) {
        JSONObject params = jsonDataEntity.getParams();
        List<SensorValueTemplate> sensorValueList = new ArrayList<>();
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            String key = entry.getKey();
            String value = String.valueOf(entry.getValue());

            TargetModelEntity targetModelEntity = targetModelService.getByTargetNameMap(key);

            SensorValueTemplate build = SensorValueTemplate.builder()
                    .ename(key).value(value).build();
            sensorValueList.add(build);
            if (targetModelEntity != null) {
                build.setName(targetModelEntity.getTargetCnname());
            }
        }
        return JSONObject.toJSONString(sensorValueList);
    }
}
