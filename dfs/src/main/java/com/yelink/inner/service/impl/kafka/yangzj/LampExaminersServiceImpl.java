package com.yelink.inner.service.impl.kafka.yangzj;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yelink.dfs.config.LampExaminersAddress;
import com.yelink.dfs.config.LeakPickerAddress;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.alarm.AlarmAddTypeEnum;
import com.yelink.dfs.constant.sensor.SensorParamEnum;
import com.yelink.dfs.constant.sensor.SensorTypeCodeEnum;
import com.yelink.dfs.entity.alarm.AlarmDefinitionEntity;
import com.yelink.dfs.entity.device.DeviceEntity;
import com.yelink.dfs.entity.device.DeviceSensorEntity;
import com.yelink.dfs.entity.device.dto.EquipmentDockingAlarmDO;
import com.yelink.dfs.entity.device.dto.EquipmentDockingDO;
import com.yelink.dfs.entity.sensor.SensorEntity;
import com.yelink.dfs.entity.target.AlarmModelEntity;
import com.yelink.dfs.service.alarm.AlarmDefinitionService;
import com.yelink.dfs.service.alarm.AlarmService;
import com.yelink.dfs.service.device.DeviceSensorService;
import com.yelink.dfs.service.device.DeviceService;
import com.yelink.dfs.service.target.AlarmModelService;
import com.yelink.dfscommon.utils.MathUtil;
import com.yelink.inner.constant.iot.DeviceAlarmEnum;
import com.yelink.inner.data.access.JsonObjectEntity;
import com.yelink.inner.data.access.SensorValueTemplate;
import com.yelink.inner.provider.SensorHandler;
import com.yelink.inner.service.SensorDataByKafkaService;
import com.yelink.inner.service.impl.kafka.KafkaCommonMethod;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Vector;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 灯检机数据处理
 * @Date 2021/5/24 19:26
 */
@Slf4j
@AllArgsConstructor
@Service("lampExaminers")
@ConditionalOnProperty(value = "factory", havingValue = "yzj", matchIfMissing = true)
public class LampExaminersServiceImpl implements SensorDataByKafkaService {
    @Autowired
    private SensorHandler sensorHandler;
    @Autowired
    private KafkaCommonMethod kafkaCommonMethod;
    @Autowired
    private LampExaminersAddress lampExaminersAddress;
    @Autowired
    private DeviceSensorService deviceSensorService;
    @Autowired
    private DeviceService deviceService;
    @Autowired
    private AlarmService alarmService;
    @Autowired
    private LeakPickerServiceImpl leakPickerServiceImpl;
    @Autowired
    private LeakPickerAddress leakPickerAddress;
    @Autowired
    private AlarmDefinitionService alarmDefinitionService;
    @Autowired
    private AlarmModelService alarmModelService;

    private static final ConcurrentHashMap<String, Vector<EquipmentDockingDO>> MAP = new ConcurrentHashMap<>();

    @Override
    public void converterData(JsonObjectEntity jsonDataEntity) {
        // 获取sensorValue值
        JSONObject params = jsonDataEntity.getParams();
        JSONArray jsonArray = params.getJSONArray(Constant.LIST);
        Vector<EquipmentDockingDO> equipmentDockingDOS = MAP.get(jsonDataEntity.getDevName());
        if (equipmentDockingDOS == null) {
            equipmentDockingDOS = new Vector<>();
            MAP.put(jsonDataEntity.getDevName(), equipmentDockingDOS);
        }
        equipmentDockingDOS.addAll(jsonArray.toJavaList(EquipmentDockingDO.class));
        if (jsonArray.size() == 100) {
            return;
        }
        log.info( "{}聚合后的数据条数：{}" ,jsonDataEntity.getDevName() ,equipmentDockingDOS.size());
        //重置concurrentHashMap中的集合
        MAP.put(jsonDataEntity.getDevName(), new Vector<>());

        // 获取灯检配置文件的对象
        Map<String, String> maps = lampExaminersAddress.getMapping();
        // 获取捡漏机配置文件的对象
        Map<String, String> leakMaps = leakPickerAddress.getMapping();
        Map<String, EquipmentDockingDO> equipmentDockingMap = new HashMap<>();
        // 获取设备指标list列表
        //JSONArray jsonArray = params.getJSONArray(Constant.LIST);
        /*List<EquipmentDockingDO> equipmentDockingDOS = */
        JSONArray.parseArray(jsonArray.toJSONString(), EquipmentDockingDO.class);

        //灯检所有数据包含子批次，每个子批次key重复，将重复的key对应的值进行累加合成一个对象
        List<EquipmentDockingDO> collect = equipmentDockingDOS.stream().collect(Collectors.toMap(EquipmentDockingDO::getFldCounterId, a -> a, (o1, o2) -> {
            Double sum = Double.valueOf(o1.getFldValue()) + Double.valueOf(o2.getFldValue());
            Double percent = (Double.valueOf(o1.getFldPercent()) + Double.valueOf(o2.getFldPercent())) / 2;
            o1.setFldValue(String.valueOf(sum.intValue()));
            o1.setFldPercent(String.valueOf(MathUtil.round(percent, 2)));
            return o1;
        })).values().stream().collect(Collectors.toList());


        collect.forEach(equipmentDockingDO -> equipmentDockingMap.put(equipmentDockingDO.getFldCounterId(), equipmentDockingDO));

        List<SensorValueTemplate> sensorValueList = new ArrayList<>();
        List<SensorValueTemplate> leakPickerSensorValueList = new ArrayList<>();
        sensorValueList.add(new SensorValueTemplate(SensorParamEnum.BATCH_ID, equipmentDockingDOS.get(0).getFldBatchId()));
        leakPickerSensorValueList.add(new SensorValueTemplate(SensorParamEnum.BATCH_ID, equipmentDockingDOS.get(0).getFldBatchId()));
        // 捡漏机设备指标
        for (Map.Entry<String, String> entry : leakMaps.entrySet()) {
            EquipmentDockingDO aDo = equipmentDockingMap.get(entry.getValue());
            if (aDo == null) {
                continue;
            }
            String value;
            // value当值
            value = aDo.getFldValue();
            SensorParamEnum eName = SensorParamEnum.getByEname(entry.getKey());
            if (eName != null) {
                leakPickerSensorValueList.add(new SensorValueTemplate(eName, value, aDo.getFldBatchId()));
            }
        }
        String leakPickerSensorValue = JSONObject.toJSONString(leakPickerSensorValueList);
        // 推送捡漏机的数据
        leakPickerServiceImpl.messagePush(jsonDataEntity, leakPickerSensorValue);

        // 灯检设备指标
        for (Map.Entry<String, String> entry : maps.entrySet()) {
            EquipmentDockingDO aDo = equipmentDockingMap.get(entry.getValue());
            if (aDo == null) {
                continue;
            }
            String entryKey = entry.getKey();
            String value;
            // 如果地址相同，取百分比字段或值字段当值
            if (entryKey.equals(SensorParamEnum.TV_ONE_SCRAP_NUMBER.getEname())) {
                setSensorList(sensorValueList, aDo, SensorParamEnum.TV_ONE_SCRAP_NUMBER, SensorParamEnum.TV_ONE_SCRAP_PERCENT);
            } else if (entryKey.equals(SensorParamEnum.TV_TWO_SCRAP_NUMBER.getEname())) {
                setSensorList(sensorValueList, aDo, SensorParamEnum.TV_TWO_SCRAP_NUMBER, SensorParamEnum.TV_TWO_SCRAP_PERCENT);
            } else if (entryKey.equals(SensorParamEnum.TV_THREE_SCRAP_NUMBER.getEname())) {
                setSensorList(sensorValueList, aDo, SensorParamEnum.TV_THREE_SCRAP_NUMBER, SensorParamEnum.TV_THREE_SCRAP_PERCENT);
            } else if (entryKey.equals(SensorParamEnum.TV_FOUR_SCRAP_NUMBER.getEname())) {
                setSensorList(sensorValueList, aDo, SensorParamEnum.TV_FOUR_SCRAP_NUMBER, SensorParamEnum.TV_FOUR_SCRAP_PERCENT);
            } else if (entryKey.equals(SensorParamEnum.TV_FIVE_SCRAP_NUMBER.getEname())) {
                setSensorList(sensorValueList, aDo, SensorParamEnum.TV_FIVE_SCRAP_NUMBER, SensorParamEnum.TV_FIVE_SCRAP_PERCENT);
            } else if (entryKey.equals(SensorParamEnum.TV_SIX_SCRAP_NUMBER.getEname())) {
                setSensorList(sensorValueList, aDo, SensorParamEnum.TV_SIX_SCRAP_NUMBER, SensorParamEnum.TV_SIX_SCRAP_PERCENT);
            } else if (entryKey.equals(SensorParamEnum.TV_SEVEN_SCRAP_NUMBER.getEname())) {
                setSensorList(sensorValueList, aDo, SensorParamEnum.TV_SEVEN_SCRAP_NUMBER, SensorParamEnum.TV_SEVEN_SCRAP_PERCENT);
            } else if (entryKey.equals(SensorParamEnum.TV_EIGHT_SCRAP_NUMBER.getEname())) {
                setSensorList(sensorValueList, aDo, SensorParamEnum.TV_EIGHT_SCRAP_NUMBER, SensorParamEnum.TV_EIGHT_SCRAP_PERCENT);
            } else if (entryKey.equals(SensorParamEnum.TV_NINE_SCRAP_NUMBER.getEname())) {
                setSensorList(sensorValueList, aDo, SensorParamEnum.TV_NINE_SCRAP_NUMBER, SensorParamEnum.TV_NINE_SCRAP_PERCENT);
            } else if (entryKey.equals(SensorParamEnum.REJECT_TRAY_ONE_NUMBER.getEname())) {
                setSensorList(sensorValueList, aDo, SensorParamEnum.REJECT_TRAY_ONE_NUMBER, SensorParamEnum.REJECT_TRAY_ONE_PERCENT);
            } else if (entryKey.equals(SensorParamEnum.REJECT_TRAY_TWO_NUMBER.getEname())) {
                setSensorList(sensorValueList, aDo, SensorParamEnum.REJECT_TRAY_TWO_NUMBER, SensorParamEnum.REJECT_TRAY_TWO_PERCENT);
            } else if (entryKey.equals(SensorParamEnum.ACCEPTED_TRAY_NUMBER.getEname())) {
                setSensorList(sensorValueList, aDo, SensorParamEnum.ACCEPTED_TRAY_NUMBER, SensorParamEnum.ACCEPTED_TRAY_PERCENT);
            } else if (entryKey.equals(SensorParamEnum.REJECT_CHANNEL_ONE_NUMBER.getEname())) {
                setSensorList(sensorValueList, aDo, SensorParamEnum.REJECT_CHANNEL_ONE_NUMBER, SensorParamEnum.REJECT_CHANNEL_ONE_PERCENT);
            } else if (entryKey.equals(SensorParamEnum.REJECT_CHANNEL_TWO_NUMBER.getEname())) {
                setSensorList(sensorValueList, aDo, SensorParamEnum.REJECT_CHANNEL_TWO_NUMBER, SensorParamEnum.REJECT_CHANNEL_TWO_PERCENT);
            } else {
                // value当值
                value = aDo.getFldValue();
                SensorParamEnum ename3 = SensorParamEnum.getByEname(entry.getKey());
                if (ename3 != null) {
                    sensorValueList.add(new SensorValueTemplate(ename3, value, aDo.getFldBatchId()));
                }
            }
        }

        /**
         获取分组个数，用于计算平均速度
         Map<String, List<EquipmentDockingDO>> listMap = equipmentDockingDOS.stream().collect(Collectors.groupingBy(EquipmentDockingDO::getFldSubBatchId));
         int size = listMap.size();
         Double lampInspectionTotal = 0.0;
         Double lampInspectionQualifiedNumber = 0.0;
         for (SensorValueTemplate sensorValueTemplate : sensorValueList) {
         //获取一次灯检总数
         if (sensorValueTemplate.getEname().equals(SensorParamEnum.LAMP_INSPECTION_TOTAL.getEname())) {
         lampInspectionTotal = Double.valueOf(sensorValueTemplate.getValue());
         }
         //获取一次灯检合格数
         if (sensorValueTemplate.getEname().equals(SensorParamEnum.LAMP_INSPECTION_QUALIFIED_NUMBER.getEname())) {
         lampInspectionQualifiedNumber = Double.valueOf(sensorValueTemplate.getValue());
         }
         }
         Double lampInspectionPassRate = lampInspectionQualifiedNumber / lampInspectionTotal;

         for (SensorValueTemplate sensorValueTemplate : sensorValueList) {
         //一次灯检合格率 重新计算
         if (sensorValueTemplate.getEname().equals(TargetParamEnum.LAMP_INSPECTION_PASS_RATE.getEname())) {
         String value;
         if (lampInspectionPassRate.isNaN()) {
         value = "--";
         } else {
         value = String.valueOf(MathUtil.round(lampInspectionPassRate, 4));
         }
         sensorValueTemplate.setValue(value);
         }
         //速度 重新计算
         if (sensorValueTemplate.getEname().equals(TargetParamEnum.SPEED.getEname())) {
         String value = sensorValueTemplate.getValue();
         Double avg = Double.valueOf(value) / size;
         sensorValueTemplate.setValue(avg.toString());
         }
         }
         */

        String sensorValue = JSONObject.toJSONString(sensorValueList);
        // 推送消息到sensor_record表、sensor表、redis
        messagePush(jsonDataEntity, sensorValue);
    }

    private void setSensorList(List<SensorValueTemplate> sensorValueList, EquipmentDockingDO aDo,
                               SensorParamEnum sensorParamEnum1, SensorParamEnum sensorParamEnum2) {
        String value;// value当值
        value = aDo.getFldValue();
        SensorParamEnum ename1 = SensorParamEnum.getByEname(sensorParamEnum1.getEname());
        if (ename1 != null) {
            sensorValueList.add(new SensorValueTemplate(ename1, value, aDo.getFldBatchId()));
        }
        // percent当值
        value = aDo.getFldPercent();
        SensorParamEnum ename2 = SensorParamEnum.getByEname(sensorParamEnum2.getEname());
        if (ename2 != null) {
            sensorValueList.add(new SensorValueTemplate(ename2, value, aDo.getFldBatchId()));
        }
    }

    /**
     * 消息推送
     *
     * @param
     * @return
     */
    private void messagePush(JsonObjectEntity jsonDataEntity, String sensorValue) {

        SensorEntity lampExaminersEntity = SensorEntity.builder()
                .eui(jsonDataEntity.getDevName())
                .sensorValue(sensorValue)
                .sensorType(SensorTypeCodeEnum.LAMP_EXAMINERS.getCode())
                .sensorTypeName(SensorTypeCodeEnum.LAMP_EXAMINERS.getName())
                .arriveTime(new Date())
                .eventTime(jsonDataEntity.getDate())
                .createTime(new Date())
                .build();
        // 推送(推送到sensor_record、sensor表)
        sensorHandler.accessOrMeasure(lampExaminersEntity);
        // 推送到队列
        kafkaCommonMethod.pushRedisQueue(jsonDataEntity, sensorValue);
        // 更新设备型号及存储到redis数据库
        sensorHandler.updateDeviceModel(jsonDataEntity, sensorValue);

    }


    @Override
    public void converterAlarm(JsonObjectEntity jsonDataEntity) {
        // 获取sensorValue值
        JSONObject params = jsonDataEntity.getParams();
        Map<String, String> maps = lampExaminersAddress.getAlarm();
        // 获取设备指标list列表
        JSONArray jsonArray = params.getJSONArray(Constant.LIST);
        List<EquipmentDockingAlarmDO> equipmentDockingDOS = JSONArray.parseArray(jsonArray.toJSONString(), EquipmentDockingAlarmDO.class);
        Map<String, EquipmentDockingAlarmDO> equipmentDockingAlarmMap = new HashMap<>();
        equipmentDockingDOS.forEach(equipmentDockingDO -> equipmentDockingAlarmMap.put(equipmentDockingDO.getFldAlarmWarningId(), equipmentDockingDO));
        // 获取deviceId
        LambdaQueryWrapper<DeviceSensorEntity> qw = new LambdaQueryWrapper<>();
        qw.eq(DeviceSensorEntity::getEui, jsonDataEntity.getDevName()).last("limit 1");
        DeviceSensorEntity deviceSensorEntity = deviceSensorService.getOne(qw);
        if (deviceSensorEntity == null) {
            return;
        }
        List<SensorValueTemplate> list = new ArrayList<>();
        for (Map.Entry<String, String> entry : maps.entrySet()) {
            EquipmentDockingAlarmDO aDo = equipmentDockingAlarmMap.get(entry.getValue());
            if (aDo == null) {
                continue;
            }
            SensorParamEnum paramEnum = SensorParamEnum.getByEname(entry.getKey());
            if (paramEnum != null) {
                list.add(new SensorValueTemplate(paramEnum, null, aDo.getFldBatchId()));
            }
            LambdaQueryWrapper<DeviceEntity> deviceWrapper = new LambdaQueryWrapper<>();
            deviceWrapper.eq(DeviceEntity::getDeviceId, deviceSensorEntity.getDeviceId());
            DeviceEntity deviceEntity = deviceService.getOne(deviceWrapper);
            if (deviceEntity == null) {
                return;
            }
            //deviceEntity.setAlarmDesc(DeviceAlarmEnum.getAlarmDescByAddress(aDo.getFldAlarmWarningId()));
            AlarmDefinitionEntity alarmDefinitionEntity;
            alarmDefinitionEntity = alarmDefinitionService.getOneByAlarmDefinitionCode(aDo.getFldAlarmWarningId());
            if (alarmDefinitionEntity == null) {
               alarmDefinitionEntity = AlarmDefinitionEntity.builder()
                        .alarmDefinitionCode(aDo.getFldAlarmWarningId())
                        .alarmClassifyName(DeviceAlarmEnum.getAlarmDescByAddress(aDo.getFldAlarmWarningId()))
                        .dataSources(AlarmAddTypeEnum.AUTO.getCode())
                        .build();
                alarmDefinitionService.save(alarmDefinitionEntity);
            }
            AlarmModelEntity alarmModelEntity = alarmModelService.getListByModelAndByAlarmDefinitionCode(deviceEntity.getModelId(), alarmDefinitionEntity.getAlarmDefinitionCode());
            if (alarmModelEntity == null) {
                return;
            }
            deviceEntity.setAlarmDesc(alarmDefinitionEntity.getAlarmDefinitionName());
            deviceEntity.setAlarmTime(aDo.getFldDate());
            alarmService.dealDeviceAutoReportAlarm(deviceEntity, JSON.toJSONString(list), null, jsonDataEntity.getDevName(), alarmDefinitionEntity);
        }

    }

}
