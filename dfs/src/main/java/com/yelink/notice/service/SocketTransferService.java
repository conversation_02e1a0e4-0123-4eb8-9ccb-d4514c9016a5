package com.yelink.notice.service;

/**
 * @description: 用来处理服务器双机情况转发小程序消息的接口
 * @author: shuang
 * @time: 2022/4/27
 */
public interface SocketTransferService {

    /**
     * 发送消息到kafka
     * @param transferTopic
     * @param toJSONString
     */
    void sendMessage(String transferTopic, String toJSONString);

    /**
     * 发送消息到websocket
     * @param topic
     * @param value
     */
    void sendWebsocket(String topic, String value);
}
