package com.yelink.dfs;

import com.yelink.dfs.service.energy.manage.EnergyTimePeriodConfigService;
import com.yelink.dfscommon.constant.EnergyTimePeriodEnum;
import com.yelink.dfscommon.utils.DateUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/7/7 15:57
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class DfsApplications {
    @Resource
    private EnergyTimePeriodConfigService energyTimePeriodConfigService;

    @Test
    public void test() {
        Date date = DateUtil.getDateByStr("2022-07-01 07:30:00");
        EnergyTimePeriodEnum timePeriodEnum = energyTimePeriodConfigService.judgeTimePeriod(date);
        System.out.println(timePeriodEnum.getCode());
    }
}
