FROM swr.cn-south-1.myhuaweicloud.com/yelink_dfs/adoptopenjdk/openjdk8:jdk8u312-b07

RUN apt-get update && apt-get install -y vim

#ARG JAR_FILE
COPY ./out/dfs/common-libs /opt/dfs/common-libs
COPY ./out/dfs/dfs-1.0.jar /opt/dfs/
#COPY ./src/main/resources/application.yml /opt/dfs/config/
COPY --from=swr.cn-south-1.myhuaweicloud.com/yelink_dfs/hengyunabc/arthas:latest /opt/arthas /opt/arthas

WORKDIR /opt/dfs
EXPOSE 8181

ARG SERVER_VERSION_VAR=1.0.0.1
ENV SERVER_VERSION=$SERVER_VERSION_VAR

CMD java ${JAVA_OPTS} -jar /opt/dfs/dfs-1.0.jar
