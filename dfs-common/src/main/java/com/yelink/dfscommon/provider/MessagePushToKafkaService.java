package com.yelink.dfscommon.provider;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfscommon.provider.constant.KafkaMessageTypeInterface;

import java.util.List;

/**
 * @description: 内部消息推送kafka接口
 * @author: shuang
 * @time: 2022/2/16
 */
public interface MessagePushToKafkaService {

    /**
     * 推送消息到doc
     *
     * @param newObj
     * @param oldObj
     * @param changeObj
     * @param kafkaMessageTypeEnum
     */
    void pushMessage(Object newObj, Object oldObj, Object changeObj, KafkaMessageTypeInterface kafkaMessageTypeEnum);

    /**
     * 推送消息到doc
     *
     * @param newObj
     * @param oldObj
     * @param kafkaMessageTypeEnum
     */
    void pushMessage(Object newObj, Object oldObj, KafkaMessageTypeInterface kafkaMessageTypeEnum);


    /**
     * 推送消息到doc
     * 该方法默认将数据推送到event_main_data上去，推荐使用新的带topic参数的方法，避免报错
     *
     * @param newObj
     * @param kafkaMessageTypeEnum
     */
    @Deprecated
    void pushNewMessage(Object newObj, KafkaMessageTypeInterface kafkaMessageTypeEnum);


    /**
     * 指定topic并推送消息
     *
     * @param newObj
     * @param topic
     * @param kafkaMessageTypeEnum
     */
    void pushNewMessage(Object newObj, String topic, KafkaMessageTypeInterface kafkaMessageTypeEnum);

    /**
     * 推送消息到doc
     *
     * @param oldObj
     * @param topic
     * @param kafkaMessageTypeEnum
     */
    void pushOldMessage(Object oldObj,String topic, KafkaMessageTypeInterface kafkaMessageTypeEnum);


    /**
     * 推送消息到doc
     *
     * @param changeObj
     * @param kafkaMessageTypeEnum
     */
    void pushChangeMessage(Object changeObj, KafkaMessageTypeInterface kafkaMessageTypeEnum);

    /**
     * 推送至kafka消息后，等待其他微服务响应，并将响应的结果转换为对象
     * 结果存储在dfs的redis中
     *
     * @param object 发送对象
     * @param topic topic主题
     * @param kafkaMessageTypeEnum 消息推送枚举
     * @param waitingTime 等待时长（秒）
     * @return
     */
    <T> T parseObjectWithRedis(Object object, String topic, KafkaMessageTypeInterface kafkaMessageTypeEnum, Integer waitingTime, Class<T> clazz);

    /**
     * 推送至kafka消息后，等待其他微服务响应，并将响应的结果转换为对象
     * 结果存储在dfs的redis中
     *
     * @param object 发送对象
     * @param topic topic主题
     * @param kafkaMessageTypeEnum 消息推送枚举
     * @param waitingTime 等待时长（秒）
     * @return
     */
    <T> List<T> parseArrayWithRedis(Object object, String topic, KafkaMessageTypeInterface kafkaMessageTypeEnum, Integer waitingTime, Class<T> clazz);

    /**
     * 推送至kafka消息后，等待其他微服务响应，并将响应的结果转换为对象
     * 结果存储在dfs的redis中
     *
     * @param object 发送对象
     * @param topic topic主题
     * @param kafkaMessageTypeEnum 消息推送枚举
     * @param waitingTime 等待时长（秒）
     * @return
     */
    <T> Page<T> parsePageWithRedis(Object object, String topic, KafkaMessageTypeInterface kafkaMessageTypeEnum, Integer waitingTime, Class<T> clazz);
}
