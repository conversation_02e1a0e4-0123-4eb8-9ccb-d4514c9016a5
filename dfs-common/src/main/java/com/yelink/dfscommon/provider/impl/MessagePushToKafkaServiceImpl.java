package com.yelink.dfscommon.provider.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfscommon.api.dfs.CommonInterface;
import com.yelink.dfscommon.constant.Constants;
import com.yelink.dfscommon.dto.KafkaDTO;
import com.yelink.dfscommon.entity.DfsStandardMessageEntity;
import com.yelink.dfscommon.provider.MessagePushToKafkaService;
import com.yelink.dfscommon.provider.constant.KafkaMessageTypeInterface;
import com.yelink.dfscommon.utils.ColumnUtil;
import com.yelink.dfscommon.utils.DateUtil;
import com.yelink.dfscommon.utils.JacksonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * @description: kafka消息发送实现
 * @author: shuang
 * @time:
 */
@ConditionalOnClass(KafkaTemplate.class)
@Slf4j
@Service
@RequiredArgsConstructor
public class MessagePushToKafkaServiceImpl implements MessagePushToKafkaService {

    private final KafkaTemplate<String, String> kafkaTemplate;
    private final RedisTemplate redisTemplate;
    private final CommonInterface commonInterface;
    private final static String LATEST_CONTENT = "latestContent";
    private final static String OLD_CONTENT = "oldContent";
    private final static String CHANGE_CONTENT = "changeContent";
    private final static String VERSION_ONE = "1.0";

    @Async
    @Override
    public void pushMessage(Object newObj, Object oldObj, Object changeObj, KafkaMessageTypeInterface kafkaMessageTypeEnum) {
        JSONObject object = new JSONObject();
        object.put(LATEST_CONTENT, newObj);
        object.put(OLD_CONTENT, oldObj);
        object.put(CHANGE_CONTENT, changeObj);

        send(Constants.KAFKA_MAIN_DATA_TOPIC, kafkaMessageTypeEnum, object, ColumnUtil.getTableId(newObj));
    }

    @Async
    @Override
    public void pushMessage(Object newObj, Object oldObj, KafkaMessageTypeInterface kafkaMessageTypeEnum) {
        JSONObject object = new JSONObject();
        object.put(LATEST_CONTENT, newObj);
        object.put(OLD_CONTENT, oldObj);

        send(Constants.KAFKA_MAIN_DATA_TOPIC, kafkaMessageTypeEnum, object, ColumnUtil.getTableId(newObj));
    }

    @Async
    @Override
    public void pushNewMessage(Object newObj, KafkaMessageTypeInterface kafkaMessageTypeEnum) {
        JSONObject object = new JSONObject();
        object.put(LATEST_CONTENT, newObj);

        send(Constants.KAFKA_MAIN_DATA_TOPIC, kafkaMessageTypeEnum, object, ColumnUtil.getTableId(newObj));
    }


    @Async
    @Override
    public void pushNewMessage(Object newObj, String topic, KafkaMessageTypeInterface kafkaMessageTypeEnum) {
        JSONObject object = new JSONObject();
        object.put(LATEST_CONTENT, newObj);

        send(topic, kafkaMessageTypeEnum, object, ColumnUtil.getTableId(newObj));
    }

    @Async
    @Override
    public void pushOldMessage(Object oldObj, String topic, KafkaMessageTypeInterface kafkaMessageTypeEnum) {
        JSONObject object = new JSONObject();
        object.put(OLD_CONTENT, oldObj);

        send(topic, kafkaMessageTypeEnum, object, ColumnUtil.getTableId(oldObj));
    }

    @Async
    @Override
    public void pushChangeMessage(Object changeObj, KafkaMessageTypeInterface kafkaMessageTypeEnum) {
        JSONObject object = new JSONObject();
        object.put(CHANGE_CONTENT, changeObj);

        send(Constants.KAFKA_MAIN_DATA_TOPIC, kafkaMessageTypeEnum, object, ColumnUtil.getTableId(changeObj));
    }

    private void send(String topic, KafkaMessageTypeInterface kafkaMessageTypeEnum, JSONObject params, String objectId) {
        //redis自增序列
        String key = Constants.EVENT_ID_INCREASE + ":" + kafkaMessageTypeEnum.getModelCode() + ":" + objectId;
        Long id = redisTemplate.opsForValue().increment(key);
        redisTemplate.expire(key, 1, TimeUnit.DAYS);

        DfsStandardMessageEntity build = DfsStandardMessageEntity.builder()
                .id(String.valueOf(id))
                .method(kafkaMessageTypeEnum.getModelCode() + Constants.SPOT + kafkaMessageTypeEnum.getTypeCode())
                .params(params)
                .date(DateUtil.dateTimeStr(new Date()))
                .version(VERSION_ONE)
                .build();

        log.info("topic:{}, 推送kafka消息:{}", topic, JSONObject.toJSONString(build));

        kafkaTemplate.send(topic, build.getMethod(), JSONObject.toJSONString(build));
    }

    /**
     * 推送至kafka消息后，等待其他微服务响应，并将响应的结果转换为对象
     * 结果存储在dfs的redis中
     *
     * @param object               发送对象
     * @param topic                topic主题
     * @param kafkaMessageTypeEnum 消息推送枚举
     * @param waitingTime          等待时长（秒）
     * @return
     */
    @Override
    public <T> T parseObjectWithRedis(Object object, String topic, KafkaMessageTypeInterface kafkaMessageTypeEnum, Integer waitingTime, Class<T> clazz) {
        Object popObject = getRedisObjectOfKafka(object, topic, kafkaMessageTypeEnum, waitingTime);
        if (popObject == null) {
            return null;
        }
        return JSONObject.parseObject(JSON.toJSONString(popObject), clazz);
    }

    private Object getRedisObjectOfKafka(Object object, String topic, KafkaMessageTypeInterface kafkaMessageTypeEnum, Integer waitingTime) {
        if (ObjectUtils.isEmpty(object)) {
            return null;
        }
        String uuid = UUID.randomUUID().toString();
        KafkaDTO kafkaDTO = KafkaDTO.builder().uuid(uuid).object(object).build();
        this.pushNewMessage(kafkaDTO, topic, kafkaMessageTypeEnum);
        if (waitingTime == null || waitingTime == 0) {
            commonInterface.removeRedisKey(uuid);
            return null;
        }
        Object popObject = JacksonUtil.getResponseObject(commonInterface.getObjectByUuid(uuid, waitingTime), Object.class);
        commonInterface.removeRedisKey(uuid);
        return popObject;
    }

    /**
     * 推送至kafka消息后，等待其他微服务响应，并将响应的结果转换为对象
     * 结果存储在dfs的redis中
     *
     * @param object               发送对象
     * @param topic                topic主题
     * @param kafkaMessageTypeEnum 消息推送枚举
     * @param waitingTime          等待时长（秒）
     * @return
     */
    @Override
    public <T> List<T> parseArrayWithRedis(Object object, String topic, KafkaMessageTypeInterface kafkaMessageTypeEnum, Integer waitingTime, Class<T> clazz) {
        Object popObject = getRedisObjectOfKafka(object, topic, kafkaMessageTypeEnum, waitingTime);
        if (popObject == null) {
            return new ArrayList<>();
        }
        return JSONObject.parseArray(JSON.toJSONString(popObject), clazz);
    }

    /**
     * 推送至kafka消息后，等待其他微服务响应，并将响应的结果转换为对象
     * 结果存储在dfs的redis中
     *
     * @param object               发送对象
     * @param topic                topic主题
     * @param kafkaMessageTypeEnum 消息推送枚举
     * @param waitingTime          等待时长（秒）
     * @return
     */
    @Override
    public <T> Page<T> parsePageWithRedis(Object object, String topic, KafkaMessageTypeInterface kafkaMessageTypeEnum, Integer waitingTime, Class<T> clazz) {
        Object popObject = getRedisObjectOfKafka(object, topic, kafkaMessageTypeEnum, waitingTime);
        if (popObject == null) {
            return new Page<>();
        }
        Page page = JSONObject.parseObject(JSON.toJSONString(popObject), Page.class);

        List<T> records = JSONObject.parseArray(JSON.toJSONString(page.getRecords()), clazz);
        if (CollectionUtils.isEmpty(records)) {
            return new Page<>();
        }
        page.setRecords(records);
        return page;
    }

}
