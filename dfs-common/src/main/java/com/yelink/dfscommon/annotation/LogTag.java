package com.yelink.dfscommon.annotation;


import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 自定义操作日志类/字段注解
 * <p>
 * 注解在类上，标识为比对对象。
 * 默认使用mybatis-plus的selectById获取新旧值，
 * 如果默认方法不能满足查询，则填写bean、method、param查出新旧值
 * <p>
 * 注解在字段上，标识为比对字段。默认用原始值，可通过bean、method将原始值经行转换
 *
 * <AUTHOR>
 */

@Target({ElementType.TYPE, ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface LogTag {

    // 中文
    String name() default "";

    // 转换bean
    String bean() default "";

    // 转换bean对应接口
    Class interfaceClass() default Object.class;

    // 转换方法
    String method() default "";

    // 转换方法参数
    String param() default "";

    // 获取对象的唯一标识(用于列表数据的变化)
    boolean isKey() default false;

}



