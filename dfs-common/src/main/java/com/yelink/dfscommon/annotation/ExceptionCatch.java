package com.yelink.dfscommon.annotation;


import com.netflix.client.ClientException;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 服务间调用异常捕获注解
 *
 * <AUTHOR>
 */

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ExceptionCatch {

    // 异常类
    Class[] exceptions() default {ClientException.class};

}



