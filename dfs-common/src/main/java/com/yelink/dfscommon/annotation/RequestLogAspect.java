package com.yelink.dfscommon.annotation;

import com.alibaba.fastjson.JSONObject;
import com.yelink.dfscommon.utils.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;

@Aspect
@Component
@Slf4j
public class RequestLogAspect {

    @Around("@annotation(requestLog)")
    private Object handler(ProceedingJoinPoint joinPoint, RequestLog requestLog) throws Throwable {
        HttpServletRequest request = CommonUtils.getRequest();
        String uri;
        if (null != request) {
            uri = request.getRequestURI();
            logParamInfo(joinPoint.getArgs(), requestLog.value(), uri);
            if (requestLog.login()) {
//                if (null == CommonUtils.getUser()) {
//                    return ResultBody.fail(ResultCode.NO_LOGIN);
//                }
            }
            Object result = joinPoint.proceed();
            logResultInfo(result, requestLog.value(), uri);
            return result;
        }
        return joinPoint.proceed();
    }

    /**
     * 输出参数日志
     *
     * @param args
     * @param name
     */
    private void logParamInfo(Object[] args, String name, String uri) {

        try {
            StringBuilder s = new StringBuilder().append("req ")
                    .append(name).append("[").append(uri).append("]");

            if (null != args && args.length > 0) {
                boolean flag = true;
                for (Object object : args) {
                    if (object instanceof MultipartFile) {
                        flag = false;
                        break;
                    }
                }
                if (flag) {
                    try {
                        s.append(" PARAM: ")
                                .append(JSONObject.toJSONString(args));
                    } catch (Exception e) {
                    }
                }
            }

            log.info(s.toString());
        } catch (Exception e) {
            StringBuilder s = new StringBuilder().append("req ")
                    .append(name).append("[").append(uri).append("]").append(" ERROR: ").append(e.getMessage());
            log.info(s.toString(), e);
        }
    }

    /**
     * 返回值输出
     *
     * @param result
     * @param uri
     * @param desc
     */
    private void logResultInfo(Object result, String desc, String uri) {
        StringBuilder s = new StringBuilder().append("res ")
                .append(desc).append("[").append(uri).append("]")
                .append(" RESULT: ").append(JSONObject.toJSONString(result));
        log.info(s.toString());
    }
}
