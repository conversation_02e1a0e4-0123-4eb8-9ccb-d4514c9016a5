package com.yelink.dfscommon.exception;

/**
 * <AUTHOR> Assistant
 * @description 批量操作异常，用于在批量操作失败时携带失败详情
 * @Date 2025-07-28
 */
public class BatchOperationException extends RuntimeException {

    /**
     * 批量操作结果详情
     */
    private final Object operationResult;

    public BatchOperationException(String message, Object operationResult) {
        super(message);
        this.operationResult = operationResult;
    }

    public BatchOperationException(String message, Throwable cause, Object operationResult) {
        super(message, cause);
        this.operationResult = operationResult;
    }

    /**
     * 获取批量操作结果详情
     * @return 操作结果详情
     */
    public Object getOperationResult() {
        return operationResult;
    }

    /**
     * 获取指定类型的操作结果
     * @param resultClass 结果类型
     * @param <T> 结果类型泛型
     * @return 指定类型的操作结果
     */
    @SuppressWarnings("unchecked")
    public <T> T getOperationResult(Class<T> resultClass) {
        if (resultClass.isInstance(operationResult)) {
            return (T) operationResult;
        }
        throw new ClassCastException("操作结果类型不匹配，期望：" + resultClass.getName() + "，实际：" + operationResult.getClass().getName());
    }
}
