package com.yelink.dfscommon.constant.dfs;


/**
 * @Description: 调拨状态枚举
 * @Author: z<PERSON><PERSON><PERSON>
 * @Date: 2020/12/5
 */
public enum StockDeliveryStateEnum {

    /**
     * 状态编码及描述
     * 1-创建 2-生效 4-完成 5-关闭 6-取消
     */
    CREATED(1, "创建"),
    RELEASED(2, "生效"),
//    DELIVERY(3, "送货"),
    FINISHED(4, "完成"),
    CLOSED(5, "关闭"),
    CANCELED(6, "取消");

    private int code;
    private String name;

    StockDeliveryStateEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (StockDeliveryStateEnum stateEnum : StockDeliveryStateEnum.values()) {
            if (stateEnum.code == code) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static Integer getCodeByName(String name) {
        for (StockDeliveryStateEnum stateEnum : StockDeliveryStateEnum.values()) {
            if (name.equals(stateEnum.name)) {
                return stateEnum.code;
            }
        }
        return null;
    }
}
