package com.yelink.dfscommon.constant.pms;



import com.yelink.dfscommon.constant.EnumDTO;

import java.util.ArrayList;
import java.util.List;

/**
 * 合同类型
 */
public enum ReceiptTypePMSEnum {

    SALE_ORDER("saleOrder", "销售订单"),
    PRUDUCT_ORDER("productOrder", "生产订单"),
    PRUDUCT_WORK_ORDER("workOrder", "生产工单"),
    PURCHASE_DEMAND("purchaseDemand", "采购需求"),
    PURCHASE_ORDER("purchaseOrder", "采购订单"),
    ;

    private String code;
    private String name;

    ReceiptTypePMSEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        if (code == null) {
            return null;
        }
        for (ReceiptTypePMSEnum appendixTypeEnum : ReceiptTypePMSEnum.values()) {
            if (appendixTypeEnum.code.equals(code)) {
                return appendixTypeEnum.name;
            }
        }
        return null;
    }

    public static ReceiptTypePMSEnum getTypeEnum(String code) {
        if (code == null) {
            return null;
        }
        for (ReceiptTypePMSEnum appendixTypeEnum : ReceiptTypePMSEnum.values()) {
            if (appendixTypeEnum.code.equals(code)) {
                return appendixTypeEnum;
            }
        }
        return null;
    }

    /**
     * 将枚举值转化成list集合
     *
     * @return
     */
    public static List<EnumDTO> toList() {
        List<EnumDTO> list = new ArrayList<>();
        for (ReceiptTypePMSEnum appendixTypeEnum : ReceiptTypePMSEnum.values()) {
            list.add(EnumDTO.builder().code(appendixTypeEnum.code).name(appendixTypeEnum.name).build());
        }
        return list;
    }
}
