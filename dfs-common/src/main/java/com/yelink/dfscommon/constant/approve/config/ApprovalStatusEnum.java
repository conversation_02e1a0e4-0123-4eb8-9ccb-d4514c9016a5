package com.yelink.dfscommon.constant.approve.config;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/10/9 11:10
 */
public enum ApprovalStatusEnum {

    /**
     * 审批状态枚举
     */
    TO_BE_SUBMIT(0, null,"待提交"),
    TO_BE_APPROVAL(1, null,"待审批"),
    APPROVED(2, 2,"已通过"),
    REJECTED(3, 3,"已驳回"),
    CANCELLED(4, 4,"已撤销"),
    UNDER_APPROVAL(5, 1,"审批中")
    ;

    private final Integer code;
    private final Integer wechatCode;
    private final String name;

    ApprovalStatusEnum(int code, Integer wechatCode, String name) {
        this.code = code;
        this.wechatCode = wechatCode;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public Integer getWechatCode() {
        return wechatCode;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ApprovalStatusEnum cycleEnum : ApprovalStatusEnum.values()) {
            if (cycleEnum.code.equals(code)) {
                return cycleEnum.name;
            }
        }
        return null;
    }
    public static Integer getCodeByName(String name) {
        for (ApprovalStatusEnum stateEnum : ApprovalStatusEnum.values()) {
            if (name.equals(stateEnum.name)) {
                return stateEnum.code;
            }
        }
        return null;
    }

    public static ApprovalStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ApprovalStatusEnum cycleEnum : ApprovalStatusEnum.values()) {
            if (cycleEnum.code.equals(code)) {
                return cycleEnum;
            }
        }
        return null;
    }

    public static Integer getCodeByWechatCode(Integer wechatCode) {
        if (wechatCode == null) {
            return null;
        }
        for (ApprovalStatusEnum cycleEnum : ApprovalStatusEnum.values()) {
            if (wechatCode.equals(cycleEnum.wechatCode)) {
                return cycleEnum.getCode();
            }
        }
        return null;
    }

    public static List<ApprovalStatusEnum> getSystemValues() {
        List<ApprovalStatusEnum> statusEnums = new ArrayList<>();
        statusEnums.add(ApprovalStatusEnum.TO_BE_SUBMIT);
        statusEnums.add(ApprovalStatusEnum.TO_BE_APPROVAL);
        statusEnums.add(ApprovalStatusEnum.APPROVED);
        statusEnums.add(ApprovalStatusEnum.REJECTED);
        return statusEnums;
    }

}
