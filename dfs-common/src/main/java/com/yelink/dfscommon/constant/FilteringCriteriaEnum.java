package com.yelink.dfscommon.constant;


/**
 * <AUTHOR>
 * @description 筛选条件枚举
 * @Date 2021/11/2 10:58
 */
public enum FilteringCriteriaEnum {

    /**
     * 事件详细信息字段
     */

    GREATER_THAN("greaterThan", ">", "大于"),
    EQUAL("equal", "=", "等于"),
    LESS_THAN("lessThan", "<", "小于"),
    CONTAINS("contains", "like", "包含"),
    NOT_EQUAL("notEqual", "!=", "不等于"),
    ;


    private String type;
    private String sql;
    private String name;


    FilteringCriteriaEnum(String type, String sql, String name) {
        this.type = type;
        this.sql = sql;
        this.name = name;
    }

    public String getSql() {
        return sql;
    }

    public String getType() {
        return type;
    }

    public String getName() {
        return name;
    }


    public static FilteringCriteriaEnum getEnumBySql(String sql) {
        for (FilteringCriteriaEnum alarmTypeDescEnum : FilteringCriteriaEnum.values()) {
            if (alarmTypeDescEnum.getSql().equals(sql)) {
                return alarmTypeDescEnum;
            }
        }
        return null;
    }

    public static FilteringCriteriaEnum getEnumByName(String name) {
        for (FilteringCriteriaEnum alarmTypeDescEnum : FilteringCriteriaEnum.values()) {
            if (alarmTypeDescEnum.getName().equals(name)) {
                return alarmTypeDescEnum;
            }
        }
        return null;
    }

    public static FilteringCriteriaEnum getEnumByType(String type) {
        for (FilteringCriteriaEnum alarmTypeDescEnum : FilteringCriteriaEnum.values()) {
            if (alarmTypeDescEnum.getType().equals(type)) {
                return alarmTypeDescEnum;
            }
        }
        return null;
    }

    public static String getTypeBySql(String sql) {
        for (FilteringCriteriaEnum alarmTypeDescEnum : FilteringCriteriaEnum.values()) {
            if (alarmTypeDescEnum.getSql().equals(sql)) {
                return alarmTypeDescEnum.getType();
            }
        }
        return null;
    }
}
