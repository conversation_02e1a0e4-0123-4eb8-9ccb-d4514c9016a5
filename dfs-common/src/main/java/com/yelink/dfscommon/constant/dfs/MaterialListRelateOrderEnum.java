package com.yelink.dfscommon.constant.dfs;


import java.util.Arrays;

/**
 * @Description: 生产工单用料清单关联单据
 * @Author: zhengfu
 * @Date: 2020/12/5
 */
public enum MaterialListRelateOrderEnum {

    /**
     * 生产工单用料清单关联单据
     */
    WORK_ORDER("workOrder", "生产工单"),
    ;
    private String code;
    private String name;

    MaterialListRelateOrderEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static Object getByName(String stateName) {
        if (stateName == null) {
            return null;
        }
        return Arrays.stream(MaterialListRelateOrderEnum.values()).filter(v -> stateName.equals(v.getName())).findFirst().orElse(null);
    }

    public static String getNameByCode(String code) {
        if (code == null) {
            return null;
        }
        for (MaterialListRelateOrderEnum stateEnum : MaterialListRelateOrderEnum.values()) {
            if (stateEnum.code.equals(code)) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        if (name == null) {
            return null;
        }
        for (MaterialListRelateOrderEnum stateEnum : MaterialListRelateOrderEnum.values()) {
            if (name.equals(stateEnum.name)) {
                return stateEnum.code;
            }
        }
        return null;
    }
}
