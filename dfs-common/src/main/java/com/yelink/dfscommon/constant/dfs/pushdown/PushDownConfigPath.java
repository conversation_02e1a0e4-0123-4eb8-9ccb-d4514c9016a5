package com.yelink.dfscommon.constant.dfs.pushdown;

/**
 * <AUTHOR>
 */
public interface PushDownConfigPath {
    // 销售订单下推
    /**
     * 销售订单 -> 生产订单
     */
    String SALE_ORDER_2_PRODUCT_ORDER = "saleOrder.pushDownConfig.productOrder";
    /**
     * 销售订单 -> 采购需求
     */
    String SALE_ORDER_2_PURCHASE_REQUEST = "saleOrder.pushDownConfig.purchaseRequest";
    /**
     * 销售订单 -> 出货申请
     */
    String SALE_ORDER_2_DELIVERY_APPLICATION = "saleOrder.pushDownConfig.deliveryApplication";
    /**
     * 销售订单 -> 委外订单
     */
    String SALE_ORDER_2_SUBCONTRACT_ORDER = "saleOrder.pushDownConfig.subcontractOrder";


    // 生产订单下推
    /**
     * 生产订单 -> 生产工单
     */
    String PRODUCT_ORDER_2_WORK_ORDER = "production.productOrderPushDownConfig.workOrder";
    /**
     * 生产订单 -> 采购需求
     */
    String PRODUCT_ORDER_2_PURCHASE_REQUEST = "production.productOrderPushDownConfig.purchaseRequest";
    /**
     * 生产订单 -> 生产订单用料清单
     */
    String PRODUCT_ORDER_2_PRODUCT_MATERIALS_LIST = "production.productOrderPushDownConfig.productMaterialsList";


    // 生产工单下推
    /**
     * 生产工单 -> 领料申请
     */
    String WORK_ORDER_2_PRODUCT_TAKEOUT_APPLICATION = "production.workOrderPushDownConfig.takeOutApplication";
    /**
     * 生产工单 -> 生产工单用料清单
     */
    String WORK_ORDER_2_WORK_MATERIAL_LIST = "production.workOrderPushDownConfig.materialList";


    // 采购
    /**
     * 采购需求 -> 采购订单
     */
    String PURCHASE_REQUEST_2_PURCHASE = "purchase.purchaseRequestPushDownConfig.purchaseOrder";

    /**
     * 采购订单 -> 采购收料
     */
    String PURCHASE_2_PURCHASE_RECEIPT = "purchase.purchaseOrderPushDownConfig.purchaseReceipt";

    /**
     * 采购收料单 -> 采购退料
     */
    String PURCHASE_RECEIPT_2_PURCHASE_RETURN = "purchase.purchaseReceiptPushDownConfig.returnOrder";


}
