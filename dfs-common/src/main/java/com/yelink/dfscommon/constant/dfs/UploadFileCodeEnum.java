package com.yelink.dfscommon.constant.dfs;

import java.util.Arrays;

/**
 * @description: 文件上传编号枚举定义，
 * @author: shuang
 * @time: 2022/11/11
 */
public enum UploadFileCodeEnum {

    /**
     * 比如 修改 用户导入模板的上传，其中的fileCode就在此进行定义即可
     *
     */
    SALE_ORDER_TEMPLATE("saleOrderTemplate","销售订单自定义导入模板"),
    PRODUCT_ORDER_TEMPLATE("productOrderTemplate","生产订单自定义导入模板"),
    REPORT_LINE_TEMPLATE("reportRecordTemplate","作业工单报工自定义导入模板"),
    PURCHASE_ORDER_TEMPLATE("purchaseOrderTemplate","采购订单自定义导入模板"),
    STOCK_IN_ORDER_TEMPLATE("stockInOrderTemplate","采购入库单自定义导入模板"),
    STOCK_SALE_MATERIAL_TEMPLATE("stockSaleMaterialTemplate","销售出库单自定义导入模板（按物料）"),
    STOCK_SALE_ORDER_TEMPLATE("stockSaleOrderTemplate","销售出库单自定义导入模板（按单）"),
    WORK_ORDER_TEMPLATE_NAME("workOrderTemplate","生产工单自定义导入模板"),
    WORK_ORDER_PLAN_TEMPLATE_NAME("workOrderPlanTemplate","生产工单日计划自定义导入模板"),
    MATERIAL_TEMPLATE_TEMPLATE("materialTemplate", "物料自定义导入模板"),
    CUSTOMER_TEMPLATE_TEMPLATE("customerTemplate", "客户档案自定义导入模板"),
    CAPACITY_TEMPLATE_TEMPLATE("capacityTemplate", "产能自定义导入模板"),
    BOM_TEMPLATE_NAME ( "BomTemplate","BOM自定义导入模板"),
    REPLACE_SCHEME_TEMPLATE ( "replaceSchemeTemplate","替代物料导入模板"),
    WORK_ORDER_MATERIAL_LIST_TEMPLATE ( "workOrderMaterialListTemplate","生产工单用料清单导入模板"),
    AUXILIARY_ATTR_TEMPLATE ( "auxiliaryAttrTemplate","物料特征参数导入模板"),
    FILE_IMPORT_TEMPLATE ( "fileImportTemplate","批量excel自定义导入模板"),
    ;


    private String code;
    private String name;

    UploadFileCodeEnum(String code,String name){
        this.code = code;
        this.name = name;
    }

    public static UploadFileCodeEnum getUploadFileCodeEnumByCode(String uploadFileCode) {
        if(uploadFileCode == null){
            return null;
        }
        return Arrays.stream(UploadFileCodeEnum.values())
                .filter(uploadFileCodeEnum -> uploadFileCodeEnum.getCode().equals(uploadFileCode))
                .findAny()
                .orElse(null);

    }


    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }
}
