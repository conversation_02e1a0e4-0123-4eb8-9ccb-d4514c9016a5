package com.yelink.dfscommon.constant;

/**
 * <AUTHOR>
 * @Date 2022/5/31 14:55
 */
public enum NetworkStatusEnum {

    /**
     * 控制校验级别：1-强、2-弱
     */
    STABILITY(1, "强"),
    INSTABILITY(2, "弱");

    private Integer code;
    private String name;


    NetworkStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (NetworkStatusEnum teamTypeEnum : NetworkStatusEnum.values()) {
            if (code.intValue() == teamTypeEnum.code.intValue()) {
                return teamTypeEnum.getName();
            }
        }
        return null;
    }

    public static Integer getCodeByName(String name) {
        if(name == null){
            return null;
        }
        for (NetworkStatusEnum teamTypeEnum : NetworkStatusEnum.values()) {
            if (name.equals(teamTypeEnum.name)) {
                return teamTypeEnum.code;
            }
        }
        return null;
    }
}
