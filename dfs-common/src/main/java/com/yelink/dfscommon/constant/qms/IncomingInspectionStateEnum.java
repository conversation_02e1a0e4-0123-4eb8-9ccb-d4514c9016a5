package com.yelink.dfscommon.constant.qms;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Date 2023/5/30 17:23
 */
public enum IncomingInspectionStateEnum {
    /**
     * 状态编码及描述
     * 1-创建，2-生效 3-完成 4-关闭
     */
    CREATED(1, "创建"),
    RELEASED(2, "生效"),
    FINISHED(3, "完成"),
    CLOSED(4, "关闭");

    private final int code;
    private final String name;

    IncomingInspectionStateEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code != null) {
            for (IncomingInspectionStateEnum stateEnum : IncomingInspectionStateEnum.values()) {
                if (stateEnum.code == code) {
                    return stateEnum.name;
                }
            }
        }
        return null;
    }

    public static Integer getCodeByName(String name) {
        if (StringUtils.isNotBlank(name)) {
            for (IncomingInspectionStateEnum stateEnum : IncomingInspectionStateEnum.values()) {
                if (name.equals(stateEnum.name)) {
                    return stateEnum.code;
                }
            }
        }
        return null;
    }
}
