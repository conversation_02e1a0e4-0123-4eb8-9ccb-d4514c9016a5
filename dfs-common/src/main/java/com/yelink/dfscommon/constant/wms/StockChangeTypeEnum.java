package com.yelink.dfscommon.constant.wms;

/**
 * <AUTHOR>
 * @date 2021/4/7 19:17
 */
public enum StockChangeTypeEnum {
    //
    INPUT("input", "入库"),
    OUTPUT("output", "出库"),
    TRANSFER_INPUT("transferInput", "调拨入库"),
    TRANSFER_OUTPUT("transferOutput", "调拨出库"),
    CHECK("check", "库房盘点"),
    INITIALIZATION("initialization", "库存初始化"),
    PURCHASERETURN("purchaseReturn", "采购退料出库"),
    SYNCHRONIZE("synchronize", "库存同步"),
    WORK_ORDER_COMPLETE("workOrderComplete", "生产入库"),
    SALE_INPUT("saleInput", "销售入库"),
    SALE_OUTPUT("saleOutput", "销售出库"),
    DELIVERY("delivery", "交付出库"),
    SUBCONTRACTING_OUTPUT("subcontractingOutput", "委外出库"),
    SUBCONTRACTING_INPUT("subcontractingInput", "委外入库");


    private String typeCode;
    private String typeName;

    StockChangeTypeEnum(String typeCode, String typeName) {
        this.typeCode = typeCode;
        this.typeName = typeName;
    }

    public String getTypeCode() {
        return typeCode;
    }

    public String getTypeName() {
        return typeName;
    }


    public static StockChangeTypeEnum getEnumByCode(String code) {
        for (StockChangeTypeEnum typeEnum : StockChangeTypeEnum.values()) {
            if (typeEnum.typeCode.equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }

    public static String getNameByCode(String typeCode) {
        if (typeCode == null) {
            return null;
        }
        for (StockChangeTypeEnum typeEnum : StockChangeTypeEnum.values()) {
            if (typeEnum.typeCode.equals(typeCode)) {
                return typeEnum.typeName;
            }
        }
        return null;
    }
}
