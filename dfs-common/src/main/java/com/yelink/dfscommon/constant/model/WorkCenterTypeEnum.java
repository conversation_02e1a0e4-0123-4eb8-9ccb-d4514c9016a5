package com.yelink.dfscommon.constant.model;


import com.baomidou.mybatisplus.annotation.EnumValue;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 生产基本单元
 * @Author: zhengfu
 * @Date: 2020/12/5
 */
public enum WorkCenterTypeEnum {

    /**
     * 生产基本单元
     * line-制造单元 team-班组 device-设备
     */
    LINE("line", "制造单元"),
    TEAM("team", "班组"),
    DEVICE("device", "设备") ;

    @EnumValue
    private String code;
    private String name;

    WorkCenterTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (WorkCenterTypeEnum stateEnum : WorkCenterTypeEnum.values()) {
            if (stateEnum.code.equals(code)) {
                return stateEnum.name;
            }
        }
        return null;
    }
    public static WorkCenterTypeEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (WorkCenterTypeEnum stateEnum : WorkCenterTypeEnum.values()) {
            if (stateEnum.code.equals(code)) {
                return stateEnum;
            }
        }
        return null;
    }
    public static WorkCenterTypeEnum getByName(String name) {
        if (name == null) {
            return null;
        }
        for (WorkCenterTypeEnum stateEnum : WorkCenterTypeEnum.values()) {
            if (stateEnum.name.equals(name)) {
                return stateEnum;
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        if (name == null) {
            return null;
        }
        for (WorkCenterTypeEnum stateEnum : WorkCenterTypeEnum.values()) {
            if (stateEnum.name.equals(name)) {
                return stateEnum.code;
            }
        }
        return null;
    }

    public static List<String> getCodesByNames(List<String> names) {
        List<String> codes = new ArrayList<>();
        if (CollectionUtils.isEmpty(names)) {
            return codes;
        }
        for (String name : names) {
            for (WorkCenterTypeEnum stateEnum : WorkCenterTypeEnum.values()) {
                if (stateEnum.name.equals(name)) {
                    codes.add(stateEnum.code);
                }
            }
        }
        return codes;
    }
}
