package com.yelink.dfscommon.constant;


import java.text.MessageFormat;

/**
 * @Description: 返回码
 * @Author: zhengfu
 * @Date: 2020/12/3
 */
public enum RespCodeEnum {

    /**
     * ==========校验相关 1000-1100==============
     * ==========订单工单相关 1100-1500==============
     * ==========产品相关 1500-2000==============
     * ==========员工相关 2000-3000==============
     * ==========用户相关 3000-4000==============
     * ==========传感器 4000-4500==============
     * ==========基础信息 4500-5000==============
     * ==========生产模型 5000-6000==============
     * ==========生产相关 6000-7000==============
     * ==========库存相关 7000-8000==============
     * ==========告警相关 8000-8500==============
     * ==========采购相关 8500-9000==============
     * ==========指标相关 9001-9500==============
     * ==========系统相关 9500-9600==============
     * ==========大屏相关 9601-9650==============
     * ==========维修相关 9701-9750==============
     * ==========炉次相关 9751-9800==============
     * ===========产能相关9801-9850==============
     * ===========系统同步相关9851-9870==========
     * ===========表格数据处理相关9871-9990======
     * ===========产品质检相关10001-10020======
     * ===========产前检验相关12000-13000======
     * ===========配置中心报错集合13000-14000======
     * ============单据回退相关14000 - 15000=====
     */
    AUTHENTICATION_FAILED("1000", "认证失败"),
    UNSUPPORTED_GRANT_TYPE("1001", "不支持该认证类型"),
    INVALIDTOKEN_EXPIRED("1002", "刷新令牌已过期，请重新登录"),
    INVALID_SCOPE("1003", "不是有效的scope值"),
    INVALIDGRANT_REFRESH("1004", "refresh token无效"),
    INVALIDGRANT_LOCKED("1005", "用户已被锁定，请联系管理员"),
    INVALIDGRANT_USERNAME_PWD("1006", "用户名或密码错误"),
    OAUTH_TOKEN_DENIED("1007", "操作权限不足"),
    PARAM_EXCEPTION("1008", "请求参数错误"),
    PARAM_CURRENT_EXCEPTION("1009", "current不能为空"),
    PARAM_SIZE_EXCEPTION("1010", "size不能为空"),
    REPEAT_REQUEST_EXCEPTION("1011", "请勿重复请求"),
    SERVICE_NOT_USE("503", "服务不可用"),
    API_NOT_FOUND("404", "找不到对应接口: {0}"),
    INVALID_TOKEN("50000", "token格式非法或失效"),
    FILE_SERVICE_NOT_READING("9000", "图片服务器未启动"),
    SYS_DATA_MODIFY_FORBIDDEN("1012", "系统数据，不允许修改"),
    SYS_DATA_DELETE_FORBIDDEN("1013", "系统数据，不允许删除"),
    FIELD_FORMAT_ILLEGAL("1014", "字段格式不合法，字段：{0}"),
    TABLE_FIELD_NOT_FOUND("1015", "找不到对应的数据库字段，字段：{0}"),
    IN_VALUE_MUST_BE_ARRAY("1016", "in操作符的值必须是数组"),
    OPERATOR_ILLEGAL("1017", "操作符不合法，操作符：{0}"),
    LOGIC_ILLEGAL("1018", "连接符不合法，连接符：{0}"),

    //==========指标/模型相关 9001-9500==============
    TARGET_FAIL2CHOOSE("9001", "请选择模型节点"),
    TARGET_TYPE_NOT_FOUND("9002", "未找到相关对象类型"),
    TARGET_ADD_FAILED("9003", "指标添加失败"),
    TARGET_UPDATE_FAILED("9004", "指标更新失败"),
    TARGET_DELETE_FAILED("9005", "指标删除失败"),
    TARGET_RECORD_ORDER_NOT_FOUND("9006", "未找到正在生产的工单"),
    WORK_ORDER_NOT_FOUND("9007", "未找到工单"),
    TARGET_NAME_NOSPECT("9008", "指标名称不能重复选择，请重新选择"),
    ONE_VALUE_SMALL_TWO("9009", "第一个值必须小于第二个值，请重新输入"),
    TARGET_NAME_FORMAT_ERROR("9010", "指标名错误，只能包含字母、数字和下划线"),
    MODEL_FAIL2DEL("9101", "存在关联该模型的数据"),
    AREA_TYPE_ID_NOT_FOUND("9102", "工厂类型ID不能为空"),
    AREA_DESC_NOT_FOUND("9103", "工厂类型描述不能为空"),
    GRID_TYPE_ID_NOT_FOUND("9104", "车间类型ID不能为空"),
    GRID_DESC_NOT_FOUND("9105", "车间类型描述不能为空"),
    LINE_TYPE_ID_NOT_FOUND("9106", "产线类型编号不能为空"),
    LINE_DESC_NOT_FOUND("9107", "产线类型描述不能为空"),
    SUBLINE_NOT_FOUND("9108", "产线分支不能为空"),
    FACILITY_SEQ_NOT_FOUND("9109", "工位类型顺序编号不能为空"),
    FACILITY_TYPE_NOT_FOUND("9110", "工位类型不能为空"),
    MANUAL_TARGET_ADD_REPEAT("9111", "新增重复，同个批次一天只可新增一条指标记录"),
    TARGET_ID_IS_NULL("9112", "所传入的指标不可以为空"),
    RECORDS_IS_NULL("9113", "未找到相关记录"),
    NOT_EXIST_TARGET("9114", "不存在该指标"),
    NOT_SET_TARGET("9115", "未设置查看指标记录"),
    THIS_STATION_IS_NOT_FOR_TESTING("9116", "该工位非检测工位"),
    NO_STATION_SPECIFIED("9117", "未指定工位"),
    STANDARD_OUT_OF_RANGE("9118", "标准超出0-100范围"),
    LINE_TYPE_ID_REPEAT("9119", "产线类型编号不能重复"),
    FACILITY_SEQ_REPEAT("9120", "工位类型顺序编号不能重复"),
    DEVICE_ADD_REPEAT("9121", "设备不能重复添加"),
    ASSEMBLY_ID_NOT_EMPTY("9122", "组件信息不存在"),
    COMPONENT_TAG_NAME_IS_EXIST("9123", "该大屏的tag已经存在"),
    GRID_DATA_NOT_FIND("9124", "未获取到车间数据"),
    PROCESS_ASSEMBLY_ADD_REPEAT("9125", "工艺工装编码不能重复"),
    PROCESS_ASSEMBLY_MODEL_ADD_REPEAT("9126", "工艺工装类型不能重复"),

    SUPPLIER_TYPE_MODEL_ADD_REPEAT("9127", "供应商类型不能重复"),
    SUPPLIER_TYPE_IS_NOT_EXIST("9128", "供应商类型不存在"),
    EXIST_SAME_FORMULA_NUMBER("9129", "已存在相同设备类型和配方号组合"),
    CREATE_TARGET_GROUP_VIEW_NEED_SCRIPT("9130", "视图创建时脚本必填"),
    EXIST_SAME_MODEL_CODE("9131", "类型编码重复"),

    //==========系统相关 9500-9550==============
    EXIST_UNCLEAN_TABLE("9501", "基础表不能删除"),
    DISK_DRIVE_FULL("9502", "磁盘空间已满，请进行数据清理"),
    TABLE_DATA_CLEANING("9503", "数据正在清理中，请稍等"),
    FRPC_PARAM_ERROR("9504", "远程操作frpc参数错误"),
    TABLE_EXIST("9505", "表已存在"),
    DATA_OUT_OF_RANGE("9506", "字段：{0}修改的字段类型：{1}不符合已存在数据的存储规范"),
    INDEX_DUPLICATE_KEY_ERROR("9507", "添加的索引：{0}不符合已存在数据的存储规范"),
    INSERT_OR_UPDATE_DATA_ERROR("9508", "表：{0}数据插入失败"),
    TABLE_FIELD_EMPTY("9509", "表字段不能为空"),
    TABLE_INDEX_INCLUDE_NOT_EXIST_FIELD("9510", "索引字段中包含不存在的字段：{0}"),
    TABLE_INDEX_NAME_REPEAT("9511", "索引名称不能重复"),
    SELECT_SQL_FIELD_NUM_NOT_EQUAL_TABLE_FIELD_NUM("9512", "sql脚本查询的字段列数和表定义的字段列数不一致"),
    SQL_IS_NOT_VALID("9513", "脚本语句错误或语句非法"),


    //==========审批相关 9550-9600==============
    ORDER_NEED_TO_APPROVE("9550", "单据未经审批"),
    CURRENT_USER_CAN_ONLY_APPROVED_HIS_OWN("9551", "只可以批量审批当前用户的单据"),
    EXCEL_TEMPLATE_NOT_EXIST("9552", "模板不存在"),
    CHECKED_QUANTITY_IS_NULL("9553", "已检查数不可为空"),
    EXCEL_TEMPLATE_ERROR("9554", "该模板公式执行含有不支持函数，请检查模板公式的正确性"),
    APPROVE_DATA_IS_NULL("9555", "审批数据不能为空"),
    APPROVE_STATUS_IS_NULL("9556", "审批状态不能为空"),
    TEMPLATE_BASE_INFO_ERROR("9557", "请检查模板使用说明基本数据配置"),
    APPROVE_PERSON_IS_NOT_EXIST("9558", "审批人不存在，请确认"),

    APPROVE_IS_USER_PERSON_IS_NOT_EMPTY("9559", "单据开启审批，审批人不能为空"),

    APPROVE_IS_USER_UPDATE_STATE_IS_EFFECT("9560", "单据开启审批，生效状态才可修改状态"),
    UPDATE_STATE_NOT_CROSS_LEVEL("9561", "编辑状态时，只能顺序依次编辑"),
    IS_NOT_CREATE_NOT_APPROVAL("9562", "非编辑状态时，不可审批"),

    OPEN_APPROVAL_NOT_RELEASE("9563", "单据开启审批，不可直接保存并生效"),

    EXISTS_CODE("9564", "该审批编码已存在"),
    INNER_CANNOT_DELETE("9565", "内置审篇配置无法删除"),

    //==========审批相关 9650-9700==============
    FAULT_TYPE_IS_BLANK("9650", "请选择故障类型"),
    TEMPLATE_NAME_DUPLICATE("9651", "模板名称重复"),
    TEMPLATE_CONTROL_IS_BLANK("9652", "模板控件不能为空"),
    TABLE_CHILD_CONTROL_IS_BLANK("9653", "明细子控件不能为空"),
    CONTROL_NAME_DUPLICATE("9654", "控件名称重复，名称：{0}"),
    CONTROL_NAME_IS_BLANK("9655", "控件名称不能为空"),
    OPTION_NAME_IS_BLANK("9656", "单选框/多选框的可选项名称不能为空"),

    //==========告警 8000-8500==============
    ALARM_FAIL2SEL("8101", "所查询告警信息不存在"),
    ALARM_AGGREGATE_CODE_ADD_REPEAT("8102", "告警编号重复"),
    ALARM_DEFINITION_CODE_ID_ADD_REPEAT("8103", "告警定义ID重复"),
    ALARM_DEFINITION_HAS_ASSOCIATED_ID("8104", "告警定义存在关联ID"),
    ALARM_NOT_EXIST("8105", "导入失败告警不存在"),
    ALARM_FIELD_MESSAGE_NOT_EXIST("8106", "告警字段信息不可为空"),

    ALARM_AGGREGATE_CODE_ID_ADD_REPEAT("8107", "告警流水号重复"),
    ALARM_DEFINITION_CODE_ID_ADD_REPEAT_NOT_IMPORT("8108", "告警定义ID重复,不可导入"),
    NOT_EXPORT_NUMBER_OVER_LIMIT("8109", "超出50000条数据限制，不可导出"),
    ALARM_CLASSIFY_ID_IS_NULL("8110", "告警类型ID为空"),
    ALARM_TYPE_IS_NOT_FOUND("8111", "该告警类型未找到！"),
    PARAMETER_IS_NULL("8112", "未传入参数，请传入参数！"),
    ALARM_CLASSIFY_ALREADY_ASSOCIATED("8113", "该告警类型已被告警定义关联，不可删除！"),
    EVENT_CLASSIFY_ALREADY_ASSOCIATED("8114", "该事件类型已被事件定义关联，不可删除！"),
    EVENT_CLASSIFY_ID_IS_NULL("8115", "事件类型ID为空"),
    EVENT_DEFINITION_CODE_ID_ADD_REPEAT("8116", "事件定义ID重复"),
    EVENT_DEFINITION_NOT_FOUND("8117", "事件定义未找到"),
    ALARM_NOTICE_RULE_IS_EXISTS("8118", "相同的告警通知设置已存在"),

    //==========采购相关 8500-9000==============
    SUPPLIER_FAIL2SAME_CODE("8501", "供应商编码重复"),
    REQUEST_NOT_ALLOWED_TO_AUDIT("8502", "当前登录用户不允许审核"),
    REQUEST_NOT_ALLOWED_TO_APPROVE("8503", "当前登录用户不允许批准"),
    SUPPLIER_NOT_ALLOWED_TO_AUDIT("8504", "当前登录用户不允许审核"),
    SUPPLIER_NOT_ALLOWED_TO_APPROVE("8505", "当前登录用户不允许批准"),
    NOT_ALLOW_TO_UPDATE_REMARK("8506", "该状态下不允许修改备注"),
    REQUEST_ORDER_NUMBER_EXCEED_THIRTY_QUANTITY("8507", "添加失败"),
    SUPPLIER_FAIL2SAME_NAME("8508", "供应商名称重复"),
    SUPPLIER_RETURN_NUMBER_EXIT("8509", "已有退料申请单: {0}"),
    SUPPLIER_RETURN_NOT_FOUND("8510", "未找到退料申请单信息"),
    SUPPLIER_RETURN_NOT_FOUND_MATERIAL("8511", "未找到编码:{0}的物料信息"),
    REPLACE_SCHEME_REPEAT("8512", "替代方案重复"),
    REPLACE_MATERIAL_REPEAT("8513", "替代物料重复"),
    SUBCONTRACT_ORDER_FAIL2SAME_CODE("8514", "委外订单号已存在"),
    SUBCONTRACT_RECEIPT_FAIL2SAME_CODE("8515", "委外收货单号已存在"),
    AUXILIARY_ATTR_REPEAT("8516", "特征参数编号或者名称重复"),
    AUXILIARY_ATTR_VALUE_CODE_REPEAT("8517", "特征值编号重复"),
    AUXILIARY_ATTR_VALUE_NAME_REPEAT("8518", "特征值名称重复"),
    SUPPLIER_NOT_EXIST("8519", "供应商不存在"),
    REPLACE_MATERIAL_CAN_NOT_SAME_WITH_MAIN("8520", "替代物料:{0}中不能和主物料相同"),


    //通用失败提示
    OPERATION_FAIL("300", "操作失败"),


    //========================库存相关 7000-8000============================
    STOCK_PURCHASE_ORDER_IS_BLANK("7001", "未给出采购单"),
    STOCK_WORK_ORDER_IS_BLANK("7002", "未给出工单号"),
    STOCK_SALE_ORDER_IS_BLANK("7003", "未给出销售订单号"),
    STOCK_WAREHOUSE_CODE_HAS_EXISTS("7004", "仓库编号已存在"),
    WAREHOUSE_NOT_DELETE("7005", "非创建状态仓库不可删除"),
    STOCK_WORK_ORDER_NOT_FOUND("7006", "未找到该工单"),
    STOCK_WORK_ORDER_MATERIAL_NOT_FOUND("7007", "未找到该工单的物料"),
    STOCK_END_OF_PROCESS("7008", "流程已结束，不可修改"),
    STOCK_STATE_UPDATE_WRONGFUL("7009", "状态修改不符合流程，请重新选择"),
    STOCK_MATERIAL_NOT_FOUND_IN_STOCK("7010", "仓库中未找到该物料"),
    STOCK_ORDER_NOT_FOUND("7011", "未找到该出入库单号"),
    STOCK_ORDER_HAS_RELEASED("7012", "单据已发放，不可删除"),
    STOCK_DELIVERY_NOT_FOUND("7013", "未找到该出入库单号"),
    STOCK_DELIVERY_HAS_RELEASED("7014", "送货单已发放，不可删除"),
    STOCK_ORDER_CONTAIN_ABANDON_MATERIAL("7015", "存在已停用/已废弃物料，库单不可发放"),
    STOCK_ORDER_NUMBER_EXIST("7016", "单号已存在，请重新创建"),
    STOCK_MATERIAL_NOT_FOUND("7017", "物料表中未找到该物料"),
    STOCK_DELIVERY_APPLICATION_NOT_FOUND("7018", "未找到该销售出货单"),
    STOCK_CAN_NOT_EXAMINE("7019", "非审核人不可审核销售出货单"),
    STOCK_CAN_NOT_APPROVAL("7020", "非批准人不可批准销售出货单"),
    STOCK_MATERIALS_CAN_NOT_EMPTY("7021", "物料明细不能为空"),
    STOCK_NOT_ALLOW_CHANGE_WAREHOUSE("7022", "不允许更改仓库"),
    STOCK_TRANSFER_NOT_FOUND("7023", "未找到该调拨记录"),
    STOCK_TRANSFER_HAS_RELEASED("7024", "调拨记录已发放，不可删除"),
    STOCK_MATERIAL_NOT_EXIST_IN_WAREHOUSE("7025", "库房不存在的物料，不可入库"),
    BAR_CODE_IS_EXIST("7026", "批次已存在，请重新设置"),
    BAR_CODE_INFO_IS_EMPTY("7027", "字段信息不可为空"),
    BAR_CODE_IS_OCCUPIED("7028", "批次已被使用"),
    BAR_CODE_RULE_TYPE_IS_EXIST("7029", "该规则类型已存在"),
    BAR_CODE_RULE_CODE_IS_EXIST("7030", "规则编号已存在"),
    BAR_CODE_RULE_IS_NOT_EXIST("7031", "未找到该规则编号"),
    BAR_CODE_RELATE_NUMBER_IS_BLANK("7032", "批次关联单号不能为空"),
    BAR_CODE_RELATE_MATERIAL_IS_BLANK("7033", "批次关联物料编号不能为空"),
    BAR_CODE_RULE_IS_BLANK("7034", "未指定批次号生成规则"),
    BAR_CODE_TRACK_EXIST("7035", "存在批次追溯信息，不能修改"),
    STOCK_IN_OUT_WAREHOUSE_IS_SAME("7036", "调出调入仓库为同一仓库"),
    BATCH_ADD_ERROR("7037", "批号规则包含手动输入部分，不可批量新增"),
    CANNOT_FOUND_RELATED_NUMBER("7038", "找不到关联单据，请检查"),
    BATCH_IS_WAREHOUSING("7039", "批号已入库"),
    STOCK_LOCATION_NUM_NOT_EXIST("7040", "库位定义编码不存在"),
    CANNOT_FOUND_BARCODE_INFO("7041", "找不到条码信息"),
    FINISHED_INPUT_ORDER_CANNOT_STACK("7042", "完成状态的入库单不再支持上架调整"),
    WAREHOUSE_CORRESPOND_STOCK_LOCATION("7043", "上架库位与库房不一致,请检查"),
    BATCH_CANNOT_FOUND_INPUT_ORDER("7045", "该批号未生成入库单，请先入库"),
    STOCK_LOCATION_NUM_REPEAT("7046", "库位定义编码重复"),
    STOCK_LOCATION_APPROVE_CONFIG_NOT_EXIST("7047", "未找到库位定义审批配置"),
    BATCH_IS_NOT_IN_WAREHOUSING("7048", "批号不在库房中，可能未入库或已出库"),
    BATCH_IS_IN_WAREHOUSING("7049", "该批号已入库"),
    STACKED_BARCODE_MUST_BATCH_DELIVERY("7050", "修改的批次为上架批次，必须整批出库"),
    WAREHOUSE_DOES_NOT_HAVE_THE_BATCH("7052", "仓库没有该批次"),
    IN_OUT_TYPE_NAME_REPEAT("7053", "类型名称重复"),
    OUT_BATCH_REPEAT("7054", "出库批次重复"),
    BATCH_NOT_ISSUED_CANNOT_RECEIVE_AGAIN("7055", "扫描的批次未出库，无法再次入库"),
    CANNOT_STACK_BECAUSE_RECEIPT_QUANTITY_NOT_EQUAL_CURRENT_QUANTITY("7056", "扫描的批次必须整批入整批出"),
    BARCODE_ADD_LAST_PREFIX_ERROR("7057", "批号规则最后一个后缀不是自动生成序号规则，无法批量生成批次号"),
    BAR_CODE_IS_EMPTY("7058", "批次号是空的"),
    BAR_CODE_IS_REPEAT("7059", "批次号重复，请检查"),
    NO_PERMISSION_OPERATE("7060", "无该仓库操作权限"),
    INSUFFICIENT_WAREHOUSE_PERMISSIONS("7061", "仓库权限不足"),
    SYSTEM_NOT_BAR_CODE("7062", "系统无此批次"),
    BAR_CODE_LABEL_RULE_IS_BLANK("7063", "未指定标签生成规则"),
    NOT_FOUND_PURCHASE_RECEIPT_ORDER("7064", "系统无此生效或完成状态的采购收货单,请检查!"),
    WAREHOUSE_NOT_FIND("7065", "仓库不存在"),
    LIGHT_IDLE_STATE("7066", "库房中灯颜色是空闲状态"),
    COLOUR_OCCUPY("7067", "颜色已被占用，请稍后再试"),
    BAR_CODE_RULE_NAME_IS_EXIST("7068", "规则名称已存在"),
    BAR_CODE_RULE_CODE_OR_NAME_IS_NOT_NULL("7069", "标签编号或者名称不能为空"),
    BAR_CODE_NOT_EXIST("7070", "批次不存在"),
    DELETION_ONLY_CREATED_STATE("7071", "删除失败: 非创建状态不允许删除"),
    DATA_DOES_NOT_EXIST("7072", "删除失败: 数据不存在"),
    PROHIBITION_TO_CHANGE_STATE("7073", "物料编码[{0}]不存在该仓库！"),
    BATCH_MANAGEMENT_MATERIAL("7074", "存在批次管理物料，请使用小程序执行!"),
    MATERIAL_CODE_DOES_NOT_EXIST("7075", "物料编码: {0}不存在"),
    FIRST_COMPLETE_OUT_OF_WAREHOUSE("7076", "物料编码: {0}是批次管理物料,请扫码完成出库"),
    SCAN_THE_BATCH_THEN_WAREHOUSING("7077", "该物料按批次管理，请扫批次入库"),
    DISMANTLING_BATCH_THEN_WAREHOUSING("7078", "该批次不可使用，请拆批入库"),
    MATERIALS_MUST_EXIST_IN_BATCHES("7079", "批次管理物料必须存在批次!"),
    OUT_STOCK_TYPE_NOT_FOUND("7080", "未找到出库类型"),
    MATERIAL_INFORMATION_NOT_FOUND("7081", "未找到物料信息"),
    BATCH_STORAGE_RECORD_WAS_NOT_FOUND("7082", "未找到该批次入库记录"),
    BATCH_SELECTED_OR_OUT_OF_STOCK("7083", "所选批次已被他人选中或已出库完"),
    BATCH_NOT_FOUND_OR_HAS_EXPIRED("7084", "未找到该批次信息或已失效"),
    OUT_ORDER_WAS_NOT_FOUND("7085", "未找到此出库单：{0}"),
    NON_BATCH_MANAGED_MATERIALS("7086", "该物料为非批次管理物料，请勿扫批次出库"),
    DOES_NOT_EXIST_IN_WAREHOUSE("7087", "仓库内不存在该物料，请勿出库"),
    LOT_NOT_IN_SELECTED_WAREHOUSE("7088", "批次: {0}不在所选库房中"),
    CANNOT_GREATER_THAN_BATCH_REMAINING("7089", "选择数量不能大于批次剩余库存: {0}"),
    THE_BATCH_HAS_BEEN_SPLIT("7090", "该批次已被拆分，不可使用"),
    BATCH_NOT_IN_STOCK("7091", "{0} 批次未入库"),
    INSUFFICIENT_BATCH_INVENTORY("7092", "{0} 批次库存不足，当前批次库存: {1}"),
    DIFFERENT_RELATED_NUMBERS_BATCH_STORAGE("7093", "不同关联单号的物料请分批入库"),
    LOCATION_NOT_ACTIVE_OR_COMPLETE("7094", "该库位非生效/完成状态，不可上架"),
    NO_SUCH_BATCH_IN_WAREHOUSE("7095", "库房无此批次"),
    INVENTORY_LESS_THAN_BATCH_QUANTITY("7096", "物料库存少于批次出库量,出库失败。物料: {0},当前库存: {1}"),
    MATERIAL_IS_NOT_ASSOCIATED_NUMBER("7097", "物料未关联单号"),
    DELETE_FAILED_DATA_DOES_NOT_EXIST("7098", "删除失败：数据不存在"),
    DELETION_FAILED_IN_NON_EDITING_STATE("7099", "删除失败：非编辑状态不允许删除"),
    BAR_CODE_IS_NOT_BATCH_MNG("7100", "物料不是按批次管理物料，不能新增批次"),
    BAR_CODE_BATCH_ADD_NEED_AUTO_INCREMENT("7101", "编码规则需要存在自动生成序号"),
    BAR_CODE_PROHIBIT_STACK_THE_SAME_SITUATION("7102", "批号禁止重复上架到同一个位置"),
    BAR_CODE_AND_ACTUAL_QUANTITY_TOTAL_DISCREPANCY("7103", "批次总数与实际数量不符，是否以批次总数入库？"),
    ACTUAL_SMALLER_THAN_PLAN("7104", "存在实际数量不等于计划数量的物料，是否确认出库？"),
    BAR_CODE_NEED_RULE_DETAIL("7105", "新增批次需要编码规则数据"),
    CODE_NO_WAREHOUSE("7074", "物料编码{0}不存在该仓库！"),
    ACTUAL_OUTWEIGH_STOCK("7075", "实际数量大于库存数量，不可出库！"),
    GENERATE_RULE_CODE_NEED_COMPOSE_INFO("7076", "生成编码时需要组成信息：{0}"),
    BATCH_IS_BIND_PRODUCT_FLOW_CODE("7201", "批次绑定了流水码，不能删除！"),


    // ==========生产 6000-7000==============
    FACILITIES_BINDED("6000", "产线已绑定工位"),

    GUIDANCE_FAIL2ADD("6101", "作业指导添加失败"),

    GUIDANCE_FAIL2UP("6102", "作业指导修改失败"),

    GUIDANCE_FAIL2DEL("6103", "作业指导删除失败"),

    GUIDANCE_FAIL2SEL("6104", "所查询作业指导不存在"),

    GUIDANCE_EXIST("6105", "作业指导已存在"),

    PRODUCTION_FAIL2SAME_CODE("6106", "产品编号重复"),

    PRODUCTION_FAIL2UP("6107", "产品修改失败"),

    PRODUCTION_FAIL2DEL("6108", "产品删除失败"),

    PRODUCTION_FAIL2SEL("6109", "所查询成品不存在"),

    WEIGHING_FAIL2ADD("6110", "来料信息添加失败"),

    WEIGHING_FAIL2UP("6111", "来料信息修改失败"),

    WEIGHING_FAIL2DEL("6112", "来料信息删除失败"),

    WEIGHING_FAIL2SEL("6113", "所查询来料信息不存在"),

    SCHEDULE_FAIL2ADD("6114", "生产计划添加失败"),

    SCHEDULE_FAIL2UP("6115", "生产计划修改失败"),

    SCHEDULE_FAIL2DEL("6116", "生产计划删除失败"),

    SCHEDULE_FAIL2SEL("6117", "所查询称重信息不存在"),

    SCHEDULE_EXIST("6118", "生产计划已执行，不可再修改计划"),

    EXECUTE_FAIL2ADD("6119", "生产表添加失败"),

    EXECUTE_FAIL2UP("6120", "生产表修改失败"),

    EXECUTE_FAIL2DEL("6121", "生产表删除失败"),

    EXECUTE_FAIL2SEL("6122", "所查询生产信息不存在"),

    EXECUTE_EXIST("6123", "生产计划已执行，不可修改或删除计划"),

    LINE_BIND_WORK_ORDER("6124", "该产线已绑定工单，不允许删除"),

    REPORT_NOT_ALLOW("6125", "该产线存在正在报工的工单，该工单不允许上报"),

    COUNTER_OCCUPIED("6126", "该产线计数器被占用，该工单不允许上报"),
    ORDER_HANG_UP("6127", "工单已挂起"),
    ORDER_FINISHED("6128", "工单已完成"),
    DUPLICATE_CAPACITY_CONFIGURATION("6129", "该产能配置已重复，请重新配置"),
    NOT_FOUND_LINE("6130", "未找到该产线，请重新配置"),
    NOT_FOUND_MATERIAL("6131", "未找到该物料，请重新配置"),
    NOT_FOUND_WORK_ORDER("6132", "工单编号信息有误，请检查"),
    WORK_ORDER_STATE_ERROR("6133", "创建状态的工单不可生成工单批次"),
    NOT_FOUND_WEIGHT_RECORD("6134", "称重记录id信息有误，请检查"),
    INVEST_TIME_LATER_THAN_CURRENT_TIME("6135", "投产时间不能晚于当前时间"),
    WORK_ORDER_WITHOUT_LINE("6136", "该工单未选择产线"),
    NOT_FOUND_FLOW_CARD("6137", "解析不到对应流水码或成品码 ！"),
    WORK_ORDER_BATCH_MATERIAL_IS_REQUIRED("6138", "生产批次配置的批次物料为必填"),
    WORK_ORDER_IS_PRODUCING("6139", "该工单在生产中或者生产完成，是否删除?"),
    WORK_ORDER_NO_HAS_PRODUCING_BASIC_UNIT("6140", "该工单下没有投产的生产基本单元"),
    WORK_ORDER_NO_MAPPING_PRODUCING_BASIC_UNIT("6141", "所选择报工的生产基本单元未投产到该工单下"),


    //==========生产模型 5500-5999==============
    DEVICE_WORK_ORDER_IS_BLANK("5500", "工单不可为空"),

    DEVICE_FINISH_REPORT_IS_EXIST("5501", "该工单当天完成记录已存在"),

    //==========生产模型 5000-5500==============
    AREA_FAIL2ADD("5101", "厂区添加失败"),

    AREA_FAIL2UP("5102", "厂区修改失败"),

    AREA_FAIL2DEL("5103", "厂区删除失败"),

    AREA_GRID_CASCADE("5104", "厂区下存在未删除的车间"),

    AREA_FAIL2SEL("5105", "所查询厂区不存在"),

    AREA_CODE_REPEAT("5106", "所填写厂区编号已存在"),

    AREA_NAME_REPEAT("5108", "所填写厂区名称已存在"),

    GRID_CODE_REPEAT("5109", "所填写车间编号已存在"),

    GRID_NAME_REPEAT("5110", "所填写车间名称已存在"),

    LINE_CODE_REPEAT("5111", "所填写产线编号已存在"),

    LINE_NAME_REPEAT("5112", "所填写产线名称已存在"),

    FACI_NAME_REPEAT("5113", "所填写工位名称已存在"),

    GRID_FAIL2SEL("5114", "查询车间不存在"),

    GRID_FAIL2DEL("5115", "车间已被删除"),

    GRID_PRODUCT_LINE_CASCADE("5116", "车间下存在未删除的产线"),

    GRID_FAIL2UP("5117", "车间修改失败"),

    CUBICLE_WITHOUT_PRODUCTION_LINE("5118", "请指定产线"),

    PRODUCE_WITH_SENSOR_WITHOUT_EUI("5119", "请输入eui"),

    SECURITY_WITHOUT_GID("5120", "请选择车间"),

    FID_IS_NULL("5121", "fid不能为空"),

    TYPE_ONE_CODE_IS_NULL("5122", "类型1代码不能为空"),

    NAME_EXIST("5123", "名称已存在"),

    FAC_NOT_FOUND("5124", "未找到相关设施/工位"),

    LINE_BOARD_NOT_BIND("5125", "产线看板未绑定"),

    CUBICLE_BOARD_BINDED("5126", "工位机已绑定"),

    LINE_BOARD_BINDED("5127", "产线看板已绑定"),

    CUBICLE_BOARD_NOT_BINDED("5128", "工位看板未绑定"),

    FAC_STATE_NO_CHANGE("5129", "工位状态未改变"),

    LINE_NOT_FOUND("5130", "未找到相关产线"),

    FACILITIES_UNBOUND_SENSOR("5131", "工位未绑定相应的的计数器"),

    MODEL_TABLE_NOT_FOUND("5132", "未获取到工厂模型表"),

    MODEL_TABLE_NAME_FAIL("5133", "所导入表格名不是为“工厂模型”"),

    LCD_LINE_MODEL_NOT_FOUND("5134", "未找到LCD单颗IC线对应的产线模型"),

    MODEL_NAME_DUPLICATE("5135", "模型名称重复"),

    MODEL_IS_NOT_DELETE("5136", "该设备模型已绑定设备实例，不可删除"),

    MODEL_TYPE_NOT_EXIT("5137", "模型类型不存在"),

    MODEL_NOT_EXIT("5138", "模型不存在"),

    MODEL_CODE_NOT_IS_NUMBER("5139", "编号必须为数字，请重新输入"),

    WORK_CENTER_IS_NOT_UPDATE("5139", "制造单元已关联工单，无法更换工作中心"),

    PRODUCTION_LINE_IS_NOT_EXIT("5140", "制造单元：{0}不存在"),

    LINE_GRID_IS_EXIT("5141", "制造单元：{0}已绑定车间：{1}"),
    PARAM_NOT_NULL("5142", "传入的参数不能为空"),
    WORK_CENTER_IS_NOT_FOUND("5143", "工作中心不存在"),
    WORK_CENTER_NEED_MAIN_RESOURCE("5144", "工作中心必须存在主资源"),


    //==========传感器 4000-4500==============
    INCORRECT_PARAMETER("4000", "参数不正确"),

    COUNTER_STATE_ERROR("4100", "计数器状态码异常"),

    COUNTER_F_STATE_ERROR("4101", "设施/工位状态码异常"),

    COUNTER_NOT_FOUND("4102", "未找到相关计数器"),

    COUNTER_NOT_FOUND_CACHE("4103", "缓存中未找到相关计数器"),

    COUNTER_FAC_NOT_FOUND("4105", "未找到相关设施/工位"),

    COUNTER_COUNT_ERROR("4106", "计数器当前计数少于前一次计数"),

    COUNTER_DATE_ERROR("4107", "计数器当前时间早于上次上报时间"),

    SENSOR_NAME_DUPLICATE("4108", "传感器名称重复"),

    SENSOR_EUI_DUPLICATE("4109", "传感器eui重复"),

    DEVICE_CODE_DUPLICATE("4110", "设备编码重复"),

    SENSOR_NOT_EXIST("4111", "采集设备不存在"),

    DEVICE_NAME_DUPLICATE("4112", "设备名称重复"),

    //===========加锁=============
    LOCK_TAKES_UP("4113", "当前方法已被占用，请稍后再试"),
    PAGE_IN_OPERATION("4114", "当前页面有数据正在被操作!"),
    NOT_EXIST_DEVICE("4115", "找不到该设备"),
    NOT_EXIST_MODEL("4116", "找不到该模型"),
    DEVICE_UPDATE_ERROR("4117", "添加tag更改设备异常"),
    ELECTRONIC_RACK_NOT_EXIT("4118", "电子料架不存在"),
    ELECTRONIC_RACK_COLOR_NOT_EXIT("4119", "电子料架指示灯信息应该为0-7"),
    ELECTRONIC_STOCK_NOT_EXIT("4120", "库位不能为空"),
    ORDER_IS_RETURNING("4121", "当前单据正在回退中，请稍后再试"),

    //==========基础信息 4500-5000==============
    TERMS_NAME_DUPLICATE("4501", "条款名称重复"),
    UNIT_NAME_DUPLICATE("4502", "单位名称重复"),
    MATERIAL_TYPE_CODE_DUPLICATE("4503", "物料类型编码重复"),
    MATERIAL_TYPE_NAME_DUPLICATE("4503", "物料类型名称重复"),
    DEVICE_SORT_NAME_DUPLICATE("4504", "设备类型名称重复"),
    SHIFT_TYPE_DUPLICATE("4505", "班次类型名称重复"),
    STOPS_NAME_DUPLICATE("4506", "停机类型名称重复"),
    CALENDAR_TIME_IS_BLANK("4507", "日历模板编号已存在"),
    CALENDAR_TEMPLATE_IS_NOT_EXIST("4508", "日历模板不存在"),
    CALENDAR_TEMPLATE_DETAIL_IS_NOT_EXIST("4509", "日历模板明细不存在"),
    CALENDAR_TEMPLATE_DATE_TYPE_IS_BLANK("4510", "模板明细的日期类型不能为空"),
    CALENDAR_TEMPLATE_WORK_TYPE_IS_BLANK("4511", "模板明细的工作类型不能为空"),
    CALENDAR_TEMPLATE_PRIOR_IS_BLANK("4512", "模板明细的优先级不能为空"),
    CALENDAR_CODE_IS_EXIST("4513", "日历编号已存在"),
    CALENDAR_BEGIN_IS_AFTER_THAN_END("4514", "结束时间不可早于开始时间"),
    CALENDAR_HAS_BEEN_USED("4515", "日历已被使用，不可删除"),
    CALENDAR_DEPLOY_IS_EXIST("4516", "日历配置已存在"),
    FILE_EXPORT_FAILED("4517", "数据导出失败"),
    SHIFT_WORK_TIME_IS_BLANK("4518", "工作时间不能为空"),
    SHIFT_TIME_INTERVAL_INCOMPLETE("4519", "请将时间区间填写完整"),
    TEMPLATE_IS_NOT_EXIST("4520", "模板文件不存在,请上传模板文件"),
    TEMPLATE_IS_FAIL("4521", "该类型的单据导入未实现"),
    IMPORT_OPREATION_NOT_RE("4522", "已经存在正在执行导入的数据，请稍后重试"),
    TEMPLATE_RAW_FILE_IS_NOT_EXIST("4523", "导入原始数据文件没有保存成功，重新导入"),
    TEMPLATE_MODE_IS_NULL("4524", "导入的模板指定的导入数据模式为空，重新导入指定好导入数据模式的模板！"),
    FILE_DOWNLOAD_FAILED("4525", "文件下载失败"),
    TEMPLATE_IS_ERROR_FORMAT("4526", "请导入xlsx格式的模板文件"),
    FILE_NOT_EXIST("4527", "文件不存在"),
    DUPLICATE_WAREHOUSE_TYPE("4528", "仓库类型重复"),
    DICT_NAME_DUPLICATE("4529", "基础配置名称重复"),
    CONFIG_PARAMS_IS_NOT_JSON("4530", "{0}[{1}]的接口请求参数不是json格式"),
    CONFIG_OPTION_VALUES_IS_NOT_JSON("4531", "{0}[{1}]的可选值选项不是json格式"),
    CONFIG_VALUE_IS_NOT_JSON("4532", "{0}[{1}]的配置值不是json格式"),
    CONFIG_CODE_DUPLICATE("4533", "配置的全路径编码重复"),
    CONFIG_VALUE_CODE_DUPLICATE("4534", "配置值[{0}]的全路径编码重复"),
    CLASS_NOT_FOUND("4535", "className参数错误，未找到对应的实体类"),
    CALENDAR_DATE_IS_BLANK("4536", "班次时间不能为空"),
    EXIST_RELATED_FAC_QUALITY_INSPECT_RECORD("4537", "存在关联的工位质检记录"),
    EXIST_RELATED_FAC_QUALITY_INSPECT_PLAN("4538", "存在关联的工位质检方案"),
    EXIST_RELATED_PROCEDURE_DEFINE("4539", "存在关联的工序定义"),
    EXIST_RELATED_MAINTAIN_RECORD("4540", "存在关联的质检返工记录"),
    EXIST_RELATED_MAINTAIN_PLAN("4541", "存在关联的质检返工方案"),
    EXIST_RELATED_FAC("4542", "存在关联的工位"),
    EXIST_RELATED_CRAFT_PROCEDURE("4543", "存在关联的工艺工序"),
    EXIST_RELATED_PROCEDURE_INSPECT("4544", "存在关联的工序检验项"),
    EXIST_RELATED_PRODUCT_INSPECT_PLAN("4545", "存在关联的产品检验方案"),
    CRAFT_PROCEDURE_IS_LOOP("4546", "工艺工序存在顺序循环引用，请检查后重试"),
    TEAM_TYPE_NAME_IS_DUPLICATE("4547", "班组类型名称重复"),
    TEAM_TYPE_CODE_IS_DUPLICATE("4548", "班组类型编码重复"),
    TEAM_TYPE_CODE_RELATED_TEAM("4549", "存在关联班组"),
    PROCEDURE_PROCESS_PARAMETER_IS_NULL("4550", "工艺参数默认值为空"),
    PROCEDURE_PROCESS_PARAMETER_CODE_IS_DUPLICATE("4551", "工艺参数编码重复"),
    PROCEDURE_PROCESS_PARAMETER_NAME_IS_DUPLICATE("4552", "工艺参数名称重复"),
    REDIRECT_CODE_IS_DUPLICATE("4553", "唯一标识重复"),
    UNIT_NOT_FOUND("4554", "单位不存在"),
    DEVICE_NOT_FOUND("4555", "设备不存在"),
    CRAFT_PARAMETER_VALUE_IS_NULL("4556", "工艺参数值不能为空"),
    ORDER_TYPE_NAME_IS_DUPLICATE("4557", "同个单据大类下单据类型名称不能重复"),
    INNER_DATA_NOT_MODIFY("4558", "内置数据不能修改"),
    ORDER_TYPE_EXIST("4559", "系统中存在相同编码的数据，请开发人员确认"),
    BUSINESS_TYPE_NOT_EXIST("4560", "系统中不存在:{0}的业务类型"),
    ORDER_TYPE_CODE_IS_DUPLICATE("4561", "同个单据大类下单据类型编码不能重复"),
    CANNOT_FIND_ORDER_TYPE("4562", "未找到对应的单据类型"),
    CANNOT_FIND_CATEGORY_CODE_TYPE("4562", "未找到对应的单据大类"),
    CANNOT_FIND_BUSINESS_TYPE("4562", "未找到对应的业务类型"),
    AT_LEAST_ONE_DEFAULT_IS_REQUIRED("4563", "至少要有一个默认值"),
    AT_LEAST_ONE_DEFAULT_IS_ENABLE("4564", "至少要有一个启用"),
    TERMS_EXIST("4565","条款已存在"),
    TERMS_NOT_EXIST("4566","条款不存在"),

    //==========用户 3000-4000==============
    USER_EXIST("3001", "账号已被注册"),
    TOKEN_EXPIRED("3002", "登录信息过期，请重新登录"),
    PERMISSION_DENIED("3003", "权限不足"),
    AUTHORIZATION_TYPE_NOT_SUPPORT("3004", "不支持该认证类型"),
    REFRESH_TOKEN_EXPIRED("3005", "刷新令牌已过期，请重新登录"),
    SCOPE_INVALID("3006", "不是有效的scope值"),
    REFRESH_TOKEN_INVALID("3007", "refresh token无效"),
    USER_LOCKED("3008", "用户已被锁定，请联系管理员"),
    OLD_PASSWORD_FAIL("3009", "原始密码错误"),
    USER_OR_PASSWORD_ERROR("3010", "用户名或密码错误"),
    ROLE_NOEXIST("3011", "角色ID不存在"),
    USER_NOT_EXIST("3012", "用户不存在"),
    NICKNAME_EXIST("3013", "已存在启用状态的用户姓名，请重新填写并做好区分"),

    USERNAME_EXIST("3014", "用户名不能重复"),

    ROLE_PERMISSION_NOT_EMPTY("3015", "角色权限不能为空"),

    YELINK_ONCALL_NOT_ALLOWED("3016", "yelinkOncall用户不允许新增用户"),

    PASSWORD_IS_SAME_AS_OLD("3017", "新密码与旧密码相同"),

    DEPARTMENT_EXIST_RELATE_USER("3100", "部门存在关联的用户"),

    ROLE_EXIST_RELATE_USER("3101", "角色存在关联的用户"),

//    USER_AT_LEAST_ONE_ROLE("3102", "移除的用户至少有一个角色"),

    USER_EXIST_RELATE_DEPARTMENT("3103", "禁用的用户属于部门的负责人"),

    DEPARTMENT_NAME_REPEAT("3104", "同级下的部门名称重复"),

    ROLE_NAME_REPEAT("3105", "角色名称重复"),

    ONLY_ADMIN_CAN_OPERATE("3106", "只有管理员才能操作"),

    NO_WORK_CENTER_PERMISSION("3107", "用户未绑定任何工作中心权限"),

    PAD_MAC_REPEAT("3108", "PAD MAC地址重复"),

    PAD_NOT_EXIST("3109", "Pad不存在"),

    PAD_ADD_FAILED("3110", "Pad新增失败"),

    PERMISSION_ID_CONFLICT("3111", "权限ID冲突,权限更新失败"),

    DUPLICATION_OF_ROLES("3112", "keycloak域角色重复: {0}"),

    KC_USER_ADD_EXCEPTION("3203", "keycloak client 用户添加异常"),

    KC_USER_LOGIN_EXCEPTION("3204", "用户登录失败"),

    KC_USER_ENABLE_EXCEPTION("3205", "keycloak client 用户启用禁用异常"),

    KC_USER_RESSET_EXCEPTION("3206", "keycloak client 用户重置密码异常"),

    KC_USER_UPDATE_EXCEPTION("3207", "keycloak client 用户修改异常"),

    FAILED_TO_ADD_DEPARTMENT("3208", "部门添加失败"),

    PERMISSION_DATA_UPDATE_FAILED("3209", "小程序权限数据更新失败"),

    TEAM_CODE_REPEAT("3210", "班组编号重复"),

    POST_CODE_REPEAT("3211", "岗位编码已存在"),

    POST_LEVEL_REPEAT("3212", "岗位等级已存在"),

    POST_EXIST_RELATE_USER("3213", "岗位存在关联的用户"),

    POST_EXIST_RELATE_RELEASED_CRAFT_PROCEDURE("3214", "岗位存在关联的生效工艺工序"),

    POST_BE_ASSOCIATED_OTHER_POST("3215", "该岗位被其他岗位关联"),

    EXIST_CHILDREN_DEPARTMENT("3216", "该组织存在下级部门，请先删除下级部门"),
    DEPARTMENT_CODE_REPEAT("3217", "部门编码重复"),
    CANNOT_REMOVE_YELINK_ONCALL("3218", "系统运维角色不能移除yelinkoncall账号"),
    CANNOT_REMOVE_ADMIN("3219", "管理员角色不能移除admin账号"),
    CANNOT_DISABLE_BUILT_IN_USER("3220", "内置用户不能禁用"),
    JINZHI_ADD_USER_FAIL("3221", "精制同步新增用户时出现问题，请联系精制开发人员"),
    SYS_DATA_NOT_DELETE("3222", "系统数据，请勿删除!"),
    TEAM_NOT_FOUND("3223", "班组不存在"),
    JZ_NO_PERMISSION_ASSIGN_ADMIN_ROLE("3224", "您没有权限分配管理员角色"),

    JZ_NO_PERMISSION_ASSIGN_ROLE("3225", "您没有权限分配角色"),
    NEED_ADMIN_PERMISSION("3226", "只有管理员才能操作"),
    CANNOT_DELETE_INNER_USER("3227", "内置用户不能删除"),
    UPDATE_ROLE_NOT_PERMIT("3228", "改角色不允许修改"),
    TEAM_NAME_REPEAT("3229", "班组名称重复"),

    //==========员工相关 2000-3000==============
    LINE_EMPLOYEE_FAIL2ADD("2001", "产线关联用户失败"),

    LINE_EMPLOYEE_FAIL2UP("2002", "更新产线关联用户失败"),

    INSERT_EMPLOYEE_FAIL("2003", "员工添加失败"),

    EMPLOYEE_NOEXIST("2004", "员工ID不存在"),

    EMPLOYEE_JOBNUMBER_FAIL("2005", "所填写工号已重复"),

    EMPLOYEE_MOBILE_FAIL("2006", "所填写手机号已重复"),

    //==========订单工单相关 1100-1500==============

    WORK_ORDER_IS_NOT_OPEN("1100", "作业工单特性未开启"),
    CUSTOMER_IS_NOT_EXIST("1101", "客户不存在"),
    IMPORT_FILE_FAC_TYPE_NAME_NOT_LIST("1102", "导入的文件中工位类型名称不在工序对应的工位列表中"),
    CAN_NOT_FOUND_CORRESPONDING_CRAFT("1103", "下推失败，无法找到产品对应的工艺"),
    ORDER_NUMBER_EXCEED_THIRTY_QUANTITY("1104", "添加失败"),
    CAN_NOT_ISSUE_AGAIN("1105", "该工单已下发，请勿重复下发"),
    MUST_BE_IN_THE_SAME_STATE_TO_MODIFY("1106", "更改时单据状态必须一致"),

    SELECTED_OPERATION_MUST_BE_THE_SAME_OPERATION_GROUP("1107", "所选工序必须为同一工序组"),

    THE_TRACKING_NUMBER_DOES_NOT_EXIST("1108", "该单号不存在，请重新扫码"),

    ORDER_MATERIAL_HAS_NO_PROCESS_INFORMATION("1109", "该订单物料没有工艺信息"),

    ORDER_MATERIAL_FOR_WORK_ORDER_DOES_NOT_MATCH("1110", "该工单所关联的订单物料不匹配"),

    MULTIPLE_PRODUCTION_ORDERS_ARE_ASSOCIATED("1111", "该工单关联了多个生产订单"),

    NO_WORK_ORDER_RELATED_ORDER_INFORMATION_FOUND("1112", "未找到该工单关联订单信息"),
    NO_WORK_ORDER_LIST_BY_STATE("1113", "该状态的工单列表为空"),
    NO_WORK_ORDER_NUMBER_LIST_BY_STATE("1114", "该状态的工单号列表为空"),
    WORK_ORDER_NOT_FAC("1115", "该工单不是这个工位下的"),

    NO_WORK_ORDER_PERMISSION("1116", "无该工单权限"),

    WORK_ORDER_BIND_PROCESS_NOT_FOUND("1117", "未找到工单绑定的工序信息"),

    NOT_ALLOW_TO_MODIFY("1146", "不允许修改"),

    WORK_ORDER_MODIFICATION_STATUS("1147", "存在未完成的工单，该订单状态不可以修改为关闭状态"),

    WORK_ORDER_DELETE_FAIL2("1148", "工单删除失败，该工单存在子工单"),

    ORDER_MODIFICATION_STATUS("1149", "存在未关闭的订单，该工单状态不可以修改为关闭状态"),

    TAKE_OUT_APPLICATION_NUM_DUPLICATE("1150", "生产领料单号重复，请刷新重试"),

    NUMBER_DUPLICATE("1151", "编号重复"),

    ORDER_TO_EXAMINE_FAIL("1152", "该订单操作失败"),

    ORDER_EXAMINE_NO_MATCH("1153", "审核失败，您与该订单审核人不匹配"),

    ORDER_APPROVAL_NO_MATCH("1154", "批准失败，您与该订单批准人不匹配"),

    WORK_ORDER_TO_EXAMINE_FAIL("1155", "该工单操作失败"),

    WORK_ORDER_EXAMINE_NO_MATCH("1156", "审核失败，您与该工单审核人不匹配"),

    WORK_ORDER_APPROVAL_NO_MATCH("1157", "批准失败，您与该工单批准人不匹配"),

    MATERIAL_TYPE_RELATE_DATA("1160", "存在关联该物料类型的物料"),

    HAS_RUNNING_WORKORDER("1161", "该产线存在正在运行的工单"),

    WORKORDER_NOT_RUNNING("1162", "该工单未运行"),

    IMPORT_FILE_INCLUDE_NON_EXIST_PROCEDURE("1163", "导入的文件包含不存在的工序"),

    IMPORT_FILE_INCLUDE_NON_EXIST_FAC("1164", "导入的文件包含不存在的工位类型"),

    ABNORMAL_NAME_NOT_NULL("1165", "不良原因不能为空"),

    WORK_ORDER_EDIT_FALL("1166", "当前制造单元/班组已存在投产状态的工单"),

    NOT_SALE_ORDER_RECORD("1167", "暂无可同步的销售订单"),

    CAN_NOT_OPERATE("1168", "该订单已下发，不能进行操作"),

    NOT_FOUND_INSPECT_ORDER("1169", "暂无可齐备性检查的订单"),

    WORK_ORDER_END_DATE_AFTER("1170", "工单计划结束时间不能早于计划开始时间"),

    WORK_ORDER_START_DATE_AFTER("1171", "工单计划开始时间不能早于工单创建时间"),

    NOT_FOUND_INSPECT_WORK_ORDER("1172", "暂无可齐备性检查的工单"),

    PRODUCT_ORDER_NOT_SCHEDULE("1173", "该生产订单不能排程"),

    SCHEDULED_PRODUCT_ORDER_NOT_SPLIT("1174", "已排程的生产订单不能拆单"),

    SOMEONE_IS_PERFORMING_THE_OPERATION("1175", "该操作正在被执行，请勿重复操作"),

    ONLY_FINISHED_PRODUCTS_CAN_BE_SPLIT("1176", "非成品订单不能拆单"),

    NOT_MEET_THE_ORDER_TRANSFER_REQ("1177", "该单不满足挪单要求，请重新选择"),

    ILLEGAL_ACCESS("1178", "属性不能访问"),

    WORK_ORDER_IMPORT_DUPLICATE("1179", "所导入的ERP同步单号已重复导入，请重新导入"),

    UNSATISFIED_SPLIT_ORDER_CONDITION("1180", "该订单不满足拆单条件"),

    DUPLICATE_WORK_ORDER_RECORD("1181", "该工单已有绑定的数据，不可重复绑定！"),

    WORK_ORDER_HANG_UP_FAIL("1182", "该工单挂起失败！"),

    WORK_ORDER_IMPORT_DEFEAT("1190", "工单导入失败，请重新导入"),

    EXPORT_WORK_ORDER_SCHEDULE_DEFEAT("1191", "找不到物料"),

    IMPORT_FILE_INCLUDE_NON_EXIST_UNIT_TYPE("1193", "导入的文件中包含不和工序关联的制造单元模型"),

    IMPORT_FILE_CONTRACTING_OPERATION_IS_YES_OR_NO("1194", "导入的文件中检验工序应该填写是或否"),

    IMPORT_FILE_OUT_SOURCING_SUPPLIER_NOT_EXIST_LIST("1195", "委外供应商名称不在委外供应商类型的列表中"),

    IMPORT_FILE_OUT_SOURCING_SUPPLIER_NOT_NULL("1196", "导入的文件中委外供应商的名称不能为空"),

    IMPORT_FILE_INSPECTION_OPERATION_IS_YES_OR_NO("1197", "导入的文件中委外工序应该填写是或否"),

    PATH_PARAM_ERROR("1198", "路径参数错误"),

    DATE_FORMAT_ERROR("1199", "时间格式不正确，格式为：yyyy-MM-dd"),

    IMPORT_FAIL("1200", "数据导入错误"),

    WORK_ORDER_ADD_FAILED("1201", "添加失败，此时间段产线正在生产中"),

    WORK_ORDER_FAIL2TIMEOUT("1202", "工单已过启动时间，不可以修改"),

    WORK_ORDER_UPDATE_FAILED("1203", "更新失败，此时间段产线正在生产中"),

    WORK_ORDER_DELETE_FAIL("1204", "工单删除失败"),

    WORK_ORDER_SELECT_FAIL("1205", "工单编号:{0}不存在或已被删除，请勿操作！"),
    WORK_ORDER_ID_SELECT_FAIL("1205", "工单id:{0}不存在或已被删除，请勿操作！"),
    WORK_ORDER_NAME_SELECT_FAIL("1205", "工单名称:{0}不存在或已被删除，请勿操作！"),

    WORK_ORDER_IMPORT_FAIL("1206", "重复导入"),


    IMPORT_TABLE_NAME("1207", "导入的表名字错误，应为sheet1"),

    OPERATION_FAILED("1208", "操作此条数据失败"),

    ORDER_TIME_FAIL("1209", "计划交货日期不能小于计划开始时间"),

    ORDER_DELIVERY_DATE_FAIL("1210", "发货日期不能小于要货日期"),

    ORDER_NUMBER_FAIL("1211", "单据订单编号重复"),

    WORK_ORDER_CODE_DUPLICATE("1212", "单据编号重复"),

    ORDER_CODE_IS_BLANK("1213", "订单编号不能为空"),

    WORK_ORDER_CODE_IS_BLANK("1214", "工单编号不能为空"),

    WORK_ORDER_NAME_IS_BLANK("1215", "工单名称不能为空"),

    PNUMBER_IS_BLANK("1216", "父工单编号栏：父工单不能有值，子工单不能为空"),

    ID_SEQUENCE_IS_BLANK("1217", "子工单顺序栏：父工单不能有值，子工单不能为空"),

    LINE_CODE_IS_BLANK("1218", "产线编号栏：父工单不能有值，子工单不能为空"),

    PLAN_QUALITY_IS_BLANK("1219", "计划数量不能为空"),

    START_DATE_IS_BLANK("1220", "起始时间不能为空"),

    END_DATE_IS_BLANK("1221", "结束时间不能为空"),

    ORDER_NAME_IS_BLANK("1222", "订单名称不能为空"),

    TIME_FORMAT_ERROR("1223", "时间格式不正确，格式为：yyyy/MM/dd HH:mm:ss"),

    PRODUCT_NAME_NOT_FOUNT("1224", "请输入正确的产品名称"),

    PRODUCT_VERSION_NOT_FOUNT("1225", "请输入正确的产品版本号"),

    PRODUCT_CODE_NOT_FOUNT("1226", "所输入产品编号不存在"),

    CANCEL_WORK_ORDER_FAILED("1227", "状态变更为取消失败，该工单:{0}存在报工记录"),

    ORDER_NOT_FOUNT("1230", "未查询到该订单"),

    ORDER_NAME_NOT_FOUNT("1231", "请输入正确的订单名称"),

    CUSTOMER_CODE_REPEAT("1232", "客户代码不能重复"),

    CUSTOMER_NAME_REPEAT("1233", "客户名称不能重复"),

    PRODUCT_IS_EMPTY("1234", "产品不能为空"),

    ORDER_NAME_DUPLICATE("1235", "订单名称重复,请重新填写"),

    ORDER_NOT_ALLOW_MODIFY("1236", "该状态下订单不允许修改"),

    WORK_ORDER_NAME_DUPLICATE("1237", "工单名称重复，请重新填写"),

    NOT_ALLOW_TO_MODIFY_CREATED_STATE("1238", "不允许修改为创建状态"),

    BOM_LIST_IS_NULL("1239", "物料清单不能为空"),

    NOT_ALLOW_TO_REMOVE("1240", "该状态下不允许删除"),

    NOT_ALLOW_TO_UPDATE("1242", "该状态下不允许修改"),
    SUP_BOM_CODE_DUPLICATE("1243", "该供应商物单号称已重复，请刷新重试"),
    ORDER_NOT_EXIST("1244", "订单不存在"),
    ORDER_NOT_EXIST_MATERIAL("1245", "选择的销售订单没有关联的物料"),
    ORDER_PRODUCT_TIME_FAIL("1246", "计划开始完成时间不能小于计划开始生产时间"),
    COUNTERPART_CODE_FAIL("1247", "本厂对接人不存在请确认"),
    PRODUCT_ORDER_MATERIAL_LIST_CODE_REPEAT("1248", "生产订单用料清单编号已存在"),
    GRID_CODE_IS_BLANK("1249", "该订单所属车间信息为空"),
    WORK_ORDER_NUMBER_NOT_NULL("1250", "工单号不能为空"),
    IMPORT_FAIL_TEMPLATE("1251", " 导入数据错误，请按模板进行导入"),
    RULE_HAS_MANUAL_INPUT("1252", "编码规则存在人工输入的组成信息"),
    RULE_HAS_NOT_AUTO_INCREMENT("1253", "编码规则没有自增序列号的组成信息"),
    WORK_ORDER_PLAN_DUPLICATE_DATE("1254", "工单分日计划日期重复"),
    WORK_ORDER_INSPECT_FAIL("1255", "{0}{1}类型的送检单，不允许{2}"),
    CUSTOMER_MATERIAL_NOT_REPEAT("1256", "客户物料不允许重复"),
    SUPPLIER_MATERIAL_NOT_REPEAT("1257", "供应商物料不允许重复"),
    WORK_ORDER_MATERIAL_LIST_CODE_REPEAT("1258", "生产工单用料清单编号已存在"),
    WORK_ORDER_NOT_RELATE_PROCEDURE("1259", "工单没有关联工序"),
    WORK_ORDER_FINISH_NOT_LESS_THAN_DEFECT_COUNT("1260", "工单不良数不能大于完成数"),
    CUSTOMER_NOT_EXIST("1261", "客户不存在"),
    PRODUCT_ORDER_NOT_FOUND("1262", "生产订单不存在"),
    SALE_ORDER_NOT_FOUND("1263", "销售订单不存在"),
    WORK_ORDER_NOT_FOUND_MATERIAL_LIST("1264", "工单:{0}找不到对应的用料"),
    NOT_PUSH_DOWN_BECAUSE_HAS_WORK_ORDER_CODE_DUPLICATE("1265", "下推失败！失败原因：\n按工艺路线下推工单时出现重复的工单编码：{0}"),
    ASSIGNMENT_STATE_CONFIG_IS_NOT_ACCORD_WITH_PUSH_DOWN("1266", "下推失败！\n由于工单配置派工状态为已派工, 且基本生产单元必填，不符合下推工单的条件。"),
    EMPTY_MATERIAL_WITH_PUSH_DOWN("1267", "没有可下推的物料"),
    NOT_PUSH_DOWN_BECAUSE_HAS_MANY_DEFAULT_CRAFT("1268", "下推失败！失败原因：\n该物料：{0}存在多个默认工艺"),
    PRODUCT_ORDER_NUMBER_IS_EXIST("1269", "生产订单号已存在"),
    NOT_PUSH_DOWN_BECAUSE_HAS_ORDER_NUMBER_DUPLICATE("1270", "下推失败！失败原因：\n按工艺路线下推工单时出现重复的委外订单号：{0}"),
    NOT_PUSH_DOWN_BECAUSE_HAS_NOT_PROCEDURE_MATERIAL("1271", "下推失败！失败原因：\n工艺编码：{0}的工序{1}无工序物料，请前往工艺进行配置"),
    NOT_PUSH_DOWN_BECAUSE_HAS_NOT_RULE_NUMBER("1272", "下推失败！失败原因：\n编码规则编号生成失败，请通知开发定位"),
    NOT_PUSH_DOWN_BECAUSE_NO_CRAFT("1273", "下推失败！失败原因：\n物料：{0}存在工作中心为空的工序"),
    NOT_PUSH_DOWN_BECAUSE_HAS_MANY_CRAFT("1274", "下推失败！失败原因：\n该物料：{0}存在多个工艺"),
    NOT_PUSH_DOWN_BECAUSE_HAS_CRAFT_PROCEDURE_CIRCLE("1275", "下推失败！失败原因：\n该物料：{0}工艺工序中有环"),
    CANNOT_GENERATE_WORK_ORDER_NUMBER("1276", "下推失败！失败原因：\n生成工单号失败，请联系开发定位"),
    NOT_SCAN_CREATED_WORK_ORDER("1277", "不允许扫创建态工单，请重新扫码"),
    NOT_PUSH_DOWN_WORK_ORDER_MATERIAL_LIST_BECAUSE_HAS_NO_DATA("1278", "工单{0}没有可下推的数据"),
    NEED_NUMBER_RULE("1279", "编码为空，请确认参数传递是否正确"),
    CUSTOMER_STATE_NOT_ALLOWED_DELETE("1280", "客户档案编码:{0}状态不符合删除条件"),

    //==========产品相关 1500-2000==============
    CRAFT_FAIL2SAME_CODE("1501", "工艺编号重复"),

    CRAFT_FAIL2UP("1502", "工艺修改失败"),

    CRAFT_FAIL2DEL("1503", "工艺删除失败"),

    CRAFT_REPEAT_IMPORT_FAIL("1504", "工艺导入重复"),

    PROCEDURE_CODE_IS_BLANK("1505", "工序编号不能为空"),

    PROCEDURE_NAME_IS_BLANK("1506", "工序名称不能为空"),

    PROCEDURE_PATH_CODE_IS_BLANK("1507", "工序路径编号不能为空"),

    FAC_TYPE_CODE_IS_BLANK("1508", "工位类型编号不能为空"),

    PARALLEL_CODE_IS_BLANK("1509", "是否并行工位不能为空"),

    LAST_PROCEDURE_IS_BLANK("1510", "是否为最后一道工序不能为空"),

    MODEL_IMPORT_FAIL("1511", "导入失败，工厂名字重复"),

    PRODUCT_NAME_IS_BLANK("1512", "名称不能为空"),

    PRODUCT_CODE_IS_BLANK("1513", "编号不能为空"),

    PRODUCT_VERSION_IS_BLANK("1514", "版本号不能为空"),

    PRODUCT_TOTAL_IS_BLANK("1515", "个数不能为空"),

    CRAFT_NAME_IS_BLANK("1516", "工艺名称不能为空"),

    CRAFT_CODE_IS_BLANK("1517", "工艺编码不能为空"),

    CRAFT_VERSION_IS_BLANK("1518", "工艺版本号不能为空"),

    END_CRAFT_IS_BLANK("1519", "是否为最后一道工艺不能为空"),

    PROCEDURE_FAIL2SAME_CODE("1520", "工序编号重复"),

    PROCEDURE_FAIL2UP("1521", "工序修改失败"),

    PROCEDURE_FAIL2DEL("1522", "工序删除失败"),

    PROCEDURE_FAC_NOT_EXIST("1523", "未找到该工位类型"),

    PROCEDURE_SUBLINE_NOT_EXIST("1524", "未找到该产线分支类型"),

    PROCEDURE_LINE_NOT_EXIST("1525", "未找到该产线类型"),

    PROCEDURE_REPEAT_IMPORT_FAIL("1526", "工序编号导入重复"),

    CRAFT_CODE_IS_EMPTY("1527", "工艺编号不能为空"),

    CRAFT_NAME_IS_EMPTY("1528", "工艺名称不能为空"),

    CRAFT_VERSION_IS_EMPTY("1529", "工艺版本号不能为空"),

    CRAFT_PERIOD_IS_BLANK("1530", "工艺有效期不能为空"),

    MATERIAL_CODE_IS_EMPTY("1531", "成品编号不能为空"),

    MATERIAL_NAME_IS_EMPTY("1532", "成品名称不能为空"),

    MATERIAL_VERSION_IS_EMPTY("1533", "成品版本号不能为空"),

    PUBLISH_STATE_IS_BLANK("1534", "发布状态不能为空"),

    MATERIAL_FAIL2SAME_CODE("1535", "物料编号重复"),

    HAS_BOM_PROCESS_DATA("1536", "停用/废弃的物料存在发放/完成中的BOM"),

    HAS_REQUEST_PROCESS_DATA("1537", "停用/废弃的物料存在发放/完成中的采购需求单"),

    HAS_PURCHASE_PROCESS_DATA("1538", "停用/废弃的物料存在发放/完成中的采购清单"),

    HAS_SUPPLIER_BOM_PROCESS_DATA("1539", "停用/废弃的物料存在发放/完成中的供应商物料清单"),

    HAS_ORDER_PROCESS_DATA("1540", "停用/废弃的物料存在发放/完成中的销售订单"),

    HAS_TAKE_OUT_APPLICATION_PROCESS_DATA("1541", "停用/废弃的物料存在发放/完成中的领料单"),

    HAS_TAKE_IN_AND_OUT_PROCESS_DATA("1542", "停用/废弃的物料存在发放/完成中的出入库单"),

    HAS_WORK_ORDER_PROCESS_DATA("1543", "停用/废弃的物料存在发放/完成/投入/挂起中的工单"),

    HAS_PROCESS_DATA("1544", "停用/废弃的物料存在发放/完成中的数据，请进行数据分析"),

    IS_LAST_BOM("1545", "停用/废弃的BOM为最后一个BOM，如需操作，请先新增同物料的BOM"),

    BOM_NOT_ALLOWED_TO_AUDIT("1546", "当前登录用户不允许审核"),

    BOM_NOT_ALLOWED_TO_APPROVE("1547", "当前登录用户不允许批准"),

    PROCEDURE_NOT_ALLOWED_TO_AUDIT("1548", "当前登录用户不允许审核"),

    PROCEDURE_NOT_ALLOWED_TO_APPROVE("1549", "当前登录用户不允许批准"),

    CRAFT_NOT_ALLOWED_TO_AUDIT("1550", "当前登录用户不允许审核"),

    CRAFT_NOT_ALLOWED_TO_APPROVE("1551", "当前登录用户不允许批准"),

    MATERIAL_CRAFT_NOT_RELEASED("1552", "所选物料工艺未发放"),

    BOM_NUM_DUPLICATE("1553", "BOM编号重复"),

    EXIST_MANUAL_TARGET("1554", "已存在手动指标"),

    WORK_CODE_IS_NOT_EMPTY("1555", "工单编号不能为空"),

    EXIST_AUTO_TARGET("1556", "已存在自动指标"),

    THE_SAME_MATERIAL_ONLY_HAS_ONE_RELEASED_CRAFT("1557", "同种物料生效状态下的工艺只能是一个"),

    COMBINATION_INDICATOR_ALREADY_EXISTS("1559", "已存在组合指标"),

    DUPLICATE_COMBINATION_NAME("1560", "组合指标名重复"),

    DUPLICATE_COMBINATION_CNAME("1561", "组合指标中文名重复"),

    KEY_INDICATOR_DOES_NOT_EXIST("1562", "主指标不存在"),

    ASSOCIATED_METRIC_DOES_NOT_EXIST("1563", "关联指标不存在"),

    COPY_BOM_IS_NOT_START("1564", "BOM状态未启用，不可直接复制"),
    COPY_CRAFT_IS_NOT_START("1565", "工艺状态未启用，不可直接复制"),

    SYNC_BOM_IS_MATERIAL_EXIST("1566", "BOM对应的物料编码不存在"),

    PROCEDURE_PROCESS_ASSEMBLY_IS_EXIST("1567", "工序工装已维护，不可重复维护"),

    PROCEDURE_POST_IS_EXIST("1568", "工序人力已维护，不可重复维护"),
    PROCESS_ASSEMBLY_IS_USED("1569", "工装类型已被工装引用，不可修改"),

    CRAFT_MATERIAL_CODE_IS_USED("1570", "同一物料不可创建不同的工艺编码"),
    BOM_MATERIAL_CODE_IS_USED("1571", "同一物料不可创建不同的BOM编码"),
    PROCEDURE_IS_NOT_EXIST("1572", "工序未在工序定义中维护，请先维护工序"),

    CRAFT_NAME_NOT_FOUNT("1573", "请输入正确的工艺名称"),

    CRAFT_VERSION_NOT_FOUNT("1574", "请输入正确的工艺版本号"),

    CRAFT_CODE_NOT_FOUNT("1575", "工艺不存在"),

    PROCEDURE_NAME_IS_NOT_REPEAT("1576", "工序名称不能重复"),
    BOM_NUMBER_IS_EMPTY("1577", "BOM编号不能为空"),
    TEMPLATE_CODE_DUPLICATE("1578", "模板编码重复"),
    MATERIAL_SKU_LIST_IS_EMPTY("1579", "物料sku列表不能为空"),
    AUXILIARY_ATTR_VALUE_IS_REPEAT("1580", "特征参数[｛0｝]已经存在特征值[｛1｝]"),
    IS_EXIST_MANY_DEFAULT_CRAFT("1581", "存在多个默认工艺模板"),
    LAST_PROCEDURE_CONVERT_FACTOR_NEED_ONE("1582", "最后一道工艺的换算系数必须为1"),
    NEED_SATISFY_ORIGINAL_ORDER_STATE("1583", "原单状态需为：{0}"),
    MATERIAL_TYPE_ID_NAME_IS_NULL("1584", "物料类型id和物料类型名称不能同时为空"),
    MATERIAL_TYPE_NOT_FOUND("1585", "物料类型不存在"),
    MATERIAL_NOT_EXISTS("1586", "物料不存在"),
    CUSTOMER_MATERIAL_NOT_FOUND("1587", "客户物料不存在"),
    CRAFT_RELATED_OTHER_DATA("1588", "工艺：{0}存在关联数据：{1}"),
    RELEASE_CRAFT_NEED_PROCEDURE("1589", "生效的工艺需要关联工序"),
    MATERIAL_UNIT_NOT_EQUAL_MEASUREMENT_UNIT("1590", "物料单位不能和计量单位一致"),
    PROCEDURE_CODE_NOT_FOUNT("1591", "工序定义不存在"),
    CRAFT_PROCEDURE_CODE_NOT_FOUNT("1592", "工艺工序不存在"),
    MATERIAL_UNIT_NUMERATOR_OR_DENOMINATOR_NOT_NULL("1593", "物料计量系数分子或者分母不能小于0"),
    BOM_NOT_EIXSTS("1594", "BOM不存在"),
    CRAFT_TEMP_NOT_FOUNT("1595", "工艺模板不存在"),
    FAC_STATE_ERROR("1596", "工位状态不正确"),


    // ==========大屏相关 9601-9700==============
    FILE_CONFIGURE_ERROR("9601", "配置文件配置错误"),
    TARGET_PARAMS_IS_BLANK("9602", "指标参数不能为空"),
    TARGET_PARAMS_ERROR("9603", "指标参数错误"),

    // ==========维修相关 9701-9750==============,
    NO_MAINTAIN_RECORD("9701", "该工单暂未有维修记录，不能更新为已解决状态"),

    BASE_TYPE_DUPLICATED_CODE("9702", "编号重复"),

    TYPE_IS_OCCUPIED("9703", "类型已被使用，不可以删除"),

    BARCODE_CAN_ONLY_BE_ONE_RECORD("9704", "多个记录不可共用批次"),

    MISMATCH_BETWEEN_WORK_ORDER_AND_BARCODE("9705", "工单与批次不匹配,请重新选择工单"),

    MATAIN_RULE_IS_EXIST("9706", "该类设备的告警定义关联的告警转工单规则已经存在"),

    FAULT_TYPE_DEFINE_CODE("9707", "故障类型定义编码已存在"),

    FAULT_TYPE_DEFINE_CODE_USED("9708", "故障类型定义编码已被使用,不允许删除"),
    BASE_TYPE_DUPLICATED_NAME("9709", "名称重复"),


    // ==========炉次相关 9751-9800==============
    FURNACE_IS_CONFIRM("9751", "该炉次已确认，请勿重复确认"),

    NOT_CREATE_ELECTRONIC_SCALE_DEVICE("9752", "为添加或关联电子秤设备"),

    NOT_CONFIG_CORRECT_MODEL_TABLE("9753", "未正确配置基础模型表"),

    NOT_CREATE_FURNACE_DEVICE("9754", "未添加电炉/精炼炉的生产设备"),

    NEED_TO_BE_WITHIN_RANGE("9755", "需要在工单实际开始结束范围之内生成"),

    CANNOT_FOUND_MODEL_ENTITY("9756", "找不到设备模型"),

    ONLY_CREATE_ONE_FURNACE("9757", "该工位只能创建一个正在运行的炉次"),

    CORRECT_VALUE_IS_CHANGE("9758", "“要求添加量”已变更，请重新称重"),

    ELEMENT_REPEAT("9759", "元素已存在，请勿重复配置"),

    FURNACE_TIME_EXPIRED("9760", "该炉次已过期，请重新选择"),

    FURNACE_CODE_NULL("9761", "炉次信息为空"),

    NUMBER_CODE_IS_NOT_EXIST("9762", "编码规则为空"),
    NUMBER_CODE_SAVE_AT_LEAST_ONE("9763", "至少需要保存一条编码规则"),
    THIS_FIELD_EXISTS_IN_THE_COMPOSITION_INFORMATION_CAN_BE_GROUPED_BY_THIS_FIELD("9764", "组成信息中有此字段才可按该字段归一"),
    NEED_EXIST_ONE_DEFAULT_CODE_RULE("9765", "必须存在一个默认编码规则"),

    //===========产能相关9801-9850==============
    PRODUCTION_LINE_NOT_EXIST("9801", "导入失败，所填写产线不存在"),

    MATERIAL_NOT_EXIST("9802", "导入失败，所填写物料不存在"),

    CAPACITY_ALLOCATION_IS_INCOMPLETE("9803", "产能配置不全"),

    WORK_ORDER_INPUT_FAIL("9804", "工单入库失败"),

    //系统同步相关9851-9870==========
    NOT_CONFIG_LOGIN("9852", "没有配置登录信息"),

    SELECT_PRD_MO_FAIL("9853", "ERP生产工单查询失败"),

    ERP_INSERT_WORK_ORDER_FAIL("9854", "下发工单失败"),

    ERP_LOGIN_FAIL("9855", "登录失败"),

    ERP_IN_SYN("9856", "当前已存在同步任务，稍后再试"),

    SYN_NOT_HAVE_MODEL("9857", "找不到对应同步模块"),

    //物料批量附件导入相关9901-9950============
    MATERIAL_MESSAGE_NOT_FIND("9901", "物料信息在系统内未找到"),

    MATERIAL_MESSAGE_NOT_MATCH_FILE("9902", "物料信息未匹配上附件"),

    MATCH_FILE_NOT_PDF("9903", "匹配上附件格式不是PDF的"),

    FILE_UPLOAD_EXCEPTION("9904", "文件上传异常"),

    IMPORT_FILE_AT_LEAST_ONE_SHEET("9905", "导入的文件至少包含一个工作表"),

    IMPORT_FILE_IS_NULL("9906", "导入的文件为空"),

    IMPORT_FILE_AT_LEAST_ONE_PDF("9907", "导入的文件至少包含一个PDF"),

    IMPORT_FILE_EXCEL("9907", "导入的文件为excel文件，请重新导入"),

    FILE_DELETE_FAIL("9908", "附件删除失败！"),

    IMPORT_FILE_CRAFT_PROCEDURE_IS_MAINTENANCE_PROCEDURE("9909", "导入的文件中维修工序应该填写是或否！"),

    IMPORT_FILE_CRAFT_PROCEDURE_IS_QUAlITY_PROCESS("9910", "导入的文件中质检工序应该填写是或否！"),
    MATERIAL_HAS_SAME_FILE_NAME("9911", "该物料已存在相同名称的附件，请勿重复导入"),

    //压缩文件相关9951-9970=====
    IMPORT_FILE_NOT_ZIP("9951", "传入文件格式不是zip文件"),

    IMPORT_FILE_FORMAT_WRONG("9952", "传入文件格式错误"),


    READ_FILE_CONTENT_FAIL("9953", "读取部署包文件内容失败,请确认部署包格式正确:"),

    FILE_NOT_EMPTY("9954", "文件不能为空"),

    FILE_NAME_NOT_EMPTY("9954", "文件名称不能为空"),

    VERSION_NOT_SUPPORTED("9955", "不支持rar5.0及之后的版本压缩包，请改用zip文件"),

    FILE_READ_FAIL("9956", "读取部署包文件内容失败,请确认部署包格式正确"),

    FILE_IS_NULL("9957", "传入文件为空"),

    SAVE_TEMP_FILE_FAIL("9958", "保存临时文件错误"),

    FILE_FORMAT_FAIL("9959", "只支持zip和rar格式的压缩包"),

    // ==========调度模块相关 9701-9800==============
    NOT_CONFIG_TASK_DEFINITION("9701", "该工位未配置默认路线"),

    FACILITY_CONFIG_EXIST("9702", "该工位配置已存在"),

    FAILED_CREAT_TASK("9703", "创建任务失败"),

    INSTANCE_NOT_CONFIG_LOCATION("9704", "工位未配置点位"),

    NO_THIS_TASK("9705", "没有该任务"),

    THIS_TASK_NOT_WAITING("9706", "该任务非等待状态，无法取消"),

    THIS_STATE_CAN_NOT_BACK("9707", "该状态无法还车"),

    BEGIN_LOCATION_ABANDON("9708", "起点点位已被废弃"),

    END_LOCATION_ABANDON("9709", "结束点位已被废弃"),

    PATH_HAS_TASK_NOT_COMPLETED("9710", "该路线还有未完成任务"),

    FACILITY_CAN_NOT_CONFIG_MUCH_LOCATION("9711", "工位不能绑定多个点位"),

    THIRD_PARTY_SYSTEM_IS_UNAVAILABLE("9712", "第三方系统不可用"),

    NO_NEED_BACK_TASK("9713", "暂无需要还车的任务"),

    HAS_TASK_NOT_END("9714", "已有入库任务未完成，请稍等"),

    NO_THIS_INPUT_TASK("9715", "没改入库任务"),

    AVAILABLE_LOC_ALARM("9716", "无可用库位"),

    LOCATION_LIGHT_HAS_BEEN_BOUND("9717", "该库位灯已经被绑定到对应的电子料架设备"),

    /**
     * ===========表格数据处理相关9871-9980======
     */
    PARSING_DATA_IS_EMPTY("9871", "文件错误或文件内容为空,请导入.xls,xlsm,.xlsx格式Excel文件!"),

    /**
     * ===========检验项目组相关9981-9985======
     */
    INSPECTION_ITEM_GROUP_NAME_NOSPECT("9981", "检验项目组名称不能重复，请重新输入"),

    INSPECTION_ITEM_GROUP_NOT_REMOVE("9982", "检验项目组关联了检验项目，不能删除"),

    /**
     * ===========检验项目组相关9986-9990======
     */
    QUALITY_INSPECTION_SCHEME_NAME_NOSPECT("9986", "质量检测方案名称不能重复，请重新输入"),

    /**
     * ===========产品质检 10001-10020======
     */
    INSPECTION_SHEET_IS_EXIST("10001", "送检单编码已经存在"),
    MATERIAL_INSPECTIONS_NOT_BIND("10002", "物料没有和质检方案关联"),
    INSPECTION_SHEET_NOT_DELETE("10003", "送检单处于生效状态,不允许删除"),
    INSPECTION_RESULT_ADD_ERROR("10004", "检验结果记录添加异常"),
    INSPECTION_RESULT_DELETE_ERROR("10005", "检验结果删除异常"),
    INSPECTION_ITEMS_NOT_EXIST("10006", "该质检方案没有关联检查项目,请添加检验项。"),
    INSPECTION_NUMS_SUMITED("10007", "检验结果已提交，不允许添加新的次数"),
    INSPECTION_REPORTS_NOT_EXIST("10008", "该批次号下无检测报告"),
    BARCODE_IS_NOT_USED("10009", "该批次号报告已经创建，请选择其他批次号"),
    INSPECTION_RESULT_IS_NOT_EXIST("10010", "该送检单没有质检结果，不允许提交"),
    INSPECTION_RESULT_IS_EXIST("10011", "该送检单已经有检验结论，不允许取消"),
    INSPECTION_SHEET_IS_NOT_EXIST("10012", "送检单不存在"),
    TEMPLATE_NOT_EXIST("10013", "模板不存在"),
    INSPECTION_SHEET_EXPORT_ERROR("10014", "导出送检单异常"),
    INSPECTION_REPORT_EXPORT_ERROR("10015", "导出报告异常"),
    INSPECTION_REPORT_PREVIEW_ERROR("10016", "报告预览异常"),
    TEMPLATE_NOT_DOWNLOAD_EXIST("10017", "模板文件获取失败，请确认是否上传模板"),
    INSPECTION_NUM_IS_EXIST("100018", "样次重复提交,请重试"),
    INSPECTION_RESULT_NOT_COMPLETED("100019", "样次正在检测中,不可修改为完成"),
    RELEASE_STATE_NOT_UPDATE("100020", "生效状态不允许修改"),
    INSPECTION_SCHEME_BIND_MATERIAL_OR_CRAFT_PROCEDURE("100021", "存在关联的物料或工艺工序，不能改为废弃"),
    CRAFT_PROCEDURE_NEED_BIND_INSPECTION_SCHEME_AND_METHOD("100022", "工艺工序如关联检验方式，需要绑定检验方案"),
    MATERIAL_NEED_BIND_INSPECTION_SCHEME_AND_METHOD("100023", "物料如关联检验方式，需要绑定检验方案"),
    MATERIAL_NEED_BIND_INSPECTION_TRIGGER_CONDITION_AND_METHOD("100024", "物料如关联检验方式，需要绑定检验触发条件"),
    INSPECTION_SCHEME_IS_NOT_EXIST("100024", "检验方案不存在"),

    /**
     * ===========SMT管理 10021-10040======
     */
    STATION_REMOVE_FAIL("10021", "该数据已有扫描作业数据，不可编辑或删除。"),
    MATERIAL_MATCHING_FAIL("10022", "所输入的工单绑定的物料与所选择物料匹配错误，请重新输入。"),
    MATERIAL_BINDING_FAIL("10023", "所输入的物料和程序名称信息已存在，请重新输入。"),
    MATERIAL_WORK_ORDER_BINDING_FAIL("10024", "所输入的物料和程序名称以及工单信息已存在，请重新输入。"),
    MATERIAL_NOT_FOUND("10025", "所输入的物料在系统中不存在，请重新输入。"),
    STATION_ID_IS_NULL("10026", "站位id为空,请重新输入。"),
    MATERIAL_STANDARD_NOT_MISMATCH("10027", "所输入的物料规格与查询到的物料规格不匹配。"),
    WORK_ORDER_NOT_SPECIFIED_LINE("10028", "所输入的工单未指定产线,请先为该工单指定产线。"),
    WORK_ORDER_NOT_FOUND_BY_SYSTEM("10029", "所输入的工单在系统中未找到，请重新输入。"),
    DEVICE_TYPE_NOT_FOUND_BY_SYSTEM("10030", "所选择设备类型在系统中未找到，请重新选择。"),
    FEEDER_ALREADY_EXISTS("10031", "所输入Feeder编号已存在，请重新输入。"),
    IMPORT_DATA_CONTAIN_WRONG_DATA("10032", "数据有误，请核对站位表"),
    NOT_FOUND_MOUNTER_PROCESSING_METHOD("10033", "未找到该贴片机表格数据的具体实现"),
    MOUNTER_EXCEL_PROCESSING_ERROR("10034", "处理贴片机表格数据错误"),
    MOUNTER_EXCEL_REPEAT("10035", "该站位已有导入的数据，不可再次导入"),
    SN_BOX_NOT_MATE("10036", "铭牌和箱标不匹配"),

    /**
     * ===========keycloak 10050-10055======
     */
    KC_USER_EXIST("10500", "用户创建失败，Keycloak用户已经存在"),
    KC_USER_IM_ERROR("10501", "用户创建失败，导入IM失败"),
    KC_USER_IS_NOT_EXIST("10502", "该keycloak用户不存在"),
    VIDEO_NOT_EXITS("11000", "设备不存在"),
    REQUEST_ERROR("10503", "请求方式错误"),
    VIDEO_CODE_IS_REPLY("10504", "设备编号不可重复"),

    /**
     * ===========产品质检相关10021-10040=====
     */
    //传入必要参数错误
    REQUIRED_PARAMETERS_FAIL("10021", "传入必要参数错误！"),
    CANNOT_FIND_AQL("10022", "找不到对应的AQL！"),
    /**
     * ===========生产流水码相关10060-10065=======
     */
    CHECK_SERIAL_CODE_TO_BE_EXPORT("10061", "请勾选要导出的序列号"),
    MATERIAL_NOT_SUPPORT_CODE_MANAGE("10062", "该工单下的物料不支持按流水码管理"),
    NOT_WORK_ORDER_STATE_ADD_FLOW_CODE("10063", "该工单状态下不能添加生产流水码"),
    NOT_CONFIG_FLOW_CODE_RULE("10064", "未指定生产流水码的生成规则"),
    FLOW_CODE_NOT_FOUND("10065", "该流水码在系统中未找到，请检查"),
    FLOW_CODE_REPEAT("10066", "该流水码已经存在，请重新生成"),

    /**
     * ==========配置小程序权限10067-10100===
     */
    APP_NOT_REPEAT_ADD("10068", "小程序不能重复添加"),
    APP_BATCH_FAC_NOT_REPEAT("10069", "小程序绑定的工位不能重复"),
    APP_LICENSE_ERROR("10070", "脚本数据错误，请检查是否满足json格式和数据的正确性"),

    /**
     * ==========项目管理10101-10120===
     */
    NODE_EXCEPTION_CODE_REPEAT("10101", "节点异常代号重复，请重新输入。"),
    NODE_EXCEPTION_CODE_REFERENCED("10102", "已被使用，不可删除。"),
    NODE_DEFINITION_CODE_REPEAT("10103", "节点编号重复，请重新输入。"),
    NODE_CONFIGURE_CODE_REPEAT("10104", "配置代号重复，请重新输入。"),
    NODE_CONFIGURE_SEQUENCE_REPEAT("10105", "配置代号绑定的顺序号重复，请重新输入。"),


    /**
     * ==========合作方权限配置10500-10600===
     */
    SERVICE_NAME_IS_NULL("10500", "服务名不能为空"),
    HAS_NO_PERMISSIONS("10501", "该合作方没有配置权限"),
    PERMISSIONS_ID_ILLEGAL("10502", "权限id不合法"),
    PERMISSIONS_PATH_ILLEGAL("10503", "权限路径不合法"),
    PERMISSION_IDS_IS_NULL("10504", "权限id不能为空"),
    PERMISSION_IDS_DUPLICATE("10505", "权限id重复"),

    /**
     * ==========标签规则10601-10610===
     */
    NOT_FIND_BAR_CODE_RULE("10601", "没有找到对应的标签规则"),
    BAR_CODE_RULE_PRINT_DIGIT_OUT_OF_RANGE("10602", "打印位数超出范围"),
    PRINT_DATA_IS_LOSE("10603", "打印数据丢失，请确保数据存在"),
    LABEL_TYPE_CODE_OR_NAME_IS_REPEAT("10604", "标签类型或者名称重复"),
    LABEL_INFO_CODE_OR_NAME_OR_PLACEHOLDER_IS_REPEAT("10605", "标签组成信息、名称或者替换符重复"),
    FOUND_PRINTED_DATA("10607", "存在已打印的数据"),
    DONT_DELETE_LABEL_SYS_FIELD("10608", "不能删除系统组成信息"),
    EXIST_LABEL_BY_APP("10609", "{0}下已存在此{1}标签"),
    PRINT_IS_REQUIRE("10610", "标签打印需要必填项"),

    /**
     * ==========工序检验项10620-10690===
     */
    PROCEDURE_INSPECTION_CODE_NO_SPECT("10621", "工序检验项的代号不能重复"),
    PROCEDURE_INSPECTION_NAME_NO_SPECT("10622", "工序检验项的名称不能重复"),
    NOT_CREATE_STATE_NO_UPDATE("10623", "创建状态才可以修改"),
    USED_NOT_ABANDON("10624", "工序检验项已被引用，无法废弃"),
    /**
     * ==========工序检验项配置10690-10720===
     */
    STANDARD_VALUE_CAN_ONLY_BE_NUMERIC("10690", "标准值只能为数值"),
    UPPER_VALUE_CAN_ONLY_BE_NUMERIC("10691", "上限只能为数值"),
    DOWN_VALUE_CAN_ONLY_BE_NUMERIC("10692", "下限只能为数值"),
    STANDARD_VALUE_CAN_ONLY_BE_CHAR("10692", "标准值只能为字符串"),
    STANDARD_VALUE_CAN_YES_OR_NO("10692", "标准值只能是Y/N或Y,N"),
    NOT_PROCEDURE_INSPECT("10693", "没有找到对应的工序检验项"),

    PLEASE_TRY_AGAIN_LATER("9999", "访问过于频繁，请稍后再试"),


    /**
     * ===========产前检验相关12000-13000======
     */
    PRENATAL_INSPECTION_ITEMS_NAME_IS_REPEAT("12000", "检查项目重复"),
    COMMENT_IS_NOT_CREATE_NOT_UPDATE("12001", "非创建状态，不可修改"),
    PLAN_IS_RELEASE_ITEMS_NOT_UPDATE("12002", "检查项目对应的检查方案是生效状态，不可直接废弃"),

    /**
     * ============配置中心报错集合13000 - 20000================,
     */
    MATERIAL_CONFIG_FIELD_NAME_NOT_REPEAT("13001", "物料扩展字段名称不能重复"),
    OPTION_VALUE_ORDER_NOT_NULL("13002", "顺序不能为空"),
    OPTION_VALUE_ORDER_IS_DUPLICATES("13003", "顺序不能重复"),
    OPTION_VALUE_VALUE_IS_DUPLICATES("13004", "编码不能重复"),
    OPTION_VALUE_LABEL_IS_DUPLICATES("13005", "名称不能重复"),
    SYSTEM_DATA_NOT_ALLOW_CHANGE("13006", "存在系统内部字段，不允许变更"),
    NEED_SELECT_RELATED_FIELD("13007", "{0}需要选择对应的单据字段"),
    MAPPING_RELATION_ERROR("13008", "{0}中{1}无上游/下游单据，无法实现映射，请重新配置或删除配置"),
    MAPPING_RELATION_ERROR_EMPTY_DATA("13009", "自定义名称:{0}中对应单据或者字段存在为空的数据"),

    /**
     * ============单据回退相关14000 - 15000================
     */
    RETURN_ORDER_IS_OCCUPIED("14001", "不可回退，单据已被占用"),
    RELATED_MATERIAL_IS_NOT_EXIST("14002", "不可回退，单据绑定的物料不存在"),
    RELATED_WORK_ORDER_CENTER_IS_NOT_EXIST("14003", "不可回退，单据绑定的工作中心不存在"),
    RELATED_LINE_IS_NOT_EXIST("14004", "不可回退，单据绑定的产线不存在"),
    RELATED_TEAM_IS_NOT_EXIST("14005", "不可回退，单据绑定的班组不存在"),
    RELATED_DEVICE_IS_NOT_EXIST("14006", "不可回退，单据绑定的设备不存在"),
    RELATED_PRODUCT_ORDER_IS_NOT_EXIST("14007", "不可回退，单据绑定的生产订单不存在"),
    RELATED_SALE_ORDER_IS_NOT_EXIST("14008", "不可回退，单据绑定的销售订单不存在"),
    RELATED_CRAFT_IS_NOT_EXIST("14009", "不可回退，单据绑定的工艺不存在"),
    RELATED_PROCEDURE_IS_NOT_EXIST("14010", "不可回退，单据绑定的工序不存在"),
    RELATED_CRAFT_PROCEDURE_IS_NOT_EXIST("14011", "不可回退，单据绑定的工艺工序不存在"),
    RELATED_TAKE_OUT_APPLICATION_IS_OCCUPIED("14012", "不可回退，单据关联的生产领料单已经存在"),
    RELATED_FURNACE_CODE_IS_OCCUPIED("14013", "不可回退，单据关联的炉次已经存在"),
    RELATED_FAC_IS_NOT_EXIST("14014", "不可回退，单据绑定的工位不存在"),
    RELATED_BOX_NUMBER_IS_OCCUPIED("14015", "不可回退，单据关联的箱编号已经存在"),
    RELATED_SERIAL_NUMBER_IS_OCCUPIED("14016", "不可回退，单据关联的维修记录已经存在"),
    RELATED_PRODUCT_FLOW_NUMBER_IS_OCCUPIED("14017", "不可回退，单据关联的流水码已经存在"),
    RELATED_BAR_CODE_IS_OCCUPIED("14018", "不可回退，单据关联的批次已经存在"),
    RELATED_MATERIAL_IS_NOT_RELEASE("14019", "不可回退，单据绑定的物料非生效态"),

    // 假数据
    NOW_CREATING_DATA("15000", "正在进行即时创建任务, 请稍后再试"),

    /**
     * ============mongodb相关15000 - 16000================
     */
    MONGODB_UPDATE_ID_NOT_FOUND("15001", "id不能为空"),

    /**
     * ============任务相关16000 - 17000================
     */
    ORDER_CATEGORY_EXIST("16001", "单据类型编码已存在"),
    ORDER_CATEGORY_NAME_EXIST("16002", "单据类型名称已存在"),
    ORDER_CATEGORY_NOT_EXIST("16003", "单据类型编码不存在"),

    ORDER_RELATION_EXIST("16004", "单据关系已存在"),
    ORDER_RELATION_NOT_EXIST("16005", "单据关系不存在"),
    ORDER_RELATION_IS_CYCLE("16006", "单据关系存在循环依赖"),


    /**
     * ============mongodb相关90000 - 99999================
     */
    NOT_FOUND_DATA("90001", "找不到数据"),


    /**
     * ============DFS二开接口异常================
     */
    /**
     * 工单相关
     */
    DFS_MUST_CHOSE_ONE_PRODUCT_BASIC_UNIT_WHEN_INVEST("DFS.WORK_ORDER.0001", "投产时必须至少选择一个生产基本单元"),
    DFS_DO_NOT_CHANGE_WORK_ORDER_STATE_BECAUSE_WORK_ORDER_IS_COMPLETED("DFS.WORK_ORDER.0002", "此工单已完成，无法操作"),
    DFS_INVEST_TIME_LATER_THAN_CURRENT_TIME("DFS.WORK_ORDER.0003", "投产时间不能晚于当前时间"),
    DFS_NOT_EXIST_WORK_ORDER("DFS.WORK_ORDER.0004", "存在不属于系统的单据"),
    /**
     * 权限相关
     */
    DFS_EXIST_SAME_APP_ONLINE_DATA("DFS.PERMISSION.0001", "存在权限id相同的数据"),
    /**
     * 物料类型相关
     */
    DFS_EXIST_SAME_MATERIAL_TYPE_CODE_OR_NAME("DFS.MATERIAL_TYPE.0001", "存在相同编码或者名称的物料类型"),
    DFS_NOT_EXIST_CRAFT_TEMPLATE("DFS.MATERIAL_TYPE.0002", "关联的工艺模板不存在"),
    DFS_NOT_EXIST_MATERIAL_TYPE("DFS.MATERIAL_TYPE.0003", "存在不属于系统的物料类型"),
    /**
     * 用户相关
     */
    DFS_NOT_EXIST_USER("DFS.USER.0001", "用户已被禁用或删除"),
    /**
     * 模型相关
     */
    DFS_EXIST_LINE_MODEL_SAME_NAME("DFS.MODEL.0001", "系统中存在相同的制造单元类型名称"),
    DFS_EXIST_DEVICE_MODEL_SAME_NAME("DFS.MODEL.0002", "系统中存在相同的设备类型名称"),
    DFS_NOT_EXIST_PARENT_MODEL_CODE("DFS.MODEL.0003", "系统中不存在该父级编码"),
    DFS_NOT_EXIST_MODEL_CODE("DFS.MODEL.0004", "存在不属于系统的类型"),
    /**
     * 编码规则相关
     */
    DFS_NUMBER_RULE_NOT_EXIST("DFS.NUMBER_RULE.0001", "编码规则不存在"),
    DFS_RULE_DETAIL_PARSE_ERROR("DFS.NUMBER_RULE.0002", "编码规则解析失败"),
    DFS_ONLY_ONE_DEFAULT_CODE_RULE_PER_TYP("DFS.NUMBER_RULE.0003", "同个编码规则类型下有且只有一个为默认"),
    /**
     * BOM相关
     */
    DFS_BOM_STATE_NOT_CROSS_LEVEL("DFS.BOM.0001", "量产态的BOM不能转为试产态"),
    /**
     * 工艺相关
     */
    DFS_CRAFT_STATE_NOT_CROSS_LEVEL("DFS.CRAFT.0001", "量产态的工艺不能转为试产态"),
    /**
     * 单位相关
     */
    DFS_UNIT_NOT_EXIST("DFS.UNIT.0001", "单位不存在"),
    DFS_UNIT_IS_NOT_NUMERIC("DFS.UNIT.0002", "单位精度必须为数字"),
    DFS_UNIT_ACCURACY_ERROR("DFS.UNIT.0003", "单位精度必须为0~10整数"),
    DFS_ROUNDING_TYPE_ERROR("DFS.UNIT.0004", "舍入类型有误"),
    /**
     * 物料
     */

    /**
     *
     */
    DFS_0014("DFS.0014", "存在重复的单据编号"),
    DFS_0015("DFS.0015", "单据编码已存在系统中"),
    DFS_0016("DFS.0016", "存在不属于系统的单据"),
    DFS_0017("DFS.0017", "单据编号不能为空"),
    DFS_0018("DFS.0018", "删除的单号和物料行不匹配"),
    DFS_0019("DFS.0019", "关联单据不存在系统中"),
    DFS_0020("DFS.0020", "物料明细不能为空"),
    DFS_0021("DFS.0021", "关联单据和物料编码不匹配"),
    DFS_0022("DFS.0022", "单据编码和单据id不能同时为null"),



    ;

    private String msgCode;
    private String msgDes;
    private ThreadLocal<String> formatDesSet = new ThreadLocal<>();

    RespCodeEnum(String msgCode, String msgDes) {
        this.msgCode = msgCode;
        this.msgDes = msgDes;
    }

    public String getMsgCode() {
        return msgCode;
    }

    public String getMsgDes() {
        String des = formatDesSet.get();
        return des == null ? msgDes : des;
    }


    /**
     * 使用占位符{num} 进行替换
     *
     * @param placeholders 替换的参数
     *                     eg:
     *                     des: 用户{0}无法操作, 申请单{1}已存在。
     *                     placeholders: "admin", "CGS2.2.12022"
     */
    public RespCodeEnum fmtDes(Object... placeholders) {
        formatDesSet.set(MessageFormat.format(this.msgDes, placeholders));
        return this;
    }
}
