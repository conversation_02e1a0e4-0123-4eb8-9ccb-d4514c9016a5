package com.yelink.dfscommon.constant;

import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.ColumnUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Arrays;

import static java.util.stream.Collectors.joining;

/**
 * @description: 自定义模块间接口调用失败时主动抛出的异常信息
 * @author: shuang
 * @time: 2020/6/4
 */

@Data
@EqualsAndHashCode(callSuper = false)
public class ResponseException extends RuntimeException {

    private static final long serialVersionUID = 1L;
    private String code;

    private RespCodeEnum respCodeEnum;

    private ResponseData responseData;

    /**
     * 无参构造方法
     */
    public ResponseException() {
        super();
    }

    /**
     * 无参构造方法
     */
   /* public ResponseException(String code,String message) {
        super(message);
        this.code = code;
    }*/

    /**
     * 有参构造方法
     *
     * @param message
     */
    public ResponseException(String message) {
        super(message);
        this.code = "300";
    }

    public ResponseException(ResponseData responseData) {
        this.responseData = responseData;
    }

    /**
     * 有参构造方法
     *
     * @param respCodeEnum
     */
    public ResponseException(RespCodeEnum respCodeEnum) {
        super(respCodeEnum.getMsgDes());
        this.code = respCodeEnum.getMsgCode();
        this.respCodeEnum = respCodeEnum;
    }

    @SafeVarargs
    public static <T> ResponseException buildByFields(RespCodeEnum respCodeEnum, SFunction<T, ?>... columns) {
        String collect = Arrays.stream(columns).map(ColumnUtil::getField).collect(joining(Constant.SEP));
        ResponseException exception = new ResponseException(respCodeEnum.getMsgDes() + ",相关字段：" + collect);
        exception.setCode(respCodeEnum.getMsgCode());
        return exception;
    }

    public RespCodeEnum getRespCodeEnum() {
        return respCodeEnum;
    }

    public static ResponseException of(String message, Object... args) {
        return new ResponseException(String.format(message.replaceAll("\\{\\}", "%s"), args));
    }
}
