package com.yelink.dfscommon.constant.qmsProductInspection;


/**
 * <AUTHOR>
 * @Date 2024/5/29 11:14
 */
public enum InspectStateEnum {
    /**
     * 检验单的状态
     */
    CREATE(1, "创建"),
    RELEASED(2, "生效"),
    FINISH(3, "完成"),
    CANCEL(4, "取消");

    private final Integer code;
    private final String name;


    InspectStateEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code != null) {
            for (InspectStateEnum inspectionSheetStatusEnum : InspectStateEnum.values()) {
                if (inspectionSheetStatusEnum.code.equals(code)) {
                    return inspectionSheetStatusEnum.name;
                }
            }
        }
        return null;
    }

}
