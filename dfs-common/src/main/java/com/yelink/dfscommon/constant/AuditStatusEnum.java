package com.yelink.dfscommon.constant;


import lombok.Getter;

/**
 * 审批状态
 */
public enum AuditStatusEnum {
    S_1(1, "待审批"),
    S_2(2, "审批中"),
    S_3(3, "已审批"),
    S_4(4, "已驳回");
    @Getter
    private int key;
    @Getter
    private String val;

    AuditStatusEnum(int key, String val) {
        this.key = key;
        this.val = val;
    }

    public static AuditStatusEnum getByKey(int key) {
        for (AuditStatusEnum stateEnum : AuditStatusEnum.values()) {
            if (stateEnum.key == key) {
                return stateEnum;
            }
        }
        return null;
    }
}
