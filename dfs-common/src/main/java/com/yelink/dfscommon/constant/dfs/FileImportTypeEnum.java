package com.yelink.dfscommon.constant.dfs;

import lombok.Getter;


/**
 * <AUTHOR>
 * 通用的导入类型枚举
 */
public enum FileImportTypeEnum {
    /**
     * 类型
     */
    MATERIAL("物料"),
    BOM("BOM"),
    PROCEDURE("工序定义"),
    CRAFT_DEFINE("工艺-工艺定义"),
    CRAFT_PARAM("工艺-工艺参数"),
    INSPECTION_SCHEME("质检方案"),
    PRODUCT_ORDER("生产订单"),
    SALE_ORDER("销售订单"),
    CUSTOMER("客户档案"),
    CUSTOMER_MATERIAL_LIST("客户物料清单"),
    SUPPLIER("供应商档案"),
    SUPPLIER_MATERIAL_LIST("供应商物料清单"),
    ;

    @Getter
    private final String name;


    FileImportTypeEnum(String name) {
        this.name = name;
    }

}
