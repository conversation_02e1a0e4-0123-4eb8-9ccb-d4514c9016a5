package com.yelink.dfscommon.constant;
import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * @Description: 入库状态枚举
 * @Author: Chensn
 * @Date: 2022/02/16
 */
public enum StoreStateEnum {

    /**
     * 状态编码及描述
     * 0-待入库 1-已完成 2-等待中
     */
    PENDING(0, "未入库"),
    CREATED(14, "已创建"),
    RESERVED(1, "已预定"),
    EXECUTED(2, "已执行"),
    COMPLETED(3,"已完成"),
    TERMINATED(4,"已终止"),
    CANCELED(5,"已取消"),
    MOVE_TO_THE_BEGINNING(6,"移到开始"),
    EXECUTE_START_OPERATION(7,"执行开始操作"),
    MOVE_TO_THE_END(8,"移到结束"),
    EXECUTION_END_OPERATION(9,"执行结束操作"),
    PARKING(10,"停车"),
    NEW(11,"新建"),
    ARCHIVED(12,"已存档"),
    INVALID(13,"无效");

    @EnumValue
    private int code;
    private String name;

    StoreStateEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return StoreStateEnum.PENDING.getName();
        }
        for (StoreStateEnum stateEnum : StoreStateEnum.values()) {
            if (stateEnum.code == code) {
                return stateEnum.name;
            }
        }
        return null;
    }
}
