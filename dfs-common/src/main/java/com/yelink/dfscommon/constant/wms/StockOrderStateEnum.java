package com.yelink.dfscommon.constant.wms;


/**
 * @Description: 出入库单状态枚举
 * @Author: z<PERSON><PERSON><PERSON>
 * @Date: 2020/12/5
 */
public enum StockOrderStateEnum {

    /**
     * 状态编码及描述
     * 0-创建 1-生效 2-完成 3-关闭 4-取消
     */
    CREATED(0, "创建"),
    RELEASED(1, "生效"),
    FINISHED(2, "完成"),
    CLOSED(3, "关闭"),
    CANCELED(4, "取消");

    private int code;
    private String name;

    StockOrderStateEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (StockOrderStateEnum stateEnum : StockOrderStateEnum.values()) {
            if (stateEnum.code == code) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static Integer getCodeByName(String name) {
        for (StockOrderStateEnum stateEnum : StockOrderStateEnum.values()) {
            if (name.equals(stateEnum.name)) {
                return stateEnum.code;
            }
        }
        return null;
    }
}
