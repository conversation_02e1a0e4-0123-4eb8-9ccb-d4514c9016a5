package com.yelink.dfscommon.constant;


/**
 * <AUTHOR>
 * @Date 2021/3/2 15:35
 */
public enum OrderShowTypeEnum {

    /**
     * 类型
     */
    ORDER("order", "订单"),
    MATERIAL("material", "物料");

    private String type;
    private String typeName;


    OrderShowTypeEnum(String type, String typeName) {
        this.type = type;
        this.typeName = typeName;
    }

    public String getType() {
        return type;
    }

    public String getTypeName() {
        return typeName;
    }

}
