package com.yelink.dfscommon.constant;

import java.util.Arrays;

/**
 * @description: 按钮跳转类型
 * @author:
 * @time: 2022/11/11
 */
public enum PageRedirectTagEnum {

    /**
     * 按钮跳转类型
     */
    COMMON("common","普通"),
    CODE_CENTER("codeCenter","条码中心")
    ;


    private String code;
    private String name;

    PageRedirectTagEnum(String code, String name){
        this.code = code;
        this.name = name;
    }

    public static PageRedirectTagEnum getRegisterInfoTypeEnumByCode(String code) {
        if(code == null){
            return null;
        }
        return Arrays.stream(PageRedirectTagEnum.values())
                .filter(registerInfoTypeEnum -> registerInfoTypeEnum.getCode().equals(code))
                .findAny()
                .orElse(null);
    }


    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }

    public static String getNameByCode(String code) {
        if (code == null) {
            return null;
        }
        for (PageRedirectTagEnum pageRedirectTagEnum : PageRedirectTagEnum.values()) {
            if (pageRedirectTagEnum.code.equals(code)) {
                return pageRedirectTagEnum.name;
            }
        }
        return null;
    }
}
