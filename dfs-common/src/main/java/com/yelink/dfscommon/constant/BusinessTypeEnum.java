package com.yelink.dfscommon.constant;

import org.apache.commons.lang3.StringUtils;

/**
 * 业务类型枚举，方便后续数据调整时统一修改引用的地方
 * <AUTHOR>
 * @date 2021/8/27 17:17
 */
public enum BusinessTypeEnum {
    /**
     * 单据大类枚举
     */
    TEST_PRODUCT_ORDER("testProductOrder", "试产订单"),
    TEST_WORK_ORDER("testWorkOrder", "试产工单"),
    ;

    private String typeCode;
    private String typeName;

    BusinessTypeEnum(String typeCode, String typeName) {
        this.typeCode = typeCode;
        this.typeName = typeName;
    }

    public String getTypeCode() {
        return typeCode;
    }

    public String getTypeName() {
        return typeName;
    }


    public static String getNameByCode(String code) {
        if (StringUtils.isNotBlank(code)) {
            for (BusinessTypeEnum recoveryTypeEnum : BusinessTypeEnum.values()) {
                if (recoveryTypeEnum.getTypeCode().equals(code)) {
                    return recoveryTypeEnum.getTypeName();
                }
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        for (BusinessTypeEnum recoveryTypeEnum : BusinessTypeEnum.values()) {
            if (recoveryTypeEnum.getTypeName().equals(name)) {
                return recoveryTypeEnum.getTypeCode();
            }
        }
        return null;
    }
}
