package com.yelink.dfscommon.constant;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Date 2021/3/2 15:35
 */
public enum ModelEnum {

    /**
     * 类型
     */
    AREA("area", "厂区"),
    GRID("grid", "车间"),
    LINE("line", "产线"),
//    SUBLINE("subline", "产线分支"),
    FACILITY("facility", "工位"),
    DEVICE("device", "设备"),
    SENSOR("sensor", "传感器"),
    ORDER("order", "订单"),
    WORK_ORDER("workOrder", "工单"),
    PRODUCT("product", "产品"),
    PROCESS_ASSEMBLY("processAssembly","工艺工装"),
    TEAM("team", "班组"),
    /**
     * 抽象化的模型
     */
    SALE_ORDER_TARGET("saleOrderTarget", "销售订单指标"),
    ;
    private String type;
    private String typeName;


    ModelEnum(String type, String typeName) {
        this.type = type;
        this.typeName = typeName;
    }

    public String getType() {
        return type;
    }

    public String getTypeName() {
        return typeName;
    }


    public static ModelEnum getByType(String type) {
        if (StringUtils.isBlank(type)) {
            return null;
        }
        for (ModelEnum modelEnum : ModelEnum.values()) {
            if (type.equals(modelEnum.getType())) {
                return modelEnum;
            }
        }
        return null;
    }

    public static String getNameByType(String type) {
        for (ModelEnum modelEnum : ModelEnum.values()) {
            if (modelEnum.type.equals(type)) {
                return modelEnum.typeName;
            }
        }
        return null;
    }
}
