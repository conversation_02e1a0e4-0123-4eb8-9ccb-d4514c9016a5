package com.yelink.dfscommon.constant.approve.config;

/**
 * <AUTHOR>
 * @Date 2023/10/27 16:24
 */
public enum ApproveControlEnum {

    TEXT("Text", "", "文本"),
    TEXTAREA("Textarea", "", "多行文本"),
    NUMBER("Number", "", "数字"),
    MONEY("Money", "", "金额"),
    DATE_DAY("Date", "day", "日期"),
    DATE_HOUR("Date", "hour", "日期+时间"),
    SELECTOR_SINGLE("Selector", "single", "单选"),
    SELECTOR_MULTI("Selector", "multi", "多选"),
    CONTACT("Contact", "", "成员/部门"),
    TIPS("Tips", "", "说明文字"),
    FILE("File", "", "附件"),
    TABLE("Table", "", "明细"),
    ATTENDANCE("Attendance", "", "假勤控件"),
    VACATION("Vacation", "", "请假控件"),
    LOCATION("Location", "", "位置"),
    RELATED_APPROVAL("RelatedApproval", "", "关联审批单"),
    FORMULA("Formula", "", "公式"),
    DATE_RANGE("DateRange", "", "DateRange"),
    PHONE_NUMBER("PhoneNumber", "", "手机号"),
    ;

    private final String code;
    private final String codeTwo;
    private final String name;

    ApproveControlEnum(String code, String codeTwo, String name) {
        this.code = code;
        this.codeTwo = codeTwo;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getCodeTwo() {
        return codeTwo;
    }

    public static String getNameByCode(String code, String codeTwo) {
        if (code == null) {
            return null;
        }
        for (ApproveControlEnum stateEnum : ApproveControlEnum.values()) {
            if (stateEnum.code.equals(code) && stateEnum.codeTwo.equals(codeTwo)) {
                return stateEnum.name;
            }
        }
        return null;
    }

}
