package com.yelink.dfscommon.constant;

/**
 * <AUTHOR>
 * @description 对外接口类型枚举
 * @Date 2023/2/17
 */
public enum OpenApiEnum {
    /**
     * 生产工单相关拓展接口
     */
    WORK_ORDER_INVEST_CHECK("open", "workOrder", "investCheck", "生产工单投产检查"),
    /**
     * 精制相关接口
     */
    UPDATE_JZ_ROLE("jingzhi", "jingzhi", "updateJzRole", "修改精制角色信息"),
    GET_JZ_APP_DETAIL("jingzhi", "jingzhi", "getJzAppDetail", "获取精制小程序详情"),
    JZ_REGISTERED_PAD("jingzhi", "jingzhi", "jzRegisteredPad", "pad 注册免密登录用户"),
    SYNC_JZ_ROLE_USER_PERMISSION("jingzhi", "jingzhi", "syncJzRoleUserPermission", "全量同步修改精制角色用户权限信息"),
    SYNC_JZ_ADD_ROLE_USER("jingzhi", "jingzhi", "syncJzAddRoleUser", "增量同步修改精制角色用户权限信息"),
    PUSH_MESSAGE_TO_JZ("jingzhi", "jingzhi", "pushMessageToJz", "发送新消息至精制"),
    PUSH_MESSAGE_TO_JZ_V2("jingzhi", "jingzhi", "pushMessageToJzV2", "发送新消息至精制V2"),
    ADD_USER_BY_JZ("jingzhi", "jingzhi", "addUserByJz", "通过精制新增keycloak用户"),
    GET_JZ_USER_BY_USERNAME("jingzhi", "jingzhi", "getJzUserByUsername", "通过用户名获取精制用户"),
    UPDATE_JZ_USER("jingzhi", "jingzhi", "updateJzUser", "更新精制用户"),
    ADD_JZ_ROLE("jingzhi", "jingzhi", "addJzRole", "新增精制角色"),
    DELETE_JZ_ROLE("jingzhi", "jingzhi", "deleteJzRole", "删除精制角色"),
    CHECK_JZ_ROLE_BY_NAME("jingzhi", "jingzhi", "checkJzRoleByName", "根据角色名称检查精制角色是否存在"),
    RESET_JZ_PASSWORD("jingzhi", "jingzhi", "resetJzPassword", "调用精制修改密码"),
    GET_JZ_ROLE_BIND_APPS("jingzhi", "jingzhi", "getJzRoleBindApps", "获取某个精制角色绑定的小程序|集合信息"),
    SYNC_JZ_ROLE_KEYCLOAK_ID("jingzhi", "jingzhi", "syncJzRoleKeycloakId", "获取精制所有dfs角色"),
    GET_JZ_DOWNLOAD_ADDR("jingzhi", "jingzhi", "getJzDownloadAddr", "获取精制下载地址（apk、windows）"),
    GET_JZ_USER_LIST("jingzhi", "jingzhi", "getJzUserList", "获取用户列表"),
    GET_JZ_ROLE_LIST("jingzhi", "jingzhi", "getJzRoleList", "获取角色列表"),
    GET_JZ_APP_V2("jingzhi", "jingzhi", "getJzAppV2", "查询精制app列表V2"),
    BIND_ROLE_APP("jingzhi", "jingzhi", "bindRoleApp", "角色绑定小程序"),
    UNBIND_ROLE_APP("jingzhi", "jingzhi", "unbindRoleApp", "角色解绑小程序"),
    GET_ALL_ROLE_BIND_APPS("jingzhi", "jingzhi", "getAllRoleBindApps", "获取所有角色对应的在线小程序"),
    GET_ROLES_BY_APPLICATION_ID("jingzhi", "jingzhi", "getRolesByApplicationId", "获取小程序的角色信息"),
    CREATE_SHORTCUT("jingzhi", "jingzhi", "createShortcut", "创建小程序快捷方式"),
    DELETE_SHORTCUT("jingzhi", "jingzhi", "deleteShortcut", "删除小程序快捷方式"),

    /**
     * 关联物料数据相关接口
     */
    AMS_RELATED_MATERIAL_DATA("ams", "material", "getIsRelateMaterialData", "是否关联物料数据"),
    QMS_RELATED_MATERIAL_DATA("qms", "material", "getIsRelateMaterialData", "是否关联物料数据"),
    WMS_RELATED_MATERIAL_DATA("wms", "material", "getIsRelateMaterialData", "是否关联物料数据"),

    ;
    private String service;
    private String modelCode;

    private String interfaceCode;

    private String des;

    OpenApiEnum(String service, String modelCode, String interfaceCode, String des) {
        this.service = service;
        this.modelCode = modelCode;
        this.interfaceCode = interfaceCode;
        this.des = des;
    }

    public String getService() {
        return service;
    }

    public String getModelCode() {
        return modelCode;
    }

    public String getInterfaceCode() {
        return interfaceCode;
    }

    public String getDes() {
        return des;
    }

}
