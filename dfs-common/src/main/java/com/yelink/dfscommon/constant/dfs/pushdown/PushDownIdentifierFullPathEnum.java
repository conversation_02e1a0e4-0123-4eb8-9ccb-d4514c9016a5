package com.yelink.dfscommon.constant.dfs.pushdown;

import com.yelink.dfscommon.entity.CommonEnumInterface;

/**
 * 下推标识全路径编码枚举
 * 用于维护各个单据类型的下推标识的fullPathCode
 * 
 * <AUTHOR>
 */
public enum PushDownIdentifierFullPathEnum implements CommonEnumInterface {
    
    // ==================== 生产工单相关 ====================
    
    // 生产工单 -> 生产工单用料清单
    WORK_ORDER_TO_MATERIAL_LIST_NO_PUSH_DOWN("workOrderMaterialList_noPushDown", 
            "生产工单用料清单-未下推", 
            "production.workOrderConfig.pushDownIdentifierConf.workOrderMaterialList.noPushDown"),
    WORK_ORDER_TO_MATERIAL_LIST_PART_PUSH_DOWN("workOrderMaterialList_partPushDown", 
            "生产工单用料清单-部分下推", 
            "production.workOrderConfig.pushDownIdentifierConf.workOrderMaterialList.partPushDown"),
    WORK_ORDER_TO_MATERIAL_LIST_ALL_PUSH_DOWN("workOrderMaterialList_allPushDown", 
            "生产工单用料清单-已下推", 
            "production.workOrderConfig.pushDownIdentifierConf.workOrderMaterialList.allPushDown"),
    
    // 生产工单 -> 生产领料出库单
    WORK_ORDER_TO_OUTPUT_PICKING_NO_PUSH_DOWN("outputPickingProduct_noPushDown", 
            "生产领料出库单-未下推", 
            "production.workOrderConfig.pushDownIdentifierConf.outputPickingProduct.noPushDown"),
    WORK_ORDER_TO_OUTPUT_PICKING_PART_PUSH_DOWN("outputPickingProduct_partPushDown", 
            "生产领料出库单-部分下推", 
            "production.workOrderConfig.pushDownIdentifierConf.outputPickingProduct.partPushDown"),
    WORK_ORDER_TO_OUTPUT_PICKING_ALL_PUSH_DOWN("outputPickingProduct_allPushDown", 
            "生产领料出库单-已下推", 
            "production.workOrderConfig.pushDownIdentifierConf.outputPickingProduct.allPushDown"),
    
    // 生产工单 -> 生产补料出库单
    WORK_ORDER_TO_SUPPLEMENT_NO_PUSH_DOWN("workOrderSupplement_noPushDown", 
            "生产补料出库单-未下推", 
            "production.workOrderConfig.pushDownIdentifierConf.workOrderSupplement.noPushDown"),
    WORK_ORDER_TO_SUPPLEMENT_PART_PUSH_DOWN("workOrderSupplement_partPushDown", 
            "生产补料出库单-部分下推", 
            "production.workOrderConfig.pushDownIdentifierConf.workOrderSupplement.partPushDown"),
    WORK_ORDER_TO_SUPPLEMENT_ALL_PUSH_DOWN("workOrderSupplement_allPushDown", 
            "生产补料出库单-已下推", 
            "production.workOrderConfig.pushDownIdentifierConf.workOrderSupplement.allPushDown"),
    
    // 生产工单 -> 生产退料入库单
    WORK_ORDER_TO_RETURN_RECEIPT_NO_PUSH_DOWN("productionReturnReceipt_noPushDown", 
            "生产退料入库单-未下推", 
            "production.workOrderConfig.pushDownIdentifierConf.productionReturnReceipt.noPushDown"),
    WORK_ORDER_TO_RETURN_RECEIPT_PART_PUSH_DOWN("productionReturnReceipt_partPushDown", 
            "生产退料入库单-部分下推", 
            "production.workOrderConfig.pushDownIdentifierConf.productionReturnReceipt.partPushDown"),
    WORK_ORDER_TO_RETURN_RECEIPT_ALL_PUSH_DOWN("productionReturnReceipt_allPushDown", 
            "生产退料入库单-已下推", 
            "production.workOrderConfig.pushDownIdentifierConf.productionReturnReceipt.allPushDown"),
    
    // 生产工单 -> 生产入库单
    WORK_ORDER_TO_PRODUCTION_IN_NO_PUSH_DOWN("productionIn_noPushDown", 
            "生产入库单-未下推", 
            "production.workOrderConfig.pushDownIdentifierConf.productionIn.noPushDown"),
    WORK_ORDER_TO_PRODUCTION_IN_PART_PUSH_DOWN("productionIn_partPushDown", 
            "生产入库单-部分下推", 
            "production.workOrderConfig.pushDownIdentifierConf.productionIn.partPushDown"),
    WORK_ORDER_TO_PRODUCTION_IN_ALL_PUSH_DOWN("productionIn_allPushDown", 
            "生产入库单-已下推", 
            "production.workOrderConfig.pushDownIdentifierConf.productionIn.allPushDown"),
    
    // 生产工单 -> 生产检验单
    WORK_ORDER_TO_INSPECT_ORDER_NO_PUSH_DOWN("productionInspectOrder_noPushDown", 
            "生产检验单-未下推", 
            "production.workOrderConfig.pushDownIdentifierConf.productionInspectOrder.noPushDown"),
    WORK_ORDER_TO_INSPECT_ORDER_PART_PUSH_DOWN("productionInspectOrder_partPushDown", 
            "生产检验单-部分下推", 
            "production.workOrderConfig.pushDownIdentifierConf.productionInspectOrder.partPushDown"),
    WORK_ORDER_TO_INSPECT_ORDER_ALL_PUSH_DOWN("productionInspectOrder_allPushDown", 
            "生产检验单-已下推", 
            "production.workOrderConfig.pushDownIdentifierConf.productionInspectOrder.allPushDown"),
    
    // 生产工单 -> 生产巡检单
    WORK_ORDER_TO_PROCESS_ORDER_NO_PUSH_DOWN("productionProcessOrder_noPushDown",
            "生产巡检单-未下推",
            "production.workOrderConfig.pushDownIdentifierConf.productionProcessOrder.noPushDown"),
    WORK_ORDER_TO_PROCESS_ORDER_PART_PUSH_DOWN("productionProcessOrder_partPushDown",
            "生产巡检单-部分下推",
            "production.workOrderConfig.pushDownIdentifierConf.productionProcessOrder.partPushDown"),
    WORK_ORDER_TO_PROCESS_ORDER_ALL_PUSH_DOWN("productionProcessOrder_allPushDown",
            "生产巡检单-已下推",
            "production.workOrderConfig.pushDownIdentifierConf.productionProcessOrder.allPushDown"),

    // ==================== 生产工单用料清单相关 ====================

    // 生产工单用料清单 -> 生产领料出库单
    MATERIAL_LIST_TO_OUTPUT_PICKING_NO_PUSH_DOWN("outputPickingProduct_noPushDown",
            "生产领料出库单-未下推",
            "production.workOrderMaterialListConfig.pushDownIdentifierConf.outputPickingProduct.noPushDown"),
    MATERIAL_LIST_TO_OUTPUT_PICKING_PART_PUSH_DOWN("outputPickingProduct_partPushDown",
            "生产领料出库单-部分下推",
            "production.workOrderMaterialListConfig.pushDownIdentifierConf.outputPickingProduct.partPushDown"),
    MATERIAL_LIST_TO_OUTPUT_PICKING_ALL_PUSH_DOWN("outputPickingProduct_allPushDown",
            "生产领料出库单-已下推",
            "production.workOrderMaterialListConfig.pushDownIdentifierConf.outputPickingProduct.allPushDown"),

    // 生产工单用料清单 -> 生产补料出库单
    MATERIAL_LIST_TO_SUPPLEMENT_NO_PUSH_DOWN("workOrderSupplement_noPushDown",
            "生产补料出库单-未下推",
            "production.workOrderMaterialListConfig.pushDownIdentifierConf.workOrderSupplement.noPushDown"),
    MATERIAL_LIST_TO_SUPPLEMENT_PART_PUSH_DOWN("workOrderSupplement_partPushDown",
            "生产补料出库单-部分下推",
            "production.workOrderMaterialListConfig.pushDownIdentifierConf.workOrderSupplement.partPushDown"),
    MATERIAL_LIST_TO_SUPPLEMENT_ALL_PUSH_DOWN("workOrderSupplement_allPushDown",
            "生产补料出库单-已下推",
            "production.workOrderMaterialListConfig.pushDownIdentifierConf.workOrderSupplement.allPushDown"),

    // 生产工单用料清单 -> 生产退料入库单
    MATERIAL_LIST_TO_RETURN_RECEIPT_NO_PUSH_DOWN("productionReturnReceipt_noPushDown",
            "生产退料入库单-未下推",
            "production.workOrderMaterialListConfig.pushDownIdentifierConf.productionReturnReceipt.noPushDown"),
    MATERIAL_LIST_TO_RETURN_RECEIPT_PART_PUSH_DOWN("productionReturnReceipt_partPushDown",
            "生产退料入库单-部分下推",
            "production.workOrderMaterialListConfig.pushDownIdentifierConf.productionReturnReceipt.partPushDown"),
    MATERIAL_LIST_TO_RETURN_RECEIPT_ALL_PUSH_DOWN("productionReturnReceipt_allPushDown",
            "生产退料入库单-已下推",
            "production.workOrderMaterialListConfig.pushDownIdentifierConf.productionReturnReceipt.allPushDown");

    private final String code;
    private final String name;
    private final String fullPathCode;

    PushDownIdentifierFullPathEnum(String code, String name, String fullPathCode) {
        this.code = code;
        this.name = name;
        this.fullPathCode = fullPathCode;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }

    public String getFullPathCode() {
        return fullPathCode;
    }

    /**
     * 根据目标单据类型和下推状态获取对应的全路径编码
     *
     * @param targetOrderType 目标单据类型
     * @param pushDownState 下推状态
     * @return 全路径编码
     */
    public static String getFullPathCode(String targetOrderType, String pushDownState) {
        if (targetOrderType == null || pushDownState == null) {
            return null;
        }
        
        String enumCode = targetOrderType + "_" + pushDownState;
        for (PushDownIdentifierFullPathEnum pathEnum : PushDownIdentifierFullPathEnum.values()) {
            if (enumCode.equals(pathEnum.getCode())) {
                return pathEnum.getFullPathCode();
            }
        }
        return null;
    }

    /**
     * 根据目标单据类型和下推状态获取对应的枚举
     *
     * @param targetOrderType 目标单据类型
     * @param pushDownState 下推状态
     * @return 枚举值
     */
    public static PushDownIdentifierFullPathEnum getByTargetTypeAndState(String targetOrderType, String pushDownState) {
        if (targetOrderType == null || pushDownState == null) {
            return null;
        }
        
        String enumCode = targetOrderType + "_" + pushDownState;
        for (PushDownIdentifierFullPathEnum pathEnum : PushDownIdentifierFullPathEnum.values()) {
            if (enumCode.equals(pathEnum.getCode())) {
                return pathEnum;
            }
        }
        return null;
    }

    /**
     * 根据代码获取枚举
     *
     * @param code 状态代码
     * @return 枚举值
     */
    public static PushDownIdentifierFullPathEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (PushDownIdentifierFullPathEnum pathEnum : PushDownIdentifierFullPathEnum.values()) {
            if (code.equals(pathEnum.getCode())) {
                return pathEnum;
            }
        }
        return null;
    }

    /**
     * 根据全路径编码获取枚举
     *
     * @param fullPathCode 全路径编码
     * @return 枚举值
     */
    public static PushDownIdentifierFullPathEnum getByFullPathCode(String fullPathCode) {
        if (fullPathCode == null) {
            return null;
        }
        for (PushDownIdentifierFullPathEnum pathEnum : PushDownIdentifierFullPathEnum.values()) {
            if (fullPathCode.equals(pathEnum.getFullPathCode())) {
                return pathEnum;
            }
        }
        return null;
    }
}
