package com.yelink.dfscommon.constant.qms;

import com.yelink.dfscommon.constant.EnumDTO;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * @Date 2023/5/29 9:45
 */
public enum InspectionsSchemeTypeEnum {
    /**
     * 检测类型：1过程检验、2库存检验、3首件检验、4末件检验、5来料检验、6退货检验、7发货检验、8生产巡检、9生产退料检验
     */
    PROCESS_INSPECTION(1, "过程检验"),
    FINISHED_PRODUCT_INSPECTION(2, "库存检验"),
    FIRST_PRODUCT_INSPECTION(3, "首件检验"),
    LAST_PRODUCT_INSPECTION(4, "末件检验"),
    INCOMING_INSPECTION(5, "来料检验"),
    RETURN_INSPECTION(6, "退货检验"),
    DELIVERY_INSPECTION(7, "发货检验"),
    PRODUCTION_INSPECTION(8, "生产巡检"),
    PRODUCTION_RETURN(9, "生产退料检验"),
    ;
    private final Integer code;
    private final String name;

    InspectionsSchemeTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code != null) {
            for (InspectionsSchemeTypeEnum inspectionsTypeEnum : InspectionsSchemeTypeEnum.values()) {
                if (inspectionsTypeEnum.code.equals(code)) {
                    return inspectionsTypeEnum.name;
                }
            }
        }
        return null;
    }

    public static Integer getCodeByName(String name) {
        if (StringUtils.isEmpty(name)) {
            return null;
        }
        for (InspectionsSchemeTypeEnum stateEnum : InspectionsSchemeTypeEnum.values()) {
            if (name.equals(stateEnum.name)) {
                return stateEnum.code;
            }
        }
        return null;
    }

    public static List<String> getNames() {
        ArrayList<String> names = new ArrayList<>();
        for (InspectionsSchemeTypeEnum typeEnum : InspectionsSchemeTypeEnum.values()) {
            names.add(typeEnum.name);
        }
        return names;
    }

    public static InspectionsSchemeTypeEnum getInspectionsSchemeTypeEnum(Integer code) {
        if (code != null) {
            for (InspectionsSchemeTypeEnum inspectionsTypeEnum : InspectionsSchemeTypeEnum.values()) {
                if (inspectionsTypeEnum.code.equals(code)) {
                    return inspectionsTypeEnum;
                }
            }
        }
        return null;
    }

    /**
     * 将枚举值转化成list集合
     *
     * @return
     */
    public static List<EnumDTO> toList() {
        List<EnumDTO> list = new ArrayList<>();
        for (InspectionsSchemeTypeEnum inspectionsTypeEnum : InspectionsSchemeTypeEnum.values()) {
            list.add(EnumDTO.builder().code(inspectionsTypeEnum.code).name(inspectionsTypeEnum.name).build());
        }
        return list;
    }

}
