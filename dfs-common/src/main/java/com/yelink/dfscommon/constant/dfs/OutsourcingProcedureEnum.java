package com.yelink.dfscommon.constant.dfs;

/**
 * <AUTHOR>
 * @Date 2024/5/14 16:50
 */
public enum OutsourcingProcedureEnum {
    /**
     * 委外工序 0：否、1：完全、2：部分
     */
    NO(0, "否"),
    YES(1, "完全"),
    PART(2, "部分"),
    ;

    private final int code;
    private final String name;

    OutsourcingProcedureEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (OutsourcingProcedureEnum stateEnum : OutsourcingProcedureEnum.values()) {
            if (stateEnum.code == code) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static Integer getCodeByName(String name) {
        for (OutsourcingProcedureEnum stateEnum : OutsourcingProcedureEnum.values()) {
            if (stateEnum.name.equals(name)) {
                return stateEnum.getCode();
            }
        }
        return null;
    }

}
