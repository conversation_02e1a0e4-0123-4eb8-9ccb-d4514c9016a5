package com.yelink.dfscommon.constant;

import org.apache.commons.lang3.StringUtils;

/**
 * excel导出时所需的枚举
 *
 * <AUTHOR>
 * @Date 2021/3/2 15:35
 */
public enum ExcelExportFormEnum {

    /**
     * 类型
     */
    WORK_ORDER("workOrder.detail", "生产工单"),
    WORK_ORDER_MATERIAL_LIST("workOrderMaterialList.detail", "生产工单用料清单"),
    WORK_SCHEDULE_PRODUCT_TO("workScheduleProduction.toBeScheduledList", "工单排产未排产列表"),
    WORK_SCHEDULE_PRODUCT_LIST("workScheduleProduction.scheduledList", "工单排产已排产列表"),
    ;
    private String fullPathCode;
    private String formName;


    ExcelExportFormEnum(String fullPathCode, String formName) {
        this.fullPathCode = fullPathCode;
        this.formName = formName;
    }

    public String getFullPathCode() {
        return fullPathCode;
    }

    public String getFormName() {
        return formName;
    }


    public static ExcelExportFormEnum getByType(String type) {
        if (StringUtils.isBlank(type)) {
            return null;
        }
        for (ExcelExportFormEnum modelEnum : ExcelExportFormEnum.values()) {
            if (type.equals(modelEnum.getFullPathCode())) {
                return modelEnum;
            }
        }
        return null;
    }

    public static String getNameByType(String type) {
        for (ExcelExportFormEnum modelEnum : ExcelExportFormEnum.values()) {
            if (modelEnum.fullPathCode.equals(type)) {
                return modelEnum.formName;
            }
        }
        return null;
    }

    public static String getTypeByName(String name) {
        for (ExcelExportFormEnum modelEnum : ExcelExportFormEnum.values()) {
            if (modelEnum.name().equals(name)) {
                return modelEnum.fullPathCode;
            }
        }
        return null;
    }
}
