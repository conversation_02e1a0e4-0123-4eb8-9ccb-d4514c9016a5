package com.yelink.dfscommon.constant;

import org.apache.commons.lang3.StringUtils;

/**
 * 单据大类枚举，方便后续数据调整时统一修改引用的地方
 * <AUTHOR>
 * @date 2021/8/27 17:17
 */
public enum CategoryTypeEnum {
    /**
     * 单据大类枚举
     */
    WORK_ORDER("workOrder", "生产工单"),
    WORK_ORDER_MATERIAL_LIST("workOrderMaterialList", "生产工单用料清单"),
    ;

    private String typeCode;
    private String typeName;

    CategoryTypeEnum(String typeCode, String typeName) {
        this.typeCode = typeCode;
        this.typeName = typeName;
    }

    public String getTypeCode() {
        return typeCode;
    }

    public String getTypeName() {
        return typeName;
    }


    public static String getNameByCode(String code) {
        if (StringUtils.isNotBlank(code)) {
            for (CategoryTypeEnum recoveryTypeEnum : CategoryTypeEnum.values()) {
                if (recoveryTypeEnum.getTypeCode().equals(code)) {
                    return recoveryTypeEnum.getTypeName();
                }
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        for (CategoryTypeEnum recoveryTypeEnum : CategoryTypeEnum.values()) {
            if (recoveryTypeEnum.getTypeName().equals(name)) {
                return recoveryTypeEnum.getTypeCode();
            }
        }
        return null;
    }
}
