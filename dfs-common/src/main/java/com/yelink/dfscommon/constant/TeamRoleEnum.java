package com.yelink.dfscommon.constant;

/**
 * <AUTHOR>
 * @Date 2022/5/31 14:55
 */
public enum TeamRoleEnum {

    /**
     * 班组角色枚举
     */
    LEADER(1, "班组长"),
    MEMBER(2, "组员"),
    TEMP_MEMBER(3, "临时组员");

    private Integer code;
    private String name;


    TeamRoleEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (TeamRoleEnum teamTypeEnum : TeamRoleEnum.values()) {
            if (code.intValue() == teamTypeEnum.code.intValue()) {
                return teamTypeEnum.getName();
            }
        }
        return null;
    }

    public static Integer getCodeByName(String name) {
        if(name == null){
            return null;
        }
        for (TeamRoleEnum teamTypeEnum : TeamRoleEnum.values()) {
            if (name.equals(teamTypeEnum.name)) {
                return teamTypeEnum.code;
            }
        }
        return null;
    }
}
