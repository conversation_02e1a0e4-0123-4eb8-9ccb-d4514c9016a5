package com.yelink.dfscommon.constant;


import java.util.ArrayList;
import java.util.List;

/**
 * 传感器类型
 */
public enum SensorTypeEnum {
    /**
     * 传感器类型
     */
    DEVICE_SENSOR("deviceSensor", "设备传感器"),
    FAC_SENSOR("facSensor", "工位传感器");

    private String code;
    private String name;

    SensorTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (SensorTypeEnum appendixTypeEnum : SensorTypeEnum.values()) {
            if (appendixTypeEnum.code.equals(code)) {
                return appendixTypeEnum.name;
            }
        }
        return null;
    }

    /**
     * 将枚举值转化成list集合
     *
     * @return
     */
    public static List<EnumDTO> toList() {
        List<EnumDTO> list = new ArrayList<>();
        for (SensorTypeEnum appendixTypeEnum : SensorTypeEnum.values()) {
            list.add(EnumDTO.builder().code(appendixTypeEnum.code).name(appendixTypeEnum.name).build());
        }
        return list;
    }
}
