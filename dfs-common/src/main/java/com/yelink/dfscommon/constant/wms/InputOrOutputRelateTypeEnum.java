package com.yelink.dfscommon.constant.wms;

import com.yelink.dfscommon.entity.wms.dto.InOrOutRelateTypeDTO;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/7 19:17
 */
public enum InputOrOutputRelateTypeEnum {
    // 入库 processReturn
    PURCHASE_RECEIPT("purchase", "采购入库单", "purchaseReceipt", "采购收料单"),
    PURCHASE_ORDER("purchase", "采购入库单", "purchaseOrder", "采购订单"),
    SALE_ORDER("saleReturn", "销售退库", "saleOrder", "销售订单"),
    IN_PRODUCT_ORDER("workOrderComplete", "生产入库", "productOrder", "生产订单"),
    IN_WORK_ORDER("workOrderComplete", "生产入库", "workOrder", "生产工单"),
    IN_WORK_ORDER_REPORT("workOrderComplete", "完工入库", "workOrderReport", "生产工单"),
    APPLICATION_RETURN_OUTPUT_PICKING_PRODUCT("applicationReturn", "生产退料入库", "outputPickingProduct", "生产领料出库单"),
    APPLICATION_RETURN_OUTPUT_PATCH_PRODUCT("applicationReturn", "生产退料入库", "outputPatchProduct", "生产补料出库单"),
    APPLICATION_RETURN_WORK_ORDER("applicationReturn", "生产退料入库", "workOrder", "生产工单"),
    APPLICATION_RETURN_WORK_CONSUME_OUT("applicationReturn", "生产退料入库", "outWorkConsumeProduct", "生产超耗出库单"),
    INPUT_DEVELOP_RETURN("developReturn", "其它入库", "developTakeAppReturn", "其它入库申请单"),
    INPUT_DEVELOP_PROCESS("processReturn", "受托加工", "developTakeAppReturn", "其它入库申请单"),
    INPUT_DEVELOP_STOCKTAKING_TASK("stocktakingReturn", "盘盈入库单", "stocktakingTaskIn", "盘点任务"),
    INPUT_DEVELOP_STOCKTAKING_PLAN("stocktakingReturn", "盘盈入库单", "stocktakingPlanIn", "盘点计划"),
    INPUT_DEVELOP_ALLOCATE("allocateReturn", "调拨入库单", "allocateIn", "调拨单"),
    INPUT_SUBCONTRACT_PRODUCT_RECEIPT( "subcontractProductReceipt", "委外订单入库单", "subcontractReceipt", "委外订单收货单"),
    OUTPUT_SUBCONTRACT_SUPPLY_OUTBOUND("subcontractSupplyOutbound", "委外超耗出库单", "subcontractMaterial", "委外订单物料清单"),
    OUTPUT_SUBCONTRACT_SUPPLEMENTARY_FOOD("subcontractSupplementaryFood", "委外补料出库单", "subcontractMaterial", "委外订单物料清单"),
    OUTPUT_SUBCONTRACT_PRODUCT_OUTBOUND("subcontractProductOutbound", "委外领料出库单", "subcontractMaterial", "委外订单物料清单"),
    INPUT_SUBCONTRACT_RETURN_RECEIPT_FOOD("subcontractReturnReceipt", "委外退料入库单", "subcontractSupplementaryFood", "委外补料出库单"),
    INPUT_SUBCONTRACT_RETURN_RECEIPT_OUTBOUND("subcontractReturnReceipt", "委外退料入库单", "subcontractProductOutbound", "委外领料出库单"),
    OUTPUT_STOCK_SALE("stockSaleOut", "销售出库单", "saleOrder", "销售出入库"),

    OUTPUT_DIRECT_SALE("directSale", "销售直接出库", "saleOrder", "销售订单"),
    INPUT_DEVELOP_TAKE_APP_RETURN("developTakeAppReturn", "其它入库申请单", "other", "其它入库申请单"),
    INPUT_DEVELOP_TAKE_APP_PROCESS("processAppReturn", "受托加工", "other", "其它入库申请单"),

    INPUT_DEVELOP_ADJUST("adjustReturn", "调整入库单", "adjust", "调整单"),

    // 出库
    DELIVERY_APPLICATION("delivery", "交付出库", "deliveryApplication", "销售出货单"),

    WORK_ORDER_TAKE_OUT_PRODUCT_ORDER_MATERIAL_LIST("workOrderTakeOut", "生产领料出库", "productOrderMaterialList", "生产订单用料清单"),
    WORK_ORDER_TAKE_OUT_WORK_ORDER("workOrderTakeOut", "生产领料出库", "workOrder", "生产工单"),
    WORK_ORDER_TAKEOUT_WORK_MATERIAL("workOrderTakeOut", "生产领料出库", "workMaterial", "生产工单用料清单"),
    WORK_ORDER_SUPPLEMENT_WORK_MATERIAL("workOrderSupplement", "生产补料出库", "workMaterial", "生产工单用料清单"),
    WORK_ORDER_SUPPLEMENT_OUT_WORK_ORDER("workOrderSupplement", "生产补料出库", "workOrder", "生产工单"),
    WORK_ORDER_SUPPLEMENT_OUT_PRODUCT_ORDER_MATERIAL_LIST("workOrderSupplement", "生产补料出库", "productOrderMaterialList", "生产订单用料清单"),
    WORK_CONSUME_OUT_PRODUCT_ORDER("workConsumeOut", "生产超耗出库", "productOrder", "生产订单"),
    WORK_CONSUME_OUT_WORK_ORDER("workConsumeOut", "生产超耗出库", "workOrder", "生产工单"),
    PURCHASE_RETURN_APPLICATION("purchaseReturn", "采购退料出库", "purchaseReturnApplication", "采购退料申请单"),
    //    PURCHASE("purchaseReturn", "采购退料出库", "purchaseOrder", "采购订单"),
    PURCHASE_RETURN_PURCHASE("purchaseReturn", "采购退料出库", "purchase", "采购入库单"),
    OUTPUT_DEVELOP_TAKE_OUT("developTakeOut", "其它出库", "developTakeAppOut", "其它出库申请单"),
    OUTPUT_DEVELOP_PROCESS("processOut", "受托加工", "developTakeAppOut", "其它出库申请单"),
    OUTPUT_DEVELOP_STOCKTAKING_TASK("stocktakingOut", "盘亏出库单", "stocktakingTaskOut", "盘点任务"),
    OUTPUT_DEVELOP_STOCKTAKING_PLAN("stocktakingOut", "盘亏出库单", "stocktakingPlanOut", "盘点计划"),
    OUTPUT_DEVELOP_ALLOCATE("allocateOut", "调拨出库单", "allocateOut", "调拨单"),
    OUTPUT_DEVELOP_TAKE_APP_OUT("developTakeAppOut", "其它出库申请单", "other", "其它出库申请单"),
    OUTPUT_DEVELOP_TAKE_APP_PROCESS("processAppOut", "受托加工", "other", "其它出库申请单"),
    OUTPUT_DEVELOP_ADJUST("adjustOut", "调整出库单", "adjust", "调整单"),

    SCRAP("scrapReturn", "报废", "other", "报废"),

    SALES_ISSUE_DOC_DELIVERY_REQUISITION("salesIssueDoc", "销售出库单", "deliveryRequisition", "销售出货单"),
    SALES_ISSUE_DOC_SALES_ORDER("salesIssueDoc", "销售出库单", "salesOrder", "销售订单"),
    SALES_ISSUE_DOC_STOCK_TRANSFER("salesIssueDoc", "销售出库单", "stockTransfer", "调拨单"),

    SALES_RETURN_RECEIPT_DOC_SALES_ISSUE_DOC("salesReturnReceiptDoc", "销售退货入库单", "salesIssueDoc", "销售出库单"),
    SALES_RETURN_RECEIPT_DOC_SALES_ORDER("salesReturnReceiptDoc", "销售退货入库单", "salesOrder", "销售订单"),

    SUBCONTRACT_ORDER("subcontractOrder", "委外订单", "other", ""),
    SUBCONTRACT_MATERIAL("subcontractMaterial", "委外订单物料清单", "other", ""),
    SUBCONTRACT_RECEIPT("subcontractReceipt", "委外订单收货单", "other", ""),
    INPUT("input", "入库", "other", ""),
    OUTPUT("output", "出库", "other", ""),
    SALE_INPUT("saleInput", "销售入库", "other", ""),
    SALE_OUTPUT("saleOutput", "销售出库", "other", ""),
    SUBCONTRACTING_OUTPUT("subcontractingOutput", "委外出库", "other", ""),
    SUBCONTRACTING_INPUT("subcontractingInput", "委外入库", "other", ""),
    TRANSFER_INPUT("transferInput", "调拨入库", "other", ""),
    TRANSFER_OUTPUT("transferOutput", "调拨出库", "other", ""),
    INITIALIZATION("initialization", "库存初始化", "other", ""),
    INPUT_STOCK_SALE_RETURN("stockSaleReturn", "销售退货入库", "other", ""),

    NO_SOURCE_ORDER("purchase", "采购入库单", "noOrderSource", "无来源单据"),
    ;

    private String typeCode;
    private String typeName;
    private String relateOrderType;
    private String relateOrderTypeName;

    InputOrOutputRelateTypeEnum(String typeCode, String typeName, String relateOrderType, String relateOrderTypeName) {
        this.typeCode = typeCode;
        this.typeName = typeName;
        this.relateOrderType = relateOrderType;
        this.relateOrderTypeName = relateOrderTypeName;
    }

    public String getTypeCode() {
        return typeCode;
    }

    public String getTypeName() {
        return typeName;
    }

    public String getRelateOrderType() {
        return relateOrderType;
    }

    public String getRelateOrderTypeName() {
        return relateOrderTypeName;
    }


    public static List<InOrOutRelateTypeDTO> getRelateTypeByInOrOutType(String inOrOutType) {
        List<InOrOutRelateTypeDTO> list = new ArrayList<>();
        for (InputOrOutputRelateTypeEnum typeEnum : InputOrOutputRelateTypeEnum.values()) {
            if (typeEnum.typeCode.equals(inOrOutType)) {
                list.add(
                        InOrOutRelateTypeDTO.builder()
                                .typeCode(typeEnum.relateOrderType)
                                .typeName(typeEnum.relateOrderTypeName)
                                .build()
                );
            }
        }
        return list;
    }

    public static List<InOrOutRelateTypeDTO> getAllocationType() {
        List<InOrOutRelateTypeDTO> list = new ArrayList<>();
        list.add(
                InOrOutRelateTypeDTO.builder()
                        .typeCode(InputOrOutputRelateTypeEnum.DELIVERY_APPLICATION.relateOrderType)
                        .typeName(InputOrOutputRelateTypeEnum.DELIVERY_APPLICATION.relateOrderTypeName)
                        .build()
        );
        list.add(
                InOrOutRelateTypeDTO.builder()
                        .typeCode(InputOrOutputRelateTypeEnum.WORK_ORDER_TAKE_OUT_PRODUCT_ORDER_MATERIAL_LIST.relateOrderType)
                        .typeName("生产订单用料清单")
                        .build()
        );
        list.add(
                InOrOutRelateTypeDTO.builder()
                        .typeCode(InputOrOutputRelateTypeEnum.OUTPUT_DEVELOP_TAKE_APP_OUT.relateOrderType)
                        .typeName(InputOrOutputRelateTypeEnum.OUTPUT_DEVELOP_TAKE_APP_OUT.relateOrderTypeName)
                        .build()
        );
        return list;
    }

    public static InputOrOutputRelateTypeEnum get(String typeCode, String relateOrderType) {
        if (StringUtils.isNotBlank(typeCode) && StringUtils.isNotBlank(relateOrderType)) {
            for (InputOrOutputRelateTypeEnum typeEnum : InputOrOutputRelateTypeEnum.values()) {
                if (typeEnum.getTypeCode().equals(typeCode) && typeEnum.getRelateOrderType().equals(relateOrderType)) {
                    return typeEnum;
                }
            }
        }
        return null;
    }

    public static String getRelateOrderTypeByCode(String typeCode) {
        if (StringUtils.isNotBlank(typeCode)) {
            for (InputOrOutputRelateTypeEnum typeEnum : InputOrOutputRelateTypeEnum.values()) {
                if (typeEnum.typeCode.equals(typeCode)) {
                    return typeEnum.relateOrderType;
                }
            }
        }
        return null;
    }

    public static String getRelateOrderTypeNameByRelateTypeCode(String relateTypeCode) {
        String typeName = "";
        if (StringUtils.isNotBlank(relateTypeCode)) {
            for (InputOrOutputRelateTypeEnum typeEnum : InputOrOutputRelateTypeEnum.values()) {
                if (typeEnum.relateOrderType.equals(relateTypeCode)) {
                    return typeEnum.relateOrderTypeName;
                }
            }
        }
        if("".equals(typeName)) {
            return  getTypeNameByTypeCode(relateTypeCode);
        }
        return null;
    }

    public static String getTypeNameByTypeCode(String typeCode) {
        if (StringUtils.isNotBlank(typeCode)) {
            for (InputOrOutputRelateTypeEnum typeEnum : InputOrOutputRelateTypeEnum.values()) {
                if (typeEnum.typeCode.equals(typeCode)) {
                    return typeEnum.typeName;
                }
            }
        }
        return null;
    }

    public static String getTypeCodeByTypeName(String typeName) {
        if (StringUtils.isNotBlank(typeName)) {
            for (InputOrOutputRelateTypeEnum typeEnum : InputOrOutputRelateTypeEnum.values()) {
                if (typeEnum.typeName.equals(typeName)) {
                    return typeEnum.typeCode;
                }
            }
        }
        return null;
    }

    public static InputOrOutputRelateTypeEnum getType(String typeCode, String relateOrderTypeName) {
        if (StringUtils.isNotBlank(typeCode) && StringUtils.isNotBlank(relateOrderTypeName)) {
            for (InputOrOutputRelateTypeEnum typeEnum : InputOrOutputRelateTypeEnum.values()) {
                if (typeEnum.getTypeCode().equals(typeCode) && typeEnum.getRelateOrderTypeName().equals(relateOrderTypeName)) {
                    return typeEnum;
                }
            }
        }
        return null;
    }
    public static boolean between(String typeCode,InputOrOutputRelateTypeEnum ... types) {
        boolean res=false;
        if (StringUtils.isNotBlank(typeCode)&&null!=types&&types.length>0) {
            for (InputOrOutputRelateTypeEnum t : types) {
                if (t.typeCode.equals(typeCode)) {
                    res=true;
                    break;
                }
            }
        }
        return res;
    }

}
