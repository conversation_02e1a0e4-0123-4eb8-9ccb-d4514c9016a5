package com.yelink.dfscommon.constant;

import org.apache.commons.lang3.StringUtils;

/**
 * 输入方式类型
 *
 * <AUTHOR>
 * @Date 2021/3/2 15:35
 */
public enum InputTypeEnum {

    /**
     * 类型
     */
    INPUT("input", "输入框"),
    SELECT("select", "下拉单选"),
    DATE_TIME_PICKER("dateTimePicker", "日期时间选择器"),
//    DYNAMIC_FORMULA("dynamicFormula", "动态公式"),
//    SELECT_MUTIPLE("select-mutiple", "下拉多选"),
    ;
    private String type;
    private String typeName;


    InputTypeEnum(String type, String typeName) {
        this.type = type;
        this.typeName = typeName;
    }

    public String getType() {
        return type;
    }

    public String getTypeName() {
        return typeName;
    }


    public static InputTypeEnum getByType(String type) {
        if (StringUtils.isBlank(type)) {
            return null;
        }
        for (InputTypeEnum modelEnum : InputTypeEnum.values()) {
            if (type.equals(modelEnum.getType())) {
                return modelEnum;
            }
        }
        return null;
    }

    public static String getNameByType(String type) {
        for (InputTypeEnum modelEnum : InputTypeEnum.values()) {
            if (modelEnum.type.equals(type)) {
                return modelEnum.typeName;
            }
        }
        return null;
    }

    public static String getTypeByName(String name) {
        for (InputTypeEnum modelEnum : InputTypeEnum.values()) {
            if (modelEnum.name().equals(name)) {
                return modelEnum.type;
            }
        }
        return null;
    }
}
