package com.yelink.dfscommon.constant;


import cn.hutool.extra.spring.SpringUtil;
import com.yelink.dfscommon.config.KafkaTopic;

/**
 * @description: 常量类
 * @author: zengfu
 * @create: 2020-12-04 15:22
 **/
public class Constants {

    public static final String SEP = ",";
    public static final String SPOT = ".";
    public static final String CN_FULL_POINT = "。";
    public static final String CROSSBAR = "-";
    public static final String UNDER_LINE = "_";
    public static final String CN_DUN_HAO = "、";
    public static final String CN_COMMA = "，";
    public static final String COLON = ":";
    public static final String CN_COLON = "：";
    public static final String UNIT = "unit";
    public static final String ZERO = "0";
    public static final Integer NOUGHT = 0;
    public static final Integer ONE = 1;
    public static final String DFS_RECORD_DEVICE_ = "dfs_record_device_";
    public static final String DIAGONAL_LINE = "/";
    public static final Boolean TRUE = true;
    public static final Boolean FALSE = false;
    public static final String TRUE_STR = "true";
    public static final String FALSE_STR = "false";
    public static final String NULL_STR = "null";
    public static final String LIMIT = "Limit";
    public static final String TIPS = "Tips";
    public static final String SKU = "-skuId";
    public static final Integer SKU_ID_DEFAULT_VAL = 0;
    public static final String API_TYPE = "ApiType";
    public static final String INNER_API = "innerApi";
    public static final String CUSTOM_FORMAT = "自定义格式";

    // 特殊字符，分割需转义
    public static final String SPOT_SPLIT = "\\.";

    /**
     * 炉次号常量
     */
    public static final String FURNACE_TWO = "H2";
    public static final String FURNACE_ONE = "H1";
    public static final String TAPPING_CAPACITY_EN = "tappingCapacity";
    public static final String TAPPING_CAPACITY_CN = "出钢量";

    /**
     * 成功状态码
     */
    public static final String SUCCESS_CODE = "200";

    public static final String PDF = ".pdf";
    public static final String TXT = ".txt";
    public static final String ZIP = ".zip";
    public static final String RAR = ".rar";
    public static final String MP4 = ".mp4";
    public static final String MOV = ".mov";
    public static final String AVI = ".avi";
    public static final String WMV = ".wmv";
    public static final String JPG = ".jpg";
    public static final String PNG = ".png";
    public static final String THRRE_GP = ".3gp";
    public static final String RMVB = ".rmvb";


    public static final String A = "A";
    public static final String B = "B";
    public static final String C = "C";

    public static final String EVENT_ID_INCREASE = "event_increase";

    /**
     * 产能锁
     */
    public static final Object FACTORY_CAPACITY_LOCK = "factoryCapacityLock";


    /**
     * --------------- dfs平台kafka消息推送topic ----------------
     * 由于现网需存在测试态功能，从dfs内部发送的出去的topic需要保证与正式环境的不一致
     */
    private static final KafkaTopic TOPIC = SpringUtil.getBean(KafkaTopic.class);
    public static final String WEBSOCKET = TOPIC.getWebsocket();
    public static final String TRANSFER_SOCKET = TOPIC.getTransferSocket();
    public static final String FORWARD_REMIND_MESSAGE_TOPIC = TOPIC.getForwardRemindMessage();
    //增删改主数据：物料、工厂模型、配置、bom、工艺工序等等
    public static final String KAFKA_MAIN_DATA_TOPIC = TOPIC.getEventMainData();
    //增删改仓库单据
    public static final String KAFKA_WAREHOUSE_TOPIC = TOPIC.getEventWarehouse();
    //增删改价值流：ams、dfs、qms等除wms 的单据
    public static final String KAFKA_VALUE_CHAIN_TOPIC = TOPIC.getEventValueChain();
    //增删改  权限、用户、角色、部门
    public static final String KAFKA_AUTHORITY_AUDIT_TOPIC = TOPIC.getEventAuthorityAudit();
    //  ams 采购增删改
    public static final String KAFKA_PURCHASE_TOPIC = TOPIC.getEventPurchase();
    // 指标的
    public static final String KAFKA_METRICS_TOPIC = TOPIC.getEventMetrics();
    // 报工记录增删改
    public static final String KAFKA_RECORD_TOPIC = TOPIC.getEventRecord();
    // 告警 增删改
    public static final String KAFKA_ALARM_TOPIC = TOPIC.getEventAlarm();
    //主数据状态变更
    public static final String KAFKA_MAIN_DATA_STATE_TOPIC = TOPIC.getEventMainDataState();
    //价值流数据状态变更
    public static final String KAFKA_VALUE_CHAIN_STATE_TOPIC = TOPIC.getEventValueChainState();
    // 仓库价值流状态变更
    public static final String KAFKA_WAREHOUSE_STATE_TOPIC = TOPIC.getEventWarehouseState();
    // 采购状态变更
    public static final String KAFKA_PURCHASE_STATE_TOPIC = TOPIC.getEventPurchaseState();
    // 演示数据
    public static final String TARGET_TOPIC = TOPIC.getDeviceTest();
    // 演示数据
    public static final String COUNTER_TOPIC = TOPIC.getFakeCounter();

    /**
     * 表单配置取值选项
     */
    public static final String API ="api";  // api
    public static final String TABLE ="table";  // 枚举
    public static final String FORMULA ="formula";  // 公式（存入数据库）
    public static final String TIME_FORMULA ="timeFormula"; // 时间格式化
    public static final String DYNAMIC_FORMULA ="dynamicFormula"; // 动态公式（不存入数据库）

    /**
     * 表单配置字段模块
     */
    public static final String BASE_FIELD = "baseField";
    public static final String BASE_EXTEND_FIELD = "baseExtendField";
    public static final String BASE_MATERIAL_LINE_FIELD = "baseMaterialLineField";
    public static final String BASE_MATERIAL_LINE_EXTEND_FIELD = "baseMaterialLineExtendField";
    public static final String MATERIAL_EXTEND_FIELD = "materialExtendField";
    public static final String MATERIAL_BASE_FIELD = "materialBaseField";
    public static final String BATCH_FIELD = "batchField";
    /**
     * 表单字段路由常量
     */
    public static final String FROM_MATERIAL_ROUTE = "/product-management/supplies";


    /**
     * 标签打印字段类型
     */
    public static final String LABEL_BASE = "base";
    public static final String LABEL_MATERIAL_EXTEND_FIELD = "materialExtendField";
    public static final String LABEL_BATCH_EXTEND_FIELD = "batchExtendField";

    public static final String QRCODE = "qrcode";
}
