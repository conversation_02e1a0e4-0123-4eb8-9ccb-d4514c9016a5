package com.yelink.dfscommon.constant;

/**
 * @description: 常量类
 * @author: zengfu
 * @create: 2020-12-04 15:22
 **/
public class RedisKeyPrefix {

    public static final String OPERATION_PROCESS_LOCK = "OPERATION_PROCESS_LOCK";
    /**
     * 内存计算
     */
    public static final String MEMORY_CAL_WORK_ORDER = "MEMORY_CAL_WORK_ORDER_";
    public static final String MEMORY_CAL_INPUT_WORK_ORDER = "MEMORY_CAL_INPUT_WORK_ORDER_";
    public static final Object STATION_IMPORT_LOCK = "";
    /**
     * 下推 进度
     */
    public static final String PUSH_DOWN_PROGRESS = "PUSH_DOWN_PROGRESS_";
    /**
     * 删除单据 进度
     */
    public static final String DELETE_PROGRESS = "DELETE_PROGRESS_";
    /**
     * 生产工单下推 给单加锁key
     */
    public static final String WORK_ORDER_PUSH_DOWN_MATERIAL_LIST_LOCK = "WORK_ORDER_PUSH_DOWN_MATERIAL_LIST_LOCK_";
    /**
     * 工单删除锁key
     */
    public static final String DELETE_WORK_ORDER_LOCK = "DELETE_WORK_ORDER_LOCK_";
    /**
     * BOM删除锁key
     */
    public static final String DELETE_BOM_LOCK = "DELETE_BOM_LOCK_";
    /**
     * 销售订单下推 给单加锁key
     */
    public static final String SALE_ORDER_PUSH_DOWN_LOCK = "SALE_ORDER_PUSH_DOWN_LOCK_";
    /**
     * 生产订单下推 给单加锁key
     */
    public static final String PRODUCT_ORDER_PUSH_DOWN_LOCK = "PRODUCT_ORDER_PUSH_DOWN_LOCK_";
    /**
     * 下推时生成的单据
     */
    public static final String NEW_MATERIAL_LIST_ORDER_NUMBER = "NEW_MATERIAL_LIST_ORDER_NUMBER_";
    /**
     * 产线看板前缀
     */
    public static final String LINE_BOARD_ = "LINE_BOARD_";
    /**
     * 工位看板前缀
     */
    public static final String CUBICLE_BOARD_ = "CUBICLE_BOARD_";
    /**
     * 指标前缀
     */
    public static final String TARGET_ = "TARGET_";
    /**
     * 字典表
     */
    public static final String DICT = "DICT";

    /**
     * 状态指标中缀
     */
    public static final String _STATE_ = "_STATE_";

    /**
     * 状态更改时间中缀
     */
    public static final String _STATE_CHANGE_ = "_STATE_CHANGE_";

    /**
     * 数量指标中缀
     */
    public static final String _COUNT_ = "_COUNT_";

    /**
     * 手环指标中缀
     */
    public static final String _WRISTBAND_ = "_WRISTBAND_";

    /**
     * 当日数量指标中缀
     */
    public static final String _TODAY_COUNT_ = "_TODAY_COUNT_";

    /**
     * 指标记录前缀
     */
    public static final String TARGET_METHOD_ = "TARGET_METHOD:";
    /**
     * 指标记录
     */
    public static final String TARGET_METHOD_LIST = "TARGET_METHOD_LIST";

    /**
     * 内部账户token，用于服务间的通信
     */
    public static final String INNER_TOKEN = "INNER_TOKEN";

    /**
     * 编号自增前缀
     */
    public static final String CODE_INCR_ = "CODE_INCR_";

    /**
     * 数据库清理前缀
     */
    public static final String TABLE_DATA_CLEAN_ = "TABLE_DATA_CLEAN_";

    /**
     * device指标记录
     */
    public static final String TARGET_DEVICE_LIST = "TARGET_DEVICE_LIST";

    /**
     * 五分钟指标记录
     */
    public static final String TARGET_RECORD_FIVE_MIN = "TARGET_RECORD_FIVE_MIN";

    /**
     * 一小时指标记录
     */
    public static final String TARGET_RECORD_ONE_HOUR = "TARGET_RECORD_ONE_HOUR";

    /**
     * 七天指标记录
     */
    public static final String TARGET_RECORD_WEEK = "TARGET_RECORD_WEEK";

    /**
     * 20分钟指标记录
     */
    public static final String TARGET_RECORD_TWENTY_MIN = "TARGET_RECORD_TWENTY_MIN";

    /**
     * 24小时指标记录
     */
    public static final String TARGET_RECORD_ONE_DAY = "TARGET_RECORD_ONE_DAY";

    /**
     * 一个月指标记录
     */
    public static final String TARGET_RECORD_MONTH = "TARGET_RECORD_MONTH";

    /**
     * 设备采集器指标key
     */
    public static final String DEVICE_SENSOR_VALUE = "DEVICE_SENSOR_VALUE:";

    /**
     * 工单指标任务
     */
    public static final String WORK_ORDER_TARGET_TASK = "WORK_ORDER_TARGET_TASK:";

    /**
     * 工单告警任务
     */
    public static final String WORK_ORDER_ALARM_TASK = "WORK_ORDER_ALARM_TASK:";

    /**
     * 产能记录
     */
    public static final String RECORD_OUT_PUT = "RECORD_OUT_PUT";

    /**
     * 采集器上报数据保存的key
     */
    public static final String SENSOR_VALUE_ = "SENSOR_VALUE_";

    /**
     * iot---productKey的key
     */
    public static final String PRODUCT_KEY = "PRODUCT_KEY";
    /**
     * 编码规则-防止重复的key
     */
    public static final String NUMBER_KEY = "NUMBER_KEY";
    /**
     * 条形码不良品-防止重复的key
     */
    public static final String BAR_CODE_KEY = "NUMBER_KEY";


    public static final String ALARM = "ALARM_";

    public static final String WORK_ORDER_STATE = "WORK_ORDER_STATE_";

    /**
     * 计数设备
     */
    public static final String COUNT_SENSOR = "COUNT_SENSOR_";

    /**
     * 插入产能记录加锁
     */
    public static final String INERT_RECORD_OUT_PUT = "INERT_RECORD_OUT_PUT";

    /**
     * 向前遍历到存在日历那天所减的天数
     */
    public static final String CALENDAR_EXIST_DATE_MINUS_NUM = "CALENDAR_EXIST_DATE_MINUS_NUM_";
    /**
     * bom导入进度的key
     */
    public static final String BOM_IMPORT_PROGRESS = "BOM_IMPORT_PROGRESS";
    /**
     * 销售订单导入进度的key
     */
    public static final String SALEORDER_IMPORT_PROGRESS = "SALEORDER_IMPORT_PROGRESS";

    /**
     * 物料导入进度的key
     */
    public static final String MATERIAL_IMPORT_PROGRESS = "MATERIAL_IMPORT_PROGRESS";

    /**
     * 工序导入进度的key
     */
    public static final String PROCEDURE_IMPORT_PROGRESS = "PROCEDURE_IMPORT_PROGRESS";

    /**
     * 工艺导入进度的key
     */
    public static final String CRAFT_IMPORT_PROGRESS = "CRAFT_IMPORT_PROGRESS";

    /**
     * 工艺工序导入进度的key
     */
    public static final String CRAFT_PROCEDURE_IMPORT_PROGRESS = "CRAFT_PROCEDURE_IMPORT_PROGRESS";

    /**
     * 工艺工序用料导入进度的key
     */
    public static final String CRAFT_PROCEDURE_MATERIAL_USED_IMPORT_PROGRESS = "CRAFT_PROCEDURE_MATERIAL_USED_IMPORT_PROGRESS";
    /**
     * 工艺工序物料导入进度的key
     */
    public static final String CRAFT_PROCEDURE_MATERIAL_IMPORT_PROGRESS = "CRAFT_PROCEDURE_MATERIAL_IMPORT_PROGRESS";
    /**
     * 工艺工序检验项导入进度的key
     */
    public static final String CRAFT_PROCEDURE_INSPECT_IMPORT_PROGRESS = "CRAFT_PROCEDURE_INSPECT_IMPORT_PROGRESS";
    /**
     * 工艺工序检验方案控制导入进度的key
     */
    public static final String CRAFT_PROCEDURE_INSPECT_CONTROLLER_IMPORT_PROGRESS = "CRAFT_PROCEDURE_INSPECT_CONTROLLER_IMPORT_PROGRESS";
    /**
     * 工序校验项定义导入进度的key
     */
    public static final String PROCEDURE_INSPECT_DEFINE_IMPORT_PROGRESS = "PROCEDURE_INSPECT_DEFINE_IMPORT_PROGRESS";
    /**
     * 工艺工序控制导入进度的key
     */
    public static final String CRAFT_PROCEDURE_CONTROL_IMPORT_PROGRESS = "CRAFT_PROCEDURE_CONTROL_IMPORT_PROGRESS";
    /**
     * 工艺工序工时导入进度的key
     */
    public static final String CRAFT_PROCEDURE_WORK_HOUR_IMPORT_PROGRESS = "CRAFT_PROCEDURE_WORK_HOUR_IMPORT_PROGRESS";
    /**
     * 工艺工序的工艺参数导入进度的key
     */
    public static final String CRAFT_PROCEDURE_PARAMETER_IMPORT_PROGRESS = "CRAFT_PROCEDURE_PARAMETER_IMPORT_PROGRESS";
    /**
     * 工艺工序附件导入导入进度的key
     */
    public static final String CRAFT_PROCEDURE_FILE_IMPORT_PROGRESS = "CRAFT_PROCEDURE_FILE_IMPORT_PROGRESS";

    /**
     * 工艺工序物料导入进度的key
     */
    public static final String CRAFT_PROCEDURE_DEVICE_PARAMETER_IMPORT_PROGRESS = "CRAFT_PROCEDURE_DEVICE_PARAMETER_IMPORT_PROGRESS";
    /**
     * 根据isTemplate判断获取工艺key还是工艺模板key
     */
    public static String getCraftOrTemplateKey(String key, Boolean isTemplate) {
        if (isTemplate != null && isTemplate) {
            key = "CRAFT_TEMPLATE_" + key;
        }
        return key;
    }

    /**
     * 库位导入进度的key
     */
    public static final String LOCATION_IMPORT_PROGRESS = "LOCATION_IMPORT_PROGRESS";
    /**
     * 库位导入锁的key
     */
    public static final String LOCATION_IMPORT_LOCK = "LOCATION_IMPORT_LOCK";

    /**
     * 批次库位导入进度的key
     */
    public static final String BAR_CODE_LOCATION_PROGRESS = "BAR_CODE_LOCATION_PROGRESS";
    /**
     * 批次库位导入锁的key
     */
    public static final String BAR_CODE_LOCATION_IMPORT_LOCK = "BAR_CODE_LOCATION_IMPORT_LOCK";

    /**
     * 工单导入进度的key
     */
    public static final String WORK_ORDER_IMPORT_PROGRESS = "WORK_ORDER_IMPORT_PROGRESS";

    /**
     * 生产订单导入进度的key
     */
    public static final String PRODUCTION_ORDER_IMPORT_PROGRESS = "PRODUCTION_ORDER_IMPORT_PROGRESS";

    /**
     * 产品检测方案导入进度的key
     */
    public static final String PRODUCT_INSPECTION_SCHEME_IMPORT_PROGRESS = "PRODUCT_INSPECTION_SCHEME_IMPORT_PROGRESS";

    /**
     * BOM导入锁的key
     */
    public static final String BOM_IMPORT_LOCK = "BOM_IMPORT_LOCK";
    /**
     * 销售订单导入锁的key
     */
    public static final String SALEORDER_IMPORT_LOCK = "SALEORDER_IMPORT_LOCK";
    /**
     * 生产订单导入锁的key
     */
    public static final String PRODUCTION_ORDER_IMPORT_LOCK = "PRODUCTION_ORDER_IMPORT_LOCK";
    /**
     * 物料导入锁的key
     */
    public static final String MATERIAL_IMPORT_LOCK = "MATERIAL_IMPORT_LOCK";

    /**
     * 工序导入锁的key
     */
    public static final String PROCEDURE_IMPORT_LOCK = "PROCEDURE_IMPORT_LOCK";

    /**
     * 工艺导入锁的key
     */
    public static final String CRAFT_IMPORT_LOCK = "CRAFT_IMPORT_LOCK";

    /**
     * 工艺导入锁的key
     */
    public static final String CRAFT_PROCEDURE_IMPORT_LOCK = "CRAFT_PROCEDURE_IMPORT_LOCK";
    /**
     * 工艺工序物料导入锁的key
     */
    public static final String CRAFT_IMPORT_MATERIAL_USED_LOCK = "CRAFT_IMPORT_MATERIAL_USED_LOCK";
    /**
     * 工艺工序物料导入锁的key
     */
    public static final String CRAFT_IMPORT_MATERIAL_LOCK = "CRAFT_IMPORT_MATERIAL_LOCK";
    /**
     * 工艺工序检验项导入锁的key
     */
    public static final String CRAFT_PROCEDURE_INSPECT_LOCK = "CRAFT_PROCEDURE_INSPECT_LOCK";
    /**
     * 工艺工序检验方案控制导入锁的key
     */
    public static final String CRAFT_PROCEDURE_INSPECT_CONTROLLER_LOCK = "CRAFT_PROCEDURE_INSPECT_CONTROLLER_LOCK";
    /**
     * 工艺工序控制导入锁的key
     */
    public static final String CRAFT_PROCEDURE_CONTROL_LOCK = "CRAFT_PROCEDURE_CONTROL_LOCK";
    /**
     * 工艺工序工时导入锁的key
     */
    public static final String CRAFT_PROCEDURE_WORK_HOUR_LOCK = "CRAFT_PROCEDURE_WORK_HOUR_LOCK";
    /**
     * 工艺工序参数导入锁的key
     */
    public static final String CRAFT_PROCEDURE_PARAMETER_LOCK = "CRAFT_PROCEDURE_PARAMETER_LOCK";
    /**
     * 工艺工序附件导入锁的key
     */
    public static final String CRAFT_PROCEDURE_FILE_LOCK = "CRAFT_PROCEDURE_FILE_LOCK";
    /**
     * 工序校验项定义导入锁的key
     */
    public static final String PROCEDURE_INSPECT_DEFINE_LOCK = "PROCEDURE_INSPECT_DEFINE_LOCK";

    /**
     * 工单导入锁的key
     */
    public static final String WORK_ORDER_IMPORT_LOCK = "WORK_ORDER_IMPORT_LOCK";
    /**
     * 产品检测方案导入导入锁的key
     */
    public static final String PRODUCT_INSPECTION_SCHEME_IMPORT_LOCK = "PRODUCT_INSPECTION_SCHEME_IMPORT_LOCK";

    /**
     * 工艺工序 设备配方参数 导入锁key
     */
    public static final String CRAFT_IMPORT_DEVICE_PARAMETER_LOCK = "CRAFT_IMPORT_DEVICE_PARAMETER_LOCK";

    /**
     * 用户批量导入进度
     */
    public static final String USER_IMPORT_PROGRESS = "USER_IMPORT_PROGRESS";


    /**
     * 用户批量导入锁
     */
    public static final String USER_IMPORT_LOCK = "USER_IMPORT_LOCK";

    /**
     * 客户档案批量导入进度
     */
    public static final String CUSTOMER_IMPORT_PROGRESS = "CUSTOMER_IMPORT_PROGRESS";
    /**
     * 客户物料清单批量导入进度
     */
    public static final String CUSTOMER_MATERIAL_LIST_IMPORT_PROGRESS = "CUSTOMER_MATERIAL_LIST_IMPORT_PROGRESS";
    /**
     * 客户档案批量导入锁
     */
    public static final String CUSTOMER_IMPORT_LOCK = "CUSTOMER_IMPORT_LOCK";
    /**
     * 客户档案批量导入锁
     */
    public static final String CUSTOMER_MATERIAL_LIST_IMPORT_LOCK = "CUSTOMER_MATERIAL_LIST_IMPORT_LOCK";

    /**
     * 供应商档案批量导入进度
     */
    public static final String SUPPLIER_IMPORT_PROGRESS = "SUPPLIER_IMPORT_PROGRESS";
    /**
     * 供应商档案批量导入锁
     */
    public static final String SUPPLIER_IMPORT_LOCK = "SUPPLIER_IMPORT_LOCK";


    /**
     * 供应商物料批量导入进度
     */
    public static final String SUPPLIER_MATERIAL_IMPORT_PROGRESS = "SUPPLIER_MATERIAL_IMPORT_PROGRESS";
    /**
     * 供应商物料批量导入锁
     */
    public static final String SUPPLIER_MATERIAL_IMPORT_LOCK = "SUPPLIER_MATERIAL_IMPORT_LOCK";
    /**
     * 工位批量导入进度
     */
    public static final String FACILITY_IMPORT_PROGRESS = "FACILITY_IMPORT_PROGRESS";

    /**
     * 工位批量导入锁
     */
    public static final String FACILITY_IMPORT_LOCK = "FACILITY_IMPORT_LOCK";


    /**
     * [扫码入库小程序]入库完成锁的key
     */
    public static final String WAREHOUSING_COMPLETED_LOCK = "WAREHOUSING_COMPLETED_LOCK";

    /**
     * 计数器计算周期
     */
    public static final String CAL_INTERVAL = "CAL_INTERVAL";

    /**
     * 定时任务每天推送产线oee锁
     */
    public static final String TASK_LOCK_PUSH_OEE_DAY = "TASK_LOCK_PUSH_OEE_DAY";
    /**
     * 定时任务锁
     */
    public static final String TASK_LOCK_PUSH_PRODUCT_STATUS_DATA = "TASK_LOCK_PUSH_PRODUCT_STATUS_DATA";

    /**
     * dfs_record_line_input_output_order产线工单投入记录表
     */
    public static final String DFS_RECORD_LINE_INPUT_OUTPUT_ORDER = "dfs_record_line_input_output_order";

    /**
     * 生产流水表
     */
    public static final String DFS_PRODUCT_FLOW_CODE = "dfs_product_flow_code";
    /**
     * 炉次号生成同步锁
     */
    public static final String FURNACE_SYC_LOCK = "FURNACE_SYC_LOCK";
    /**
     * 告警升级同步锁
     */
    public static final String ALARM_UPGRADE_LOCK = "ALARM_UPGRADE_LOCK";

    /**
     * 叉车入库任务生产
     */
    public static final String DFS_INPUT_TASK = "dfs_INPUT_TASK";


    public static final String GOODS_SHELVES_IMPORT_LOCK = "GOODS_SHELVES_IMPORT_LOCK";
    public static final String GOODS_SHELVES_IMPORT_PROGRESS = "GOODS_SHELVES_IMPORT_PROGRESS";

    public static final String REPLACE_SCHEME_IMPORT_LOCK = "REPLACE_SCHEME_IMPORT_LOCK";
    public static final String REPLACE_SCHEME_IMPORT_PROGRESS = "REPLACE_SCHEME_IMPORT_PROGRESS";

    /**
     * 设备采集器指标key
     */
    public static String getDeviceTargetKey(Integer deviceId, String targetName) {
        return "DEVICE_TARGET_" + deviceId + "_" + targetName;
    }

    public static String getLastRecordKey(String eui) {
        return "LAST_RECORD_" + eui;
    }

    public static final String RULE_SEQ = "RULE_SEQ_";


    public static String getAllowReport(String workOrderNumber) {
        return "ALLOW_REPORT_" + workOrderNumber;
    }

    public static String getCalTmpList(String eui) {
        return "CAL_TMP_LIST:" + eui;
    }

    /**
     * 内存计算相关key
     */
    public static final String DEAL_MEMORY_LIST = "DEAL_MEMORY_LIST_";
    public static final String MEMORY_CAL_REPORT_WORK_ORDER = "MEMORY_CAL_REPORT_WORK_ORDER_";
    public static final String MEMORY_CAL_FAC_INPUT_WORK_ORDER = "MEMORY_CAL_FAC_INPUT_WORK_ORDER_";
    public static final String MEMORY_CAL_TASK_LOCK_ = "MEMORY_CAL_TASK_LOCK_";
    public static final String MEMORY_CAL_TASK_ITEM = "MEMORY_CAL_TASK_ITEM_";

    /**
     * 组合指标相关key
     */
    public static final String COMBINE_TARGET_ITEM = "COMBINE_TARGET_ITEM_";
    public static final String COMBINE_TARGET_VALUE = "COMBINE_TARGET_VALUE_";
    public static final String COMBINE_TARGET_LOCK = "COMBINE_TARGET_LOCK_";

    /**
     * 下推物料key
     */
    public static final String PUSH_DOWN_MATERIAL_CODE = "PUSH_DOWN_MATERIAL_CODE_";
    /**
     * 下推物料缓存选择的工艺id
     *      key -> 缓存CraftId值
     */
    public static final String PUSH_DOWN_MATERIAL_SELECT_CRAFT = "AMS:PUSH_DOWN_MATERIAL_SELECT_CRAFT:";

    /**
     * 下推工单key
     */
    public static final String PUSH_DOWN_WORK_ORDER_NUMBER = "PUSH_DOWN_WORK_ORDER_NUMBER_";

    public static String getPopDataKey(String key) {
        return "POP_DATA_" + key;
    }
    public static final String UPDATE_OPERATION_DAY_COUNT_BY_COUNTER = "UPDATE_OPERATION_DAY_COUNT_BY_COUNTER:";
    public static final String REFRESH_WORK_ORDER = "REFRESH_WORK_ORDER:";
    public static final String OPERATION_DAY_COUNT_CALL_BACK_LOCK = "OPERATION_DAY_COUNT_CALL_BACK_LOCK:";
    public static final String REFRESH_WORK_ORDER_CALL_BACK_LOCK = "REFRESH_WORK_ORDER_CALL_BACK_LOCK:";

    /**
     * 设备检查key
     */
    public static final String EQUIPMENT_INSPECTION_CALL_BACK_LOCK = "EQUIPMENT_INSPECTION_CALL_BACK_LOCK:";

    public static final String TRUNCATE_TASK = "TRUNCATE_TASK";

    /**
     * 生产订单进度key
     */
    public static final String PRODUCT_ORDER_PROCEDURE_PROGRESS = "PRODUCT_ORDER_PROCEDURE_PROGRESS:";

    /**
     * SMT模块
     */
    public static final String SCAN_SN = "SCAN_SN";
    public static final String BIND_MAC_SN = "BIND_MAC_SN";
    public static final String PRINT_NAMEPLATE_SN = "PRINT_NAMEPLATE_SN";
    public static final String PRINT_TOPBOXNAME_NAMEPLATE_SN = "PRINT_TOPBOXNAME_NAMEPLATE_SN";

    public static final String CREATE_BOX_NUMBER = "CREATE_BOX_NUMBER";
    public static final String CREATE_PALLET_NUMBER = "CREATE_PALLET_NUMBER";
    public static final String CREATE_INVOICE_NUMBER = "CREATE_INVOICE_NUMBER";
    public static final String PACKAGE = "PACKAGE";



    /**
     * BATTERY模块
     */
    /**
     * 电芯数据导入
     */
    public static final String CELL_DATA_DETAIL_IMPORT_LOCK = "CELL_DATA_DETAIL_IMPORT_LOCK";
    public  static final String CELL_DATA_DETAIL_IMPORT_PROGRESS = "CELL_DATA_DETAIL_IMPORT_PROGRESS";

    /**
     * PACK数据导入
     */
    public static final String PACK_DATA_DETAIL_IMPORT_LOCK = "PACK_DATA_DETAIL_IMPORT_LOCK";
    public  static final String PACK_DATA_DETAIL_IMPORT_PROGRESS = "PACK_DATA_DETAIL_IMPORT_PROGRESS";

    /**
     * 模组数据导入
     */
    public static final String MODULE_DATA_DETAIL_IMPORT_LOCK = "MODULE_DATA_DETAIL_IMPORT_LOCK";
    public  static final String MODULE_DATA_DETAIL_IMPORT_PROGRESS = "MODULE_DATA_DETAIL_IMPORT_PROGRESS";

    /**
     * 电芯档位导入
     */
    public static final String CELL_GEAR_POSITION_IMPORT_LOCK = "CELL_GEAR_POSITION_IMPORT_LOCK";
    public static final String CELL_GEAR_POSITION_IMPORT_PROGRESS = "CELL_GEAR_POSITION_IMPORT_PROGRESS";

    /**
     * BATTERY模块总数据导出
     */
    public  static final String BATTERY_EXPORT = "BATTERY_EXPORT";

    /**
     * 回退单据进度key
     */
    public  static final String RETURN_ORDER_KEY = "RETURN_ORDER_KEY_";
    /**
     * 回退单据锁key
     */
    public  static final String RETURN_ORDER_LOCK_KEY = "RETURN_ORDER_LOCK_KEY_";

    /**
     * 电池条码表
     */
    public static final String DFS_BATTERY_CODE = "DFS_BATTERY_CODE";

    /**
     * 电池条码表未绑定锁
     */
    public static final String DFS_BATTERY_CODE_UNBIND = "DFS_BATTERY_CODE_UNBIND";

    /**
     * 电池条码关联表
     */
    public static final String DFS_BATTERY_CODE_RELEVANCE = "DFS_BATTERY_CODE_RELEVANCE";

    /**
     * 工艺工序检验默认项key
     */
    public static final String PROCEDURE_DEFAULT_CONFIG = "PROCEDURE_DEFAULT_CONFIG_";

    /**
     * 电池条码数据导出
     */
    public static final String DFS_BATTERY_EXPORT = "DFS_BATTERY_EXPORT";
    public static final String NUMBER_RULE_LOCK_KEY = "NUMBER_RULE_LOCK_KEY";

    /**
     * 条码生成规则序号表
     */
    public static final String DFS_BATTERY_CODE_RULE_SEQ = "DFS_BATTERY_CODE_RULE_SEQ";

    /**
     * 用户签名导入锁的key
     */
    public static final String USER_SIGNATURE_FILE_LOCK = "USER_SIGNATURE_FILE_LOCK";

    /**
     * 用户签名导入进度的key
     */
    public static final String USER_SIGNATURE_FILE_IMPORT_PROGRESS = "USER_SIGNATURE_FILE_IMPORT_PROGRESS";

    /**
     * 组件假数据导入
     */
    public static final String COMPONENT_DATA_IMPORT_LOCK = "COMPONENT_DATA_IMPORT_LOCK";
    public static final String COMPONENT_DATA_IMPORT_PROGRESS = "COMPONENT_DATA_IMPORT_PROGRESS";

    /**
     * 物料属性清单默认项key
     */
    public static final String MATERIAL_ATTRIBUTE_LIST_DEFAULT_CONFIG = "MATERIAL_ATTRIBUTE_LIST_DEFAULT_CONFIG_";

    /**
     * 电池条码批量生成key
     */
    public static final String BATTERY_CODE_LOCK = "BATTERY_CODE_LOCK";
    public static final String BATTERY_CODE_PROGRESS = "BATTERY_CODE_PROGRESS";

    /**
     * 生产工单用料清单导入
     */
    public static final String WORK_ORDER_MATERIAL_LIST_IMPORT_LOCK = "WORK_ORDER_MATERIAL_LIST_IMPORT_LOCK";
    public static final String WORK_ORDER_MATERIAL_LIST_IMPORT_PROGRESS = "WORK_ORDER_MATERIAL_LIST_IMPORT_PROGRESS";

    /**
     * 物料特征参数导入
     */
    public static final String AUXILIARY_ATTR_IMPORT_LOCK = "AUXILIARY_ATTR_IMPORT_LOCK";
    public static final String AUXILIARY_ATTR_IMPORT_PROGRESS = "AUXILIARY_ATTR_IMPORT_PROGRESS";

    /**
     * 角色权限导入
     */
    public static final String ROLE_PERMISSION_IMPORT_LOCK = "ROLE_PERMISSION_IMPORT_LOCK";
    public static final String ROLE_PERMISSION_IMPORT_PROGRESS = "ROLE_PERMISSION_IMPORT_PROGRESS";

    /**
     * 查询字段缓存
     */
    public static final String QUERY_FIELD_CACHE = "QUERY_FIELD_CACHE";

}
