package com.yelink.dfscommon.constant;

import org.apache.commons.lang3.StringUtils;

/**
 * 产能单位
 * <AUTHOR>
 * @Date 2021/3/2 15:35
 */
public enum CapacityUnitEnum {

    /**
     * 产能单位
     */
    AREA("perHour", "每小时"),
    CENTER("perTeam", "每班次"),
    ;
    private String type;
    private String typeName;


    CapacityUnitEnum(String type, String typeName) {
        this.type = type;
        this.typeName = typeName;
    }

    public String getType() {
        return type;
    }

    public String getTypeName() {
        return typeName;
    }


    public static CapacityUnitEnum getByType(String type) {
        if (StringUtils.isBlank(type)) {
            return null;
        }
        for (CapacityUnitEnum modelEnum : CapacityUnitEnum.values()) {
            if (type.equals(modelEnum.getType())) {
                return modelEnum;
            }
        }
        return null;
    }

    public static String getNameByType(String type) {
        for (CapacityUnitEnum modelEnum : CapacityUnitEnum.values()) {
            if (modelEnum.type.equals(type)) {
                return modelEnum.typeName;
            }
        }
        return null;
    }

    public static String getTypeByName(String name) {
        for (CapacityUnitEnum modelEnum : CapacityUnitEnum.values()) {
            if (modelEnum.typeName.equals(name)) {
                return modelEnum.type;
            }
        }
        return null;
    }
}
