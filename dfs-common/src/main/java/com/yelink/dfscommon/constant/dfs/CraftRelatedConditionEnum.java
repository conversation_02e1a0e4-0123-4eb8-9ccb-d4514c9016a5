package com.yelink.dfscommon.constant.dfs;


import org.apache.commons.lang3.StringUtils;

/**
 * @Description: 表单字段对应的父级模块
 * @Author: zhengfu
 * @Date: 2020/12/5
 */
public enum CraftRelatedConditionEnum {

    /**
     */
    CRAFT_TEMPLATE("craftTemplate", "关联工艺模板"),
    CRAFT("craft", "关联工艺"),
    NO_CRAFT("noCraft", "无工艺配置"),

    ;


    private String code;
    private String name;

    CraftRelatedConditionEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        if (StringUtils.isNotBlank(code)) {
            for (CraftRelatedConditionEnum stateEnum : CraftRelatedConditionEnum.values()) {
                if (stateEnum.code.equals(code)) {
                    return stateEnum.name;
                }
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        if (StringUtils.isNotBlank(name)) {
            for (CraftRelatedConditionEnum stateEnum : CraftRelatedConditionEnum.values()) {
                if (name.equals(stateEnum.name)) {
                    return stateEnum.code;
                }
            }
        }
        return null;
    }
}
