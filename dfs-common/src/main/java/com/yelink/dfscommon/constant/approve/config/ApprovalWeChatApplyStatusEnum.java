package com.yelink.dfscommon.constant.approve.config;

/**
 * 企业微信审批申请状态变化类型
 *
 * <AUTHOR>
 * @Date 2024-06-16 15:49
 */
public enum ApprovalWeChatApplyStatusEnum {

    /**
     * 审批状态枚举
     */
    SUBMIT_ORDER(1, "提单"),
    APPROVE(2, "同意"),
    REJECT(3, "驳回"),
    FORWORD_FOR_REVIEW(4, "转审"),
    URGE_TO_HANDLE(5, "催办"),
    WITHDRAW(6, "撤销"),
    WITHDRAW_AFTER_APPROVAL(8, "通过后撤销"),
    ADD_REMARKS(10, "添加备注"),
    RETURN_TO_DESIGNATED_APPROVER(11, "回退给指定审批人"),
    ADD_APPROVER(12, "添加审批人"),
    COUNTERSIGN_AND_APPROVER(13, "加签并同意"),
    PROCESSED(14, "已办理"),
    TRANSFERRED(15, "已转交");

    private final Integer code;
    private final String name;

    ApprovalWeChatApplyStatusEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ApprovalWeChatApplyStatusEnum cycleEnum : ApprovalWeChatApplyStatusEnum.values()) {
            if (cycleEnum.code.equals(code)) {
                return cycleEnum.name;
            }
        }
        return null;
    }

    public static Integer getCodeByName(String name) {
        for (ApprovalWeChatApplyStatusEnum stateEnum : ApprovalWeChatApplyStatusEnum.values()) {
            if (name.equals(stateEnum.name)) {
                return stateEnum.code;
            }
        }
        return null;
    }

    public static ApprovalWeChatApplyStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ApprovalWeChatApplyStatusEnum cycleEnum : ApprovalWeChatApplyStatusEnum.values()) {
            if (cycleEnum.code.equals(code)) {
                return cycleEnum;
            }
        }
        return null;
    }

}
