package com.yelink.dfscommon.constant.approve.config;


import org.apache.commons.lang3.StringUtils;

/**
 * @Description: 需要审批的单据/模块枚举
 * @Author: zhengfu
 * @Date: 2020/12/5
 */
public enum ApproveModuleEnum {

    /**
     * 需要审批的单据/模块即审批按钮路径
     */
    SALES_ORDER("saleOrder", "销售订单", "sale.order:approval"),
    CUSTOMER_PROFILE("customerProfile", "客户档案", "client.file:approval"),
    DELIVERY_APPLICATION("deliveryApplication", "出货申请", "shipment.application:approval"),
    DELIVERY("delivery", "发货管理", "deliver.goods:approval"),
    PRODUCT_ORDER("productOrder", "生产订单", "product.order:approval"),
    WORK_ORDER("workOrder", "生产工单", "production.workorder:approval"),
    PACKAGE_ORDER("packageOrder", "包装工单", "package.order:approval"),
    PRODUCTION_TAKING("productionTaking", "生产领料", "production.requisition:approval"),
    SUPPLIER_PROFILE("supplierProfile", "供应商档案", "supplier.profile:approval"),
    PROCURE_DEMAND("procureDemand", "采购需求", "purchasing.demand:approval"),
    PURCHASE_ORDER("purchaseOrder", "采购订单", "purchasing.list:approval"),
    RECEIPT("receipt", "收货单", "delivery.order:approval"),
    MATERIAL("material", "物料", "material:approval"),
    AUXILIARY_ATTR("auxiliaryAttr", "特征参数", "auxiliary.attr:approval"),
    BOM("bom", "BOM", "bom:approval"),
    BOM_TEMPLATE("bomTemplate", "BOM模板", "bom.template:approval"),
    REPLACE_SCHEME("replaceScheme", "替代方案", "replace.scheme:approval"),
    CRAFT("craft", "工艺", "technology:approval"),
    CRAFT_TEMPLATE("craftTemplate", "工艺模板", "technology.template:approval"),
    PROCEDURE("procedure", "工序", "procedure:approval"),
    OUTPUT("output", "出库记录", "factory.record:approval"),
    INPUT("input", "入库记录", "access.record:approval"),
    TRANSFER("transfer", "物料调拨", "material.allocation:approval"),
    PRODUCT_INSPECTION_PLAN("productInspectionPlan", "产品检测方案", "product.inspection.plan:approval"),
    INCOMING_INSPECTION("incomingInspection", "来料检验单", "incoming.inspection:approval"),
    PRODUCT_INSPECTION_PROJECT("productInspectionProject", "产品检测项目", "product.inspection.project:approval"),
    PRODUCT_INSPECTION_REPORT_PROJECT("productInspectionReportProject", "检验报告", "product.inspection.result:approval"),
    PRODUCT_INSPECTION_RESULT_PROJECT("productInspectionResultProject", "质检结果", "product.inspection.report:approval"),
    NODE_DEFINITION("nodeDefinition", "节点定义", "node.definition:approval"),
    NODE_CONFIGURE("nodeConfigure", "节点配置", "node.configure:approval"),
    NODE_FACTORY_CONFIGURE("nodeFactoryConfigure", "节点工厂配置", "node.configure:approval"),
    PRODUCT_ORDER_MATERIAL_LIST("productOrderMaterialList", "生产订单用料清单", "production.materials:approval"),
    WORK_ORDER_MATERIAL_LIST("workOrderMaterialList", "生产工单用料清单", "workOrder.materials:approval"),
    PURCHASE_RETURN_REQUISITION("purchaseReturnRequisition", "采购退料申请单", "purchase.return.requisition:approval"),
    PRODUCT_RECEIPT_DOC("productReceiptDoc", "生产入库单", "product.receipt.doc:approval"),
    PURCHASE_RECEIPT_DOC("purchaseReceiptDoc", "采购入库单", "purchase.receipt.doc:approval"),
    PRODUCT_MATERIAL_RETURN_RECEIPT("productMaterialReturnReceipt", "生产退料入库单", "product.material.return.receipt:approval"),
    PICKING_ISSUE("pickingIssue", "生产领料出库", "picking.issue:approval"),
    PRODUCT_REPLENISH_DELIVERY("productReplenishDelivery", "生产补料出库", "product.replenish.delivery:approval"),
    PURCHASE_RETURN_ISSUE_DOC("purchaseReturnIssueDoc", "采购退料出库单", "purchase.return.issue.doc:approval"),
    SUBCONTRACT_ORDER("subcontractOrder", "委外订单", "outsourcingManagement.outsourcingOrder:approval"),
    SUBCONTRACT_MATERIAL("subcontractMaterial", "委外订单物料清单", "outsourcingManagement.outsourcingMaterialOrder:approval"),
    SUBCONTRACT_RECEIPT("subcontractReceipt", "委外订单收货单", "outsourcingManagement.outsourcingReceiptOrder:approval"),
    SUBCONTRACT_SUPPLY_OUTBOUND("subcontractSupplyOutbound", "委外超耗/补料出库单", "outsourcingManagement.outsourcingSupplyOutboundOrder:approval"),
    SUBCONTRACT_RETURN_RECEIPT("subcontractReturnReceipt", "委外退料入库单", "outsourcingManagement.outsourcingReturnReceiptOrder:approval"),
    SUBCONTRACT_PRODUCT_OUTBOUND("subcontractProductOutbound", "委外领料出库单", "outsourcingManagement.outsourcingProductOutboundOrder:approval"),
    SUBCONTRACT_PRODUCT_RECEIPT("subcontractProductReceipt", "委外订单入库单", "outsourcingManagement.outsourcingProductReceiptOrder:approval"),
    COUNT_SHEET("countSheet", "盘点单", "cougny:inventory:approval"),
    STOCK_TRANSFER("stockTransfer", "调拨单", "stock.transfer:approval"),
    SALE_ISSUE_DOC("salesIssueDoc", "销售出库单", "sales.issue.doc:approval"),
    SALES_RETURN_RECEIPT_DOC("salesReturnReceiptDoc", "销售退货入库单", "sales.return.receipt.doc:approval"),
    DELIVERY_REQUISITION("deliveryRequisition", "出货申请单", "delivery.requisition:approval"),
    OTHER_IN_APP_FORM("otherInAppForm", "其他入库申请单", "other.in.app.form:approval"),
    OTHER_OUT_APP_FORM("otherOutAppForm", "其他出库申请单", "other.out.app.form:approval"),
    OTHER_IN_FORM("otherInForm", "其他入库单", "other.in.form:approval"),
    OTHER_OUT_FORM("otherOutForm", "其他出库单", "other.out.form:approval"),

    ANTENATAL_PREPARATION_INSPECTION_ITEMS("antenatalPreparationInspectionItems", "产前检验项目", "inspection-items:approval"),
    ANTENATAL_PREPARATION_INSPECTION_PLAN("antenatalPreparationInspectionPlan", "产前检验方案", "inspection-plan:approval"),
    RETURN_ORDER("returnOrder", "采购退料", "return.order:approval"),
    INSPECTION_SHEET("inspectionSheet", "送检单", "inspection.sheet:approval"),
    MAC_INVOICE_SHEET("macInvoiceSheet", "光猫发货单", "invoice.mac:approval"),
    SUBCONTRACT_RETURN_ORDER("subcontractReturnOrder", "委外订单退料", "subcontract.return.order:approval"),

    PRODUCT_INSPECTION_SCHEME("productInspectionScheme", "产品检测方案", "product.inspection.scheme:approval"),
    PRODUCT_INSPECTION_SAMPLE("productInspectionScheme", "产品检测方案", "product.inspection.sample:approval"),
    INSPECT_ORDER("inspectOrder", "检验单", "inspect.order:approval"),
    ;


    private String code;
    private String name;
    private String path;

    ApproveModuleEnum(String code, String name, String path) {
        this.code = code;
        this.name = name;
        this.path = path;
    }

    public String getCode() {
        return code;
    }

    /**
     * 新审批配置生效编码
     * @return
     */
    public String getReleasedCode() {
        return code + ".releasedApprove";
    }

    public String getName() {
        return name;
    }

    public String getPath() {
        return path;
    }

    public static String getNameByCode(String code) {
        if (StringUtils.isNotBlank(code)) {
            for (ApproveModuleEnum stateEnum : ApproveModuleEnum.values()) {
                if (stateEnum.code.equals(code)) {
                    return stateEnum.name;
                }
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        if (StringUtils.isNotBlank(name)) {
            for (ApproveModuleEnum stateEnum : ApproveModuleEnum.values()) {
                if (name.equals(stateEnum.name)) {
                    return stateEnum.code;
                }
            }
        }
        return null;
    }

    public static String getPathByCode(String code) {
        if (StringUtils.isNotBlank(code)) {
            for (ApproveModuleEnum stateEnum : ApproveModuleEnum.values()) {
                if (stateEnum.code.equals(code)) {
                    return stateEnum.path;
                }
            }
        }
        return null;
    }
}
