package com.yelink.dfscommon.constant;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;

import java.util.Optional;


/**
 * @Description: 参数错误抛出的异常信息
 * @Author: zhengfu
 * @Date: 2020/12/4
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ParamException extends RuntimeException {

    private String code = "300";

    private String message;

    private RespCodeEnum respCodeEnum;

    /**
     * 无参构造方法
     */
    public ParamException() {
        super();
    }

    /**
     * 有参构造方法
     *
     * @param message
     */
    public ParamException(String message) {
        this.message = message;
        //super(message);
    }

    /**
     * 有参构造方法
     *
     * @param bindingResult
     */
    public ParamException(BindingResult bindingResult) {
        FieldError fieldError = bindingResult.getFieldErrors().get(0);
        this.message = Optional.ofNullable(fieldError).map(u -> fieldError.getDefaultMessage()).orElse("数据格式不合法");
    }
    /**
     * 有参构造方法
     *
     * @param respCodeEnum
     */
    public ParamException(RespCodeEnum respCodeEnum) {
        super(respCodeEnum.getMsgDes());
        this.code = respCodeEnum.getMsgCode();
        this.respCodeEnum = respCodeEnum;
    }

}
