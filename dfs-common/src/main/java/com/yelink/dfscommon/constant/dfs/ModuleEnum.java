package com.yelink.dfscommon.constant.dfs;


import org.apache.commons.lang3.StringUtils;

/**
 * @Description: 需要审批的单据/模块枚举
 * @Author: zhengfu
 * @Date: 2020/12/5
 */
public enum ModuleEnum {

    /**
     */
    SALES_ORDER("saleOrder", "销售订单"),
    CUSTOMER_PROFILE("customerProfile", "客户档案"),
    DELIVERY_APPLICATION("deliveryApplication", "出货申请"),
    DELIVERY("delivery", "发货管理"),
    PRODUCT_ORDER("productOrder", "生产订单"),
    WORK_ORDER("workOrder", "生产工单"),
    PRODUCTION_TAKING("productionTaking", "生产领料"),
    SUPPLIER_PROFILE("supplierProfile", "供应商档案"),
    PROCURE_DEMAND("procureDemand", "采购需求"),
    PURCHASE_ORDER("purchaseOrder", "采购订单"),
    RECEIPT("receipt", "收货单"),
    MATERIAL("material", "物料"),
    AUXILIARY_ATTR("auxiliaryAttr", "特征参数"),
    BOM("bom", "BOM"),
    BOM_TEMPLATE("bomTemplate", "BOM模板"),
    REPLACE_SCHEME("replaceScheme", "替代方案"),
    CRAFT("craft", "工艺"),
    CRAFT_TEMPLATE("craftTemplate", "工艺模板"),
    PROCEDURE("procedure", "工序"),
    OUTPUT("output", "出库记录"),
    INPUT("input", "入库记录"),
    TRANSFER("transfer", "物料调拨"),
    PRODUCT_INSPECTION_PLAN("productInspectionPlan", "产品检测方案"),
    INCOMING_INSPECTION("incomingInspection", "来料检验单"),
    PRODUCT_INSPECTION_PROJECT("productInspectionProject", "产品检测项目"),
    PRODUCT_INSPECTION_REPORT_PROJECT("productInspectionReportProject", "检验报告"),
    PRODUCT_INSPECTION_RESULT_PROJECT("productInspectionResultProject", "质检结果"),
    NODE_DEFINITION("nodeDefinition", "节点定义"),
    NODE_CONFIGURE("nodeConfigure", "节点配置"),
    NODE_FACTORY_CONFIGURE("nodeFactoryConfigure", "节点工厂配置"),
    PRODUCT_ORDER_MATERIAL_LIST("productOrderMaterialList", "生产订单用料清单"),
    PURCHASE_RETURN_REQUISITION("purchaseReturnRequisition", "采购退料申请单"),
    PRODUCT_RECEIPT_DOC("productReceiptDoc", "生产入库单"),
    PURCHASE_RECEIPT_DOC("purchaseReceiptDoc", "采购入库单"),
    PRODUCT_MATERIAL_RETURN_RECEIPT("productMaterialReturnReceipt", "生产退料入库单"),
    PICKING_ISSUE("pickingIssue", "生产领料出库"),
    PRODUCT_REPLENISH_DELIVERY("productReplenishDelivery", "生产补料出库"),
    PURCHASE_RETURN_ISSUE_DOC("purchaseReturnIssueDoc", "采购退料出库单"),
    SUBCONTRACT_ORDER("subcontractOrder", "委外订单"),
    SUBCONTRACT_MATERIAL("subcontractMaterial", "委外订单物料清单"),
    SUBCONTRACT_RECEIPT("subcontractReceipt", "委外订单收货单"),
    SUBCONTRACT_SUPPLY_OUTBOUND("subcontractSupplyOutbound", "委外超耗/补料出库单"),
    SUBCONTRACT_RETURN_RECEIPT("subcontractReturnReceipt", "委外退料入库单"),
    SUBCONTRACT_PRODUCT_OUTBOUND("subcontractProductOutbound", "委外领料出库单"),
    SUBCONTRACT_PRODUCT_RECEIPT("subcontractProductReceipt", "委外订单入库单"),
    COUNT_SHEET("countSheet", "盘点单"),
    STOCK_TRANSFER("stockTransfer", "调拨单"),
    SALE_ISSUE_DOC("salesIssueDoc", "销售出库单"),
    SALES_RETURN_RECEIPT_DOC("salesReturnReceiptDoc", "销售退货入库单"),
    DELIVERY_REQUISITION("deliveryRequisition", "销售出货单"),
    OTHER_IN_APP_FORM("otherInAppForm", "其他入库申请单"),
    OTHER_OUT_APP_FORM("otherOutAppForm", "其他出库申请单"),
    OTHER_IN_FORM("otherInForm", "其他入库单"),
    OTHER_OUT_FORM("otherOutForm", "其他出库单"),

    ANTENATAL_PREPARATION_INSPECTION_ITEMS("antenatalPreparationInspectionItems", "产前检验项目"),
    ANTENATAL_PREPARATION_INSPECTION_PLAN("antenatalPreparationInspectionPlan", "产前检验方案"),
    RETURN_ORDER("returnOrder", "采购退料"),

    PRODUCT_INSPECTION_SCHEME("productInspectionScheme", "检验方案"),
    PRODUCT_INSPECTION_SAMPLE("productInspectionSample", "抽检方案"),
    INSPECT_ORDER("inspectOrder", "检验单"),


    ;


    private String code;
    private String name;

    ModuleEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        if (StringUtils.isNotBlank(code)) {
            for (ModuleEnum stateEnum : ModuleEnum.values()) {
                if (stateEnum.code.equals(code)) {
                    return stateEnum.name;
                }
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        if (StringUtils.isNotBlank(name)) {
            for (ModuleEnum stateEnum : ModuleEnum.values()) {
                if (name.equals(stateEnum.name)) {
                    return stateEnum.code;
                }
            }
        }
        return null;
    }
}
