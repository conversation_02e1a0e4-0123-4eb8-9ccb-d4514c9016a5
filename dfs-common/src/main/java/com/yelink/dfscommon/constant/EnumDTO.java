package com.yelink.dfscommon.constant;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @Description: 枚举类
 * @Author: SQ
 * @Date: 2021/12/17 10:54
 * @Version:1.0
 */
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class EnumDTO {

    @ApiModelProperty("code")
    private Object code;

    @ApiModelProperty("name")
    private Object name;
}
