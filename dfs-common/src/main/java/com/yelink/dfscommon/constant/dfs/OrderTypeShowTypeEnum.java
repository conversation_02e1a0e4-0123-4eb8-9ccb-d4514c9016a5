package com.yelink.dfscommon.constant.dfs;


/**
 * @Description: 单据类型展示类型
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020/12/5
 */
public enum OrderTypeShowTypeEnum {

    /**
     * 单据类型展示类型
     */
    SHOW_ALL("all", "展示所有"),
    SHOW_ENABLE("enable", "仅查询启用态"),
    ;
    private String code;
    private String name;

    OrderTypeShowTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }


}
