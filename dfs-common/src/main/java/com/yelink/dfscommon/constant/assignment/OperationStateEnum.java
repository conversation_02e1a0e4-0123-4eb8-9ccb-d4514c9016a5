package com.yelink.dfscommon.constant.assignment;


/**
 * @Description: 作业工单状态枚举
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020/12/5
 */
public enum OperationStateEnum {

    /**
     * 状态编码及描述
     * 1-创建、2-生效、3-投产、4-挂起、5-完成、6-关闭、7-取消
     */
    CREATED(1, "创建"),
    RELEASED(2, "生效"),
    INVESTMENT(3, "投产"),
    HANG_UP(4, "挂起"),
    FINISHED(5, "完成"),
    CLOSED(6, "关闭"),
    CANCELED(7, "取消");


    private int code;
    private String name;

    OperationStateEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (OperationStateEnum operationStateEnum : OperationStateEnum.values()) {
            if (operationStateEnum.code == code) {
                return operationStateEnum.name;
            }
        }
        return null;
    }

    public static Integer getCodeByName(String name) {
        for (OperationStateEnum operationStateEnum : OperationStateEnum.values()) {
            if (name.equals(operationStateEnum.name)) {
                return operationStateEnum.code;
            }
        }
        return null;
    }
}
