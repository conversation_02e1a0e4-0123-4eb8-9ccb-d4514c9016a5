package com.yelink.dfscommon.constant.approve.config;


/**
 * @Description: 需要审批的单据/模块枚举
 * @Author: zheng<PERSON>
 * @Date: 2020/12/5
 */
public enum IsApproveEnum {

    /**
     * 类型编码及描述 0：关闭，1：系统审批，2：企微审批
     */
    NO(0, "关闭", "no"),
    SYSTEM(1, "系统审批", "system"),
    WECHAT(2, "企微审批", "wechat");

    private Integer code;
    private String name;
    private String codeStr;

    IsApproveEnum(Integer code, String name, String codeStr) {
        this.code = code;
        this.name = name;
        this.codeStr = codeStr;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getCodeStr() {
        return codeStr;
    }

    public static Boolean isApprove(Integer code) {
        if (NO.getCode().equals(code)) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }
}
