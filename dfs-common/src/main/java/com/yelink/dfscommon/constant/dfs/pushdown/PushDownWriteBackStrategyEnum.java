package com.yelink.dfscommon.constant.dfs.pushdown;

import com.yelink.dfscommon.entity.CommonEnumInterface;

/**
 * 下推反写策略类型
 * <AUTHOR>
 */
public enum PushDownWriteBackStrategyEnum implements CommonEnumInterface {
    /**
     * 类型
     */
    ALL_CHANGE("任一下推单据改变后, 改变源单"),
    ONE_CHANGE("全部下推单据改变后, 改变源单"),

    ;
    private final String name;


    PushDownWriteBackStrategyEnum(String name) {
        this.name = name;
    }

    @Override
    public String getCode() {
        return this.name();
    }

    @Override
    public String getName() {
        return name;
    }
}
