package com.yelink.dfscommon.constant;


/**
 * @description: 字典常量类
 * @author: z<PERSON><PERSON>
 * @create: 2020-12-04 15:22
 **/
public class DictConstant {

    /**
     * 表单系统全局配置
     */
    public final static String FORM_GLOBAL_CONF = "formGlobalConf";
    /**
     * 表单字段名配置
     */
    public final static String FORM_FIELD_TYPE = "formFieldType";
    /**
     * 表单全局字段配置类型
     */
    // 语言
    public final static String LANGUAGE = "language";
    // 行业
    public final static String INDUSTRY = "industry";
    // 系统默认取值字段
    public final static String DEFAULT_FIELD = "defaultField";
    /**
     * 表单字段配置类型
     */
    // 系统字段
    public final static String SYS_FIELD = "sysField";
    // 自定义字段
    public final static String CUSTOM_FIELD = "customField";


    /**
     * 标签打印APP的标签类型
     */
    // 总类型
    public final static String BAR_CODE_PRINT_TYPE = "barCodePrintType";
    public final static String BATCH_PRINT = "批次打印";
    public final static String SINGLE_ITEM_PRINT = "单品打印";
    public final static String ORDER_PRINT_TYPE = "单据标签打印";
    public final static String OTHER_PRINT_TYPE = "其他类型";

    /**
     * 业务类型：用于获取物料/工艺工序  关联的检验触发条件
     */
    public static final String INSPECT_TRIGGER_CONDITION = "inspectTriggerCondition";

}
