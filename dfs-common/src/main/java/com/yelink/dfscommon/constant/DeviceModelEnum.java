package com.yelink.dfscommon.constant;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Date 2021/3/2 15:35
 */
public enum DeviceModelEnum {

    /**
     * 类型
     */
    GRID("grid", "车间"),
    FACILITY("facility", "工位"),
    ;
    private String type;
    private String typeName;


    DeviceModelEnum(String type, String typeName) {
        this.type = type;
        this.typeName = typeName;
    }

    public String getType() {
        return type;
    }

    public String getTypeName() {
        return typeName;
    }


    public static DeviceModelEnum getByType(String type) {
        if (StringUtils.isBlank(type)) {
            return null;
        }
        for (DeviceModelEnum modelEnum : DeviceModelEnum.values()) {
            if (type.equals(modelEnum.getType())) {
                return modelEnum;
            }
        }
        return null;
    }

    public static String getNameByType(String type) {
        for (DeviceModelEnum modelEnum : DeviceModelEnum.values()) {
            if (modelEnum.type.equals(type)) {
                return modelEnum.typeName;
            }
        }
        return null;
    }

    public static String getTypeByName(String name) {
        for (DeviceModelEnum modelEnum : DeviceModelEnum.values()) {
            if (modelEnum.name().equals(name)) {
                return modelEnum.type;
            }
        }
        return null;
    }
}
