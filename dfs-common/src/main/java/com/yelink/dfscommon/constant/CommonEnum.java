package com.yelink.dfscommon.constant;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description: 公共下拉选项实体
 * @author: z<PERSON><PERSON>
 * @create: 2020-07-02 14:21
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CommonEnum {
    /**
     * 编号
     */
    @ApiModelProperty("编号")
    private Object code;
    /**
     * 名称
     */
    @ApiModelProperty("名称")
    private Object name;
    /**
     * 类型
     */
    @ApiModelProperty("类型")
    private Object type;
}
