package com.yelink.dfscommon.constant.dfs;


import org.apache.commons.lang3.StringUtils;

/**
 * @Description: 表单字段对应的父级模块
 * @Author: zhengfu
 * @Date: 2020/12/5
 */
public enum FormParentModuleEnum {

    /**
     */
    BASE("base", "基础字段"),
    EXTEND("extend", "扩展字段"),


    ;


    private String code;
    private String name;

    FormParentModuleEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        if (StringUtils.isNotBlank(code)) {
            for (FormParentModuleEnum stateEnum : FormParentModuleEnum.values()) {
                if (stateEnum.code.equals(code)) {
                    return stateEnum.name;
                }
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        if (StringUtils.isNotBlank(name)) {
            for (FormParentModuleEnum stateEnum : FormParentModuleEnum.values()) {
                if (name.equals(stateEnum.name)) {
                    return stateEnum.code;
                }
            }
        }
        return null;
    }
}
