package com.yelink.dfscommon.constant.pms;

import com.yelink.dfscommon.provider.constant.KafkaMessageTypeInterface;

/**
 * <AUTHOR>
 * @description ams事件类型枚举
 * @Date 2023/2/17
 */
public enum PmsEventTypeEnum implements KafkaMessageTypeInterface {

    /**
     * 项目合同单据关联
     */
    UPSERT_ORDER_RELATION("project", "upsertOrderRelation", "保存或更新项目合同跟单据的关联关系"),
    DELETE_ORDER_RELATION("project", "deleteOrderRelation", "删除项目合同跟单据的关联关系"),


    ;
    private String modelCode;

    private String typeCode;

    private String des;

    PmsEventTypeEnum(String modelCode, String typeCode, String des) {
        this.modelCode = modelCode;
        this.typeCode = typeCode;
        this.des = des;
    }

    @Override
    public String getModelCode() {
        return modelCode;
    }

    @Override
    public String getTypeCode() {
        return typeCode;
    }

    @Override
    public String getDes() {
        return des;
    }

}
