package com.yelink.dfscommon.constant;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @description: 列表展示形式枚举
 * @author: z<PERSON><PERSON>
 * @create: 2020-07-02 14:21
 **/
public enum ListDisplayFormatEnum {


    MATERIAL("material","按物料"),

    ORDER("order","按单")
    ;

    @Getter
    private final String key;
    @Getter
    private final String val;

    ListDisplayFormatEnum(String key, String val) {
        this.key = key;
        this.val = val;
    }

    public static ListDisplayFormatEnum getByKey(String key) {
        for (ListDisplayFormatEnum stateEnum : ListDisplayFormatEnum.values()) {
            if (stateEnum.key.equals(key)) {
                return stateEnum;
            }
        }
        return null;
    }
}
