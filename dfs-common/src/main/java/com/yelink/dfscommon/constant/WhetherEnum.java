package com.yelink.dfscommon.constant;


import lombok.Getter;

/**
 * 是否
 * <AUTHOR>
 */
public enum WhetherEnum {
    /**
     *
     */
    NO(false, "否"),
    YES(true, "是");

    @Getter
    private final Boolean code;
    @Getter
    private final String name;

    WhetherEnum(Boolean code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(Boolean code) {
        for (WhetherEnum e : WhetherEnum.values()) {
            if (e.code.equals(code)) {
                return e.name;
            }
        }
        return null;
    }
    public static Boolean getCodeByName(String name) {
        for (WhetherEnum e : WhetherEnum.values()) {
            if (e.name.equals(name)) {
                return e.code;
            }
        }
        return null;
    }
}
