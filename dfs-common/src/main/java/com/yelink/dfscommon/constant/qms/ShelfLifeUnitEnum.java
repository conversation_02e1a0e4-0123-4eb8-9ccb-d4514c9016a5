package com.yelink.dfscommon.constant.qms;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Description 保质期单位
 * @Date 2021/12/18 15:23
 */
public enum ShelfLifeUnitEnum {
    /**
     * 保质期单位
     */
    YEAR("year","年"),
    <PERSON>ON<PERSON>("month","个月"),
    DAY("day","天"),
    HOUR("hour","小时");

    private String code;
    private String name;

    ShelfLifeUnitEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (ShelfLifeUnitEnum stateEnum : ShelfLifeUnitEnum.values()) {
            if (stateEnum.code.equals(code)) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        for (ShelfLifeUnitEnum stateEnum : ShelfLifeUnitEnum.values()) {
            if (name.equals(stateEnum.name)) {
                return stateEnum.code;
            }
        }
        return null;
    }

}
