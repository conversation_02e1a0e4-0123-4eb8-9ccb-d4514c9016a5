package com.yelink.dfscommon.constant.qms;

/**
 * @Description: 
 * @Author: SQ
 * @Date: 2021/12/16 19:26
 * @Version:1.0
 */
public enum InspectionSheetConclusionEnum {
    /**
     * 送检单的状态
     */
    QUALIFIED("qualified", "合格"),
    UNQUALIFIED("unqualified", "不合格"),
    ;

    private String code;
    private String name;


    InspectionSheetConclusionEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        if (code != null) {
            for (InspectionSheetConclusionEnum inspectionSheetStatusEnum : InspectionSheetConclusionEnum.values()) {
                if (inspectionSheetStatusEnum.code.equals(code)) {
                    return inspectionSheetStatusEnum.name;
                }
            }
        }
        return null;
    }
}
