package com.yelink.dfscommon.constant;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2021/8/27 17:17
 */
public enum OrderNumTypeEnum {
    // 销售订单、生产订单
    SALE_ORDER("saleOrder", "销售订单"),
    PRODUCT_ORDER("productOrder", "生产订单"),
    WORK_ORDER("workOrder", "生产工单"),
    WORK_ORDER_MATERIAL_LIST("workOrderMaterialList", "生产工单用料清单"),
    PRODUCT_ORDER_MATERIAL_LIST("productOrderMaterialList", "生产订单用料清单"),
    DELIVERY_APPLICATION("deliveryApplication", "出货申请单"),
    SALE_RETURN_ORDER("saleReturnOrder", "销售退货单"),
    PURCHASE_REQUEST("purchaseRequest", "采购需求单"),
    PURCHASE_ORDER("purchaseOrder", "采购订单"),
    PURCHASE_RECEIPT("purchaseReceipt", "采购收料单"),
    PURCHASE_RETURN("purchaseReturn", "采购退料单"),
    SUBCONTRACT_ORDER("subcontractOrder", "委外订单"),
    SUBCONTRACT_RECEIPT_ORDER("subcontractReceiptOrder", "委外收货单"),
    SUBCONTRACT_RETURN_ORDER("subcontractReturnOrder", "委外退货单"),
    SUBCONTRACT_DELIVERY_ORDER("subcontractDeliveryOrder", "委外发料单"),
    SUBCONTRACT_ORDER_MATERIAL("subcontractOrderMaterial", "委外订单用料清单"),
    ;

    private String typeCode;
    private String typeName;

    OrderNumTypeEnum(String typeCode, String typeName) {
        this.typeCode = typeCode;
        this.typeName = typeName;
    }

    public String getTypeCode() {
        return typeCode;
    }

    public String getTypeName() {
        return typeName;
    }


    public static String getNameByCode(String code) {
        if (StringUtils.isNotBlank(code)) {
            for (OrderNumTypeEnum recoveryTypeEnum : OrderNumTypeEnum.values()) {
                if (recoveryTypeEnum.getTypeCode().equals(code)) {
                    return recoveryTypeEnum.getTypeName();
                }
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        for (OrderNumTypeEnum recoveryTypeEnum : OrderNumTypeEnum.values()) {
            if (recoveryTypeEnum.getTypeName().equals(name)) {
                return recoveryTypeEnum.getTypeCode();
            }
        }
        return null;
    }
}
