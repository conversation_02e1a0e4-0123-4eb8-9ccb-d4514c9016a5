package com.yelink.dfscommon.constant;

import lombok.Getter;

/**
 * 工位机枚举
 * <AUTHOR>
 */
public enum LocationMachineEnum {

    /**
     * 一般工位机
     */
    ORDINARY_STATION_MACHINE("ordinaryStationMachine", "一般工位机"),

    /**
     * 维修工位机
     */
    MAINTENANCE_STATION_MACHINE("maintenanceStationMachine", "维修工位机"),

    /**
     * 包装工位机
     */
    PACKAGING_STATION_MACHINE("packagingStationMachine", "包装工位机"),
    /**
     * 质检工位机
     */
    QUALITY_INSPECTION_STATION_MACHINE("qualityInspectionStationMachine", "质检工位机"),
    ;

    @Getter
    private final String code;
    @Getter
    private final String name;
    LocationMachineEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
}
