package com.yelink.dfscommon.constant.wms;


/**
 * @Description: 出入库关联单据枚举
 * @Author: Chensn
 * @Date: 2020/12/5
 */
public enum StockInOrOutTypeEnum {

    // 入库关联
    STOCK_TAKING_RETURN("stocktakingReturn", "合箱入库"),
    DEVELOP_TAKE_OUT("developTakeOut", "拆箱出库"),

    ;

    private String code;
    private String name;

    StockInOrOutTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        if (code == null) {
            return null;
        }
        for (StockInOrOutTypeEnum stateEnum : StockInOrOutTypeEnum.values()) {
            if (stateEnum.code.equals(code)) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        for (StockInOrOutTypeEnum stateEnum : StockInOrOutTypeEnum.values()) {
            if (name.equals(stateEnum.name)) {
                return stateEnum.code;
            }
        }
        return null;
    }
    }
