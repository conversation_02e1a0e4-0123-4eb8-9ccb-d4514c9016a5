package com.yelink.dfscommon.constant.wms;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/7 19:17
 */
public enum StockInputOrOutputTypeEnum {
    // 入库
    INPUT_PURCHASE(0, "purchase", "采购入库单", StockChangeTypeEnum.INPUT),
    INPUT_SALE_RETURN(0, "saleReturn", "销售退库", StockChangeTypeEnum.INPUT),
    INPUT_WORK_ORDER_COMPLETE(0, "workOrderComplete", "生产入库", StockChangeTypeEnum.WORK_ORDER_COMPLETE),
    INPUT_WORK_ORDER_RETURN(0, "applicationReturn", "生产退料入库", StockChangeTypeEnum.INPUT),
    INPUT_DEVELOP_RETURN(0, "developReturn", "其它入库",  StockChangeTypeEnum.INPUT),
    INPUT_SUBCONTRACT_PRODUCT_RECEIPT(0, "subcontractProductReceipt", "委外订单入库单", StockChangeTypeEnum.SUBCONTRACTING_INPUT),
    INPUT_SUBCONTRACT_RETURN_RECEIPT(0, "subcontractReturnReceipt", "委外退料入库单", StockChangeTypeEnum.SUBCONTRACTING_INPUT),
	INPUT_STOCK_SALE_RETURN(0, "stockSaleReturn", "销售退货入库", StockChangeTypeEnum.SALE_INPUT),
    INPUT_ALLOCATION(0, "transferReceipt", "调拨入库", StockChangeTypeEnum.TRANSFER_INPUT),

    // 出库
    OUTPUT_DELIVERY(1, "delivery", "交付出库", StockChangeTypeEnum.DELIVERY),
    OUTPUT_WORK_ORDER_TAKE_OUT(1, "workOrderTakeOut", "生产领料出库", StockChangeTypeEnum.OUTPUT),
    OUTPUT_WORK_ORDER_SUPPLEMENT(1, "workOrderSupplement", "生产补料出库", StockChangeTypeEnum.OUTPUT),
    OUTPUT_PURCHASE_RETURN(1, "purchaseReturn", "采购退料出库", StockChangeTypeEnum.PURCHASERETURN),
    OUTPUT_DIRECT_SALE(1, "directSale", "销售直接出库", StockChangeTypeEnum.SALE_OUTPUT),
    OUTPUT_DEVELOP_TAKE_OUT(1, "developTakeOut", "其它出库", StockChangeTypeEnum.OUTPUT),
    OUTPUT_SUBCONTRACT_SUPPLY_OUTBOUND(1, "subcontractSupplyOutbound", "委外超耗出库单", StockChangeTypeEnum.SUBCONTRACTING_OUTPUT),
    OUTPUT_SUBCONTRACT_SUPPLEMENTARY_FOOD(1, "subcontractSupplementaryFood", "委外补料出库单", StockChangeTypeEnum.SUBCONTRACTING_OUTPUT),
    OUTPUT_SUBCONTRACT_PRODUCT_OUTBOUND(1, "subcontractProductOutbound", "委外领料出库单", StockChangeTypeEnum.SUBCONTRACTING_OUTPUT),
	OUTPUT_STOCK_SALE(1, "stockSaleOut", "销售出库单", StockChangeTypeEnum.SALE_OUTPUT),
    OUTPUT_ALLOCATION(1, "transferIssue", "调拨出库", StockChangeTypeEnum.TRANSFER_OUTPUT),
    WORK_CONSUME_OUT_WORK_ORDER(1,"workConsumeOut", "生产超耗出库", StockChangeTypeEnum.OUTPUT),

    SCRAP(1,"scrapReturn", "报废", StockChangeTypeEnum.OUTPUT),

    // 拆批
    SPLIT_BATCH(2, "splitBatch", "拆批", null),
    SUBCONTRACT_ORDER(2, "subcontractOrder", "委外订单", null),
    SUBCONTRACT_MATERIAL(2, "subcontractMaterial", "委外订单物料清单", null),
    SUBCONTRACT_RECEIPT(2, "subcontractReceipt", "委外订单收货单", null),

    CHECK(2, "check", "盘点", StockChangeTypeEnum.CHECK);

    private final Integer inputOrOutput;
    private final String typeCode;
    private final String typeName;
    private final StockChangeTypeEnum changeType;

    StockInputOrOutputTypeEnum(Integer inputOrOutput, String typeCode, String typeName, StockChangeTypeEnum changeType) {
        this.inputOrOutput = inputOrOutput;
        this.typeCode = typeCode;
        this.typeName = typeName;
        this.changeType = changeType;
    }

    public Integer getInputOrOutput() {
        return inputOrOutput;
    }

    public String getTypeCode() {
        return typeCode;
    }

    public String getTypeName() {
        return typeName;
    }

    public StockChangeTypeEnum getChangeType() {
        return changeType;
    }

    public static StockInputOrOutputTypeEnum getEnumByCode(String code) {
        for (StockInputOrOutputTypeEnum typeEnum : StockInputOrOutputTypeEnum.values()) {
            if (typeEnum.typeCode.equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }

    /**
     * 获取入库类型
     *
     * @return
     */
    public static List<String> getInputTypeCode() {
        List<String> typeCodes = new ArrayList<>();
        for (StockInputOrOutputTypeEnum typeEnum : StockInputOrOutputTypeEnum.values()) {
            if (typeEnum.inputOrOutput.equals(0)) {
                typeCodes.add(typeEnum.typeCode);
            }
        }
        return typeCodes;
    }

    /**
     * 获取出库类型
     *
     * @return
     */
    public static List<String> getOutputTypeCode() {
        List<String> typeCodes = new ArrayList<>();
        for (StockInputOrOutputTypeEnum typeEnum : StockInputOrOutputTypeEnum.values()) {
            if (typeEnum.inputOrOutput.equals(1)) {
                typeCodes.add(typeEnum.typeCode);
            }
        }
        return typeCodes;
    }

    public static Integer judgeInOrOutTypeByCode(String typeCode) {
        if (StringUtils.isNotBlank(typeCode)) {
            StockInputOrOutputTypeEnum anEnum = getEnumByCode(typeCode);
            return anEnum.inputOrOutput;
        }
        return null;
    }

    public static String getNameByCode(String typeCode) {
        if (StringUtils.isNotBlank(typeCode)) {
            for (StockInputOrOutputTypeEnum typeEnum : StockInputOrOutputTypeEnum.values()) {
                if (typeEnum.typeCode.equals(typeCode)) {
                    return typeEnum.typeName;
                }
            }
        }
        return null;
    }
}
