package com.yelink.dfscommon.constant.wms.osOrder;


import lombok.Getter;

/**
 * 委外订单状态
 */
public enum OsOrderStatusEnum {
    S_1(1, "编辑"),
    S_2(2, "生效"),
    S_3(3, "完成"),
    S_4(4, "关闭"),
    S_5(5, "取消");

    @Getter
    private int key;
    @Getter
    private String val;

    OsOrderStatusEnum(int key, String val) {
        this.key = key;
        this.val = val;
    }

    public static OsOrderStatusEnum getByKey(int key) {
        for (OsOrderStatusEnum stateEnum : OsOrderStatusEnum.values()) {
            if (stateEnum.key == key) {
                return stateEnum;
            }
        }
        return null;
    }
}
