package com.yelink.dfscommon.constant.qms;

/**
 * <AUTHOR>
 * @description qms对外接口类型枚举
 * @Date 2023/2/17
 */
public enum QmsOpenApiEnum {
    // 送检单
    INSPECTION_SHEET_LIST_BY_CONDITION("inspectionSheet", "getList", "根据查询条件获取送检单列表"),

    ;
    private String modelCode;

    private String interfaceCode;

    private String des;

    QmsOpenApiEnum(String modelCode, String interfaceCode, String des) {
        this.modelCode = modelCode;
        this.interfaceCode = interfaceCode;
        this.des = des;
    }

    public String getModelCode() {
        return modelCode;
    }

    public String getInterfaceCode() {
        return interfaceCode;
    }

    public String getDes() {
        return des;
    }

}
