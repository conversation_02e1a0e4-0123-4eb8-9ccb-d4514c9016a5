package com.yelink.dfscommon.constant;


import lombok.Getter;

/**
 * 通用状态
 */
public enum CommonStatusEnum {
    NO(0, "否"),
    YES(1, "是");

    @Getter
    private int key;
    @Getter
    private String val;

    CommonStatusEnum(int key, String val) {
        this.key = key;
        this.val = val;
    }

    public static CommonStatusEnum getByKey(int key) {
        for (CommonStatusEnum stateEnum : CommonStatusEnum.values()) {
            if (stateEnum.key == key) {
                return stateEnum;
            }
        }
        return null;
    }
}
