package com.yelink.dfscommon.constant.dfs.supplier;

/**
 * @Description: 来料检验清单-物料状态枚举
 * @Author: zhuangwq
 * @Date: 2021/06/12
 */
public enum SupplierBomTypeEnum {

    /**
     * 状态编码及描述
     */
    UNQUALIFIED("0", "不合格"),
    QUALIFIED("1", "合格");

    private String code;
    private String name;

    SupplierBomTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        if (code == null) {
            return null;
        }
        for (SupplierBomTypeEnum typeEnum : SupplierBomTypeEnum.values()) {
            if (typeEnum.code.equals(code)) {
                return typeEnum.name;
            }
        }
        return null;
    }
}
