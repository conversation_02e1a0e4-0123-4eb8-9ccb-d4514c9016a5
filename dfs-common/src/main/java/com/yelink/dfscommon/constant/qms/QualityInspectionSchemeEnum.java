package com.yelink.dfscommon.constant.qms;

/**
 * <AUTHOR>
 * @Description 检验方案和检验项目的状态编码及描述
 * @Date 2021/12/16 9:45
 */
public enum QualityInspectionSchemeEnum {

    /**
     * 检验方案和检验项目的状态编码及描述
     * 1-创建 2-生效 3-废弃
     */
    CREATE(1, "创建"),
    RELEASE(2, "生效"),
    ABANDON(3, "废弃");

    private int code;
    private String name;

    QualityInspectionSchemeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (QualityInspectionSchemeEnum stateEnum : QualityInspectionSchemeEnum.values()) {
            if (stateEnum.code == code) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static Integer getCodeByName(String name) {
        for (QualityInspectionSchemeEnum stateEnum : QualityInspectionSchemeEnum.values()) {
            if (name.equals(stateEnum.name)) {
                return stateEnum.code;
            }
        }
        return null;
    }

}
