package com.yelink.dfscommon.constant;

import java.util.Objects;

/**
 * 用电时段名称
 *
 * <AUTHOR>
 * @Date 2022/7/6 8:49
 */
public enum EnergyTimePeriodEnum {
    /**
     * 尖、峰、平、谷
     */
    SHARP(0, "尖"),
    P<PERSON><PERSON>(1, "峰"),
    SHOULDER(2, "平"),
    OFF_PEAK(3, "谷"),
    DEEP_OFF_PEAK(4, "深谷"),
    ;

    private final Integer code;
    private final String text;

    EnergyTimePeriodEnum(int code, String text) {
        this.code = code;
        this.text = text;
    }

    public String getText() {
        return text;
    }

    public Integer getCode() {
        return code;
    }

    public static EnergyTimePeriodEnum toEnum(Integer code) {
        for (EnergyTimePeriodEnum tag : EnergyTimePeriodEnum.values()) {
            if (Objects.equals(tag.code, code)) {
                return tag;
            }
        }
        return null;
    }
}
