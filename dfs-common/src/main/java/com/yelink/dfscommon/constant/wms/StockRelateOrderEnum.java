package com.yelink.dfscommon.constant.wms;


/**
 * @Description: 出入库关联单据枚举
 * @Author: Chensn
 * @Date: 2020/12/5
 */
public enum StockRelateOrderEnum {

    // 入库关联
    PURCHASE_RECEIPT("purchaseReceipt", "采购收货单"),
    OUTPUT_PICKING_PRODUCT("outputPickingProduct", "生产领料出库单"),
    OUTPUT_PATCH_PRODUCT("outputPatchProduct", "生产补料出库单"),

    INPUT_DEVELOP_TAKE_APP_RETURN("developTakeAppReturn", "其它入库申请单"),

     // 出库关联
    DELIVERY_APPLICATION("deliveryApplication", "销售出货单"),
    PURCHASE("purchase", "采购订单"),
    PRODUCT_ORDER_MATERIAL_LIST("productOrderMaterialList", "用料清单（生产订单）"),
    WORK_ORDER_MATERIAL_LIST("workOrderMaterialList", "用料清单（生产工单）"),
    PRODUCT_ORDER_BOM("productOrderBom", "生产订单BOM"),
    WORK_ORDER_BOM("workOrderBom", "生产工单BOM"),
    PURCHASE_RETURN_APPLICATION("purchaseReturnApplication", "采购退料申请单"),
    OUTPUT_DEVELOP_TAKE_APP_OUT("developTakeAppOut", "其它出库申请单"),

    // 出入库公共
    WORK_ORDER("workOrder", "生产工单"),
    SALE_ORDER("saleOrder", "销售订单"),
    PRODUCT_ORDER("productOrder", "生产订单"),
    OTHER_ORDER("other", ""),
    ;

    private String code;
    private String name;

    StockRelateOrderEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        if (code == null) {
            return null;
        }
        for (StockRelateOrderEnum stateEnum : StockRelateOrderEnum.values()) {
            if (stateEnum.code.equals(code)) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        for (StockRelateOrderEnum stateEnum : StockRelateOrderEnum.values()) {
            if (name.equals(stateEnum.name)) {
                return stateEnum.code;
            }
        }
        return null;
    }
    }
