package com.yelink.dfscommon.constant.qmsProductInspection;


/**
 * <AUTHOR>
 * @Date 2024/5/29 11:14
 */
public enum DocumentTypeEnum {
    /**
     * 单据类型：生产工单、采购订单、采购收料单、生产领料出库单、生产补料出库单、销售订单、销售出库单、项目合同
     */
    WORK_ORDER("workOrder", "生产工单"),
    PURCHASE_ORDER("purchaseOrder", "采购订单"),
    PURCHASE_RECEIVE("purchaseReceive", "采购收料单"),
    PURCHASE_RETURN("purchaseReturn", "采购退料单"),
    PRODUCT_TAKE_OUT("productTakeOut", "生产领料出库单"),
    PRODUCT_SUPPLEMENT_OUT("productSupplementOut", "生产补料出库单"),
    SALE_ORDER("saleOrder", "销售订单"),
    SALE_OUT_ORDER("saleOutOrder", "销售出库单"),
    PRODUCT_ORDER("productOrder", "生产订单"),
    DELIVERY_ORDER("deliveryOrder", "发货通知单"),
    PROJECT_ORDER("projectOrder", "项目合同"),
    RETURN_DELIVERY_NUMBER("returnDeliveryNumber", "退货快递单");

    private final String code;
    private final String name;


    DocumentTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        if (code != null) {
            for (DocumentTypeEnum documentTypeEnum : DocumentTypeEnum.values()) {
                if (documentTypeEnum.code.equals(code)) {
                    return documentTypeEnum.name;
                }
            }
        }
        return null;
    }

}
