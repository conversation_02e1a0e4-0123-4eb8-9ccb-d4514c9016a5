package com.yelink.dfscommon.constant.dfs;


import org.apache.commons.lang3.StringUtils;

/**
 * @Description: 损耗率枚举
 * @Author: zhuangwq
 * @Date: 2020/12/5
 */
public enum MaterialLossRateEnum {

    /**
     */
    LOSS_RATE_DEAL_MULTIPLY_METHOD("1*(1+lossRate)", "计划生产数量*(1+损耗率%)+固定损耗"),
    LOSS_RATE_DEAL_DIVIDE_METHOD("1/(1-lossRate)", "计划生产数量/(1-损耗率%)+固定损耗"),

    ;


    private String code;
    private String name;

    MaterialLossRateEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        if (StringUtils.isNotBlank(code)) {
            for (MaterialLossRateEnum stateEnum : MaterialLossRateEnum.values()) {
                if (stateEnum.code.equals(code)) {
                    return stateEnum.name;
                }
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        if (StringUtils.isNotBlank(name)) {
            for (MaterialLossRateEnum stateEnum : MaterialLossRateEnum.values()) {
                if (name.equals(stateEnum.name)) {
                    return stateEnum.code;
                }
            }
        }
        return null;
    }
}
