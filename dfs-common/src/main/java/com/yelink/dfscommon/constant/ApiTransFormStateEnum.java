package com.yelink.dfscommon.constant;

import com.yelink.dfscommon.entity.CommonEnumInterface;
import lombok.Getter;

/**
 *
 * <AUTHOR>
 */
public enum ApiTransFormStateEnum implements CommonEnumInterface {

    /**
     *
     */
    ENABLE("启动"),
    DISABLE("停用")
    ;

    @Getter
    private final String name;

    ApiTransFormStateEnum(String name) {
        this.name = name;
    }


    @Override
    public Object getCode() {
        return this.name();
    }
}
