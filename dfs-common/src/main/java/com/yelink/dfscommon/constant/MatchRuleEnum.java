package com.yelink.dfscommon.constant;

/**
 * @description: 匹配规则
 * @author: zhuang
 * @time: 2022/1/05
 */
public enum MatchRuleEnum {


    /**
     * 以什么开头
     */
    START_WITH("开头"),
    /**
     * 以什么结尾
     */
    END_WITH("结尾"),
    /**
     * 中间
     */
    BETWEEN_WITH("任意")
    ;

    private String name;

    MatchRuleEnum(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

}
