package com.yelink.dfscommon.constant.dfs;

import com.yelink.dfscommon.provider.constant.KafkaMessageTypeInterface;

/**
 * <AUTHOR>
 * @description dfs事件类型枚举
 * @Date 2023/2/17
 */
public enum DfsEventTypeEnum implements KafkaMessageTypeInterface {

    /**
     * 生产工单相关事件
     */
    WORK_ORDER_DELETE_MESSAGE("workOrder", "delete", "工单删除消息"),
    WORK_ORDER_RETURN_MESSAGE("workOrder", "return", "工单回退消息"),
    /**
     * 质量管理相关
     */
    DEFECT_DEFINE_DELETE_MESSAGE("defectDefine", "delete", "删除不良定义消息"),


    /**
     * 企业微信回调后,根据企业微信事件发送的消息
     */
    WECHAT_APPROVAL_CHANGE("weChat", "approval", "审批申请状态变化回调通知"),
    WECHAT_APPROVAL_RESULT_MESSAGE("weChat", "approvalResult", "审批申请结束通知"),

    /**
     * 任务注册和删除
     */
    TASK_REGISTER("task", "register", "任务注册"),
    TASK_DELETE("task", "delete", "任务删除"),
    TASK_RELATION_REGISTER("task", "relationRegister", "任务关联注册"),
    TASK_RELATION_DELETE("task", "relationDelete", "任务关联删除"),

    /**
     * 下推记录
     */
    ORDER_PUSH_DOWN_RECORD_MESSAGE_ADD("orderPushDownRecord", "add", "单据下推记录新增消息"),
    ORDER_PUSH_DOWN_RECORD_MESSAGE_UPDATE("orderPushDownRecord", "update", "单据下推记录更新消息"),
    ORDER_PUSH_DOWN_RECORD_MESSAGE_DELETE("orderPushDownRecord", "delete", "单据下推记录删除消息"),


    ;
    private String modelCode;

    private String typeCode;

    private String des;

    DfsEventTypeEnum(String modelCode, String typeCode, String des) {
        this.modelCode = modelCode;
        this.typeCode = typeCode;
        this.des = des;
    }

    @Override
    public String getModelCode() {
        return modelCode;
    }

    @Override
    public String getTypeCode() {
        return typeCode;
    }

    @Override
    public String getDes() {
        return des;
    }

}
