package com.yelink.dfscommon.constant;

import java.util.Arrays;

/**
 * @description: 微服务类型
 * @author:
 * @time: 2022/11/11
 */
public enum RegisterInfoTypeEnum {

    /**
     * 微服务类型
     */
    AMS("ams","价值流管理系统"),
    WMS("wms","仓储管理系统"),
    QMS("qms","质量管理系统"),

    ;


    private String code;
    private String name;

    RegisterInfoTypeEnum(String code, String name){
        this.code = code;
        this.name = name;
    }

    public static RegisterInfoTypeEnum getRegisterInfoTypeEnumByCode(String code) {
        if(code == null){
            return null;
        }
        return Arrays.stream(RegisterInfoTypeEnum.values())
                .filter(registerInfoTypeEnum -> registerInfoTypeEnum.getCode().equals(code))
                .findAny()
                .orElse(null);
    }


    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }
}
