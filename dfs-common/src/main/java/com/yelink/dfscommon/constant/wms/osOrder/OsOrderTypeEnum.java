package com.yelink.dfscommon.constant.wms.osOrder;

import lombok.Getter;

/**
 * 委外订单类型
 */
public enum OsOrderTypeEnum {

    wwdd(1, "委外订单"),
    wwddshd(2, "委外订单收货单"),
    wwddrkd(3, "委外订单入库单"),
    wwddylqd(4, "委外订单用料清单"),
    wwblckd(5, "委外补料出库单"),
    wwchckd(6, "委外超耗出库单"),
    wwtlrkd(7, "委外退料入库单"),
    wwllckd(8, "委外领料出库单");

    @Getter
    private int key;
    @Getter
    private String val;

    OsOrderTypeEnum(int key, String val) {
        this.key = key;
        this.val = val;
    }

    public static OsOrderTypeEnum getByKey(int key) {
        for (OsOrderTypeEnum type : OsOrderTypeEnum.values()) {
            if (type.getKey() == key) {
                return type;
            }
        }
        return null;
    }
}
