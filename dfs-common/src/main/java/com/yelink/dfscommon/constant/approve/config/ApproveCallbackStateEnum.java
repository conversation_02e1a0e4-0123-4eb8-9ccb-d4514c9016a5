package com.yelink.dfscommon.constant.approve.config;


/**
 * @Description:
 * @Author: zhengfu
 * @Date: 2020/12/5
 */
public enum ApproveCallbackStateEnum {

    /**
     * 0：待验证，1：验证通过，2：验证失败
     */
    TO_BE_VERIFY(0, "待验证"),
    SUCCESS(1, "验证通过"),
    FAIL(2, "验证失败");

    private Integer code;
    private String name;

    ApproveCallbackStateEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ApproveCallbackStateEnum stateEnum : ApproveCallbackStateEnum.values()) {
            if (stateEnum.code.equals(code)) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static Integer getCodeByName(String name) {
        for (ApproveCallbackStateEnum stateEnum : ApproveCallbackStateEnum.values()) {
            if (name.equals(stateEnum.name)) {
                return stateEnum.code;
            }
        }
        return null;
    }

}
