package com.yelink.dfscommon.constant.wms;


/**
 * @Description: 仓库通用状态枚举
 * @Author: zhuangwq
 * @Date: 2021/10/12
 */
public enum WarehouseCommonStateEnum {

    /**
     * 状态编码及描述
     * 1-创建 2-生效
     */
    CREATED(1, "创建"),
    RELEASED(2, "生效"),
    DEACTIVATE(3, "停用");
    private int code;
    private String name;

    WarehouseCommonStateEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (WarehouseCommonStateEnum stateEnum : WarehouseCommonStateEnum.values()) {
            if (stateEnum.code == code) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static Integer getCodeByName(String name) {
        for (WarehouseCommonStateEnum stateEnum : WarehouseCommonStateEnum.values()) {
            if (name.equals(stateEnum.name)) {
                return stateEnum.code;
            }
        }
        return null;
    }
}
