package com.yelink.dfscommon.api.dfs;

import com.yelink.dfscommon.dto.dfs.GenerateCodeDTO;
import com.yelink.dfscommon.dto.dfs.RuleSeqDTO;
import com.yelink.dfscommon.dto.dfs.GenerateRuleDetailDTO;
import com.yelink.dfscommon.dto.dfs.RuleSeqUpdateDTO;
import com.yelink.dfscommon.pojo.ResponseData;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

/**
 * 编码生成规则
 *
 * <AUTHOR> @Date 2021-09-24
 */
@FeignClient(value = "dfs", path = "/api")
public interface NumberRuleInterface {

    /**
     * 根据规则类型获取编号
     *
     * @param
     * @return
     */
    @GetMapping("/inner/number/rule")
    ResponseData getNumberByRuleType(@RequestParam Integer ruleType);

    /**
     * 根据规则类型获取规则配置
     *
     * @param ruleType
     * @return
     */
    @GetMapping("/inner/list/number/rule")
    ResponseData listRuleByType(@RequestParam String ruleType);

    /**
     * 根据编码规则生成对应的编号
     *
     * @param
     * @return
     */
    @RequestMapping("/inner/numberRules/generate/code")
    ResponseData generateRules(@RequestBody GenerateRuleDetailDTO build);

    @RequestMapping("/inner/numberRules/generateCodeByType")
    ResponseData generateCodeByType(@RequestBody GenerateCodeDTO dto);

    @RequestMapping("/inner/numberRules/generateCodeByTypeAndAdd")
    ResponseData generateCodeByTypeAndAdd(@RequestBody GenerateCodeDTO dto);

    /**
     * 根据id查询编码规则
     *
     * @param
     * @return
     */
    @GetMapping("/inner/number/rule/detail")
    ResponseData getById(@RequestParam(value = "id") Integer numberRuleId);

    /**
     * 更新编码规则的序列号，自动生成序号的seq加1
     * @param
     * @return
     */
    @PostMapping("/inner/rule_seq/add/one")
    ResponseData updateSeqEntityAddOne(@RequestBody RuleSeqUpdateDTO ruleSeqUpdateDTO);

    /**
     * 更新编码规则的序列号，自动生成序号的seq加1
     * @param
     * @return
     */
    @RequestMapping("/inner/number/rule/list")
    ResponseData getByType(@RequestParam(value = "typeCode") String typeCode);

    /**
     * 通过编码规则ID 生成编码
     * @param
     * @return
     */
    @RequestMapping("/inner/numberRules/generate")
    ResponseData getSeqById(@RequestBody RuleSeqDTO ruleSeqDTO);

    /**
     * 根据编码规则生成对应的编号实体
     *
     * @param
     * @return
     */
    @RequestMapping("/inner/numberRules/generate/code/dto")
    ResponseData generateRulesDto(@RequestBody GenerateRuleDetailDTO build);


}
