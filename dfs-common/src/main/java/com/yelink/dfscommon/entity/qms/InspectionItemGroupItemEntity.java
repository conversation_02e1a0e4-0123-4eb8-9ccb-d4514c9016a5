package com.yelink.dfscommon.entity.qms;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Date 2021-12-16 17:33
 */
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TableName("dfs_inspection_item_group_item")
public class InspectionItemGroupItemEntity extends Model<InspectionItemGroupItemEntity> {

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Integer inspectionItemGroupItemId;

    /**
     * 检验项目组id
     */
    @ApiModelProperty("检验项目组id")
    private Integer inspectionItemGroupId;

    /**
     * 检验项目id
     */
    @ApiModelProperty("检验项目id")
    private Integer inspectionItemId;

    /**
     * 检查项目
     */
    @ApiModelProperty("检查项目")
    private InspectionItemEntity inspectionItemEntity;

}
