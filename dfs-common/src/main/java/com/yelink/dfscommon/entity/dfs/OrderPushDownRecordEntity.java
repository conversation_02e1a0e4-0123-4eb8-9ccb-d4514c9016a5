package com.yelink.dfscommon.entity.dfs;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yelink.dfscommon.constant.Constant;
import com.yelink.dfscommon.constant.Constants;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * 单据下推记录表
 *
 * <AUTHOR>
 */
@Data
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName("dfs_order_push_down_record")
public class OrderPushDownRecordEntity extends Model<OrderPushDownRecordEntity> {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 源单据类型
     */
    @ApiModelProperty("源单据类型")
    @TableField(value = "source_order_type")
    private String sourceOrderType;

    /**
     * 源单据编码
     */
    @ApiModelProperty("源单据编码")
    @TableField(value = "source_order_number")
    private String sourceOrderNumber;

    /**
     * 源单据物料行id
     */
    @ApiModelProperty("源单据物料行id")
    @TableField(value = "source_order_material_id")
    private Integer sourceOrderMaterialId;

    /**
     * 源单据物料编码
     */
    @ApiModelProperty("源单据物料编码")
    @TableField(value = "material_code")
    private String materialCode;

    /**
     * 源单据批次号
     */
    @ApiModelProperty("源单据批次号")
    @TableField(value = "source_order_batch")
    private String sourceOrderBatch;
    /**
     * 源单据skuId
     */
    @ApiModelProperty("源单据skuId")
    @TableField(value = "source_sku_id")
    private Integer sourceSkuId;


    /**
     * 源单据下推数量
     */
    @ApiModelProperty("源单据下推数量")
    @TableField(value = "push_down_quantity")
    private Double pushDownQuantity;

    /**
     * 目标单据类型
     */
    @ApiModelProperty("目标单据类型")
    @TableField(value = "target_order_type")
    private String targetOrderType;

    /**
     * 目标单据类型名称
     */
    @ApiModelProperty("目标单据类型名称")
    @TableField(exist = false)
    private String targetOrderTypeName;

    /**
     * 目标单据编号
     */
    @ApiModelProperty("目标单据编号")
    @TableField(value = "target_order_number")
    private String targetOrderNumber;

    /**
     * 目标单据物料编码
     */
    @ApiModelProperty("目标单据物料编码")
    @TableField(value = "target_order_material_code")
    private String targetOrderMaterialCode;

    /**
     * 目标单据物料行id
     */
    @ApiModelProperty("目标单据物料行id")
    @TableField(value = "target_order_material_id")
    private Integer targetOrderMaterialId;

    /**
     * 目标单据批次号
     */
    @ApiModelProperty("目标单据批次号")
    @TableField(value = "target_order_batch")
    private String targetOrderBatch;

    /**
     * 目标单据skuId
     */
    @ApiModelProperty("源单据skuId")
    @TableField(value = "target_sku_id")
    private Integer targetSkuId;

    /**
     * 目标单据下推数量
     */
    @ApiModelProperty("目标单据下推数量")
    @TableField(value = "target_order_push_down_quantity")
    private Double targetOrderPushDownQuantity;

    /**
     * 本次下推的标识编码
     */
    @ApiModelProperty("本次下推的标识编码")
    @TableField(value = "push_down_code")
    private String pushDownCode;

    /**
     * 操作人
     */
    @ApiModelProperty("操作人")
    @TableField(value = "create_by")
    private String createBy;

    /**
     * 单据是否异常
     */
    @ApiModelProperty("单据是否异常")
    @TableField(value = "is_abnormal")
    private Boolean isAbnormal;

    /**
     * 异常情况描述
     */
    @ApiModelProperty("异常情况描述")
    @TableField(value = "abnormal_remark")
    private String abnormalRemark;

    /**
     * 下推记录时间
     */
    @ApiModelProperty("下推记录时间")
    @TableField(value = "record_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date recordDate;

    @ApiModelProperty("创建人昵称")
    @TableField(exist = false)
    private String createByName;

    /**
     * id(一些需要特殊逻辑处理传递的id，作为唯一标识)
     */
    @TableField(exist = false)
    private Integer dealId;

    public String buildSourceNumberSkuCode() {
        Integer skuInfo = sourceSkuId == null? Constants.SKU_ID_DEFAULT_VAL : sourceSkuId;
        return sourceOrderNumber + Constant.UNDERLINE + skuInfo;
    }
    public String buildSourceNumberBarCodeSkuCode() {
        Integer skuInfo = sourceSkuId == null? Constants.SKU_ID_DEFAULT_VAL : sourceSkuId;
        String barCode = StrUtil.isBlank(sourceOrderBatch) ? Constants.NULL_STR : sourceOrderBatch;
        return sourceOrderNumber + Constant.UNDERLINE + skuInfo + Constant.UNDERLINE + barCode;
    }

}
